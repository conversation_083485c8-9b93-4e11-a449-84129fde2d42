"""
Integration tests for the search API endpoints.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

@patch("services.search_service.search_knowledge_graph")
def test_search_endpoint(mock_search_knowledge_graph, test_client):
    """
    Test the search endpoint.
    
    Args:
        mock_search_knowledge_graph: Mocked search_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the search_knowledge_graph function
    mock_search_knowledge_graph.return_value = {
        "entities": [
            {
                "uuid": "entity1",
                "name": "Entity 1",
                "type": "Person"
            },
            {
                "uuid": "entity2",
                "name": "Entity 2",
                "type": "Organization"
            }
        ],
        "relationships": [
            {
                "source": "entity1",
                "target": "entity2",
                "type": "WORKS_FOR",
                "properties": {"confidence": 0.9}
            }
        ],
        "facts": [
            {
                "uuid": "fact1",
                "body": "Entity 1 works for Entity 2",
                "entity_uuid": "entity1"
            }
        ]
    }
    
    # Make a request to the search endpoint
    response = test_client.post(
        "/api/search",
        json={
            "query": "Entity",
            "entity_types": ["Person", "Organization"],
            "relationship_types": ["WORKS_FOR"],
            "min_confidence": 0.8,
            "limit": 10
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "entities" in data
    assert "relationships" in data
    assert "facts" in data
    assert len(data["entities"]) == 2
    assert len(data["relationships"]) == 1
    assert len(data["facts"]) == 1
    assert data["entities"][0]["uuid"] == "entity1"
    assert data["entities"][1]["uuid"] == "entity2"
    assert data["relationships"][0]["source"] == "entity1"
    assert data["relationships"][0]["target"] == "entity2"
    assert data["facts"][0]["uuid"] == "fact1"
    
    # Check that the search parameters were passed to the search_knowledge_graph function
    mock_search_knowledge_graph.assert_called_once()
    search_query = mock_search_knowledge_graph.call_args[0][0]
    assert search_query.query == "Entity"
    assert search_query.entity_types == ["Person", "Organization"]
    assert search_query.relationship_types == ["WORKS_FOR"]
    assert search_query.min_confidence == 0.8
    assert search_query.limit == 10

@patch("services.search_service.search_knowledge_graph")
def test_search_endpoint_minimal(mock_search_knowledge_graph, test_client):
    """
    Test the search endpoint with minimal parameters.
    
    Args:
        mock_search_knowledge_graph: Mocked search_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the search_knowledge_graph function
    mock_search_knowledge_graph.return_value = {
        "entities": [
            {
                "uuid": "entity1",
                "name": "Entity 1",
                "type": "Person"
            }
        ],
        "relationships": [],
        "facts": []
    }
    
    # Make a request to the search endpoint with minimal parameters
    response = test_client.post(
        "/api/search",
        json={
            "query": "Entity"
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "entities" in data
    assert "relationships" in data
    assert "facts" in data
    assert len(data["entities"]) == 1
    assert len(data["relationships"]) == 0
    assert len(data["facts"]) == 0
    assert data["entities"][0]["uuid"] == "entity1"
    
    # Check that the search parameters were passed to the search_knowledge_graph function
    mock_search_knowledge_graph.assert_called_once()
    search_query = mock_search_knowledge_graph.call_args[0][0]
    assert search_query.query == "Entity"
    assert search_query.entity_types is None
    assert search_query.relationship_types is None
    assert search_query.min_confidence is None
    assert search_query.limit == 100  # Default value

@patch("services.search_service.search_knowledge_graph")
def test_search_endpoint_error(mock_search_knowledge_graph, test_client):
    """
    Test the search endpoint with an error.
    
    Args:
        mock_search_knowledge_graph: Mocked search_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the search_knowledge_graph function to raise an exception
    mock_search_knowledge_graph.side_effect = Exception("Test error")
    
    # Make a request to the search endpoint
    response = test_client.post(
        "/api/search",
        json={
            "query": "Entity"
        }
    )
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error searching knowledge graph" in response.json()["detail"]

@patch("services.search_service.search_documents")
def test_search_documents_endpoint(mock_search_documents, test_client):
    """
    Test the search documents endpoint.
    
    Args:
        mock_search_documents: Mocked search_documents function
        test_client: FastAPI test client
    """
    # Mock the search_documents function
    mock_search_documents.return_value = [
        {
            "uuid": "doc1",
            "name": "Document 1",
            "file_path": "/path/to/doc1.pdf"
        },
        {
            "uuid": "doc2",
            "name": "Document 2",
            "file_path": "/path/to/doc2.pdf"
        }
    ]
    
    # Make a request to the search documents endpoint
    response = test_client.get("/api/search/documents?query=test&limit=10")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "documents" in data
    assert "count" in data
    assert data["count"] == 2
    assert len(data["documents"]) == 2
    assert data["documents"][0]["uuid"] == "doc1"
    assert data["documents"][1]["uuid"] == "doc2"
    
    # Check that the search parameters were passed to the search_documents function
    mock_search_documents.assert_called_once_with("test", 10)

@patch("services.search_service.search_documents")
def test_search_documents_endpoint_error(mock_search_documents, test_client):
    """
    Test the search documents endpoint with an error.
    
    Args:
        mock_search_documents: Mocked search_documents function
        test_client: FastAPI test client
    """
    # Mock the search_documents function to raise an exception
    mock_search_documents.side_effect = Exception("Test error")
    
    # Make a request to the search documents endpoint
    response = test_client.get("/api/search/documents?query=test")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error searching documents" in response.json()["detail"]
