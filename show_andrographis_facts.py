#!/usr/bin/env python3
"""
Show the actual Andrographis facts that the Q&A system is finding.
"""

import asyncio

async def show_andrographis_facts():
    """Show the Andrographis facts being retrieved"""
    print("🌿 Andrographis Facts from Your Knowledge Base")
    print("=" * 60)
    
    try:
        from services.qa_service import get_relevant_facts
        
        question = "what can you tell me about the herb andrographis"
        facts = await get_relevant_facts(question, limit=10)
        
        print(f"✅ Found {len(facts)} facts about Andrographis:")
        print()
        
        for i, fact in enumerate(facts, 1):
            body = fact.get('body', 'N/A')
            doc_name = fact.get('document_name', 'Unknown Document')
            doc_id = fact.get('document_id', 'N/A')
            
            print(f"📄 **Fact {i}** (from {doc_name})")
            print(f"   Document ID: {doc_id}")
            print(f"   Content: {body}")
            print()
            print("-" * 80)
            print()
            
        # Extract key information manually
        print("🔍 **Key Information About Andrographis:**")
        print()
        
        all_text = " ".join([fact.get('body', '') for fact in facts])
        
        # Look for key therapeutic uses
        if 'immune' in all_text.lower():
            print("✅ **Immune Support**: Mentioned in the documents")
        if 'cancer' in all_text.lower():
            print("✅ **Cancer Treatment**: Referenced in cancer-related documents")
        if 'chemotherapy' in all_text.lower():
            print("✅ **Chemotherapy Support**: Used alongside conventional treatments")
        if 'astragalus' in all_text.lower():
            print("✅ **Combined with Astragalus**: Often used together")
        if 'echinacea' in all_text.lower():
            print("✅ **Combined with Echinacea**: Part of immune support protocols")
            
        print()
        print("📊 **Summary**: Your knowledge base contains substantial information about")
        print("    Andrographis, particularly its use in immune support and cancer care.")
        
    except Exception as e:
        print(f"❌ Error retrieving facts: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(show_andrographis_facts())
