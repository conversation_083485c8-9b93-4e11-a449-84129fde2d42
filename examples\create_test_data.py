"""
Script to create test data in Neo4j database
"""

import os
import asyncio
from datetime import datetime
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

async def main():
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    print(f"Connecting to Neo4j at {neo4j_uri} with user {neo4j_user}")
    
    # Connect to Neo4j
    driver = AsyncGraphDatabase.driver(
        neo4j_uri, 
        auth=(neo4j_user, neo4j_password)
    )
    
    try:
        async with driver.session() as session:
            # Check if we can connect to the database
            result = await session.run("RETURN 'Connection successful' AS message")
            record = await result.single()
            print(record["message"])
            
            # Create test data
            print("\nCreating test data...")
            
            # Create Episode nodes
            print("Creating Episode nodes...")
            await session.run("""
                CREATE (e1:Episode {
                    id: 'episode-1',
                    name: 'Test Episode 1',
                    body: 'This is a test episode created for demonstration purposes.',
                    created_at: datetime(),
                    source: 'test',
                    source_description: 'Test Data'
                })
                
                CREATE (e2:Episode {
                    id: 'episode-2',
                    name: 'Test Episode 2',
                    body: 'This is another test episode with different content.',
                    created_at: datetime(),
                    source: 'test',
                    source_description: 'Test Data'
                })
            """)
            
            # Create Fact nodes
            print("Creating Fact nodes...")
            await session.run("""
                MATCH (e1:Episode {id: 'episode-1'})
                MATCH (e2:Episode {id: 'episode-2'})
                
                CREATE (f1:Fact {
                    id: 'fact-1',
                    body: 'Knowledge graphs are a powerful way to represent information.',
                    created_at: datetime()
                })
                
                CREATE (f2:Fact {
                    id: 'fact-2',
                    body: 'Google Gemini is a family of large language models.',
                    created_at: datetime()
                })
                
                CREATE (f3:Fact {
                    id: 'fact-3',
                    body: 'Neo4j is a graph database management system.',
                    created_at: datetime()
                })
                
                // Create relationships
                CREATE (e1)-[:CONTAINS]->(f1)
                CREATE (e1)-[:CONTAINS]->(f3)
                CREATE (e2)-[:CONTAINS]->(f2)
                CREATE (f1)-[:RELATED_TO]->(f3)
                CREATE (f2)-[:RELATED_TO]->(f1)
            """)
            
            # Count all nodes
            print("\nCounting all nodes...")
            result = await session.run("MATCH (n) RETURN count(n) AS count")
            record = await result.single()
            total_nodes = record["count"]
            print(f"Total nodes in database: {total_nodes}")
            
            # Count relationships
            print("\nCounting relationships...")
            result = await session.run("MATCH ()-[r]->() RETURN count(r) AS count")
            record = await result.single()
            total_rels = record["count"]
            print(f"Total relationships in database: {total_rels}")
            
            print("\nTest data created successfully!")
            
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close the driver
        await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
