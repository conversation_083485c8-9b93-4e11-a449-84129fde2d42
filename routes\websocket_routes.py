"""
WebSocket routes for real-time progress tracking and operation management.
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, Depends
from typing import Dict, Any
from utils.websocket_manager import get_websocket_manager, WebSocketManager
from utils.operation_state_manager import get_operation_state_manager
from utils.logging_utils import get_logger

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/ws", tags=["websockets"])

@router.websocket("/progress/{operation_id}")
async def websocket_progress_endpoint(
    websocket: WebSocket, 
    operation_id: str,
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """
    WebSocket endpoint for real-time progress updates.
    
    Args:
        websocket: WebSocket connection
        operation_id: ID of the operation to track
        websocket_manager: WebSocket manager instance
    """
    try:
        # Connect the WebSocket
        await websocket_manager.connect(websocket, operation_id)
        
        # Send any existing metadata
        metadata = websocket_manager.get_operation_metadata(operation_id)
        if metadata:
            await websocket.send_text(json.dumps({
                "type": "operation_metadata",
                "operation_id": operation_id,
                "data": metadata
            }))
        
        # Keep the connection alive and handle incoming messages
        while True:
            try:
                # Wait for messages from client
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Handle different message types
                if message.get("type") == "ping":
                    # Respond to ping with pong
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                
                elif message.get("type") == "cancel_operation":
                    # Handle cancellation request
                    logger.info(f"Cancellation requested for operation {operation_id}")
                    await websocket_manager.cancel_operation(operation_id)
                    break
                
                elif message.get("type") == "request_status":
                    # Send current status if available
                    # This will be handled by the progress tracking system
                    pass
                    
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.warning(f"Invalid JSON received from WebSocket for operation {operation_id}")
            except Exception as e:
                logger.error(f"Error handling WebSocket message for operation {operation_id}: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": str(e)
                }))
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for operation {operation_id}")
    except Exception as e:
        logger.error(f"WebSocket error for operation {operation_id}: {e}")
    finally:
        # Clean up the connection
        await websocket_manager.disconnect(websocket, operation_id)

@router.websocket("/operations")
async def websocket_operations_endpoint(
    websocket: WebSocket,
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """
    WebSocket endpoint for monitoring all active operations.
    
    Args:
        websocket: WebSocket connection
        websocket_manager: WebSocket manager instance
    """
    await websocket.accept()
    
    try:
        # Send initial active operations list
        active_ops = websocket_manager.get_active_operations()
        await websocket.send_text(json.dumps({
            "type": "active_operations",
            "data": active_ops
        }))
        
        # Keep connection alive and send periodic updates
        while True:
            try:
                # Wait for messages or timeout after 30 seconds
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                message = json.loads(data)
                
                if message.get("type") == "ping":
                    await websocket.send_text(json.dumps({
                        "type": "pong",
                        "timestamp": message.get("timestamp")
                    }))
                elif message.get("type") == "get_active_operations":
                    active_ops = websocket_manager.get_active_operations()
                    await websocket.send_text(json.dumps({
                        "type": "active_operations",
                        "data": active_ops
                    }))
                    
            except asyncio.TimeoutError:
                # Send periodic heartbeat
                active_ops = websocket_manager.get_active_operations()
                await websocket.send_text(json.dumps({
                    "type": "heartbeat",
                    "active_operations": active_ops
                }))
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                logger.warning("Invalid JSON received from operations WebSocket")
            except Exception as e:
                logger.error(f"Error in operations WebSocket: {e}")
                break
                
    except WebSocketDisconnect:
        logger.info("Operations WebSocket disconnected")
    except Exception as e:
        logger.error(f"Operations WebSocket error: {e}")
    finally:
        try:
            await websocket.close()
        except:
            pass

# HTTP endpoints for operation management
@router.post("/cancel/{operation_id}")
async def cancel_operation_http(
    operation_id: str,
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """
    HTTP endpoint to cancel an operation.
    
    Args:
        operation_id: ID of the operation to cancel
        websocket_manager: WebSocket manager instance
    """
    try:
        await websocket_manager.cancel_operation(operation_id)
        return {
            "success": True,
            "message": f"Cancellation requested for operation {operation_id}",
            "operation_id": operation_id
        }
    except Exception as e:
        logger.error(f"Error cancelling operation {operation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/active-operations")
async def get_active_operations_http(
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """
    HTTP endpoint to get active operations.
    
    Args:
        websocket_manager: WebSocket manager instance
    """
    try:
        active_ops = websocket_manager.get_active_operations()
        return {
            "active_operations": active_ops,
            "total_active": len(active_ops)
        }
    except Exception as e:
        logger.error(f"Error getting active operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/operation/{operation_id}/status")
async def get_operation_status_http(
    operation_id: str,
    websocket_manager: WebSocketManager = Depends(get_websocket_manager)
):
    """
    HTTP endpoint to get operation status.

    Args:
        operation_id: ID of the operation
        websocket_manager: WebSocket manager instance
    """
    try:
        # Get persistent state
        state_manager = get_operation_state_manager()
        persistent_state = state_manager.get_operation(operation_id)

        # Get live WebSocket state
        metadata = websocket_manager.get_operation_metadata(operation_id)
        is_cancelled = websocket_manager.is_cancelled(operation_id)
        has_connections = operation_id in websocket_manager.active_connections

        return {
            "operation_id": operation_id,
            "persistent_state": persistent_state,
            "metadata": metadata,
            "is_cancelled": is_cancelled,
            "has_active_connections": has_connections,
            "connection_count": len(websocket_manager.active_connections.get(operation_id, []))
        }
    except Exception as e:
        logger.error(f"Error getting operation status for {operation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/operations/recent")
async def get_recent_operations():
    """
    HTTP endpoint to get recent operations from persistent storage.
    """
    try:
        state_manager = get_operation_state_manager()
        recent_operations = state_manager.get_recent_operations(limit=50)

        return {
            "operations": recent_operations,
            "total": len(recent_operations)
        }
    except Exception as e:
        logger.error(f"Error getting recent operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/operations/active-persistent")
async def get_active_persistent_operations():
    """
    HTTP endpoint to get active operations from persistent storage.
    """
    try:
        state_manager = get_operation_state_manager()
        active_operations = state_manager.get_active_operations()

        return {
            "operations": active_operations,
            "total": len(active_operations)
        }
    except Exception as e:
        logger.error(f"Error getting active persistent operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/operations/cleanup")
async def cleanup_old_operations(days_old: int = 7):
    """
    HTTP endpoint to clean up old operations.

    Args:
        days_old: Number of days after which to delete operations
    """
    try:
        state_manager = get_operation_state_manager()
        deleted_count = state_manager.cleanup_old_operations(days_old)

        return {
            "success": True,
            "deleted_count": deleted_count,
            "message": f"Cleaned up {deleted_count} operations older than {days_old} days"
        }
    except Exception as e:
        logger.error(f"Error cleaning up operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/health")
async def health_check():
    """
    Health check endpoint for monitoring system status.
    """
    try:
        websocket_manager = get_websocket_manager()
        state_manager = get_operation_state_manager()

        # Check WebSocket connections
        active_ws_connections = websocket_manager.get_active_operations()

        # Check persistent storage
        active_persistent_ops = state_manager.get_active_operations()
        recent_ops = state_manager.get_recent_operations(limit=10)

        # Basic system checks
        import psutil
        import os

        system_info = {
            "cpu_percent": psutil.cpu_percent(interval=1),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_percent": psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent,
            "process_count": len(psutil.pids())
        }

        # Determine overall health
        health_status = "healthy"
        issues = []

        if system_info["cpu_percent"] > 90:
            health_status = "warning"
            issues.append("High CPU usage")

        if system_info["memory_percent"] > 90:
            health_status = "warning"
            issues.append("High memory usage")

        if system_info["disk_percent"] > 90:
            health_status = "warning"
            issues.append("High disk usage")

        if len(active_persistent_ops) > 50:
            health_status = "warning"
            issues.append("Many active operations")

        return {
            "status": health_status,
            "timestamp": datetime.utcnow().isoformat(),
            "issues": issues,
            "websocket_connections": len(active_ws_connections),
            "active_operations": len(active_persistent_ops),
            "recent_operations": len(recent_ops),
            "system_info": system_info,
            "uptime_seconds": time.time() - psutil.boot_time() if hasattr(psutil, 'boot_time') else None
        }

    except Exception as e:
        logger.error(f"Error in health check: {e}")
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat(),
            "error": str(e)
        }
