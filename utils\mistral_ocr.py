"""
Mistral OCR integration for PDF processing
"""

import os
import base64
import logging
from typing import Optional, Dict, Any, List
import asyncio
from pathlib import Path
from dotenv import load_dotenv
from mistralai import Mistral

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Mistral API key
MISTRAL_API_KEY = os.environ.get('MISTRAL_API_KEY')
if not MISTRAL_API_KEY:
    logger.warning("Mistral API key not found in environment variables. OCR functionality will not be available.")

class MistralOCRProcessor:
    """
    Class for processing PDFs using Mistral's OCR capabilities
    """

    def __init__(self, api_key: Optional[str] = None, model: str = "mistral-ocr-latest"):
        """
        Initialize the Mistral OCR processor

        Args:
            api_key: Mistral API key (defaults to environment variable)
            model: Mistral OCR model to use
        """
        self.api_key = api_key or MISTRAL_API_KEY
        self.model = model

        if not self.api_key:
            logger.error("No Mistral API key provided. OCR functionality will not be available.")
        else:
            self.client = Mistral(api_key=self.api_key)
            logger.info(f"Initialized Mistral OCR processor with model: {model}")

    def encode_pdf(self, pdf_path: str) -> Optional[str]:
        """
        Encode a PDF file to base64

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Base64 encoded string of the PDF file or None if an error occurred
        """
        try:
            with open(pdf_path, "rb") as pdf_file:
                return base64.b64encode(pdf_file.read()).decode('utf-8')
        except FileNotFoundError:
            logger.error(f"Error: The file {pdf_path} was not found.")
            return None
        except Exception as e:
            logger.error(f"Error encoding PDF: {e}")
            return None

    async def process_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        Process a PDF file using Mistral's OCR API

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary containing the OCR results
        """
        if not self.api_key:
            return {"error": "No Mistral API key provided. OCR functionality is not available."}

        try:
            # Encode the PDF to base64
            base64_pdf = self.encode_pdf(pdf_path)
            if not base64_pdf:
                return {"error": f"Failed to encode PDF file: {pdf_path}"}

            # Process the PDF with Mistral OCR
            logger.info(f"Processing PDF with Mistral OCR: {pdf_path}")

            # Use asyncio to run the synchronous Mistral API call
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model=self.model,
                    document={
                        "type": "document_url",
                        "document_url": f"data:application/pdf;base64,{base64_pdf}"
                    }
                )
            )

            # Extract the text content from the OCR response
            text_content = self._extract_text_from_ocr_response(ocr_response)

            logger.info(f"Successfully processed PDF with Mistral OCR: {pdf_path}")

            return {
                "success": True,
                "text": text_content,
                "metadata": {
                    "model": self.model,
                    "source": "mistral-ocr",
                    "file_path": pdf_path
                }
            }

        except Exception as e:
            logger.error(f"Error processing PDF with Mistral OCR: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def extract_text_from_pdf(self, pdf_path: str, max_pages: Optional[int] = None) -> str:
        """
        Extract text from a PDF file using Mistral's OCR API
        This function is compatible with the existing extract_text_from_pdf function

        Args:
            pdf_path: Path to the PDF file
            max_pages: Maximum number of pages to process (not used, included for compatibility)

        Returns:
            Extracted text from the PDF
        """
        result = await self.process_pdf(pdf_path)

        if result.get("success", False):
            return result["text"]
        else:
            logger.error(f"Error extracting text from PDF: {result.get('error', 'Unknown error')}")
            return ""

    async def extract_text_from_document(self, file_path: str) -> str:
        """
        Extract text from various document types (PowerPoint, Word, etc.) using Mistral's OCR API

        Args:
            file_path: Path to the document file

        Returns:
            Extracted text as string
        """
        if not self.api_key:
            logger.error("No Mistral API key available")
            return ""

        try:
            # Determine file type and encode appropriately
            file_path_obj = Path(file_path)
            file_ext = file_path_obj.suffix.lower()

            logger.debug(f"Processing file: {file_path_obj.name}, extension: {file_ext}")

            # Encode the document to base64
            base64_doc = self.encode_document(file_path)
            if not base64_doc:
                logger.error(f"Failed to encode document file: {file_path}")
                return ""

            # Determine MIME type based on extension
            mime_type_map = {
                '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                '.ppt': 'application/vnd.ms-powerpoint',
                '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                '.doc': 'application/msword',
                '.pdf': 'application/pdf',
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.gif': 'image/gif',
                '.bmp': 'image/bmp',
                '.tiff': 'image/tiff',
                '.tif': 'image/tiff'
            }

            mime_type = mime_type_map.get(file_ext, 'application/octet-stream')

            logger.debug(f"Detected MIME type: {mime_type} for extension: {file_ext}")

            # Special handling for OneNote files - convert to images first, then process with Mistral OCR
            if file_ext == '.one':
                logger.info(f"Processing OneNote file {file_path} by converting to images for Mistral OCR")
                return await self._process_onenote_with_images(file_path)

            # Warn if we're using the fallback MIME type
            if mime_type == 'application/octet-stream':
                logger.warning(f"Using fallback MIME type for file: {file_path_obj.name} with extension: {file_ext}")

            # Process the document with Mistral OCR
            logger.info(f"Processing document with Mistral OCR: {file_path}")

            # Use asyncio to run the synchronous Mistral API call
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model=self.model,
                    document={
                        "type": "document_url",
                        "document_url": f"data:{mime_type};base64,{base64_doc}"
                    }
                )
            )

            # Extract text from response
            if hasattr(ocr_response, 'text') and ocr_response.text:
                text = ocr_response.text.strip()
                logger.info(f"Mistral OCR extracted {len(text)} characters from {file_path_obj.name}")
                return text
            else:
                logger.warning(f"Mistral OCR returned no text for {file_path}")
                return ""

        except Exception as e:
            logger.error(f"Error extracting text from document {file_path}: {e}")
            return ""

    def encode_document(self, file_path: str) -> Optional[str]:
        """
        Encode a document file to base64

        Args:
            file_path: Path to the document file

        Returns:
            Base64 encoded string or None if encoding fails
        """
        try:
            with open(file_path, 'rb') as file:
                encoded = base64.b64encode(file.read()).decode('utf-8')
                logger.debug(f"Successfully encoded document: {Path(file_path).name}")
                return encoded
        except Exception as e:
            logger.error(f"Error encoding document {file_path}: {e}")
            return None

    async def extract_references_from_pdf(self, pdf_path: str) -> Dict[str, Any]:
        """
        Extract references from a PDF file using Mistral's OCR API with enhanced prompts

        Args:
            pdf_path: Path to the PDF file

        Returns:
            Dictionary containing extracted references and metadata
        """
        if not self.api_key:
            return {"error": "No Mistral API key provided. OCR functionality is not available."}

        try:
            # Encode the PDF to base64
            base64_pdf = self.encode_pdf(pdf_path)
            if not base64_pdf:
                return {"error": f"Failed to encode PDF file: {pdf_path}"}

            # Enhanced prompt for reference extraction
            reference_prompt = """
            Please extract ALL bibliographic references from this document. Focus specifically on:

            1. REFERENCE SECTIONS: Look for sections titled "References", "Bibliography", "Literature Cited", or "Citations"
            2. NUMBERED REFERENCES: Extract each numbered reference (1., 2., 3., etc.) completely
            3. AUTHOR-YEAR CITATIONS: Extract in-text citations like (Smith et al., 2020)
            4. COMPLETE CITATIONS: Include full journal citations with authors, titles, journals, years, volumes, pages
            5. BOOK REFERENCES: Include book citations with authors, titles, publishers, years
            6. DOI AND PMID: Extract any DOI or PubMed ID references

            Extract the complete text of each reference exactly as it appears. Do not summarize or abbreviate.
            Pay special attention to reference lists at the end of the document.

            Return the full text content with special attention to preserving reference formatting.
            """

            # Process the PDF with enhanced reference extraction prompt
            logger.info(f"Extracting references from PDF with Mistral OCR: {pdf_path}")

            # Use asyncio to run the synchronous Mistral API call
            loop = asyncio.get_event_loop()
            ocr_response = await loop.run_in_executor(
                None,
                lambda: self.client.ocr.process(
                    model=self.model,
                    document={
                        "type": "document_url",
                        "document_url": f"data:application/pdf;base64,{base64_pdf}"
                    },
                    prompt=reference_prompt
                )
            )

            # Extract the text content from the OCR response
            text_content = self._extract_text_from_ocr_response(ocr_response)

            logger.info(f"Successfully extracted references from PDF with Mistral OCR: {pdf_path}")

            return {
                "success": True,
                "text": text_content,
                "extraction_type": "references",
                "metadata": {
                    "model": self.model,
                    "source": "mistral-ocr-references",
                    "file_path": pdf_path,
                    "prompt_type": "reference_extraction"
                }
            }

        except Exception as e:
            logger.error(f"Error extracting references from PDF with Mistral OCR: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _extract_text_from_ocr_response(self, ocr_response) -> str:
        """
        Extract clean text from Mistral OCR response, filtering out complex objects.

        Args:
            ocr_response: The OCR response object from Mistral API

        Returns:
            Clean text content without OCR metadata objects
        """
        try:
            # Try different response formats
            if hasattr(ocr_response, 'content'):
                text_content = ocr_response.content
            elif hasattr(ocr_response, 'text'):
                text_content = ocr_response.text
            elif hasattr(ocr_response, 'pages') and ocr_response.pages:
                # Extract markdown content from pages
                text_parts = []
                for page in ocr_response.pages:
                    if hasattr(page, 'markdown') and page.markdown:
                        text_parts.append(page.markdown)
                text_content = '\n\n'.join(text_parts)
            else:
                # Last resort: convert to string but clean it
                text_content = str(ocr_response)
                logger.warning(f"OCR response format not recognized, using raw response: {text_content[:100]}...")

            # Clean the text content to remove OCR metadata
            cleaned_text = self._clean_ocr_text(text_content)

            return cleaned_text

        except Exception as e:
            logger.error(f"Error extracting text from OCR response: {e}")
            return ""

    def _clean_ocr_text(self, text: str) -> str:
        """
        Clean OCR text by removing metadata objects and artifacts.

        Args:
            text: Raw text from OCR response

        Returns:
            Cleaned text without OCR metadata
        """
        import re

        if not isinstance(text, str):
            text = str(text)

        # Remove OCR object representations
        text = re.sub(r'OCRPageObject\([^)]*\)', '', text)
        text = re.sub(r'OCRImageObject\([^)]*\)', '', text)
        text = re.sub(r'OCRPageDimensions\([^)]*\)', '', text)
        text = re.sub(r'pages=\[[^\]]*\]', '', text)
        text = re.sub(r'images=\[[^\]]*\]', '', text)
        text = re.sub(r'dimensions=[^,)]*', '', text)
        text = re.sub(r'index=\d+', '', text)
        text = re.sub(r'markdown=["\'][^"\']*["\']', '', text)

        # Remove image references
        text = re.sub(r'img-\d+\.jpeg', '', text)
        text = re.sub(r'!\[.*?\]\(.*?\)', '', text)

        # Additional aggressive cleaning for database safety
        # Remove problematic characters that can break Cypher queries
        try:
            text = re.sub(r'[^\x20-\x7E\s]', '', text)  # Keep only printable ASCII and whitespace
        except re.error:
            # If regex fails, do simple character filtering
            text = ''.join(char for char in text if ord(char) >= 32 or char in '\t\n\r')

        # Safe character replacements without regex
        text = text.replace(';', ',')    # Replace semicolons to prevent multiple statements
        text = text.replace('`', "'")    # Replace backticks
        text = text.replace('\x00', '')  # Remove null bytes

        # Normalize multiple backslashes safely
        while '\\\\' in text:
            text = text.replace('\\\\', '\\')  # Normalize multiple backslashes

        # Clean up extra whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Ensure text is not too long (significantly increased limit for large documents)
        if len(text) > 500000:  # Increased to 500,000 characters for large academic documents
            text = text[:500000] + "..."

        return text.strip()

    async def _process_onenote_with_images(self, file_path: str) -> str:
        """
        Process OneNote file by converting to images and using Mistral OCR.

        Args:
            file_path: Path to the OneNote file

        Returns:
            Extracted text from all pages
        """
        try:
            logger.info(f"Converting OneNote to images for OCR processing: {file_path}")

            # Try to use the OneNote processor to convert to images
            try:
                from processors.onenote_processor import OneNoteProcessor

                onenote_processor = OneNoteProcessor()

                # Check if the processor has image conversion capability
                if hasattr(onenote_processor, 'convert_to_images'):
                    image_paths = await onenote_processor.convert_to_images(file_path)

                    if image_paths:
                        logger.info(f"Converted OneNote to {len(image_paths)} images")

                        # Process each image with Mistral OCR
                        all_text = ""
                        for i, image_path in enumerate(image_paths, 1):
                            try:
                                logger.info(f"Processing image {i}/{len(image_paths)}: {image_path}")

                                # Use the image processing method
                                image_text = await self.extract_text_from_image(image_path)

                                if image_text:
                                    all_text += f"\n\n--- PAGE {i} ---\n{image_text}"
                                    logger.info(f"Extracted {len(image_text)} characters from page {i}")

                            except Exception as e:
                                logger.warning(f"Error processing image {i}: {e}")
                                continue

                        if all_text:
                            logger.info(f"Successfully extracted {len(all_text)} total characters from OneNote file")
                            return all_text.strip()
                        else:
                            logger.warning("No text extracted from any OneNote images")
                            return ""

                    else:
                        logger.warning("No images generated from OneNote file")
                        return ""

                else:
                    logger.warning("OneNote processor does not support image conversion")
                    return ""

            except ImportError:
                logger.warning("OneNote processor not available")
                return ""

        except Exception as e:
            logger.error(f"Error processing OneNote file with images: {e}")
            return ""

# Async function to test the OCR processor
async def test_ocr_processor():
    """Test the Mistral OCR processor with a sample PDF"""
    processor = MistralOCRProcessor()
    pdf_path = "Documents/sample.pdf"  # Replace with an actual PDF path

    if os.path.exists(pdf_path):
        result = await processor.process_pdf(pdf_path)
        if result.get("success", False):
            print(f"Successfully extracted {len(result['text'])} characters of text")
            print(f"First 500 characters: {result['text'][:500]}...")
        else:
            print(f"Error: {result.get('error', 'Unknown error')}")
    else:
        print(f"PDF file not found: {pdf_path}")

if __name__ == "__main__":
    asyncio.run(test_ocr_processor())
