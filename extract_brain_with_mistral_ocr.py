#!/usr/bin/env python3
"""
Extract ALL content and references from Brain.one using Mistral OCR.
This script will properly process the OneNote file to find all 68 references.
"""

import asyncio
import os
import sys
import re
import csv
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add current directory to path
sys.path.append(os.getcwd())

async def extract_brain_with_mistral():
    """Extract all content from Brain.one using Mistral OCR approach."""
    
    print("🧠 BRAIN.ONE MISTRAL OCR REFERENCE EXTRACTION")
    print("=" * 80)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    try:
        # Use the enhanced document processor with Mistral OCR
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        
        processor = EnhancedDocumentProcessor()
        print("\n🔄 Processing with Enhanced Document Processor + Mistral OCR...")
        
        # Process the document with full extraction
        result = await processor.process_document(
            file_path=str(brain_file),
            chunk_size=2000,  # Larger chunks to capture full references
            overlap=100,
            extract_entities=True,
            extract_references=True,
            extract_metadata=True,
            generate_embeddings=True,
            force_ocr=True  # Force OCR processing
        )
        
        if result and result.get('success'):
            print("✅ Document processed successfully!")
            
            # Get the extracted content
            content = result.get('content', '')
            print(f"📝 Extracted content length: {len(content)} characters")
            
            # Save the full extracted content
            content_file = f"brain_mistral_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(content_file, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"💾 Saved content to {content_file}")
            
            # Extract references using comprehensive patterns
            print("\n🔄 Extracting references from content...")
            references = extract_all_references(content)
            
            if references:
                print(f"🎉 FOUND {len(references)} REFERENCES!")
                
                # Save references to CSV
                await save_references_csv(references, brain_file.name)
                
                # Display summary
                print("\n📚 REFERENCE SUMMARY:")
                print("-" * 60)
                
                # Group by type
                numbered_refs = [r for r in references if 'numbered' in r.get('extraction_method', '')]
                author_year_refs = [r for r in references if 'author_year' in r.get('extraction_method', '')]
                journal_refs = [r for r in references if 'journal' in r.get('extraction_method', '')]
                
                print(f"   Numbered references: {len(numbered_refs)}")
                print(f"   Author-year format: {len(author_year_refs)}")
                print(f"   Journal citations: {len(journal_refs)}")
                
                # Show first 15 references
                print(f"\n📖 FIRST 15 REFERENCES:")
                for i, ref in enumerate(references[:15], 1):
                    title = ref.get('title', '')
                    authors = ref.get('authors', '')
                    year = ref.get('year', '')
                    
                    if title:
                        print(f"{i:2d}. {title[:70]}...")
                    elif authors and year:
                        print(f"{i:2d}. {authors} ({year})")
                    else:
                        raw_text = ref.get('raw_text', '')
                        print(f"{i:2d}. {raw_text[:70]}...")
                
                if len(references) > 15:
                    print(f"... and {len(references) - 15} more references")
                
            else:
                print("❌ No references found")
                
                # Show sample content for debugging
                print("\n🔍 SAMPLE CONTENT (first 1000 chars):")
                print("-" * 40)
                print(content[:1000])
                print("-" * 40)
        
        else:
            print("❌ Document processing failed")
            if result:
                print(f"Error details: {result}")
    
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def extract_all_references(content: str) -> List[Dict[str, Any]]:
    """Extract references using comprehensive patterns."""
    
    references = []
    
    # Pattern 1: Numbered references (1. Author... or [1] Author...)
    patterns = [
        r'(\d+)\.\s+([A-Z][^.]*\..*?)(?=\d+\.\s+[A-Z]|$)',  # 1. Author...
        r'\[(\d+)\]\s+([A-Z][^.]*\..*?)(?=\[\d+\]|$)',      # [1] Author...
        r'(\d+)\)\s+([A-Z][^.]*\..*?)(?=\d+\)|$)',          # 1) Author...
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, content, re.DOTALL | re.MULTILINE)
        for num, ref_text in matches:
            if len(ref_text.strip()) > 30:  # Filter short matches
                ref = parse_reference(ref_text.strip(), f"numbered_{num}")
                if ref:
                    references.append(ref)
    
    # Pattern 2: Author-year format
    author_year_pattern = r'([A-Z][a-z]+(?:,\s*[A-Z]\.?)*(?:\s+et\s+al\.?)?)\s*\((\d{4})\)\.\s*([^.]+\..*?)(?=[A-Z][a-z]+\s*\(\d{4}\)|$)'
    matches = re.findall(author_year_pattern, content, re.DOTALL)
    
    for author, year, ref_text in matches:
        if len(ref_text.strip()) > 30:
            ref = parse_reference(f"{author} ({year}). {ref_text}", "author_year")
            if ref:
                references.append(ref)
    
    # Pattern 3: Journal citations
    journal_pattern = r'([^.]{15,})\.\s*([A-Z][^.]{5,})\.\s*(\d{4});?(\d+)?:?(\d+-?\d*)?\.?'
    matches = re.findall(journal_pattern, content)
    
    for title, journal, year, volume, pages in matches:
        if (len(title.strip()) > 15 and len(journal.strip()) > 5 and 
            not title.strip().startswith('http') and 
            not journal.strip().startswith('http')):
            
            ref = {
                'title': title.strip(),
                'journal': journal.strip(),
                'year': year,
                'volume': volume if volume else '',
                'pages': pages if pages else '',
                'extraction_method': 'journal_pattern',
                'raw_text': f"{title}. {journal}. {year};{volume}:{pages}"
            }
            references.append(ref)
    
    # Pattern 4: DOI and PMID references
    doi_pattern = r'(doi:\s*10\.\d+/[^\s]+)'
    doi_matches = re.findall(doi_pattern, content, re.IGNORECASE)
    
    for doi in doi_matches:
        ref = {
            'doi': doi.strip(),
            'extraction_method': 'doi_pattern',
            'raw_text': doi
        }
        references.append(ref)
    
    # Remove duplicates
    unique_refs = []
    seen_texts = set()
    
    for ref in references:
        # Create a signature for deduplication
        signature = (
            ref.get('title', '').lower()[:50] + 
            ref.get('authors', '').lower()[:30] + 
            ref.get('year', '')
        )
        
        if signature not in seen_texts and len(signature) > 10:
            seen_texts.add(signature)
            unique_refs.append(ref)
    
    return unique_refs

def parse_reference(ref_text: str, method: str) -> Dict[str, Any]:
    """Parse reference text into components."""
    
    ref = {'raw_text': ref_text, 'extraction_method': method}
    
    # Extract year
    year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
    if year_match:
        ref['year'] = year_match.group()
    
    # Extract title (first complete sentence)
    title_patterns = [
        r'^([^.]+\.)',  # First sentence
        r'^\w+.*?\.\s*([^.]+\.)',  # After author name
        r'\(\d{4}\)\.\s*([^.]+\.)',  # After year
    ]
    
    for pattern in title_patterns:
        title_match = re.search(pattern, ref_text)
        if title_match:
            title = title_match.group(1).strip('.')
            if len(title) > 10:
                ref['title'] = title
                break
    
    # Extract journal
    journal_patterns = [
        r'\.\s*([A-Z][^.]+)\.\s*\d{4}',  # Journal before year
        r'\d{4};\s*([^;]+);',            # Journal after year
        r'In:\s*([^.]+)\.',              # Book chapters
    ]
    
    for pattern in journal_patterns:
        journal_match = re.search(pattern, ref_text)
        if journal_match:
            journal = journal_match.group(1).strip()
            if len(journal) > 3:
                ref['journal'] = journal
                break
    
    # Extract authors (beginning of reference)
    author_patterns = [
        r'^([^.]+(?:\set\sal\.?)?)',  # Standard author format
        r'^([A-Z][a-z]+(?:,\s*[A-Z]\.?)*)',  # Last, F.M. format
    ]
    
    for pattern in author_patterns:
        author_match = re.search(pattern, ref_text)
        if author_match:
            authors = author_match.group(1).strip()
            if len(authors) > 3:
                ref['authors'] = authors
                break
    
    # Extract volume and pages
    vol_page_match = re.search(r'(\d+):(\d+-?\d*)', ref_text)
    if vol_page_match:
        ref['volume'] = vol_page_match.group(1)
        ref['pages'] = vol_page_match.group(2)
    
    # Extract DOI
    doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', ref_text, re.IGNORECASE)
    if doi_match:
        ref['doi'] = doi_match.group(1)
    
    return ref

async def save_references_csv(references: List[Dict[str, Any]], source_file: str):
    """Save references to CSV file."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"references/brain_mistral_all_references_{timestamp}.csv"
    
    # Ensure references directory exists
    Path("references").mkdir(exist_ok=True)
    
    fieldnames = [
        'source_document', 'extraction_method', 'reference_text', 'authors', 
        'title', 'year', 'journal', 'volume', 'pages', 'doi', 'pmid',
        'extraction_date', 'confidence_score'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for ref in references:
            row = {
                'source_document': source_file,
                'extraction_method': ref.get('extraction_method', 'mistral_comprehensive'),
                'reference_text': ref.get('raw_text', ''),
                'authors': ref.get('authors', ''),
                'title': ref.get('title', ''),
                'year': ref.get('year', ''),
                'journal': ref.get('journal', ''),
                'volume': ref.get('volume', ''),
                'pages': ref.get('pages', ''),
                'doi': ref.get('doi', ''),
                'pmid': ref.get('pmid', ''),
                'extraction_date': datetime.now().isoformat(),
                'confidence_score': 0.85
            }
            writer.writerow(row)
    
    print(f"💾 Saved {len(references)} references to {filename}")

if __name__ == "__main__":
    asyncio.run(extract_brain_with_mistral())
