"""
Fact UUID validator.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class FactValidator(BaseValidator):
    """Validator for Fact node UUIDs."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "facts_total": 0,
            "facts_missing_uuid": 0,
            "facts_fixed": 0
        }

    async def validate(self):
        """Check Fact nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Fact nodes
        count_query = """
        MATCH (f:Fact)
        RETURN count(f) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["facts_total"] = count_result[1][0][0]

        # Find Fact nodes without UUIDs
        missing_query = """
        MATCH (f:Fact)
        WHERE f.uuid IS NULL
        RETURN id(f) as id
        LIMIT 1000
        """

        missing_result = adapter.execute_cypher(missing_query)
        facts = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                facts.append({
                    "id": row[0]
                })

            self.stats["facts_missing_uuid"] = len(facts)
            self.log_info(f"Found {len(facts)} Fact nodes without UUIDs")

            if self.fix:
                fixed_count = await self._fix_facts(facts)
                self.stats["facts_fixed"] = fixed_count

    async def _fix_facts(self, facts: List[Dict[str, Any]]) -> int:
        """
        Fix Fact nodes missing UUIDs.

        Args:
            facts: List of Fact nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for fact in facts:
            fact_uuid = self.generate_new_uuid()
            timestamp = self.get_timestamp()

            update_query = f"""
            MATCH (f:Fact)
            WHERE id(f) = {fact['id']}
            SET f.uuid = '{fact_uuid}',
                f.updated_at = '{timestamp}'
            RETURN f.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                self.log_info(f"Fixed Fact node with UUID {update_result[1][0][0]}")

        return fixed_count
