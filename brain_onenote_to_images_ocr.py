#!/usr/bin/env python3
"""
Convert Brain.one file to images and use Mistral AI OCR to extract all content and references.
This is the correct approach for OneNote files.
"""

import asyncio
import os
import sys
from pathlib import Path
import json
import re
from datetime import datetime
from typing import List, Dict, Any

# Add current directory to path
sys.path.append(os.getcwd())

async def process_brain_onenote_with_images():
    """Convert Brain.one to images and extract all content with Mistral OCR."""
    
    print("🧠 BRAIN.ONE → IMAGES → MISTRAL OCR PROCESSING")
    print("=" * 80)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    try:
        # Step 1: Convert OneNote to images
        print("\n🔄 Step 1: Converting OneNote to images...")
        images = await convert_onenote_to_images(brain_file)
        
        if not images:
            print("❌ Failed to convert OneNote to images")
            return
        
        print(f"✅ Converted to {len(images)} images")
        
        # Step 2: Process each image with Mistral OCR
        print("\n🔄 Step 2: Processing images with Mistral AI OCR...")
        all_content = ""
        all_references = []
        
        for i, image_path in enumerate(images, 1):
            print(f"   📖 Processing image {i}/{len(images)}: {image_path.name}")
            
            try:
                # Use Mistral OCR to extract content
                content = await extract_content_with_mistral_ocr(image_path)
                
                if content:
                    print(f"   ✅ Extracted {len(content)} characters from image {i}")
                    all_content += f"\n\n--- PAGE {i} ---\n{content}"
                    
                    # Extract references from this page
                    page_refs = extract_references_from_content(content, f"page_{i}")
                    all_references.extend(page_refs)
                    print(f"   📚 Found {len(page_refs)} references on page {i}")
                    
                else:
                    print(f"   ⚠️ No content extracted from image {i}")
                    
            except Exception as e:
                print(f"   ❌ Error processing image {i}: {e}")
                continue
        
        # Step 3: Save all extracted content
        print(f"\n💾 Saving extracted content...")
        
        # Save full content
        content_file = f"brain_full_content_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        with open(content_file, "w", encoding="utf-8") as f:
            f.write(all_content)
        print(f"✅ Saved full content to {content_file}")
        
        # Step 4: Save all references
        if all_references:
            await save_references_to_csv(all_references, brain_file.name)
            
            print(f"\n🎉 SUCCESS! EXTRACTED {len(all_references)} REFERENCES!")
            print("=" * 60)
            
            # Show first 10 references
            print("📚 FIRST 10 REFERENCES:")
            for i, ref in enumerate(all_references[:10], 1):
                print(f"{i:2d}. {ref.get('title', ref.get('raw_text', 'Unknown'))[:80]}...")
            
            if len(all_references) > 10:
                print(f"... and {len(all_references) - 10} more references")
                
        else:
            print("❌ No references found")
        
        # Step 5: Show content summary
        print(f"\n📊 CONTENT SUMMARY:")
        print(f"   Total content length: {len(all_content)} characters")
        print(f"   Total references found: {len(all_references)}")
        print(f"   Images processed: {len(images)}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

async def convert_onenote_to_images(onenote_file: Path) -> List[Path]:
    """Convert OneNote file to images."""
    
    try:
        # Try using the OneNote processor's image conversion
        from processors.onenote_processor import OneNoteProcessor
        
        processor = OneNoteProcessor()
        
        # Check if processor has image conversion method
        if hasattr(processor, 'convert_to_images'):
            images = await processor.convert_to_images(str(onenote_file))
            return [Path(img) for img in images] if images else []
        
        # Alternative: Use win32com to convert OneNote to images
        try:
            import win32com.client
            
            # Create OneNote application
            onenote = win32com.client.Dispatch("OneNote.Application")
            
            # This is a simplified approach - you might need to implement
            # the full OneNote to image conversion logic here
            print("⚠️ OneNote COM interface available but conversion not implemented")
            return []
            
        except ImportError:
            print("⚠️ win32com not available for OneNote conversion")
            return []
            
    except Exception as e:
        print(f"❌ OneNote to images conversion error: {e}")
        return []

async def extract_content_with_mistral_ocr(image_path: Path) -> str:
    """Extract content from image using Mistral AI OCR."""
    
    try:
        # Import Mistral OCR processor
        from processors.mistral_ocr_processor import MistralOCRProcessor
        
        processor = MistralOCRProcessor()
        
        # Process the image
        result = await processor.process_image(str(image_path))
        
        if result and 'content' in result:
            return result['content']
        elif result and 'text' in result:
            return result['text']
        else:
            return ""
            
    except Exception as e:
        print(f"❌ Mistral OCR error: {e}")
        return ""

def extract_references_from_content(content: str, source: str) -> List[Dict[str, Any]]:
    """Extract references from content using comprehensive patterns."""
    
    references = []
    
    # Pattern 1: Numbered references (1. Author...)
    numbered_pattern = r'(\d+)\.\s*([A-Z][^.]*\..*?)(?=\d+\.\s*[A-Z]|$)'
    numbered_matches = re.findall(numbered_pattern, content, re.DOTALL | re.MULTILINE)
    
    for num, ref_text in numbered_matches:
        if len(ref_text.strip()) > 20:  # Filter out short matches
            ref = parse_reference_text(ref_text.strip(), f"{source}_numbered_{num}")
            if ref:
                references.append(ref)
    
    # Pattern 2: Author-year citations
    author_year_pattern = r'([A-Z][a-z]+(?:,\s*[A-Z]\.?)*(?:\s+et\s+al\.?)?)\s*\((\d{4})\)\.\s*([^.]+\..*?)(?=[A-Z][a-z]+\s*\(\d{4}\)|$)'
    author_year_matches = re.findall(author_year_pattern, content, re.DOTALL)
    
    for author, year, ref_text in author_year_matches:
        if len(ref_text.strip()) > 20:
            ref = parse_reference_text(f"{author} ({year}). {ref_text}", f"{source}_author_year")
            if ref:
                references.append(ref)
    
    # Pattern 3: Journal citations with volume/page
    journal_pattern = r'([^.]{10,})\.\s*([A-Z][^.]{3,})\.\s*(\d{4});?(\d+)?:?(\d+-?\d*)?'
    journal_matches = re.findall(journal_pattern, content)
    
    for title, journal, year, volume, pages in journal_matches:
        if len(title.strip()) > 10 and len(journal.strip()) > 3:
            ref = {
                'title': title.strip(),
                'journal': journal.strip(),
                'year': year,
                'volume': volume if volume else '',
                'pages': pages if pages else '',
                'extraction_method': f'{source}_journal',
                'raw_text': f"{title}. {journal}. {year};{volume}:{pages}"
            }
            references.append(ref)
    
    return references

def parse_reference_text(ref_text: str, method: str) -> Dict[str, Any]:
    """Parse reference text into structured components."""
    
    ref = {'raw_text': ref_text, 'extraction_method': method}
    
    # Extract year
    year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
    if year_match:
        ref['year'] = year_match.group()
    
    # Extract title (first sentence or before journal)
    title_patterns = [
        r'^([^.]+\.)',  # First sentence
        r'^\w+.*?\.\s*([^.]+\.)',  # After author
    ]
    
    for pattern in title_patterns:
        title_match = re.search(pattern, ref_text)
        if title_match:
            ref['title'] = title_match.group(1).strip('.')
            break
    
    # Extract journal
    journal_patterns = [
        r'\.\s*([A-Z][^.]+)\.\s*\d{4}',
        r'\d{4};\s*([^;]+);',
        r'In:\s*([^.]+)\.',
    ]
    
    for pattern in journal_patterns:
        journal_match = re.search(pattern, ref_text)
        if journal_match:
            ref['journal'] = journal_match.group(1).strip()
            break
    
    # Extract authors
    author_match = re.search(r'^([^.]+(?:\set\sal\.?)?)', ref_text)
    if author_match:
        ref['authors'] = author_match.group(1).strip()
    
    # Extract DOI
    doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', ref_text, re.IGNORECASE)
    if doi_match:
        ref['doi'] = doi_match.group(1)
    
    return ref

async def save_references_to_csv(references: List[Dict[str, Any]], source_file: str):
    """Save references to CSV file."""
    
    import csv
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"references/brain_mistral_ocr_references_{timestamp}.csv"
    
    # Ensure references directory exists
    Path("references").mkdir(exist_ok=True)
    
    fieldnames = [
        'source_document', 'extraction_method', 'reference_text', 'authors', 
        'title', 'year', 'journal', 'volume', 'pages', 'doi', 'pmid',
        'extraction_date', 'confidence_score'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for ref in references:
            row = {
                'source_document': source_file,
                'extraction_method': ref.get('extraction_method', 'mistral_ocr'),
                'reference_text': ref.get('raw_text', ''),
                'authors': ref.get('authors', ''),
                'title': ref.get('title', ''),
                'year': ref.get('year', ''),
                'journal': ref.get('journal', ''),
                'volume': ref.get('volume', ''),
                'pages': ref.get('pages', ''),
                'doi': ref.get('doi', ''),
                'pmid': ref.get('pmid', ''),
                'extraction_date': datetime.now().isoformat(),
                'confidence_score': 0.9
            }
            writer.writerow(row)
    
    print(f"💾 Saved {len(references)} references to {filename}")

if __name__ == "__main__":
    asyncio.run(process_brain_onenote_with_images())
