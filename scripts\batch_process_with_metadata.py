"""
Batch process PDF documents with metadata and reference extraction
"""

import os
import sys
import asyncio
import logging
import json
import argparse
from datetime import datetime
from enhanced_pdf_processor import process_directory

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('batch_processing.log')
    ]
)
logger = logging.getLogger(__name__)

async def main():
    """Main function for batch processing PDF documents"""
    parser = argparse.ArgumentParser(description="Batch process PDF documents with metadata and reference extraction")
    
    # Required arguments
    parser.add_argument("directory", help="Directory containing PDF files to process")
    
    # Optional arguments
    parser.add_argument("--output", "-o", help="Output directory for processed files")
    parser.add_argument("--chunk-size", type=int, default=1200, help="Size of text chunks in characters")
    parser.add_argument("--overlap", type=int, default=50, help="Overlap between chunks in characters")
    parser.add_argument("--max-pages", type=int, help="Maximum number of pages to process")
    parser.add_argument("--pattern", help="File pattern to match (e.g., '*.pdf')")
    parser.add_argument("--no-metadata", action="store_true", help="Skip metadata extraction")
    parser.add_argument("--no-references", action="store_true", help="Skip reference extraction")
    parser.add_argument("--llm", choices=["openai", "mistral", "none"], default="openai", help="LLM provider to use")
    
    args = parser.parse_args()
    
    # Validate directory
    if not os.path.isdir(args.directory):
        logger.error(f"Directory not found: {args.directory}")
        return 1
    
    # Set output directory
    output_dir = args.output
    if not output_dir:
        output_dir = os.path.join(args.directory, "processed")
    
    # Create output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # Set LLM provider
    llm_provider = None if args.llm == "none" else args.llm
    
    # Log processing start
    logger.info(f"Starting batch processing of PDF documents in {args.directory}")
    logger.info(f"Output directory: {output_dir}")
    logger.info(f"Settings: chunk_size={args.chunk_size}, overlap={args.overlap}, max_pages={args.max_pages}")
    logger.info(f"Extract metadata: {not args.no_metadata}, Extract references: {not args.no_references}")
    logger.info(f"LLM provider: {llm_provider}")
    
    # Start processing
    start_time = datetime.now()
    
    try:
        results = await process_directory(
            args.directory,
            output_dir=output_dir,
            chunk_size=args.chunk_size,
            overlap=args.overlap,
            max_pages=args.max_pages,
            extract_metadata=not args.no_metadata,
            extract_references=not args.no_references,
            llm_provider=llm_provider
        )
        
        # Log processing summary
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"Batch processing completed in {duration:.2f} seconds")
        logger.info(f"Processed {len(results)} documents")
        
        # Count successful documents
        successful = sum(1 for r in results if r.get("knowledge_graph", {}).get("success", False))
        logger.info(f"Successfully processed {successful} out of {len(results)} documents")
        
        # Count total references extracted
        total_references = sum(r.get("references", {}).get("total_reference_count", 0) for r in results)
        logger.info(f"Extracted {total_references} references in total")
        
        return 0
    
    except Exception as e:
        logger.error(f"Error during batch processing: {e}", exc_info=True)
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
