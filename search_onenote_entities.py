#!/usr/bin/env python3
"""
Search for OneNote-related entities in the knowledge graph.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.falkordb_adapter import <PERSON>alkor<PERSON>BAdapter

def search_entities():
    """Search for OneNote-related entities."""
    
    print("🔍 SEARCHING FOR ONENOTE-RELATED ENTITIES")
    print("=" * 60)
    
    try:
        db = FalkorDBAdapter()
        
        # Search terms related to OneNote content
        search_terms = [
            "Ginger",
            "Dopamine", 
            "Baicalein",
            "TNF",
            "Serotonin",
            "GABA",
            "Glutamate",
            "Acetylcholine",
            "Norepinephrine",
            "6-Gingerol",
            "6-Shogaol",
            "Alzheimer",
            "Parkinson"
        ]
        
        found_any = False
        
        for term in search_terms:
            print(f"\n🔍 Searching for: {term}")
            
            # Search in name
            query = f'MATCH (n) WHERE n.name CONTAINS "{term}" RETURN n.name, n.type, n.description LIMIT 5'
            result = db.execute_cypher(query)
            
            if len(result) > 1 and result[1]:
                found_any = True
                print(f"  ✅ Found {len(result[1])} entities with '{term}' in name:")
                for row in result[1]:
                    name = row[0] if len(row) > 0 else "Unknown"
                    entity_type = row[1] if len(row) > 1 else "Unknown"
                    description = row[2] if len(row) > 2 else "No description"
                    print(f"    • {name} ({entity_type})")
                    print(f"      {description[:100]}...")
            
            # Search in description
            query = f'MATCH (n) WHERE n.description CONTAINS "{term}" RETURN n.name, n.type, n.description LIMIT 5'
            result = db.execute_cypher(query)
            
            if len(result) > 1 and result[1]:
                found_any = True
                print(f"  ✅ Found {len(result[1])} entities with '{term}' in description:")
                for row in result[1]:
                    name = row[0] if len(row) > 0 else "Unknown"
                    entity_type = row[1] if len(row) > 1 else "Unknown"
                    description = row[2] if len(row) > 2 else "No description"
                    print(f"    • {name} ({entity_type})")
                    print(f"      {description[:100]}...")
        
        if not found_any:
            print("\n❌ No OneNote-related entities found in the knowledge graph")
            print("\nThis suggests that either:")
            print("1. The entities were not properly saved to the graph database")
            print("2. The entity extraction failed during processing")
            print("3. The entities were saved with different names/formats")
            
            # Let's check the most recent entities
            print("\n🔍 Checking most recent entities...")
            query = 'MATCH (n) RETURN n.name, n.type, n.description ORDER BY id(n) DESC LIMIT 10'
            result = db.execute_cypher(query)
            
            if len(result) > 1 and result[1]:
                print(f"Most recent {len(result[1])} entities:")
                for row in result[1]:
                    name = row[0] if len(row) > 0 else "Unknown"
                    entity_type = row[1] if len(row) > 1 else "Unknown"
                    print(f"  • {name} ({entity_type})")
        else:
            print(f"\n🎉 SUCCESS! Found OneNote-related entities in the knowledge graph!")
        
    except Exception as e:
        print(f"❌ Error searching entities: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    search_entities()
