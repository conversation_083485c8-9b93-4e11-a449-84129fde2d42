"""
Configuration routes for the Graphiti application settings.
"""

from fastapi import APIRouter, HTTPException, Depends, status
from typing import Dict, Any

from utils.logging_utils import get_logger
from utils.dependencies import (
    get_settings as get_app_settings,
    get_llm_config,
    get_database_config,
    get_embedding_config,
    get_ocr_config
)
from .settings_utils import (
    LLMSettings, EmbeddingSettings, DatabaseSettings, AllSettings,
    update_env_file, get_current_settings, validate_llm_settings,
    validate_embedding_settings, validate_database_settings
)
from .model_routes import get_dynamic_available_models

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(tags=["settings"])


@router.get("/settings")
async def get_settings(
    settings: Dict[str, Any] = Depends(get_app_settings),
    llm_config: Dict[str, Any] = Depends(get_llm_config),
    database_config: Dict[str, Any] = Depends(get_database_config),
    embedding_config: Dict[str, Any] = Depends(get_embedding_config),
    ocr_config: Dict[str, Any] = Depends(get_ocr_config)
):
    """
    Get all settings for the application.

    Args:
        settings: Application settings
        llm_config: LLM configuration
        database_config: Database configuration
        embedding_config: Embedding configuration
        ocr_config: OCR configuration

    Returns:
        Application settings
    """
    try:
        # Get available models dynamically
        available_models = await get_dynamic_available_models()

        # Get LLM settings
        llm_settings = {
            "provider": llm_config.get("provider", "openrouter"),
            "model": llm_config.get("model", "meta-llama/llama-4-maverick")
        }

        # Get database settings
        database_settings = {
            "type": "FalkorDB",
            "host": database_config.get("host", "localhost"),
            "port": database_config.get("port", 6379),
            "graph_name": database_config.get("graph", "graphiti")
        }

        # Get embedding settings
        embedding_settings = {
            "provider": embedding_config.get("provider", "openai"),
            "model": embedding_config.get("model", "text-embedding-3-small"),
            "use_local": embedding_config.get("use_local", False),
            "chunking_method": "recursive" if embedding_config.get("recursive_chunking", True) else "simple",
            "chunk_size": embedding_config.get("chunk_size", 1200),
            "chunk_overlap": embedding_config.get("chunk_overlap", 0),
            "recursive_chunking": embedding_config.get("recursive_chunking", True)
        }

        # Get OCR settings
        ocr_settings = {
            "provider": ocr_config.get("provider", "mistral"),
            "model": ocr_config.get("model", "mistral-ocr-latest"),
            "use_mistral": ocr_config.get("provider", "mistral") == "mistral"
        }

        return {
            "llm": llm_settings,
            "database": database_settings,
            "embedding": embedding_settings,
            "ocr": ocr_settings,
            "available_models": available_models
        }

    except Exception as e:
        logger.error(f"Error getting settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting settings: {str(e)}"
        )


@router.post("/settings/llm")
async def update_llm_settings(settings: LLMSettings):
    """
    Update LLM settings.

    Args:
        settings: LLM settings

    Returns:
        Updated LLM settings
    """
    try:
        # Validate settings
        settings_dict = settings.dict()
        if not validate_llm_settings(settings_dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid LLM settings"
            )

        # Update config
        config_updates = {
            "QA_LLM_PROVIDER": settings.provider,
            "QA_LLM_MODEL": settings.model
        }

        # Update USE_LOCAL_LLM if provided
        if hasattr(settings, 'use_local'):
            config_updates["USE_LOCAL_LLM"] = str(settings.use_local).lower()

        # Update API key if provided
        if settings.api_key:
            if settings.provider == "openrouter":
                config_updates["OPENROUTER_API_KEY"] = settings.api_key
            elif settings.provider == "openai":
                config_updates["OPENAI_API_KEY"] = settings.api_key

        # Update the .env file
        if not update_env_file(config_updates):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update configuration file"
            )

        return {
            "success": True,
            "provider": settings.provider,
            "model": settings.model,
            "use_local": getattr(settings, 'use_local', False)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating LLM settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating LLM settings: {str(e)}"
        )


@router.post("/settings/embedding")
async def update_embedding_settings(settings: EmbeddingSettings):
    """
    Update embedding settings.

    Args:
        settings: Embedding settings

    Returns:
        Updated embedding settings
    """
    try:
        # Validate settings
        settings_dict = settings.dict()
        if not validate_embedding_settings(settings_dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid embedding settings"
            )

        # Update config
        config_updates = {
            "EMBEDDING_PROVIDER": settings.provider,
            "EMBEDDING_MODEL": settings.model,
            "USE_LOCAL_EMBEDDINGS": str(settings.use_local).lower(),
            "CHUNK_SIZE": str(settings.chunk_size),
            "CHUNK_OVERLAP": str(settings.chunk_overlap),
            "RECURSIVE_CHUNKING": str(settings.recursive_chunking).lower()
        }

        # Update the .env file
        if not update_env_file(config_updates):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update configuration file"
            )

        return {
            "success": True,
            "provider": settings.provider,
            "model": settings.model,
            "use_local": settings.use_local,
            "chunk_size": settings.chunk_size,
            "chunk_overlap": settings.chunk_overlap,
            "recursive_chunking": settings.recursive_chunking
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating embedding settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating embedding settings: {str(e)}"
        )


@router.post("/settings/database")
async def update_database_settings(settings: DatabaseSettings):
    """
    Update database settings.

    Args:
        settings: Database settings

    Returns:
        Updated database settings
    """
    try:
        # Validate settings
        settings_dict = settings.dict()
        if not validate_database_settings(settings_dict):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid database settings"
            )

        # Update config
        config_updates = {
            "FALKORDB_HOST": settings.host,
            "FALKORDB_PORT": str(settings.port)
        }

        # Update the .env file
        if not update_env_file(config_updates):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update configuration file"
            )

        return {
            "success": True,
            "host": settings.host,
            "port": settings.port
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating database settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating database settings: {str(e)}"
        )
