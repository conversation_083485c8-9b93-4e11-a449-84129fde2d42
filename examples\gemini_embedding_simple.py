"""
Simple test for Google Generative AI embeddings based on the official documentation
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

def main():
    try:
        # Load environment variables
        load_dotenv()

        # Get Google API key
        api_key = os.environ.get('GOOGLE_API_KEY')
        if not api_key:
            print("No Google API key found in environment variables")
            return

        print(f"API key found: {api_key[:5]}...{api_key[-5:]}")

        # Configure the API
        genai.configure(api_key=api_key)

        # Test embedding with embedding-001
        print("\nTesting embedding with embedding-001:")
        try:
            result = genai.embed_content(
                model="models/embedding-001",
                content="What is the meaning of life?"
            )

            embedding = result['embedding']
            print(f"Embedding values: {embedding[:5]}...")
            print(f"Embedding dimension: {len(embedding)}")
        except Exception as e:
            print(f"Error with embedding-001: {e}")

        print("\nTest completed successfully!")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
