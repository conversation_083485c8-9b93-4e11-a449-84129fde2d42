"""
Duplicate UUID validator.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class DuplicateValidator(BaseValidator):
    """Validator for duplicate UUIDs."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "duplicate_uuids": 0,
            "duplicate_uuids_fixed": 0
        }

    async def validate(self):
        """Check for duplicate UUIDs."""
        adapter = await self.get_adapter()

        # Check for duplicate UUIDs in Entity nodes
        query = """
        MATCH (e:Entity)
        WITH e.uuid as uuid, collect(id(e)) as ids
        WHERE size(ids) > 1
        RETURN uuid, size(ids) as count
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        duplicate_uuids = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                duplicate_uuids.append({
                    "uuid": row[0],
                    "count": row[1]
                })

            self.stats["duplicate_uuids"] = sum(d["count"] - 1 for d in duplicate_uuids)
            self.log_info(f"Found {len(duplicate_uuids)} UUIDs with duplicates, affecting {self.stats['duplicate_uuids']} nodes")

            if self.fix:
                fixed_count = await self._fix_duplicate_uuids(duplicate_uuids)
                self.stats["duplicate_uuids_fixed"] = fixed_count

    async def _fix_duplicate_uuids(self, duplicate_uuids: List[Dict[str, Any]]) -> int:
        """
        Fix duplicate UUIDs by assigning new UUIDs to duplicates.

        Args:
            duplicate_uuids: List of duplicate UUIDs

        Returns:
            Number of duplicates fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for dup in duplicate_uuids:
            # Get all nodes with this UUID
            query = f"""
            MATCH (e:Entity)
            WHERE e.uuid = '{dup['uuid']}'
            RETURN id(e) as id, e.name as name, e.type as type
            """

            result = adapter.execute_cypher(query)
            nodes = []

            if result and len(result) > 1 and len(result[1]) > 0:
                for row in result[1]:
                    nodes.append({
                        "id": row[0],
                        "name": row[1],
                        "type": row[2]
                    })

                # Keep the first node, update the rest
                for node in nodes[1:]:
                    new_uuid = self.generate_new_uuid()
                    timestamp = self.get_timestamp()

                    update_query = f"""
                    MATCH (e:Entity)
                    WHERE id(e) = {node['id']}
                    SET e.uuid = '{new_uuid}',
                        e.updated_at = '{timestamp}'
                    RETURN e.name as name, e.type as type, e.uuid as uuid
                    """

                    update_result = adapter.execute_cypher(update_query)

                    if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                        fixed_count += 1
                        self.log_info(f"Fixed duplicate UUID for entity: {update_result[1][0][0]} ({update_result[1][0][1]}) with new UUID {update_result[1][0][2]}")

        return fixed_count
