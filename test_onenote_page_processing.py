#!/usr/bin/env python3
"""
Test OneNote Page-by-Page Processing

Tests the new OneNote page processor that creates individual pages with UUIDs
and processes each through the normal document ingestion pipeline.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from processors.onenote_page_processor import OneNotePageProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_onenote_page_processing():
    """Test OneNote page-by-page processing."""
    
    print("🧠 ONENOTE PAGE-BY-PAGE PROCESSING TEST")
    print("=" * 80)
    
    try:
        # Initialize the processor
        processor = OneNotePageProcessor()
        
        # Find OneNote files to process
        uploads_dir = Path("uploads")
        onenote_files = list(uploads_dir.glob("*.one"))
        
        if not onenote_files:
            print("❌ No OneNote files found in uploads directory")
            return
        
        print(f"📄 Found {len(onenote_files)} OneNote files:")
        for i, file_path in enumerate(onenote_files, 1):
            print(f"   {i}. {file_path.name}")
        
        # Process the first OneNote file
        test_file = onenote_files[0]
        print(f"\n🔄 Testing with file: {test_file.name}")
        print("-" * 60)
        
        # Process the file by pages
        result = await processor.process_onenote_file_by_pages(
            file_path=str(test_file),
            extract_entities=True,
            extract_references=True,
            generate_embeddings=True,
            chunk_size=1200,
            overlap=0
        )
        
        # Display results
        print("\n📊 PROCESSING RESULTS:")
        print("=" * 60)
        
        if result.get('success', False):
            print(f"✅ Processing successful!")
            print(f"📄 Pages extracted: {result.get('pages_extracted', 0)}")
            print(f"📄 Pages processed: {result.get('pages_processed', 0)}")
            print(f"✅ Pages successful: {result.get('pages_successful', 0)}")
            print(f"🧠 Total entities: {result.get('total_entities_extracted', 0)}")
            print(f"📚 Total references: {result.get('total_references_extracted', 0)}")
            print(f"🔗 Total embeddings: {result.get('total_embeddings_generated', 0)}")
            
            # Show individual page results
            print(f"\n📄 INDIVIDUAL PAGE RESULTS:")
            print("-" * 40)
            
            for i, page_result in enumerate(result.get('processing_results', []), 1):
                page_title = page_result.get('page_title', f'Page {i}')
                page_uuid = page_result.get('page_uuid', 'Unknown')
                success = page_result.get('success', False)
                entities = page_result.get('entities_extracted', 0)
                references = page_result.get('references_extracted', 0)
                embeddings = page_result.get('embeddings_generated', 0)
                
                status = "✅" if success else "❌"
                print(f"{status} Page {i}: {page_title}")
                print(f"   UUID: {page_uuid}")
                print(f"   Entities: {entities}, References: {references}, Embeddings: {embeddings}")
                
                if not success:
                    error = page_result.get('error', 'Unknown error')
                    print(f"   Error: {error}")
                print()
        
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
        
        # Test batch processing
        print("\n🔄 TESTING BATCH PROCESSING:")
        print("=" * 60)
        
        batch_result = await processor.process_all_onenote_files(
            uploads_dir="uploads",
            file_pattern="*.one"
        )
        
        if batch_result.get('success', False):
            print(f"✅ Batch processing successful!")
            print(f"📄 Files found: {batch_result.get('files_found', 0)}")
            print(f"📄 Files processed: {batch_result.get('files_processed', 0)}")
            print(f"✅ Files successful: {batch_result.get('files_successful', 0)}")
            print(f"📄 Total pages: {batch_result.get('total_pages_processed', 0)}")
            print(f"🧠 Total entities: {batch_result.get('total_entities_extracted', 0)}")
            print(f"📚 Total references: {batch_result.get('total_references_extracted', 0)}")
        else:
            print(f"❌ Batch processing failed: {batch_result.get('error', 'Unknown error')}")
        
        print("\n🎉 OneNote page processing test completed!")
        
    except Exception as e:
        logger.error(f"❌ Error in OneNote page processing test: {e}", exc_info=True)
        print(f"❌ Test failed: {e}")

async def test_page_extraction_only():
    """Test just the page extraction without full processing."""
    
    print("\n🧠 TESTING PAGE EXTRACTION ONLY")
    print("=" * 60)
    
    try:
        processor = OneNotePageProcessor()
        
        # Find OneNote files
        uploads_dir = Path("uploads")
        onenote_files = list(uploads_dir.glob("*.one"))
        
        if not onenote_files:
            print("❌ No OneNote files found")
            return
        
        test_file = onenote_files[0]
        print(f"📄 Testing page extraction from: {test_file.name}")
        
        # Extract pages only
        pages = await processor._extract_onenote_pages(str(test_file))
        
        print(f"\n📄 EXTRACTED PAGES:")
        print("-" * 40)
        
        for i, page in enumerate(pages, 1):
            print(f"Page {i}:")
            print(f"   UUID: {page['uuid']}")
            print(f"   Title: {page['title']}")
            print(f"   Content Type: {page['content_type']}")
            print(f"   File Path: {page['file_path']}")
            print(f"   Content Length: {page['content_length']} chars")
            print()
        
        print(f"✅ Successfully extracted {len(pages)} pages with unique UUIDs!")
        
    except Exception as e:
        logger.error(f"❌ Error in page extraction test: {e}", exc_info=True)
        print(f"❌ Page extraction test failed: {e}")

async def main():
    """Main test function."""
    
    print("🧠 ONENOTE PAGE PROCESSOR TESTING SUITE")
    print("=" * 80)
    
    # Test 1: Page extraction only
    await test_page_extraction_only()
    
    # Test 2: Full page processing
    await test_onenote_page_processing()
    
    print("\n🎉 ALL TESTS COMPLETED!")

if __name__ == "__main__":
    asyncio.run(main())
