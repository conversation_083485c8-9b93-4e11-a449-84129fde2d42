"""
Document-related data models for the Graphiti application.
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum

class DocumentType(str, Enum):
    """Document type enumeration"""
    PDF = "pdf"
    TEXT = "text"
    WORD = "word"
    HTML = "html"
    SPREADSHEET = "spreadsheet"
    PRESENTATION = "presentation"
    EBOOK = "ebook"
    IMAGE = "image"
    OTHER = "other"

class OCRProvider(str, Enum):
    """OCR provider enumeration"""
    MISTRAL = "mistral"
    PYPDF2 = "pypdf2"
    TESSERACT = "tesseract"
    NONE = "none"

class DocumentMetadata(BaseModel):
    """Document metadata model"""
    title: Optional[str] = None
    authors: Optional[List[str]] = None
    publication_date: Optional[datetime] = None
    publisher: Optional[str] = None
    doi: Optional[str] = None
    isbn: Optional[str] = None
    keywords: Optional[List[str]] = None
    abstract: Optional[str] = None
    language: Optional[str] = None
    pages: Optional[int] = None
    source_url: Optional[str] = None
    custom_metadata: Optional[Dict[str, Any]] = None

class DocumentChunk(BaseModel):
    """Document chunk model"""
    uuid: str
    document_id: str
    chunk_num: int
    body: str
    embedding: Optional[List[float]] = None
    start_char: Optional[int] = None
    end_char: Optional[int] = None
    page_num: Optional[int] = None

class DocumentProcessingOptions(BaseModel):
    """Document processing options"""
    chunk_size: int = 1200
    overlap: int = 0
    extract_entities: bool = True
    extract_references: bool = True
    extract_metadata: bool = True
    ocr_provider: str = "mistral"

class DocumentUploadResponse(BaseModel):
    """Document upload response"""
    filename: str
    uuid: str
    file_type: str
    chunks: int = 0
    entities: int = 0
    references: int = 0
    success: bool = True
    entity_extraction_running: bool = False
    reference_extraction_running: bool = False
    ocr_provider: str = "pypdf2"
    error: Optional[str] = None

class DocumentSummary(BaseModel):
    """Document summary model"""
    uuid: str
    filename: str
    file_type: DocumentType
    upload_date: datetime
    chunks: int
    entities: int
    references: int
    metadata: Optional[DocumentMetadata] = None

class DocumentDetails(BaseModel):
    """Document details model"""
    uuid: str
    name: str
    file_path: Optional[str] = None
    processed_at: Optional[str] = None
    chunks: int = 0
    entities: int = 0
    references: int = 0
    file_type: DocumentType = DocumentType.OTHER

class DocumentList(BaseModel):
    """Document list model"""
    documents: List[DocumentSummary]
    total: int
    page: int
    page_size: int

class DocumentProgressStatus(BaseModel):
    """Document processing progress status"""
    document_id: str
    status: str
    progress_percentage: int = 0
    step_name: str
    details: Dict[str, Any] = {}

class ProcessingQueueStatus(BaseModel):
    """Processing queue status"""
    is_processing: bool
    queue_length: int
    current_document: Optional[str] = None
    current_progress: Optional[DocumentProgressStatus] = None
    documents_progress: Dict[str, DocumentProgressStatus] = {}

class BatchDocumentUploadResponse(BaseModel):
    """Batch document upload response"""
    total_files: int
    successful_uploads: int
    failed_uploads: int
    documents: List[DocumentUploadResponse]
    success: bool = True
    error: Optional[str] = None
