Write-Host "🚀 FIXING GRAPHITI DEPENDENCY CONFLICTS" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green

Write-Host "📦 Upgrading core dependencies..." -ForegroundColor Yellow
pip install --upgrade typing-extensions>=4.12.0
pip install --upgrade pydantic>=2.8.2
pip install --upgrade fastapi>=0.115.9
pip install --upgrade httpx>=0.28.1
pip install --upgrade uvicorn>=0.34.0
pip install --upgrade anyio>=4.8.0
pip install --upgrade openai>=1.68.2
pip install --upgrade python-dotenv>=1.0.1
pip install --upgrade jinja2>=3.1.3

Write-Host ""
Write-Host "🔄 Reinstalling problematic packages..." -ForegroundColor Yellow
pip install --upgrade --force-reinstall chromadb
pip install --upgrade --force-reinstall google-genai
pip install --upgrade --force-reinstall graphiti-core
pip install --upgrade --force-reinstall langchain
pip install --upgrade --force-reinstall mistralai
pip install --upgrade --force-reinstall ollama
pip install --upgrade --force-reinstall mcp

Write-Host ""
Write-Host "📋 Installing requirements..." -ForegroundColor Yellow
if (Test-Path "requirements.txt") {
    pip install -r requirements.txt
}

Write-Host ""
Write-Host "🔍 Checking for conflicts..." -ForegroundColor Yellow
pip check

Write-Host ""
Write-Host "✅ DEPENDENCY FIX COMPLETE!" -ForegroundColor Green
Write-Host "============================================================" -ForegroundColor Green
Write-Host "🚀 Try starting the frontend now with:" -ForegroundColor Cyan
Write-Host "   python app.py" -ForegroundColor White
Write-Host "   or" -ForegroundColor White
Write-Host "   python start.py ui" -ForegroundColor White
Write-Host ""
Write-Host "📍 Frontend should be available at: http://localhost:9753" -ForegroundColor Cyan
Read-Host "Press Enter to continue"
