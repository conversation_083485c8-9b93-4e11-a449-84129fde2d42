"""
<PERSON><PERSON><PERSON> to check facts in the FalkorDB database.
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def main():
    """Main function."""
    # Connect to FalkorDB
    adapter = GraphitiFalkorDBAdapter()
    
    # Check if connected
    if not adapter.is_connected():
        logger.error("Failed to connect to FalkorDB")
        return
    
    logger.info("Connected to FalkorDB")
    
    # Get all episodes
    episodes_query = """
    MATCH (e:Episode)
    RETURN e.uuid as uuid, e.name as name, e.total_chunks as chunks
    """
    
    episodes_result = adapter.execute_cypher(episodes_query)
    
    if not episodes_result or len(episodes_result) < 2 or not episodes_result[1]:
        logger.info("No episodes found in the database")
        return
    
    logger.info(f"Found {len(episodes_result[1])} episodes in the database")
    
    for episode in episodes_result[1]:
        uuid = episode[0]
        name = episode[1]
        chunks = episode[2]
        
        logger.info(f"Episode: {name} (UUID: {uuid}, Chunks: {chunks})")
        
        # Get facts for this episode
        facts_query = f"""
        MATCH (e:Episode {{uuid: '{uuid}'}})-[:CONTAINS]->(f:Fact)
        RETURN count(f) as fact_count
        """
        
        facts_result = adapter.execute_cypher(facts_query)
        
        if facts_result and len(facts_result) > 1 and facts_result[1]:
            fact_count = facts_result[1][0][0]
            logger.info(f"  Facts: {fact_count}")
            
            # Get a sample fact
            sample_query = f"""
            MATCH (e:Episode {{uuid: '{uuid}'}})-[:CONTAINS]->(f:Fact)
            RETURN f.uuid as uuid, f.body as body
            LIMIT 1
            """
            
            sample_result = adapter.execute_cypher(sample_query)
            
            if sample_result and len(sample_result) > 1 and sample_result[1]:
                fact_uuid = sample_result[1][0][0]
                fact_body = sample_result[1][0][1]
                logger.info(f"  Sample fact UUID: {fact_uuid}")
                logger.info(f"  Sample fact body: {fact_body[:100]}...")
        else:
            logger.info("  No facts found for this episode")
    
    # Close the connection
    adapter.close()

if __name__ == "__main__":
    asyncio.run(main())
