<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progress Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>Progress Tracking Test</h2>
        
        <div class="mb-3">
            <label for="operation-id" class="form-label">Operation ID:</label>
            <input type="text" class="form-control" id="operation-id" value="fec4f92c-8a58-4274-a6db-deb2885491e5">
        </div>
        
        <button class="btn btn-primary" onclick="testProgress()">Test Progress</button>
        <button class="btn btn-success" onclick="simulateProgress()">Simulate Live Progress</button>
        
        <div id="progress-container" class="mt-4"></div>
        
        <div id="debug-output" class="mt-4">
            <h4>Debug Output:</h4>
            <pre id="debug-text" class="bg-light p-3"></pre>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const debugText = document.getElementById('debug-text');
            debugText.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            console.log(message);
        }

        async function testProgress() {
            const operationId = document.getElementById('operation-id').value;
            log(`Testing progress for operation: ${operationId}`);
            
            try {
                const response = await fetch(`/api/enhanced/progress/${operationId}`);
                log(`Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log(`Progress data: ${JSON.stringify(data, null, 2)}`);
                    
                    // Create a progress card
                    createProgressCard(operationId, data.document_name || 'Test Document');
                    updateProgressCard(operationId, data);
                } else {
                    log(`Error: ${response.statusText}`);
                }
            } catch (error) {
                log(`Exception: ${error.message}`);
            }
        }

        function createProgressCard(operationId, filename) {
            const container = document.getElementById('progress-container');
            const card = document.createElement('div');
            card.className = 'card mb-3';
            card.id = `progress-${operationId}`;
            card.innerHTML = `
                <div class="card-header">
                    <h6>${filename}</h6>
                    <span class="badge bg-primary" id="step-badge-${operationId}">Step 0/7</span>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Overall Progress</span>
                            <span id="overall-percentage-${operationId}">0%</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" id="overall-progress-${operationId}" style="width: 0%"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div id="current-step-${operationId}">Initializing...</div>
                    </div>
                    
                    <div class="row text-center">
                        <div class="col-3">
                            <div class="small text-muted">Facts</div>
                            <div class="fw-bold" id="facts-${operationId}">-</div>
                        </div>
                        <div class="col-3">
                            <div class="small text-muted">Entities</div>
                            <div class="fw-bold" id="entities-${operationId}">-</div>
                        </div>
                        <div class="col-3">
                            <div class="small text-muted">References</div>
                            <div class="fw-bold" id="references-${operationId}">-</div>
                        </div>
                        <div class="col-3">
                            <div class="small text-muted">Embeddings</div>
                            <div class="fw-bold" id="embeddings-${operationId}">-</div>
                        </div>
                    </div>
                </div>
            `;
            
            container.innerHTML = '';
            container.appendChild(card);
            log(`Created progress card for ${operationId}`);
        }

        function updateProgressCard(operationId, progressData) {
            log(`Updating progress card with: ${JSON.stringify(progressData)}`);
            
            // Update step badge
            const stepBadge = document.getElementById(`step-badge-${operationId}`);
            if (stepBadge) {
                stepBadge.textContent = `Step ${progressData.current_step || 0}/7`;
                stepBadge.className = 'badge ' + (
                    progressData.status === 'completed' ? 'bg-success' :
                    progressData.status === 'failed' ? 'bg-danger' : 'bg-primary'
                );
            }

            // Update overall progress
            const overallProgress = document.getElementById(`overall-progress-${operationId}`);
            const overallPercentage = document.getElementById(`overall-percentage-${operationId}`);
            if (overallProgress && overallPercentage) {
                overallProgress.style.width = `${progressData.progress_percentage}%`;
                overallPercentage.textContent = `${progressData.progress_percentage}%`;
            }

            // Update current step
            const currentStep = document.getElementById(`current-step-${operationId}`);
            if (currentStep) {
                currentStep.textContent = progressData.current_step_name || 'Processing...';
            }

            // Update statistics if available
            if (progressData.details) {
                const details = progressData.details;
                
                const factsElement = document.getElementById(`facts-${operationId}`);
                if (factsElement) factsElement.textContent = details.facts_count || '-';
                
                const entitiesElement = document.getElementById(`entities-${operationId}`);
                if (entitiesElement) entitiesElement.textContent = details.entities_count || '-';
                
                const referencesElement = document.getElementById(`references-${operationId}`);
                if (referencesElement) referencesElement.textContent = details.references_count || '-';
                
                const embeddingsElement = document.getElementById(`embeddings-${operationId}`);
                if (embeddingsElement) embeddingsElement.textContent = details.embeddings_count || '-';
            }
        }

        function simulateProgress() {
            const operationId = 'test-simulation';
            createProgressCard(operationId, 'Simulated Document.pdf');
            
            let progress = 0;
            let step = 1;
            const steps = ['Text Extraction', 'Chunking', 'Entity Extraction', 'Relationship Extraction', 'Reference Extraction', 'Embedding Generation', 'Finalization'];
            
            const interval = setInterval(() => {
                progress += Math.random() * 15;
                if (progress > 100) progress = 100;
                
                const currentStepIndex = Math.floor((progress / 100) * steps.length);
                const currentStepName = steps[Math.min(currentStepIndex, steps.length - 1)];
                
                const progressData = {
                    progress_percentage: Math.round(progress),
                    status: progress >= 100 ? 'completed' : 'processing',
                    current_step: Math.min(currentStepIndex + 1, steps.length),
                    current_step_name: progress >= 100 ? 'Processing complete' : currentStepName,
                    details: {
                        facts_count: Math.floor(progress * 2),
                        entities_count: Math.floor(progress * 1.5),
                        references_count: Math.floor(progress * 0.3),
                        embeddings_count: Math.floor(progress * 1.2)
                    }
                };
                
                updateProgressCard(operationId, progressData);
                log(`Simulated progress: ${progress}%`);
                
                if (progress >= 100) {
                    clearInterval(interval);
                    log('Simulation complete');
                }
            }, 1000);
        }
    </script>
</body>
</html>
