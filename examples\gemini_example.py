"""
Copyright 2024, Zep Software, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import <PERSON>Embedder, GeminiEmbedderConfig
from graphiti_core.nodes import EpisodeType
from graphiti_core.utils.maintenance.graph_data_operations import clear_data


def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )


async def main():
    # Load environment variables
    load_dotenv()
    setup_logging()

    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        print("No Google API key found in environment variables. Please add it to your .env file.")
        return

    print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")

    # Initialize Graphiti with Gemini clients
    graphiti = Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=GeminiClient(
            config=LLMConfig(
                api_key=google_api_key,
                model="models/gemini-2.0-flash"
            )
        ),
        embedder=GeminiEmbedder(
            config=GeminiEmbedderConfig(
                api_key=google_api_key,
                embedding_model="models/embedding-001"
            )
        )
    )

    try:
        print("Connecting to Neo4j database...")

        # Clear existing data (optional - remove this in production)
        print("Clearing existing data...")
        await clear_data(graphiti.driver)

        # Set up indices and constraints
        print("Setting up indices and constraints...")
        await graphiti.build_indices_and_constraints()

        # Example: Add a simple episode
        print("Adding example episode...")
        await graphiti.add_episode(
            name="Gemini Example Episode",
            episode_body="This is a test episode created using Google Gemini for LLM and embeddings.",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="Gemini Example",
        )

        # Example: Perform a search
        print("Performing search...")
        search_results = await graphiti.search(
            "test episode using Google Gemini",
            limit=5,
        )

        # Print search results
        print("\nSearch Results:")
        for i, result in enumerate(search_results.edges):
            print(f"{i+1}. {result.fact} (Score: {result.score})")

        print("\nExample completed successfully!")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close the driver
        print("Closing Neo4j connection...")
        await graphiti.driver.close()


if __name__ == "__main__":
    asyncio.run(main())
