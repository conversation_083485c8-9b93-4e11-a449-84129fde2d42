"""
Test FalkorDB with a Cypher query
"""

import asyncio
from falkordb_adapter import FalkorDBAdapter

async def test_query():
    """Test a Cypher query on FalkorDB"""
    adapter = FalkorDBAdapter(graph_name="graphiti")

    try:
        # Create test data
        print("Creating test data...")
        result = adapter.execute_cypher(
            """
            CREATE (vitC:Nutrient {name: 'Vitamin C', description: 'An essential vitamin with antioxidant properties'})
            CREATE (immune:Process {name: 'Immune Function', description: 'The body defense system against pathogens'})
            CREATE (cold:Disease {name: 'Common Cold', description: 'A viral infectious disease of the upper respiratory tract'})
            CREATE (vitC)-[:SUPPORTS]->(immune)
            CREATE (vitC)-[:PREVENTS]->(cold)
            RETURN 'Test data created' as message
            """
        )
        print(f"Create result: {result}")

        # Check available labels
        print("\nChecking available labels...")
        result = adapter.execute_cypher(
            "CALL db.labels()"
        )
        print(f"Available labels: {result}")

        # Test querying all nodes
        print("\nQuerying all nodes...")
        result = adapter.execute_cypher(
            "MATCH (n) RETURN n.name, labels(n) LIMIT 10"
        )
        print(f"Query result: {result}")

        # Test querying relationships
        print("\nQuerying relationships...")
        result = adapter.execute_cypher(
            "MATCH (n)-[r]->(m) RETURN n.name, type(r), m.name LIMIT 10"
        )
        print(f"Query result: {result}")

        # Test natural language query
        print("\nQuerying for information about Vitamin C...")
        result = adapter.execute_cypher(
            "MATCH (n {name: 'Vitamin C'})-[r]->(m) RETURN n.name, type(r), m.name"
        )
        print(f"Query result: {result}")

    finally:
        adapter.close()

if __name__ == "__main__":
    asyncio.run(test_query())
