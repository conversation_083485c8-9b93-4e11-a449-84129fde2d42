#!/usr/bin/env python3
"""
Direct test of Ollama vision model with OneNote image.

This script bypasses the model checking and directly tries to use the qwen2.5vl:3b model
that we know is installed.
"""

import sys
import os
import logging
import asyncio
import base64
from pathlib import Path
import json

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_ollama_vision_direct():
    """Test Ollama vision directly with the known model."""
    
    print("🤖 DIRECT OLLAMA VISION TEST")
    print("=" * 60)
    
    try:
        import httpx
        
        # Find test image
        image_dir = Path("extracted_onenote_images")
        test_image = image_dir / "onenote_image_4_jpeg.jpg"
        
        if not test_image.exists():
            image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
            if image_files:
                test_image = image_files[0]
            else:
                print("❌ No test images available")
                return False
        
        print(f"🖼️ Testing with: {test_image.name}")
        print(f"📁 File size: {test_image.stat().st_size:,} bytes")
        
        # Read and encode the image
        with open(test_image, 'rb') as f:
            image_data = f.read()
        
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        print(f"📄 Base64 encoded: {len(image_base64)} characters")
        
        # Test with the exact model name from your list
        model = "qwen2.5vl:3b"
        
        payload = {
            "model": model,
            "prompt": """Please extract ALL text content from this image. This appears to be from a OneNote document containing research information about brain health, neuroprotection, and bioactive compounds.

Please provide:
1. All visible text, including titles, headings, bullet points, and body text
2. Any research information, chemical names, or scientific terms
3. References, URLs, or citations if visible
4. Table data or structured information if present

Extract the text exactly as it appears, maintaining the structure and organization.""",
            "images": [image_base64],
            "stream": False
        }
        
        print(f"🚀 Making request to Ollama with model: {model}")
        
        async with httpx.AsyncClient(timeout=180.0) as client:
            try:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json=payload
                )
                
                print(f"📡 Response status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    extracted_text = result.get('response', '').strip()
                    
                    if extracted_text:
                        print(f"✅ SUCCESS: Extracted {len(extracted_text)} characters")
                        print(f"\n📝 EXTRACTED TEXT:")
                        print("=" * 60)
                        print(extracted_text)
                        print("=" * 60)
                        
                        # Save result
                        with open("ollama_vision_direct_result.txt", 'w', encoding='utf-8') as f:
                            f.write(f"=== OLLAMA VISION DIRECT TEST RESULT ===\n\n")
                            f.write(f"Model: {model}\n")
                            f.write(f"Image: {test_image.name}\n")
                            f.write(f"Text length: {len(extracted_text)} characters\n\n")
                            f.write("=== EXTRACTED TEXT ===\n\n")
                            f.write(extracted_text)
                        
                        print(f"💾 Saved result to: ollama_vision_direct_result.txt")
                        return True
                    else:
                        print("❌ No text extracted")
                        print(f"Full response: {result}")
                        return False
                else:
                    print(f"❌ Request failed: {response.status_code}")
                    print(f"Response: {response.text}")
                    return False
                    
            except Exception as e:
                print(f"❌ Request error: {e}")
                return False
                
    except ImportError:
        print("❌ httpx not available")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_all_images_direct():
    """Test all OneNote images directly."""
    
    print("\n🖼️ TESTING ALL ONENOTE IMAGES DIRECTLY")
    print("=" * 60)
    
    try:
        import httpx
        
        image_dir = Path("extracted_onenote_images")
        image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        
        if not image_files:
            print("❌ No image files found")
            return False
        
        print(f"📎 Found {len(image_files)} images to process")
        
        model = "qwen2.5vl:3b"
        total_extracted_text = ""
        successful_extractions = 0
        
        for i, image_file in enumerate(sorted(image_files)):
            print(f"\n📷 Processing {image_file.name}:")
            
            try:
                # Read and encode image
                with open(image_file, 'rb') as f:
                    image_data = f.read()
                
                image_base64 = base64.b64encode(image_data).decode('utf-8')
                
                payload = {
                    "model": model,
                    "prompt": """Extract ALL text content from this OneNote image. Include:
- All visible text, headings, bullet points
- Research information and scientific terms
- Chemical names and compounds
- References, URLs, citations
- Table data and structured information

Maintain the original structure and organization.""",
                    "images": [image_base64],
                    "stream": False
                }
                
                async with httpx.AsyncClient(timeout=180.0) as client:
                    response = await client.post(
                        "http://localhost:11434/api/generate",
                        json=payload
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        extracted_text = result.get('response', '').strip()
                        
                        if extracted_text and len(extracted_text) > 20:
                            print(f"   ✅ SUCCESS: {len(extracted_text)} characters")
                            print(f"   📝 Preview: {extracted_text[:150]}...")
                            
                            total_extracted_text += f"\n\n=== {image_file.name.upper()} ===\n"
                            total_extracted_text += extracted_text
                            successful_extractions += 1
                        else:
                            print(f"   ❌ No meaningful text extracted")
                    else:
                        print(f"   ❌ Request failed: {response.status_code}")
                        
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        # Summary
        print(f"\n📊 PROCESSING SUMMARY:")
        print("=" * 60)
        print(f"Images processed: {len(image_files)}")
        print(f"Successful extractions: {successful_extractions}")
        print(f"Total text extracted: {len(total_extracted_text):,} characters")
        
        if total_extracted_text:
            # Save comprehensive results
            output_file = Path("onenote_ollama_vision_complete_results.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"=== COMPLETE ONENOTE OLLAMA VISION RESULTS ===\n\n")
                f.write(f"Model: {model}\n")
                f.write(f"Images processed: {len(image_files)}\n")
                f.write(f"Successful extractions: {successful_extractions}\n")
                f.write(f"Total text: {len(total_extracted_text):,} characters\n\n")
                f.write("=== EXTRACTED TEXT FROM ALL IMAGES ===\n")
                f.write(total_extracted_text)
            
            print(f"💾 Saved complete results to: {output_file}")
            
            # Show preview
            print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
            print("=" * 60)
            print(total_extracted_text[:1000])
            if len(total_extracted_text) > 1000:
                print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
            print("=" * 60)
            
            # Final comparison
            print(f"\n🎉 FINAL COMPARISON:")
            print(f"Original OneNote extraction: ~1,880 characters (metadata only)")
            print(f"Ollama Vision extraction: {len(total_extracted_text):,} characters (ACTUAL CONTENT)")
            
            if len(total_extracted_text) > 1880:
                improvement = len(total_extracted_text) / 1880
                print(f"Improvement factor: {improvement:.1f}x MORE REAL CONTENT!")
                print("🎉 THIS IS THE ACTUAL ONENOTE RESEARCH CONTENT!")
            
            return True
        else:
            print("❌ No text extracted from any images")
            return False
            
    except Exception as e:
        print(f"❌ Error processing all images: {e}")
        return False


def main():
    """Run direct Ollama vision test."""
    print("🤖 DIRECT OLLAMA VISION TEST")
    print("=" * 60)
    
    async def run_tests():
        # Test single image first
        single_success = await test_ollama_vision_direct()
        
        if single_success:
            print("\n✅ Single image test successful, processing all images...")
            all_success = await test_all_images_direct()
            return all_success
        else:
            print("\n❌ Single image test failed")
            return False
    
    success = asyncio.run(run_tests())
    
    print("\n" + "=" * 60)
    print("🎯 DIRECT OLLAMA VISION TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Ollama vision extracted actual OneNote content!")
        print("✅ Real research content extracted from OneNote images")
        print("✅ This is the actual content you were looking for")
        print("✅ Local processing with Ollama qwen2.5vl:3b model")
    else:
        print("❌ Direct Ollama vision test failed")
        print("Check if Ollama is running and the model is properly loaded")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
