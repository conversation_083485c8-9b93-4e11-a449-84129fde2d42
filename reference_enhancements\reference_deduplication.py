"""
Reference Deduplication Module

Identifies and links duplicate references in the knowledge graph using fuzzy matching.
"""

import asyncio
import logging
import difflib
import re
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path
import csv
from datetime import datetime

# Add the parent directory to the path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.falkordb_adapter import FalkorDBAdapter
from models.reference import Reference

logger = logging.getLogger(__name__)

class ReferenceDeduplcator:
    """
    Identifies and links duplicate references using fuzzy matching.
    """
    
    def __init__(self, 
                 title_threshold: float = 0.85,
                 author_threshold: float = 0.75,
                 overall_threshold: float = 0.8,
                 year_match_required: bool = True):
        """
        Initialize the deduplicator.
        
        Args:
            title_threshold: Minimum similarity for titles
            author_threshold: Minimum similarity for authors
            overall_threshold: Minimum overall similarity
            year_match_required: Whether publication years must match
        """
        self.title_threshold = title_threshold
        self.author_threshold = author_threshold
        self.overall_threshold = overall_threshold
        self.year_match_required = year_match_required
        
        self.db = FalkorDBAdapter()
        
    async def find_duplicate_references(self) -> Dict[str, Any]:
        """
        Find duplicate references in the knowledge graph.
        
        Returns:
            Dictionary with duplicate groups and statistics
        """
        logger.info("🔍 Starting reference deduplication analysis")
        
        try:
            # Get all references from the database
            references = await self._get_all_references()
            logger.info(f"📚 Found {len(references)} references to analyze")
            
            if len(references) < 2:
                return {
                    "success": True,
                    "duplicate_groups": [],
                    "total_references": len(references),
                    "duplicate_count": 0,
                    "unique_count": len(references),
                    "message": "Not enough references for deduplication analysis"
                }
            
            # Find duplicate groups
            duplicate_groups = await self._find_duplicate_groups(references)
            
            # Calculate statistics
            duplicate_count = sum(len(group) - 1 for group in duplicate_groups)
            unique_count = len(references) - duplicate_count
            
            logger.info(f"✅ Found {len(duplicate_groups)} duplicate groups")
            logger.info(f"📊 {duplicate_count} duplicates, {unique_count} unique references")
            
            return {
                "success": True,
                "duplicate_groups": duplicate_groups,
                "total_references": len(references),
                "duplicate_count": duplicate_count,
                "unique_count": unique_count,
                "groups_found": len(duplicate_groups)
            }
            
        except Exception as e:
            logger.error(f"❌ Error in reference deduplication: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "duplicate_groups": [],
                "total_references": 0,
                "duplicate_count": 0,
                "unique_count": 0
            }
    
    async def create_duplicate_relationships(self, duplicate_groups: List[List[Dict]]) -> Dict[str, Any]:
        """
        Create DUPLICATE_OF relationships in the knowledge graph.
        
        Args:
            duplicate_groups: List of duplicate reference groups
            
        Returns:
            Result dictionary
        """
        logger.info(f"🔗 Creating duplicate relationships for {len(duplicate_groups)} groups")
        
        try:
            relationships_created = 0
            
            for group in duplicate_groups:
                if len(group) < 2:
                    continue
                
                # Use the first reference as canonical (could be improved with better selection)
                canonical_ref = group[0]
                
                for duplicate_ref in group[1:]:
                    # Create DUPLICATE_OF relationship
                    query = """
                    MATCH (canonical:Reference {reference_text: $canonical_text})
                    MATCH (duplicate:Reference {reference_text: $duplicate_text})
                    MERGE (duplicate)-[:DUPLICATE_OF {
                        confidence: $confidence,
                        created_date: $created_date
                    }]->(canonical)
                    """
                    
                    params = {
                        'canonical_text': canonical_ref['reference_text'],
                        'duplicate_text': duplicate_ref['reference_text'],
                        'confidence': duplicate_ref.get('similarity_score', 0.9),
                        'created_date': datetime.now().isoformat()
                    }
                    
                    result = self.db.execute_cypher(query, params)
                    relationships_created += 1
            
            logger.info(f"✅ Created {relationships_created} duplicate relationships")
            
            return {
                "success": True,
                "relationships_created": relationships_created,
                "groups_processed": len(duplicate_groups)
            }
            
        except Exception as e:
            logger.error(f"❌ Error creating duplicate relationships: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "relationships_created": 0
            }
    
    async def _get_all_references(self) -> List[Dict[str, Any]]:
        """Get all references from the database."""
        query = """
        MATCH (r:Reference)
        RETURN r.reference_text as reference_text,
               r.authors as authors,
               r.title as title,
               r.year as year,
               r.journal as journal,
               r.source_document as source_document,
               id(r) as node_id
        """
        
        result = self.db.execute_cypher(query)
        
        if len(result) > 1 and result[1]:
            references = []
            for row in result[1]:
                ref_dict = {
                    'reference_text': row[0] if row[0] else '',
                    'authors': row[1] if row[1] else '',
                    'title': row[2] if row[2] else '',
                    'year': row[3] if row[3] else '',
                    'journal': row[4] if row[4] else '',
                    'source_document': row[5] if row[5] else '',
                    'node_id': row[6] if len(row) > 6 else None
                }
                references.append(ref_dict)
            return references
        
        return []
    
    async def _find_duplicate_groups(self, references: List[Dict[str, Any]]) -> List[List[Dict]]:
        """Find groups of duplicate references."""
        duplicate_groups = []
        processed_indices = set()
        
        for i, ref1 in enumerate(references):
            if i in processed_indices:
                continue
                
            current_group = [ref1]
            processed_indices.add(i)
            
            for j, ref2 in enumerate(references[i+1:], i+1):
                if j in processed_indices:
                    continue
                
                similarity_score = await self._calculate_similarity(ref1, ref2)
                
                if similarity_score >= self.overall_threshold:
                    ref2['similarity_score'] = similarity_score
                    current_group.append(ref2)
                    processed_indices.add(j)
            
            # Only add groups with duplicates
            if len(current_group) > 1:
                duplicate_groups.append(current_group)
        
        return duplicate_groups
    
    async def _calculate_similarity(self, ref1: Dict[str, Any], ref2: Dict[str, Any]) -> float:
        """Calculate similarity between two references."""
        
        # Check year match if required
        if self.year_match_required:
            year1 = ref1.get('year', '').strip()
            year2 = ref2.get('year', '').strip()
            if year1 and year2 and year1 != year2:
                return 0.0
        
        # Calculate title similarity
        title1 = self._normalize_text(ref1.get('title', ''))
        title2 = self._normalize_text(ref2.get('title', ''))
        title_sim = difflib.SequenceMatcher(None, title1, title2).ratio() if title1 and title2 else 0.0
        
        # Calculate author similarity
        authors1 = self._normalize_text(ref1.get('authors', ''))
        authors2 = self._normalize_text(ref2.get('authors', ''))
        author_sim = difflib.SequenceMatcher(None, authors1, authors2).ratio() if authors1 and authors2 else 0.0
        
        # Calculate overall text similarity
        text1 = self._normalize_text(ref1.get('reference_text', ''))
        text2 = self._normalize_text(ref2.get('reference_text', ''))
        text_sim = difflib.SequenceMatcher(None, text1, text2).ratio() if text1 and text2 else 0.0
        
        # Check individual thresholds
        if title1 and title2 and title_sim < self.title_threshold:
            return 0.0
        if authors1 and authors2 and author_sim < self.author_threshold:
            return 0.0
        
        # Calculate weighted overall similarity
        weights = []
        scores = []
        
        if title1 and title2:
            weights.append(0.4)
            scores.append(title_sim)
        
        if authors1 and authors2:
            weights.append(0.3)
            scores.append(author_sim)
        
        if text1 and text2:
            weights.append(0.3)
            scores.append(text_sim)
        
        if not weights:
            return 0.0
        
        # Normalize weights
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]
        
        # Calculate weighted average
        overall_similarity = sum(score * weight for score, weight in zip(scores, normalized_weights))
        
        return overall_similarity
    
    def _normalize_text(self, text: str) -> str:
        """Normalize text for comparison."""
        if not text:
            return ""
        
        # Convert to lowercase and remove extra whitespace
        normalized = re.sub(r'\s+', ' ', text.lower().strip())
        
        # Remove common punctuation that might vary
        normalized = re.sub(r'[.,;:!?()[\]{}"\'-]', '', normalized)
        
        return normalized
    
    async def export_duplicate_groups(self, duplicate_groups: List[List[Dict]], output_path: str) -> bool:
        """Export duplicate groups to CSV file."""
        try:
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    'Group_ID', 'Reference_Index', 'Is_Canonical', 'Similarity_Score',
                    'Title', 'Authors', 'Year', 'Journal', 'Source_Document', 'Reference_Text'
                ])
                
                # Write duplicate groups
                for group_id, group in enumerate(duplicate_groups, 1):
                    for ref_index, ref in enumerate(group):
                        is_canonical = ref_index == 0  # First reference is canonical
                        similarity_score = ref.get('similarity_score', 1.0 if is_canonical else 0.9)
                        
                        writer.writerow([
                            group_id,
                            ref_index,
                            is_canonical,
                            similarity_score,
                            ref.get('title', ''),
                            ref.get('authors', ''),
                            ref.get('year', ''),
                            ref.get('journal', ''),
                            ref.get('source_document', ''),
                            ref.get('reference_text', '')[:200] + '...' if len(ref.get('reference_text', '')) > 200 else ref.get('reference_text', '')
                        ])
            
            logger.info(f"✅ Exported duplicate groups to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting duplicate groups: {e}")
            return False
