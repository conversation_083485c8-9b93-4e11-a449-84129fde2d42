<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Explorer - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Vis.js for Network Visualization -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <link href="https://unpkg.com/vis-network/styles/vis-network.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">

    <style>
        #graph-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            background-color: #f8f9fa;
        }

        .graph-controls {
            margin-bottom: 20px;
        }

        .node-info-panel {
            height: 600px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Knowledge Graph Explorer</h1>

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item active" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item">Knowledge Graph</li>
                <li class="breadcrumb-item active" aria-current="page">Explorer</li>
            </ol>
        </nav>

        <!-- Graph Controls -->
        <div class="row graph-controls">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="node-search" class="form-control" placeholder="Search for nodes...">
                    <button class="btn btn-primary" id="search-button">Search</button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end">
                    <div class="btn-group me-2">
                        <button class="btn btn-outline-secondary" id="zoom-in-button"><i class="bi bi-zoom-in"></i></button>
                        <button class="btn btn-outline-secondary" id="zoom-out-button"><i class="bi bi-zoom-out"></i></button>
                        <button class="btn btn-outline-secondary" id="fit-button"><i class="bi bi-arrows-fullscreen"></i></button>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="export-button"><i class="bi bi-download"></i> Export</button>
                        <button class="btn btn-outline-primary" id="settings-button"><i class="bi bi-gear"></i> Settings</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Visualization -->
        <div class="row">
            <div class="col-md-9">
                <div id="graph-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading knowledge graph...</p>
                </div>
                <div id="graph-container"></div>
            </div>
            <div class="col-md-3">
                <div class="node-info-panel">
                    <h4>Node Information</h4>
                    <div id="node-info">
                        <p class="text-muted">Click on a node to see its details</p>
                    </div>

                    <hr>

                    <h5>Graph Statistics</h5>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Nodes
                            <span class="badge bg-primary rounded-pill" id="node-count">0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Edges
                            <span class="badge bg-primary rounded-pill" id="edge-count">0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Entity Types
                            <span class="badge bg-primary rounded-pill" id="entity-type-count">0</span>
                        </li>
                    </ul>

                    <hr>

                    <h5>Legend</h5>
                    <div id="graph-legend">
                        <!-- Legend items will be added dynamically -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Settings Modal -->
        <div class="modal fade" id="graph-settings-modal" tabindex="-1" aria-labelledby="graph-settings-modal-label" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="graph-settings-modal-label">Graph Settings</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form id="graph-settings-form">
                            <div class="mb-3">
                                <label for="node-limit" class="form-label">Node Limit</label>
                                <input type="number" class="form-control" id="node-limit" min="10" max="1000" value="100">
                                <div class="form-text">Maximum number of nodes to display (10-1000)</div>
                            </div>
                            <div class="mb-3">
                                <label for="layout-algorithm" class="form-label">Layout Algorithm</label>
                                <select class="form-select" id="layout-algorithm">
                                    <option value="forceAtlas2Based">Force Atlas 2</option>
                                    <option value="hierarchical">Hierarchical</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Node Types to Display</label>
                                <div id="node-type-checkboxes">
                                    <!-- Node type checkboxes will be added dynamically -->
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="apply-settings-button">Apply</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="static/js/graphiti_ui.js"></script>
    <script src="static/js/graphiti_ui_part2.js"></script>

    <script>
        // Global variables for the knowledge graph
        window.graphData = null;
        window.network = null;
        window.knowledgeGraphData = { graph: null };

        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM content loaded, initializing knowledge graph...");

            // Initialize the knowledge graph explorer
            initializeGraphTab();

            // Set up event listeners for the graph controls
            document.getElementById('search-button').addEventListener('click', function() {
                const searchQuery = document.getElementById('node-search').value;
                searchNodes(searchQuery);
            });

            document.getElementById('zoom-in-button').addEventListener('click', function() {
                zoomIn();
            });

            document.getElementById('zoom-out-button').addEventListener('click', function() {
                zoomOut();
            });

            document.getElementById('fit-button').addEventListener('click', function() {
                fitGraph();
            });

            document.getElementById('export-button').addEventListener('click', function() {
                exportGraph();
            });

            document.getElementById('settings-button').addEventListener('click', function() {
                showGraphSettings();
            });

            document.getElementById('apply-settings-button').addEventListener('click', function() {
                applyGraphSettings();
            });
        });

        function initializeNetwork(data) {
            const container = document.getElementById('graph-container');

            // Create nodes and edges datasets
            const nodes = new vis.DataSet(data.nodes);
            const edges = new vis.DataSet(data.edges);

            // Create network
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: {
                        size: 12,
                        face: 'Arial'
                    },
                    borderWidth: 2
                },
                edges: {
                    width: 1,
                    smooth: {
                        type: 'continuous'
                    },
                    arrows: {
                        to: {
                            enabled: true,
                            scaleFactor: 0.5
                        }
                    }
                },
                physics: {
                    stabilization: true,
                    barnesHut: {
                        gravitationalConstant: -80000,
                        springConstant: 0.001,
                        springLength: 200
                    }
                },
                interaction: {
                    navigationButtons: true,
                    keyboard: true
                }
            };

            network = new vis.Network(container, { nodes, edges }, options);

            // Add event listeners
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    displayNodeInfo(nodeId);
                }
            });

            // Update statistics
            document.getElementById('node-count').textContent = data.nodes.length;
            document.getElementById('edge-count').textContent = data.edges.length;

            // Create legend
            createLegend(data.nodes);
        }

        function displayNodeInfo(nodeId) {
            // Use window.graphData to access the global variable
            const node = window.graphData?.nodes?.find(n => n.id === nodeId);
            if (!node) {
                console.error("Node not found:", nodeId);
                return;
            }

            const nodeInfo = document.getElementById('node-info');
            nodeInfo.innerHTML = `
                <h5>${node.label}</h5>
                <p><strong>Type:</strong> ${node.group}</p>
                <p><strong>ID:</strong> ${node.id}</p>
                <hr>
                <h6>Properties</h6>
                <ul class="list-group">
                    ${Object.entries(node.properties || {})
                        .map(([key, value]) => `<li class="list-group-item"><strong>${key}:</strong> ${value}</li>`)
                        .join('')}
                </ul>
                <hr>
                <a href="/entity-detail?uuid=${node.id}" class="btn btn-sm btn-primary">View Details</a>
            `;
        }

        function createLegend(nodes) {
            const legend = document.getElementById('graph-legend');
            legend.innerHTML = '';

            // Get unique node types
            const nodeTypes = [...new Set(nodes.map(node => node.group))];

            // Create legend items
            nodeTypes.forEach(type => {
                const color = getNodeColor(type);

                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';

                const colorBox = document.createElement('div');
                colorBox.className = 'legend-color';
                colorBox.style.backgroundColor = color;

                const label = document.createElement('span');
                label.textContent = type;

                legendItem.appendChild(colorBox);
                legendItem.appendChild(label);
                legend.appendChild(legendItem);
            });
        }

        function getNodeColor(type) {
            // Color mapping for different node types
            const colorMap = {
                'Person': '#4e79a7',
                'Organization': '#f28e2c',
                'Location': '#e15759',
                'Disease': '#76b7b2',
                'Symptom': '#59a14f',
                'Treatment': '#edc949',
                'Medication': '#af7aa1',
                'Food': '#ff9da7',
                'Herb': '#9c755f',
                'Nutrient': '#bab0ab'
            };

            return colorMap[type] || '#b3b3b3';
        }
    </script>
</body>
</html>
