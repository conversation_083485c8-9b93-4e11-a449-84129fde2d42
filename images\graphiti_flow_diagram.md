```mermaid
graph TD
    %% Main Components
    User([User])
    WebUI[Web Interface]
    PDFProcessor[PDF Processor]
    MetadataExtractor[Metadata Extractor]
    ReferenceExtractor[Reference Extractor]
    GraphitiCore[Graphiti Core]
    Neo4j[(Neo4j Database)]
    LLM[LLM Service]
    Embedder[Embedding Service]
    RelationshipExtractor[Relationship Extractor]
    AttributeExtractor[Attribute Extractor]
    TaxonomyBuilder[Taxonomy Builder]

    %% Document Processing Flow
    User -->|Upload PDF| WebUI
    WebUI -->|Process Document| PDFProcessor
    PDFProcessor -->|Extract Text| MistralOCR{Mistral OCR Available?}
    MistralOCR -->|Yes| MistralProcessor[Mistral OCR Processor]
    MistralOCR -->|No| PyPDF[PyPDF2 Processor]
    MistralProcessor -->|Return Text| TextChunking[Text Chunking]
    PyPDF -->|Return Text| TextChunking
    PDFProcessor -->|Extract Metadata| MetadataExtractor
    PDFProcessor -->|Extract References| ReferenceExtractor

    %% Knowledge Graph Building
    TextChunking -->|Chunked Text| GraphitiCore
    MetadataExtractor -->|Document Metadata| GraphitiCore
    ReferenceExtractor -->|Citations & References| GraphitiCore
    GraphitiCore -->|Create Episode Node| Neo4j
    GraphitiCore -->|Create Fact Nodes| Neo4j
    GraphitiCore -->|Extract Entities| LLM
    LLM -->|Entity List| GraphitiCore
    GraphitiCore -->|Create Entity Nodes| Neo4j
    GraphitiCore -->|Extract Relationships| RelationshipExtractor
    RelationshipExtractor -->|Relationships with Confidence| GraphitiCore
    GraphitiCore -->|Create Relationships| Neo4j
    GraphitiCore -->|Build Taxonomy| TaxonomyBuilder
    TaxonomyBuilder -->|Hierarchical Structure| GraphitiCore
    GraphitiCore -->|Extract Attributes| AttributeExtractor
    AttributeExtractor -->|Domain-Specific Attributes| GraphitiCore
    GraphitiCore -->|Update Entity Attributes| Neo4j
    GraphitiCore -->|Generate Embeddings| Embedder
    Embedder -->|Vector Embeddings| GraphitiCore
    GraphitiCore -->|Store Embeddings| Neo4j

    %% Querying Flow
    User -->|Ask Question| WebUI
    WebUI -->|Query| GraphitiCore
    GraphitiCore -->|Vector Search| Neo4j
    Neo4j -->|Relevant Facts & Entities| GraphitiCore
    GraphitiCore -->|Generate Answer| LLM
    LLM -->|Answer with References| GraphitiCore
    GraphitiCore -->|Display Answer| WebUI
    WebUI -->|Show Answer| User

    %% Advanced Search Flow
    User -->|Advanced Search| WebUI
    WebUI -->|Search Parameters| GraphitiCore
    GraphitiCore -->|Filtered Query| Neo4j
    Neo4j -->|Search Results| GraphitiCore
    GraphitiCore -->|Format Results| WebUI
    WebUI -->|Display Results| User

    %% Knowledge Graph Exploration
    User -->|Explore Knowledge Graph| WebUI
    WebUI -->|Get Taxonomy| GraphitiCore
    GraphitiCore -->|Query Taxonomy| Neo4j
    Neo4j -->|Hierarchical Data| GraphitiCore
    GraphitiCore -->|Taxonomy Information| WebUI
    WebUI -->|Display Knowledge Graph| User

    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef database fill:#bfb,stroke:#333,stroke-width:1px;
    classDef service fill:#fbb,stroke:#333,stroke-width:1px;
    classDef decision fill:#fffacd,stroke:#333,stroke-width:1px;
    classDef extractor fill:#ffd,stroke:#333,stroke-width:1px;

    class WebUI,GraphitiCore primary;
    class PDFProcessor,TextChunking,MistralProcessor,PyPDF secondary;
    class Neo4j database;
    class LLM,Embedder,MistralOCR service;
    class MistralOCR decision;
    class MetadataExtractor,ReferenceExtractor,RelationshipExtractor,AttributeExtractor,TaxonomyBuilder extractor;
```
