"""
Example demonstrating how to use Google's Generative AI directly
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

def main():
    try:
        # Load environment variables
        load_dotenv()

        # Get Google API key from environment variable or prompt user
        google_api_key = os.environ.get('GOOGLE_API_KEY')
        if not google_api_key:
            google_api_key = input("Please enter your Google API key: ")
            # You might want to save this to .env file for future use
            with open('.env', 'a') as f:
                f.write(f'\nGOOGLE_API_KEY={google_api_key}')

        print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")

        # Configure the Gemini API
        genai.configure(api_key=google_api_key)

        # List available models
        print("Available models:")
        for model in genai.list_models():
            if 'generateContent' in model.supported_generation_methods:
                print(f"- {model.name}")

        # Initialize the Gemini model
        model = genai.GenerativeModel('gemini-2.0-flash')

        # Generate text
        print("\nGenerating text with Gemini...")
        response = model.generate_content("Write a short paragraph about knowledge graphs and their applications.")
        print(response.text)
        # Create embeddings
        print("\nCreating embeddings with Gemini...")
        embedding_model = genai.GenerativeModel('embedding-001')
        try:
            result = embedding_model.embed_content(
                content="This is a test sentence for embedding.",
            )
            embedding = result['embedding']
            print(f"Embedding dimension: {len(embedding)}")
            print(f"First 5 values: {embedding[:5]}")
        except Exception as e:
            print(f"Error creating embeddings: {e}")

        print("\nExample completed successfully!")
    except Exception as e:
        print(f"Error in main function: {e}")

if __name__ == "__main__":
    main()
