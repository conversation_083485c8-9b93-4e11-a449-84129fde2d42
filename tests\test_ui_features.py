"""
Comprehensive test script for Graphiti UI features
"""

import requests
import json
import time

BASE_URL = "http://localhost:9753"

def test_search_feature():
    """Test the search feature"""
    print("\n=== Testing Search Feature ===")

    # Test search endpoint
    search_query = "sleep"
    response = requests.get(f"{BASE_URL}/api/search?q={search_query}")

    if response.status_code == 200:
        data = response.json()
        results = data.get('results', [])
        print(f"Search for '{search_query}' returned {len(results)} results")

        if results:
            print(f"First result: {results[0].get('preview', '')[:100]}...")
    else:
        print(f"Search API returned status code {response.status_code}")

def test_qa_feature():
    """Test the question answering feature"""
    print("\n=== Testing Question Answering Feature ===")

    # Test answer endpoint
    question = "What is ThymoDream?"
    response = requests.get(f"{BASE_URL}/api/answer?q={question}")

    if response.status_code == 200:
        data = response.json()
        answer = data.get('answer', '')
        sources = data.get('sources', [])

        print(f"Question: {question}")
        print(f"Answer: {answer[:150]}...")
        print(f"Sources: {len(sources)}")
    else:
        print(f"Answer API returned status code {response.status_code}")

def test_documents_feature():
    """Test the documents feature"""
    print("\n=== Testing Documents Feature ===")

    # Test documents endpoint
    response = requests.get(f"{BASE_URL}/api/documents")

    if response.status_code == 200:
        data = response.json()
        documents = data.get('documents', [])

        print(f"Found {len(documents)} documents")

        if documents:
            for i, doc in enumerate(documents[:3]):  # Show first 3 documents
                print(f"Document {i+1}: {doc.get('title', 'No title')} ({doc.get('chunks', 0)} chunks)")
    else:
        print(f"Documents API returned status code {response.status_code}")

def test_entities_feature():
    """Test the entities feature"""
    print("\n=== Testing Entities Feature ===")

    # Test entities endpoint with filtering
    entity_types = ["Herb", "Nutrient", "Concept"]

    for entity_type in entity_types:
        response = requests.get(f"{BASE_URL}/api/entities?type={entity_type}")

        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])

            print(f"Found {len(entities)} entities of type '{entity_type}'")

            if entities:
                for i, entity in enumerate(entities[:2]):  # Show first 2 entities
                    print(f"  {i+1}. {entity.get('name', 'No name')} (mentions: {entity.get('mention_count', 0)})")
        else:
            print(f"Entities API for type {entity_type} returned status code {response.status_code}")

    # Test entity details
    response = requests.get(f"{BASE_URL}/api/entities")

    if response.status_code == 200:
        data = response.json()
        entities = data.get('entities', [])

        if entities:
            entity_uuid = entities[0]['uuid']
            detail_response = requests.get(f"{BASE_URL}/api/entities/{entity_uuid}")

            if detail_response.status_code == 200:
                entity_data = detail_response.json()
                print(f"\nEntity details for {entity_data.get('name')}:")
                print(f"  - Type: {entity_data.get('type')}")
                print(f"  - Description: {entity_data.get('description', '')[:50]}...")
                print(f"  - Mentions: {len(entity_data.get('mentions', []))}")
                print(f"  - Relationships: {len(entity_data.get('relationships', []))}")
            else:
                print(f"Entity details API returned status code {detail_response.status_code}")

def test_settings_feature():
    """Test the settings feature"""
    print("\n=== Testing Settings Feature ===")

    # Test settings endpoint
    response = requests.get(f"{BASE_URL}/api/settings")

    if response.status_code == 200:
        data = response.json()
        print(f"Settings: {json.dumps(data, indent=2)}")
    else:
        print(f"Settings API returned status code {response.status_code}")

def run_all_tests():
    """Run all UI feature tests"""
    print("Starting comprehensive UI feature tests...")

    test_search_feature()
    test_qa_feature()
    test_documents_feature()
    test_entities_feature()
    test_settings_feature()

    print("\nAll tests completed!")

if __name__ == "__main__":
    run_all_tests()
