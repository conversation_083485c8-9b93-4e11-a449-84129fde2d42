#!/usr/bin/env python3
"""
Quick script to check entity extraction results after enhancement
"""

from database.falkordb_adapter import FalkorDBAdapter

def main():
    # Initialize database
    db = FalkorDBAdapter()

    # Get all entities
    entities = db.get_nodes_by_label("Entity", limit=1000)
    print(f"Total entities in database: {len(entities)}")

    if entities:
        # Debug: check structure
        print(f"First entity structure: {type(entities[0])}")
        if entities:
            print(f"Sample entity: {entities[0]}")

        # Show entity type breakdown
        entity_types = {}
        for entity in entities:
            # Parse the FalkorDB entity structure
            entity_props = {}
            if isinstance(entity, list) and len(entity) >= 3:
                # Extract properties from the nested structure
                properties = entity[2][1]  # properties are at index 2, value at index 1
                for prop in properties:
                    if len(prop) >= 2:
                        entity_props[prop[0]] = prop[1]

            entity_type = entity_props.get('type', 'Unknown')
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1

        print("\nEntity type breakdown:")
        for entity_type, count in sorted(entity_types.items()):
            print(f"  {entity_type}: {count}")

        # Show some examples of new entity types
        new_types = ['Part_Used', 'Application', 'Mechanism', 'Receptor', 'Dosage', 'Preparation']
        print("\nExamples of new entity types:")
        for entity_type in new_types:
            examples = []
            for entity in entities:
                # Parse entity properties
                entity_props = {}
                if isinstance(entity, list) and len(entity) >= 3:
                    properties = entity[2][1]
                    for prop in properties:
                        if len(prop) >= 2:
                            entity_props[prop[0]] = prop[1]

                if entity_props.get('type') == entity_type:
                    examples.append(entity_props)

            if examples:
                print(f"\n{entity_type} ({len(examples)} found):")
                for example in examples[:3]:  # Show first 3 examples
                    name = example.get('name', 'Unknown')
                    description = example.get('description', 'No description')
                    if len(description) > 100:
                        description = description[:100] + "..."
                    print(f"  - {name}: {description}")
    else:
        print("No entities found")

if __name__ == "__main__":
    main()
