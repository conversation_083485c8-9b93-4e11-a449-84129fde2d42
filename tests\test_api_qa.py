"""
Integration tests for the Q&A API endpoints.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

@patch("services.qa_service.generate_answer")
@patch("services.qa_service.get_relevant_facts")
def test_answer_question_endpoint(mock_get_relevant_facts, mock_generate_answer, test_client):
    """
    Test the answer question endpoint.
    
    Args:
        mock_get_relevant_facts: Mocked get_relevant_facts function
        mock_generate_answer: Mocked generate_answer function
        test_client: FastAPI test client
    """
    # Mock the get_relevant_facts function
    mock_get_relevant_facts.return_value = [
        {
            "uuid": "fact1",
            "body": "Vitamin C is an essential nutrient.",
            "chunk_num": 1
        },
        {
            "uuid": "fact2",
            "body": "Vitamin C helps boost the immune system.",
            "chunk_num": 2
        }
    ]
    
    # Mock the generate_answer function
    mock_generate_answer.return_value = {
        "question": "What are the benefits of vitamin C?",
        "answer": "Vitamin C is an essential nutrient that helps boost the immune system [1][2].",
        "references": [
            {
                "uuid": "fact1",
                "body": "Vitamin C is an essential nutrient.",
                "chunk_num": 1
            },
            {
                "uuid": "fact2",
                "body": "Vitamin C helps boost the immune system.",
                "chunk_num": 2
            }
        ]
    }
    
    # Make a request to the answer question endpoint
    response = test_client.post(
        "/api/qa/answer",
        json={
            "question": "What are the benefits of vitamin C?",
            "llm_provider": "openai",
            "llm_model": "gpt-4.1-mini",
            "max_facts": 10
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "question" in data
    assert "answer" in data
    assert "references" in data
    assert data["question"] == "What are the benefits of vitamin C?"
    assert "Vitamin C is an essential nutrient that helps boost the immune system" in data["answer"]
    assert len(data["references"]) == 2
    assert data["references"][0]["uuid"] == "fact1"
    assert data["references"][1]["uuid"] == "fact2"
    
    # Check that the parameters were passed to the functions correctly
    mock_get_relevant_facts.assert_called_once_with("What are the benefits of vitamin C?", 10)
    mock_generate_answer.assert_called_once_with(
        "What are the benefits of vitamin C?",
        mock_get_relevant_facts.return_value,
        "openai",
        "gpt-4.1-mini"
    )

@patch("services.qa_service.get_relevant_facts")
def test_answer_question_endpoint_no_facts(mock_get_relevant_facts, test_client):
    """
    Test the answer question endpoint when no relevant facts are found.
    
    Args:
        mock_get_relevant_facts: Mocked get_relevant_facts function
        test_client: FastAPI test client
    """
    # Mock the get_relevant_facts function to return an empty list
    mock_get_relevant_facts.return_value = []
    
    # Make a request to the answer question endpoint
    response = test_client.post(
        "/api/qa/answer",
        json={
            "question": "What are the benefits of vitamin C?",
            "llm_provider": "openai",
            "llm_model": "gpt-4.1-mini",
            "max_facts": 10
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "question" in data
    assert "answer" in data
    assert "references" in data
    assert data["question"] == "What are the benefits of vitamin C?"
    assert "I don't have enough information" in data["answer"]
    assert len(data["references"]) == 0

@patch("services.qa_service.generate_answer")
@patch("services.qa_service.get_relevant_facts")
def test_answer_question_endpoint_error(mock_get_relevant_facts, mock_generate_answer, test_client):
    """
    Test the answer question endpoint with an error.
    
    Args:
        mock_get_relevant_facts: Mocked get_relevant_facts function
        mock_generate_answer: Mocked generate_answer function
        test_client: FastAPI test client
    """
    # Mock the get_relevant_facts function
    mock_get_relevant_facts.return_value = [
        {
            "uuid": "fact1",
            "body": "Vitamin C is an essential nutrient.",
            "chunk_num": 1
        }
    ]
    
    # Mock the generate_answer function to raise an exception
    mock_generate_answer.side_effect = Exception("Test error")
    
    # Make a request to the answer question endpoint
    response = test_client.post(
        "/api/qa/answer",
        json={
            "question": "What are the benefits of vitamin C?",
            "llm_provider": "openai",
            "llm_model": "gpt-4.1-mini",
            "max_facts": 10
        }
    )
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error answering question" in response.json()["detail"]

@patch("services.qa_service.get_relevant_facts")
def test_get_facts_endpoint(mock_get_relevant_facts, test_client):
    """
    Test the get facts endpoint.
    
    Args:
        mock_get_relevant_facts: Mocked get_relevant_facts function
        test_client: FastAPI test client
    """
    # Mock the get_relevant_facts function
    mock_get_relevant_facts.return_value = [
        {
            "uuid": "fact1",
            "body": "Vitamin C is an essential nutrient.",
            "chunk_num": 1
        },
        {
            "uuid": "fact2",
            "body": "Vitamin C helps boost the immune system.",
            "chunk_num": 2
        }
    ]
    
    # Make a request to the get facts endpoint
    response = test_client.get("/api/qa/facts?question=vitamin%20C&limit=10")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "facts" in data
    assert "count" in data
    assert data["count"] == 2
    assert len(data["facts"]) == 2
    assert data["facts"][0]["uuid"] == "fact1"
    assert data["facts"][1]["uuid"] == "fact2"
    
    # Check that the parameters were passed to the get_relevant_facts function correctly
    mock_get_relevant_facts.assert_called_once_with("vitamin C", 10)

@patch("services.qa_service.get_relevant_facts")
def test_get_facts_endpoint_error(mock_get_relevant_facts, test_client):
    """
    Test the get facts endpoint with an error.
    
    Args:
        mock_get_relevant_facts: Mocked get_relevant_facts function
        test_client: FastAPI test client
    """
    # Mock the get_relevant_facts function to raise an exception
    mock_get_relevant_facts.side_effect = Exception("Test error")
    
    # Make a request to the get facts endpoint
    response = test_client.get("/api/qa/facts?question=vitamin%20C")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting facts" in response.json()["detail"]
