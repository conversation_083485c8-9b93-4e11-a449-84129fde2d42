#!/usr/bin/env python3
"""
Test script to verify all UI endpoints are working correctly.
"""

import requests
import json
import time

def test_endpoint(url, description, timeout=10):
    """Test a single endpoint."""
    try:
        print(f"Testing {description}...")
        response = requests.get(url, timeout=timeout)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {description}: SUCCESS")
            
            # Show some basic info about the response
            if isinstance(data, dict):
                if 'count' in data:
                    print(f"   Count: {data['count']}")
                elif len(data) > 0:
                    print(f"   Keys: {list(data.keys())[:5]}")
            elif isinstance(data, list):
                print(f"   Items: {len(data)}")
                
            return True
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            print(f"   Error: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ {description}: EXCEPTION - {str(e)}")
        return False


def main():
    """Test all UI endpoints."""
    base_url = "http://localhost:8000"
    
    print("🔍 Testing UI API Endpoints")
    print("=" * 50)
    
    # Test endpoints used by dashboard
    print("\n📊 Dashboard Endpoints:")
    test_endpoint(f"{base_url}/api/fast/graph-stats", "Fast Graph Stats")
    test_endpoint(f"{base_url}/api/fast/documents?limit=5", "Fast Documents")
    test_endpoint(f"{base_url}/api/fast/entities?limit=5", "Fast Entities")
    test_endpoint(f"{base_url}/api/system-status", "System Status")
    
    # Test endpoints used by entities page
    print("\n👥 Entity Endpoints:")
    test_endpoint(f"{base_url}/api/entity-types", "Entity Types")
    test_endpoint(f"{base_url}/api/fast/entities?limit=10", "Fast Entities List")
    test_endpoint(f"{base_url}/api/entities?limit=10", "Full Entities List")
    
    # Test a specific entity (if we can get one)
    try:
        entities_response = requests.get(f"{base_url}/api/fast/entities?limit=1", timeout=5)
        if entities_response.status_code == 200:
            entities_data = entities_response.json()
            if entities_data.get('entities') and len(entities_data['entities']) > 0:
                entity_uuid = entities_data['entities'][0]['uuid']
                test_endpoint(f"{base_url}/api/entity/{entity_uuid}", "Entity Details")
                test_endpoint(f"{base_url}/api/entity/{entity_uuid}/relationships", "Entity Relationships")
    except:
        print("⚠️  Could not test specific entity endpoints")
    
    # Test endpoints used by knowledge graph
    print("\n🕸️  Knowledge Graph Endpoints:")
    test_endpoint(f"{base_url}/api/knowledge-graph/graph?limit=10", "Knowledge Graph Data")
    test_endpoint(f"{base_url}/api/relationship-types", "Relationship Types")
    
    # Test endpoints used by search page
    print("\n🔍 Search Endpoints:")
    test_endpoint(f"{base_url}/api/search/entities?query=vitamin&limit=5", "Search Entities")
    test_endpoint(f"{base_url}/api/search/documents?query=health&limit=5", "Search Documents")
    
    # Test endpoints used by references page
    print("\n📚 Reference Endpoints:")
    test_endpoint(f"{base_url}/api/references?limit=5", "References List")
    test_endpoint(f"{base_url}/api/references/years", "Reference Years")
    test_endpoint(f"{base_url}/api/references/journals?limit=5", "Reference Journals")
    
    # Test endpoints used by enhanced upload
    print("\n📤 Enhanced Upload Endpoints:")
    test_endpoint(f"{base_url}/api/enhanced/supported-types", "Supported File Types")
    
    # Test endpoints used by settings
    print("\n⚙️  Settings Endpoints:")
    test_endpoint(f"{base_url}/api/settings", "Settings")
    test_endpoint(f"{base_url}/api/settings/models", "Available Models")
    
    print("\n" + "=" * 50)
    print("✅ Endpoint testing complete!")
    print("\nIf any endpoints failed, check:")
    print("1. Server is running on port 8000")
    print("2. Database connections are working")
    print("3. Routes are properly included in app.py")


if __name__ == "__main__":
    main()
