"""
Embedding utilities for the Graphiti application.

This module provides functions for generating embeddings for text.
"""

from typing import List

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

# OpenAI removed - using only <PERSON>llama for embeddings

# Try to import Ollama for local embeddings
try:
    from utils.ollama_client import get_ollama_client
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    logger.warning("Ollama client not available. Will use alternative embedding methods.")

async def generate_embeddings(texts: List[str], model: str = None) -> List[List[float]]:
    """
    Generate embeddings for a list of texts using Ollama.

    This function uses Ollama with Snowflake Arctic Embed2 model (local).
    Falls back to placeholder embeddings if Ollama is not available.

    Args:
        texts: List of texts to generate embeddings for
        model: Optional model name to use (defaults to snowflake-arctic-embed2)

    Returns:
        List of embedding vectors
    """
    if not texts:
        logger.warning("No texts provided for embedding generation")
        return []
    
    logger.info(f"Generating embeddings for {len(texts)} texts")
    
    # Try Ollama first (local, no API key needed)
    if OLLAMA_AVAILABLE:
        try:
            logger.info("Attempting to generate embeddings using Ollama with Snowflake Arctic Embed2 model")
            ollama_client = get_ollama_client()
            
            # Check if Ollama is available
            if ollama_client.is_available():
                # Check if the Snowflake model is available
                if model:
                    # Use the specified model
                    embeddings = ollama_client.generate_embeddings(texts, model=model)
                elif ollama_client.is_model_available("snowflake-arctic-embed2"):
                    # Use the Snowflake model
                    embeddings = ollama_client.generate_embeddings(texts, model="snowflake-arctic-embed2")
                else:
                    # Use any available embedding model
                    embeddings = ollama_client.generate_embeddings(texts)
                
                if embeddings:
                    logger.info(f"Successfully generated {len(embeddings)} embeddings using Ollama")
                    return embeddings
        except Exception as e:
            logger.error(f"Error generating embeddings with Ollama: {e}")
    
    # If Ollama fails, use placeholder embeddings
    logger.warning("Ollama embedding generation failed - using placeholder embeddings")
    return [generate_placeholder_embedding(1024) for _ in texts]

def generate_placeholder_embedding(dimension: int = 1024) -> List[float]:
    """
    Generate a placeholder embedding vector.
    
    Args:
        dimension: Dimension of the embedding vector
        
    Returns:
        Placeholder embedding vector
    """
    import random
    
    # Generate a random vector
    vector = [random.uniform(-1, 1) for _ in range(dimension)]
    
    # Normalize the vector
    magnitude = sum(x * x for x in vector) ** 0.5
    if magnitude > 0:
        vector = [x / magnitude for x in vector]
    
    return vector
