"""
<PERSON><PERSON>t to fix missing UUIDs across all node and relationship types in the database.

This script:
1. Adds UUIDs to Episode nodes (documents) that are missing them
2. Adds UUIDs to Fact nodes (document chunks) that are missing them
3. Adds UUIDs to Entity nodes that are missing them
4. Adds UUIDs to relationships that are missing them

Usage:
    python scripts/fix_missing_uuids.py [--dry-run] [--verbose]
"""

import os
import asyncio
import logging
import uuid
import argparse
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple

import sys
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from database.database_service import get_falkordb_adapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def fix_episode_nodes(dry_run: bool = False) -> int:
    """
    Fix Episode nodes missing UUIDs.

    Args:
        dry_run: If True, don't make any changes

    Returns:
        Number of nodes fixed
    """
    adapter = await get_falkordb_adapter()

    # Find Episode nodes without UUIDs
    query = """
    MATCH (e:Episode)
    WHERE e.uuid IS NULL
    RETURN id(e) as id, e.name as name
    """

    result = adapter.execute_cypher(query)
    episodes = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            episodes.append({
                "id": row[0],
                "name": row[1]
            })

    logger.info(f"Found {len(episodes)} Episode nodes without UUIDs")

    if dry_run:
        return len(episodes)

    # Fix each Episode node
    fixed_count = 0
    for episode in episodes:
        episode_uuid = str(uuid.uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()

        update_query = f"""
        MATCH (e:Episode)
        WHERE id(e) = {episode['id']}
        SET e.uuid = '{episode_uuid}',
            e.updated_at = '{timestamp}'
        RETURN e.name as name, e.uuid as uuid
        """

        update_result = adapter.execute_cypher(update_query)

        if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
            fixed_count += 1
            logger.info(f"Fixed Episode node: {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

    return fixed_count

async def fix_fact_nodes(dry_run: bool = False) -> int:
    """
    Fix Fact nodes missing UUIDs.

    Args:
        dry_run: If True, don't make any changes

    Returns:
        Number of nodes fixed
    """
    adapter = await get_falkordb_adapter()

    # Find Fact nodes without UUIDs
    query = """
    MATCH (f:Fact)
    WHERE f.uuid IS NULL
    RETURN id(f) as id, f.body as body
    LIMIT 1000
    """

    result = adapter.execute_cypher(query)
    facts = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            facts.append({
                "id": row[0],
                "body": row[1][:50] + "..." if row[1] and len(row[1]) > 50 else row[1]
            })

    logger.info(f"Found {len(facts)} Fact nodes without UUIDs")

    if dry_run:
        return len(facts)

    # Fix each Fact node
    fixed_count = 0
    for fact in facts:
        fact_uuid = str(uuid.uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()

        update_query = f"""
        MATCH (f:Fact)
        WHERE id(f) = {fact['id']}
        SET f.uuid = '{fact_uuid}',
            f.updated_at = '{timestamp}'
        RETURN f.uuid as uuid
        """

        update_result = adapter.execute_cypher(update_query)

        if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
            fixed_count += 1
            logger.info(f"Fixed Fact node with UUID {update_result[1][0][0]}")

    return fixed_count

async def fix_entity_nodes(dry_run: bool = False) -> int:
    """
    Fix Entity nodes missing UUIDs.

    Args:
        dry_run: If True, don't make any changes

    Returns:
        Number of nodes fixed
    """
    adapter = await get_falkordb_adapter()

    # Find Entity nodes without UUIDs
    query = """
    MATCH (e:Entity)
    WHERE e.uuid IS NULL
    RETURN id(e) as id, e.name as name, e.type as type
    """

    result = adapter.execute_cypher(query)
    entities = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            entities.append({
                "id": row[0],
                "name": row[1],
                "type": row[2]
            })

    logger.info(f"Found {len(entities)} Entity nodes without UUIDs")

    if dry_run:
        return len(entities)

    # Fix each Entity node
    fixed_count = 0
    for entity in entities:
        entity_uuid = str(uuid.uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()

        update_query = f"""
        MATCH (e:Entity)
        WHERE id(e) = {entity['id']}
        SET e.uuid = '{entity_uuid}',
            e.updated_at = '{timestamp}'
        RETURN e.name as name, e.type as type, e.uuid as uuid
        """

        update_result = adapter.execute_cypher(update_query)

        if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
            fixed_count += 1
            logger.info(f"Fixed Entity node: {update_result[1][0][0]} ({update_result[1][0][1]}) with UUID {update_result[1][0][2]}")

    return fixed_count

async def fix_relationships(dry_run: bool = False) -> int:
    """
    Fix relationships missing UUIDs.

    Args:
        dry_run: If True, don't make any changes

    Returns:
        Number of relationships fixed
    """
    adapter = await get_falkordb_adapter()

    # Find relationships without UUIDs
    query = """
    MATCH ()-[r]->()
    WHERE r.uuid IS NULL
    RETURN id(r) as id, type(r) as type
    LIMIT 1000
    """

    result = adapter.execute_cypher(query)
    relationships = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            relationships.append({
                "id": row[0],
                "type": row[1]
            })

    logger.info(f"Found {len(relationships)} relationships without UUIDs")

    if dry_run:
        return len(relationships)

    # Fix each relationship
    fixed_count = 0
    for rel in relationships:
        rel_uuid = str(uuid.uuid4())
        timestamp = datetime.now(timezone.utc).isoformat()

        update_query = f"""
        MATCH ()-[r]->()
        WHERE id(r) = {rel['id']}
        SET r.uuid = '{rel_uuid}',
            r.updated_at = '{timestamp}'
        RETURN type(r) as type, r.uuid as uuid
        """

        update_result = adapter.execute_cypher(update_query)

        if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
            fixed_count += 1
            logger.info(f"Fixed relationship of type {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

    return fixed_count

async def main():
    """Main function."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Fix missing UUIDs in the database")
    parser.add_argument("--dry-run", action="store_true", help="Don't make any changes, just report what would be done")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    try:
        if args.dry_run:
            logger.info("Running in dry-run mode, no changes will be made")

        # Fix Episode nodes
        episode_fixed = await fix_episode_nodes(args.dry_run)
        logger.info(f"Fixed {episode_fixed} Episode nodes")

        # Fix Fact nodes
        fact_fixed = await fix_fact_nodes(args.dry_run)
        logger.info(f"Fixed {fact_fixed} Fact nodes")

        # Fix Entity nodes
        entity_fixed = await fix_entity_nodes(args.dry_run)
        logger.info(f"Fixed {entity_fixed} Entity nodes")

        # Fix relationships
        rel_fixed = await fix_relationships(args.dry_run)
        logger.info(f"Fixed {rel_fixed} relationships")

        # Summary
        total_fixed = episode_fixed + fact_fixed + entity_fixed + rel_fixed

        if args.dry_run:
            logger.info(f"Summary: Would fix {total_fixed} elements")
            logger.info("Run without --dry-run to apply these changes")
        else:
            logger.info(f"Summary: Fixed {total_fixed} elements")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
