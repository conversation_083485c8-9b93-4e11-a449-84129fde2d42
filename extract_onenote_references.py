#!/usr/bin/env python3
"""
Extract references from OneNote page documents and add them to the CSV.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.reference_service import extract_references_from_document

async def extract_onenote_references():
    """Extract references from OneNote page documents."""
    
    print("📚 EXTRACTING REFERENCES FROM ONENOTE PAGE DOCUMENTS")
    print("=" * 60)
    
    # Find OneNote page documents
    onenote_dir = Path("onenote_page_documents")
    if not onenote_dir.exists():
        print("❌ OneNote page documents directory not found")
        return False
    
    onenote_files = list(onenote_dir.glob("onenote_page_*.txt"))
    if not onenote_files:
        print("❌ No OneNote page documents found")
        return False
    
    print(f"✅ Found {len(onenote_files)} OneNote page documents")
    
    print(f"✅ Using reference service for extraction")
    
    total_references = 0
    
    # Process each OneNote page document
    for i, file_path in enumerate(onenote_files, 1):
        print(f"\n📄 Processing {i}/{len(onenote_files)}: {file_path.name}")
        
        try:
            # Extract references from this document using the service
            result = await extract_references_from_document(str(file_path), llm_provider='openrouter')

            if result.get('success', False):
                # Count references from different extraction methods
                llm_refs = result.get('llm_reference_count', 0)
                regex_refs = result.get('regex_reference_count', 0)
                total_refs = result.get('total_reference_count', 0)

                print(f"   ✅ Extracted {total_refs} references (LLM: {llm_refs}, Regex: {regex_refs})")

                # Show some reference details if available
                if 'llm_references' in result and result['llm_references']:
                    print(f"   📚 Sample LLM references:")
                    for j, ref in enumerate(result['llm_references'][:3], 1):
                        if isinstance(ref, dict):
                            title = ref.get('title', 'No title')
                            authors = ref.get('authors', 'No authors')
                            year = ref.get('year', 'No year')
                            print(f"     {j}. {authors} ({year}). {title}")
                        else:
                            # If it's just a string
                            ref_text = str(ref)[:100] + "..." if len(str(ref)) > 100 else str(ref)
                            print(f"     {j}. {ref_text}")

                total_references += total_refs
            else:
                error = result.get('error', 'Unknown error')
                print(f"   ❌ Failed: {error}")

        except Exception as e:
            print(f"   ❌ Error processing {file_path.name}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 REFERENCE EXTRACTION SUMMARY:")
    print("=" * 60)
    print(f"✅ Processed: {len(onenote_files)} OneNote page documents")
    print(f"📚 Total references extracted: {total_references}")
    
    if total_references > 0:
        print(f"\n🎉 SUCCESS! References extracted from OneNote pages")
        print(f"References should now be available in:")
        print(f"  - Individual CSV files in references/ directory")
        print(f"  - Master references file: references/all_references.csv")
        
        # Check if references were added to master file
        master_file = Path("references/all_references.csv")
        if master_file.exists():
            # Count lines to see if new references were added
            with open(master_file, 'r', encoding='utf-8') as f:
                line_count = sum(1 for _ in f)
            print(f"  - Master file now has {line_count-1} total references")
        
        return True
    else:
        print(f"\n❌ No references were extracted")
        print(f"This suggests an issue with the reference extraction process")
        return False


def main():
    """Extract references from OneNote page documents."""
    print("📚 ONENOTE REFERENCE EXTRACTION")
    print("=" * 60)
    
    success = asyncio.run(extract_onenote_references())
    
    print("\n" + "=" * 60)
    print("🎯 REFERENCE EXTRACTION SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 OneNote references extracted successfully!")
        print("✅ References from structured OneNote content")
        print("✅ Added to individual and master CSV files")
        print("✅ Available for Q&A citation system")
        print("\nNext steps:")
        print("1. Check references tab in UI to see extracted references")
        print("2. Test Q&A system with OneNote-related questions")
        print("3. Verify proper citation formatting in answers")
    else:
        print("❌ Reference extraction encountered issues")
        print("Check the logs above for specific problems")
        print("\nPossible issues:")
        print("- Reference extraction patterns not matching OneNote format")
        print("- LLM-based extraction failing on structured content")
        print("- File processing or CSV writing errors")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
