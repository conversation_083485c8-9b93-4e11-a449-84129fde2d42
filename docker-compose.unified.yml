version: '3.8'

# Unified Docker Compose for Graphiti with MCP Implementation
# Single source of truth for all Graphiti deployments
# Prioritizes MCP implementation with fallback to traditional UI

services:
  # FalkorDB - Primary graph database for all Graphiti applications
  falkordb:
    image: falkordb/falkordb:latest
    container_name: graphiti-falkordb
    ports:
      - "6379:6379"  # Redis/FalkorDB port (standard)
      - "7688:7687"  # BOLT protocol port (unusual external port)
    volumes:
      - falkordb_data:/data
    environment:
      - FALKORDB_PASSWORD=${FALKORDB_PASSWORD:-Triathlon16!}
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - graphiti-network
    restart: unless-stopped

  # Redis Stack - Vector search and embeddings storage
  redis-stack:
    image: redis/redis-stack:latest
    container_name: graphiti-redis-stack
    ports:
      - "6380:6379"  # Redis port (unusual external port)
      - "8001:8001"  # RedisInsight port
    volumes:
      - redis_stack_data:/data
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-Triathlon16!}
    command: redis-stack-server --requirepass "${REDIS_PASSWORD:-Triathlon16!}" --protected-mode no
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "6379", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - graphiti-network
    restart: unless-stopped

  # Neo4j - MCP server database
  neo4j:
    image: neo4j:5.26.0
    container_name: graphiti-neo4j
    ports:
      - "7474:7474"  # HTTP interface
      - "7687:7687"  # Bolt protocol (standard)
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
    environment:
      - NEO4J_AUTH=neo4j/${NEO4J_PASSWORD:-Triathlon16!}
      - NEO4J_server_memory_heap_initial__size=512m
      - NEO4J_server_memory_heap_max__size=1G
      - NEO4J_server_memory_pagecache_size=512m
      - NEO4J_PLUGINS=["apoc"]
    healthcheck:
      test: ["CMD", "wget", "-O", "/dev/null", "http://localhost:7474"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - graphiti-network
    restart: unless-stopped

  # Main Graphiti UI Server (Traditional FastAPI)
  graphiti-ui:
    build:
      context: .
      dockerfile_inline: |
        FROM python:3.12-slim
        WORKDIR /app
        COPY requirements.txt .
        RUN pip install --no-cache-dir -r requirements.txt
        COPY . .
        EXPOSE 9753
        CMD ["python", "app.py"]
    container_name: graphiti-ui
    ports:
      - "9753:9753"  # Main UI port (unusual)
    volumes:
      - ./uploads:/app/uploads
      - ./documents:/app/documents
      - ./references:/app/references
      - ./processed:/app/processed
    environment:
      # Database connections
      - FALKORDB_HOST=falkordb
      - FALKORDB_PORT=6379
      - FALKORDB_PASSWORD=${FALKORDB_PASSWORD:-Triathlon16!}
      - REDIS_VECTOR_SEARCH_HOST=redis-stack
      - REDIS_VECTOR_SEARCH_PORT=6379
      - REDIS_VECTOR_SEARCH_PASSWORD=${REDIS_PASSWORD:-Triathlon16!}

      # API keys (standardized) - OpenAI removed from embeddings
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}
      - MISTRAL_API_KEY=${MISTRAL_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}

      # LLM configuration (standardized)
      - QA_LLM_MODEL=${QA_LLM_MODEL:-meta-llama/llama-4-maverick}
      - QA_LLM_TEMPERATURE=${QA_LLM_TEMPERATURE:-0.7}
      - QA_LLM_TOP_P=${QA_LLM_TOP_P:-0.9}

      # Embedding configuration (standardized) - Only Ollama
      - EMBEDDING_PROVIDER=ollama
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-snowflake-arctic-embed2}
      - EMBEDDING_DIMENSIONS=${EMBEDDING_DIMENSIONS:-1024}
      - USE_LOCAL_EMBEDDINGS=true
      - OLLAMA_HOST=ollama
      - OLLAMA_PORT=11434

      # Entity extraction configuration
      - ENTITY_EXTRACTION_PROVIDER=openrouter
      - ENTITY_EXTRACTION_MODEL=${ENTITY_EXTRACTION_MODEL:-meta-llama/llama-4-maverick}

      # OCR configuration
      - USE_MISTRAL_OCR=true

      # Application settings
      - HOST=0.0.0.0
      - PORT=9753
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import urllib.request; urllib.request.urlopen('http://localhost:9753/health')",
        ]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 60s
    depends_on:
      falkordb:
        condition: service_healthy
      redis-stack:
        condition: service_healthy
      ollama:
        condition: service_healthy
    networks:
      - graphiti-network
    restart: unless-stopped

  # Graphiti MCP Server (PRIORITY - Modern MCP Implementation)
  # NOTE: Currently uses graphiti_core which may require Neo4j
  # TODO: Adapt to use FalkorDB or create FalkorDB-compatible MCP server
  graphiti-mcp:
    build:
      context: ./mcp_server
      dockerfile: Dockerfile
    container_name: graphiti-mcp
    ports:
      - "8000:8000"  # MCP server port (SSE transport)
    volumes:
      - ./mcp_server/.env:/app/.env:ro
    environment:
      # Neo4j connection (for graphiti_core compatibility)
      - NEO4J_URI=bolt://neo4j:7687
      - NEO4J_USER=neo4j
      - NEO4J_PASSWORD=${NEO4J_PASSWORD:-Triathlon16!}

      # API keys (standardized) - OpenAI removed from embeddings
      - OPENROUTER_API_KEY=${OPENROUTER_API_KEY}

      # LLM configuration (standardized)
      - MODEL_NAME=${MCP_MODEL_NAME:-meta-llama/llama-4-maverick}
      - LLM_TEMPERATURE=${MCP_LLM_TEMPERATURE:-0.0}

      # MCP configuration
      - MCP_TRANSPORT=${MCP_TRANSPORT:-sse}
      - MCP_HOST=${MCP_HOST:-0.0.0.0}
      - MCP_PORT=${MCP_PORT:-8000}

      # Path configuration
      - PATH=/root/.local/bin:${PATH}
    command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "${MCP_TRANSPORT:-sse}"]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      neo4j:
        condition: service_healthy
    networks:
      - graphiti-network
    restart: unless-stopped

  # Ollama - Local LLM and embedding server
  ollama:
    image: ollama/ollama:latest
    container_name: graphiti-ollama
    ports:
      - "11434:11434"  # Ollama API port
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s
    networks:
      - graphiti-network
    restart: unless-stopped

networks:
  graphiti-network:
    driver: bridge
    name: graphiti-network

volumes:
  falkordb_data:
    name: graphiti_falkordb_data
  redis_stack_data:
    name: graphiti_redis_stack_data
  neo4j_data:
    name: graphiti_neo4j_data
  neo4j_logs:
    name: graphiti_neo4j_logs
  ollama_data:
    name: graphiti_ollama_data
