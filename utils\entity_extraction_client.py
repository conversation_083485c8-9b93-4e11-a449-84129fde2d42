"""
Entity extraction client that uses OpenRouter with meta-llama/llama-4-maverick and falls back to OpenAI
"""

import os
import json
import logging
import asyncio
import re
from typing import List, Dict

from open_router_client import OpenRouterClient
import openai

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Entity types we want to extract
ENTITY_TYPES = [
    "Person", "Organization", "Location", "Disease", "Symptom", "Treatment",
    "Medication", "Herb", "Nutrient", "Food", "Process", "Concept",
    "Research", "Application", "Preservative", "Artificial Sweetener", "Complex"
]

class EntityExtractionClient:
    """Client for extracting entities from text using various LLM providers"""

    def __init__(self):
        """Initialize the EntityExtractionClient with OpenRouter and OpenAI clients"""
        # Get API keys from environment
        self.open_router_api_key = os.environ.get('OPEN_ROUTER_API_KEY')
        self.open_router_model = os.environ.get('ENTITY_EXTRACTION_MODEL', 'meta-llama/llama-4-maverick')
        self.openai_api_key = os.environ.get('OPENAI_API_KEY')
        self.openai_model = os.environ.get('OPENAI_ENTITY_MODEL', 'gpt-4.1-mini')

        # Initialize OpenRouter client
        if self.open_router_api_key:
            try:
                self.open_router_client = OpenRouterClient(api_key=self.open_router_api_key, model=self.open_router_model)
                logger.info(f"Initialized OpenRouter client with model: {self.open_router_model}")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter client: {e}")
                self.open_router_client = None
        else:
            logger.warning("OpenRouter API key not found, will use OpenAI as fallback")
            self.open_router_client = None

        # Initialize OpenAI client as fallback
        if self.openai_api_key:
            try:
                self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
                logger.info(f"Initialized OpenAI client with model: {self.openai_model}")
            except Exception as e:
                logger.error(f"Failed to initialize OpenAI client: {e}")
                self.openai_client = None
        else:
            logger.warning("OpenAI API key not found")
            self.openai_client = None

    async def extract_entities(self, text: str, max_retries: int = 3) -> List[Dict[str, str]]:
        """
        Extract entities from text using OpenRouter or OpenAI

        Args:
            text: Text to extract entities from
            max_retries: Maximum number of retries

        Returns:
            List of entity dictionaries with name, type, and description
        """
        logger.info(f"Extracting entities from text ({len(text)} characters)")

        # Create a system prompt that specifies the entity types we want to extract
        system_prompt = f"""
        Extract entities from the following text. Focus on these entity types:
        {', '.join(ENTITY_TYPES)}

        For each entity, provide:
        1. name: The entity name (normalize to a canonical form)
        2. type: One of the entity types listed above
        3. description: A brief description of the entity based on the text

        Return a JSON object with an "entities" array containing the extracted entities.
        If no entities are found, return an empty array.
        """

        # Try OpenRouter first if available
        if self.open_router_client:
            for retry in range(max_retries):
                try:
                    logger.info(f"Using OpenRouter with model {self.open_router_model} for entity extraction (attempt {retry+1}/{max_retries})")

                    response = await self.open_router_client.generate_completion_async(
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": text}
                        ],
                        temperature=0.3,
                        max_tokens=2000
                    )

                    # Extract content from response
                    if 'choices' in response and len(response['choices']) > 0:
                        if 'message' in response['choices'][0] and 'content' in response['choices'][0]['message']:
                            content = response['choices'][0]['message']['content']

                            # Parse JSON response
                            try:
                                # First, check if the content contains explanatory text before or after the JSON
                                # Look for common patterns in LLM responses
                                json_content = content

                                # Check if the response is wrapped in markdown code blocks
                                json_block_match = re.search(r'```(?:json)?(.*?)```', content, re.DOTALL)
                                if json_block_match:
                                    # Extract just the JSON part from the code block
                                    json_content = json_block_match.group(1).strip()
                                    logger.info("Found JSON in code block, extracting...")

                                # If there's still no valid JSON, try to find a JSON object pattern
                                if not json_content or not json_content.strip().startswith('{'):
                                    json_obj_match = re.search(r'(\{.*\})', content, re.DOTALL)
                                    if json_obj_match:
                                        json_content = json_obj_match.group(1).strip()
                                        logger.info("Found JSON object pattern, extracting...")

                                # Try to parse the extracted JSON
                                try:
                                    data = json.loads(json_content)
                                    entities = data.get("entities", [])
                                    logger.info(f"Extracted {len(entities)} entities using OpenRouter")
                                    return entities
                                except json.JSONDecodeError:
                                    # If that fails, try a more aggressive approach to find any JSON-like structure
                                    logger.info("Initial JSON parsing failed, trying more aggressive extraction...")

                                    # Look for an array of entities
                                    entities_match = re.search(r'"entities"\s*:\s*(\[.*?\])', content, re.DOTALL)
                                    if entities_match:
                                        try:
                                            entities_json = f'{{"entities": {entities_match.group(1)}}}'
                                            data = json.loads(entities_json)
                                            entities = data.get("entities", [])
                                            logger.info(f"Extracted {len(entities)} entities using regex pattern")
                                            return entities
                                        except json.JSONDecodeError:
                                            # Continue to next approach
                                            pass

                                    # If all else fails, try to parse the original content
                                    data = json.loads(content)
                                    entities = data.get("entities", [])
                                    logger.info(f"Extracted {len(entities)} entities using original content")
                                    return entities

                            except json.JSONDecodeError as e:
                                logger.error(f"Error parsing JSON response from OpenRouter: {e}")
                                logger.debug(f"Response content: {content[:200]}...")  # Log just the beginning to avoid huge logs
                                # Continue to next retry or fallback

                except Exception as e:
                    logger.error(f"Error using OpenRouter for entity extraction: {e}")
                    # Continue to next retry or fallback

                # Wait before retrying
                if retry < max_retries - 1:
                    await asyncio.sleep(1)

        # Fallback to OpenAI if OpenRouter failed or is not available
        if self.openai_client:
            for retry in range(max_retries):
                try:
                    logger.info(f"Using OpenAI with model {self.openai_model} for entity extraction (attempt {retry+1}/{max_retries})")

                    response = self.openai_client.chat.completions.create(
                        model=self.openai_model,
                        messages=[
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": text}
                        ],
                        temperature=0.3,
                        max_tokens=2000,
                        response_format={"type": "json_object"}
                    )

                    # Extract content from response
                    content = response.choices[0].message.content

                    # Parse JSON response using the same improved extraction logic
                    try:
                        # First, check if the content contains explanatory text before or after the JSON
                        json_content = content

                        # Check if the response is wrapped in markdown code blocks
                        json_block_match = re.search(r'```(?:json)?(.*?)```', content, re.DOTALL)
                        if json_block_match:
                            # Extract just the JSON part from the code block
                            json_content = json_block_match.group(1).strip()
                            logger.info("Found JSON in code block from OpenAI, extracting...")

                        # If there's still no valid JSON, try to find a JSON object pattern
                        if not json_content or not json_content.strip().startswith('{'):
                            json_obj_match = re.search(r'(\{.*\})', content, re.DOTALL)
                            if json_obj_match:
                                json_content = json_obj_match.group(1).strip()
                                logger.info("Found JSON object pattern from OpenAI, extracting...")

                        # Try to parse the extracted JSON
                        try:
                            data = json.loads(json_content)
                            entities = data.get("entities", [])
                            logger.info(f"Extracted {len(entities)} entities using OpenAI")
                            return entities
                        except json.JSONDecodeError:
                            # If that fails, try a more aggressive approach to find any JSON-like structure
                            logger.info("Initial JSON parsing failed for OpenAI, trying more aggressive extraction...")

                            # Look for an array of entities
                            entities_match = re.search(r'"entities"\s*:\s*(\[.*?\])', content, re.DOTALL)
                            if entities_match:
                                try:
                                    entities_json = f'{{"entities": {entities_match.group(1)}}}'
                                    data = json.loads(entities_json)
                                    entities = data.get("entities", [])
                                    logger.info(f"Extracted {len(entities)} entities from OpenAI using regex pattern")
                                    return entities
                                except json.JSONDecodeError:
                                    # Continue to next approach
                                    pass

                            # If all else fails, try to parse the original content
                            data = json.loads(content)
                            entities = data.get("entities", [])
                            logger.info(f"Extracted {len(entities)} entities from OpenAI using original content")
                            return entities

                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON response from OpenAI: {e}")
                        logger.debug(f"Response content: {content[:200]}...")  # Log just the beginning to avoid huge logs
                        # Continue to next retry

                except Exception as e:
                    logger.error(f"Error using OpenAI for entity extraction: {e}")
                    # Continue to next retry

                # Wait before retrying
                if retry < max_retries - 1:
                    await asyncio.sleep(1)

        # If all methods failed, return empty list
        logger.error("All entity extraction methods failed")
        return []
