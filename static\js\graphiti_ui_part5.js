/**
 * Graphiti UI Part 5 - Additional functionality for the Graphiti Knowledge Graph UI
 * 
 * This file contains functions for the Settings tab and Enhancements tab.
 */

/**
 * Search references by keyword
 */
function searchReferences() {
    // Get search query
    const searchQuery = document.getElementById('reference-search-input')?.value || '';
    
    if (!searchQuery) {
        // If no search query, load all references
        loadReferences();
        return;
    }
    
    // Show loading spinner
    const loadingSpinner = document.getElementById('references-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }
    
    // Build URL
    let url = `/api/references?limit=20&search=${encodeURIComponent(searchQuery)}`;
    
    // Fetch references
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Render references
            renderReferences(data);
            
            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error searching references:', error);
            
            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
            
            // Show error message
            const referencesList = document.getElementById('references-list');
            if (referencesList) {
                referencesList.innerHTML = `<div class="alert alert-danger">Error searching references: ${error.message}</div>`;
            }
        });
}

/**
 * Export references as CSV
 */
function exportReferencesCSV() {
    // Get document ID
    const documentId = document.getElementById('document-selector')?.value || '';
    
    // Build URL
    let url = '/api/references-csv';
    if (documentId) {
        url += `?document_id=${documentId}`;
    }
    
    // Open URL in new tab
    window.open(url, '_blank');
}

/**
 * Visualize references
 */
function visualizeReferences() {
    // Show visualization container
    const visualizationContainer = document.getElementById('reference-visualization');
    if (visualizationContainer) {
        visualizationContainer.style.display = 'block';
    }
    
    // Get document ID
    const documentId = document.getElementById('document-selector')?.value || '';
    
    // Build URL
    let url = '/api/references-visualization';
    if (documentId) {
        url += `?document_id=${documentId}`;
    }
    
    // Fetch visualization data
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Render visualization
            renderReferenceVisualization(data);
        })
        .catch(error => {
            console.error('Error visualizing references:', error);
            
            // Show error message
            if (visualizationContainer) {
                visualizationContainer.innerHTML = `<div class="alert alert-danger">Error visualizing references: ${error.message}</div>`;
            }
        });
}

/**
 * Render reference visualization
 * 
 * @param {Object} data - Visualization data from the API
 */
function renderReferenceVisualization(data) {
    const visualizationContainer = document.getElementById('reference-visualization');
    if (!visualizationContainer) {
        console.error("Visualization container element not found");
        return;
    }
    
    // Clear existing content
    visualizationContainer.innerHTML = '';
    
    // Create visualization placeholder
    const visualization = document.createElement('div');
    visualization.className = 'reference-visualization-placeholder';
    visualization.style.height = '400px';
    visualization.style.border = '1px solid #ccc';
    visualization.style.padding = '20px';
    visualization.style.display = 'flex';
    visualization.style.alignItems = 'center';
    visualization.style.justifyContent = 'center';
    
    // Add placeholder text
    const placeholderText = document.createElement('p');
    placeholderText.textContent = 'Reference Visualization Placeholder';
    visualization.appendChild(placeholderText);
    
    visualizationContainer.appendChild(visualization);
}

/**
 * Initialize the Enhancements tab
 */
function initializeEnhancementsTab() {
    console.log("Initializing Enhancements tab");
    
    // Set up event listeners
    setupEnhancementEventListeners();
}

/**
 * Set up event listeners for the Enhancements tab
 */
function setupEnhancementEventListeners() {
    // Add event listener for deduplicate button
    const deduplicateButton = document.getElementById('deduplicate-button');
    if (deduplicateButton) {
        deduplicateButton.addEventListener('click', function() {
            runDeduplication();
        });
    }
    
    // Add event listener for build network button
    const buildNetworkButton = document.getElementById('build-network-button');
    if (buildNetworkButton) {
        buildNetworkButton.addEventListener('click', function() {
            buildCitationNetwork();
        });
    }
    
    // Add event listener for enrich references button
    const enrichReferencesButton = document.getElementById('enrich-references-button');
    if (enrichReferencesButton) {
        enrichReferencesButton.addEventListener('click', function() {
            enrichReferences();
        });
    }
    
    // Add event listener for run all enhancements button
    const runAllEnhancementsButton = document.getElementById('run-all-enhancements-button');
    if (runAllEnhancementsButton) {
        runAllEnhancementsButton.addEventListener('click', function() {
            runAllEnhancements();
        });
    }
}

/**
 * Run reference deduplication
 */
function runDeduplication() {
    // Show progress
    const progressContainer = document.getElementById('enhancements-progress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        
        const progressMessage = progressContainer.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = 'Running deduplication...';
        }
        
        const progressBar = progressContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
        }
    }
    
    // Clear previous results
    const resultsContainer = document.getElementById('enhancements-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // Run deduplication
    fetch('/api/references/deduplicate', {
        method: 'POST'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show results
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="alert alert-success">
                    <h5>Deduplication Complete</h5>
                    <p>Found ${data.duplicate_groups} duplicate groups with ${data.total_duplicates} total duplicates.</p>
                </div>
            `;
        }
        
        // Show duplicate groups
        showDuplicateGroups(data.groups);
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    })
    .catch(error => {
        console.error('Error running deduplication:', error);
        
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show error message
        if (resultsContainer) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Error running deduplication: ${error.message}</div>`;
        }
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}

/**
 * Show duplicate groups
 * 
 * @param {Array} groups - Duplicate groups
 */
function showDuplicateGroups(groups) {
    const duplicateGroupsList = document.getElementById('duplicate-groups-list');
    if (!duplicateGroupsList) {
        console.error("Duplicate groups list element not found");
        return;
    }
    
    // Clear existing content
    duplicateGroupsList.innerHTML = '';
    
    // If no groups, show message
    if (!groups || groups.length === 0) {
        duplicateGroupsList.innerHTML = '<div class="alert alert-info">No duplicate groups found.</div>';
        return;
    }
    
    // Create groups container
    const groupsContainer = document.createElement('div');
    groupsContainer.className = 'mt-4';
    
    // Add heading
    const heading = document.createElement('h4');
    heading.textContent = 'Duplicate Groups';
    groupsContainer.appendChild(heading);
    
    // Add groups
    groups.forEach((group, index) => {
        const groupCard = document.createElement('div');
        groupCard.className = 'card mb-3';
        
        const cardHeader = document.createElement('div');
        cardHeader.className = 'card-header';
        cardHeader.textContent = `Group ${index + 1} (${group.references.length} duplicates)`;
        groupCard.appendChild(cardHeader);
        
        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';
        
        // Add references table
        const table = document.createElement('table');
        table.className = 'table table-sm';
        
        const thead = document.createElement('thead');
        thead.innerHTML = `
            <tr>
                <th>Title</th>
                <th>Authors</th>
                <th>Year</th>
                <th>Document</th>
            </tr>
        `;
        table.appendChild(thead);
        
        const tbody = document.createElement('tbody');
        
        // Add reference rows
        group.references.forEach(ref => {
            const row = document.createElement('tr');
            
            row.innerHTML = `
                <td>${ref.title || 'Unknown'}</td>
                <td>${ref.authors?.join(', ') || 'Unknown'}</td>
                <td>${ref.year || 'Unknown'}</td>
                <td>${ref.document_id ? `<a href="/documents/${ref.document_id}">${ref.document_title || 'View'}</a>` : 'None'}</td>
            `;
            
            tbody.appendChild(row);
        });
        
        table.appendChild(tbody);
        cardBody.appendChild(table);
        groupCard.appendChild(cardBody);
        
        groupsContainer.appendChild(groupCard);
    });
    
    duplicateGroupsList.appendChild(groupsContainer);
}
