"""
Main application file for the Graphiti application.

This is the entry point for the Graphiti Knowledge Graph application.
It sets up the FastAPI application, includes all routes, and configures middleware.
"""

import os
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi

from utils.config import STATIC_DIR, TEMPLATES_DIR, HOST, PORT
from utils.logging_utils import get_logger

# Import routes
from routes.document_routes import router as document_router
from routes.enhanced_document_routes import router as enhanced_document_router
from routes.reference_routes import router as reference_router
from routes.settings_routes import router as settings_router
from routes.entity_routes import router as entity_router
from routes.knowledge_graph_routes import router as knowledge_graph_router
from routes.search_routes import router as search_router
from routes.qa_routes import router as qa_router
from routes.semantic_search_routes import router as semantic_search_router
from routes.worker_routes import router as worker_router
from routes.system_routes import router as system_router
from routes.fast_api_routes import router as fast_api_router
from routes.websocket_routes import router as websocket_router

# Set up logger
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Graphiti Knowledge Graph",
    description="A comprehensive knowledge graph application that processes scientific documents into a structured, queryable graph of entities and relationships.",
    version="1.0.0",
    docs_url=None,  # Disable default docs
    redoc_url=None  # Disable default redoc
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
app.mount("/static", StaticFiles(directory=STATIC_DIR), name="static")

# Mount CSS files for backward compatibility
app.mount("/css", StaticFiles(directory=STATIC_DIR / "css"), name="css")

# Mount JS files for backward compatibility
app.mount("/js", StaticFiles(directory=STATIC_DIR / "js"), name="js")

# Set up templates
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# Include routers
app.include_router(fast_api_router)  # Add fast routes first for priority
app.include_router(document_router)
app.include_router(enhanced_document_router, prefix="/api/enhanced")
app.include_router(reference_router)
app.include_router(settings_router)
app.include_router(entity_router)
app.include_router(knowledge_graph_router)
app.include_router(search_router)
app.include_router(qa_router)
app.include_router(semantic_search_router)
app.include_router(worker_router)
app.include_router(system_router)
app.include_router(websocket_router)  # WebSocket routes for real-time progress

@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """
    Render the index page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("dashboard_modern.html", {"request": request})

@app.get("/batch-upload", response_class=HTMLResponse)
async def batch_upload(request: Request):
    """
    Render the batch upload page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("batch_upload.html", {"request": request})

@app.get("/enhanced-upload", response_class=HTMLResponse)
async def enhanced_upload(request: Request):
    """
    Render the enhanced upload page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("enhanced_upload.html", {"request": request})

@app.get("/entities", response_class=HTMLResponse)
async def entities_page(request: Request):
    """
    Render the entities page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("entities_modern.html", {"request": request})

@app.get("/entity-detail", response_class=HTMLResponse)
async def entity_detail_page(request: Request):
    """
    Render the entity detail page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("entity_detail.html", {"request": request})

@app.get("/document-test", response_class=HTMLResponse)
async def document_test_page(request: Request):
    """
    Render the document test page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("document_test.html", {"request": request})



@app.get("/knowledge-graph", response_class=HTMLResponse)
async def knowledge_graph_page(request: Request):
    """
    Render the knowledge graph explorer page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("knowledge_graph_modern.html", {"request": request})

@app.get("/references", response_class=HTMLResponse)
async def references_page(request: Request):
    """
    Render the references page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("references_modern.html", {"request": request})



@app.get("/graph-search", response_class=HTMLResponse)
async def graph_search_page(request: Request):
    """
    Render the graph search page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("graph_search.html", {"request": request})

@app.get("/documents", response_class=HTMLResponse)
async def documents_page(request: Request):
    """
    Render the documents list page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("documents_modern.html", {"request": request})

@app.get("/document-search", response_class=HTMLResponse)
async def document_search_page(request: Request):
    """
    Render the document search page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("document_search.html", {"request": request})

@app.get("/qa", response_class=HTMLResponse)
async def qa_page(request: Request):
    """
    Render the Q&A page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("qa_modern.html", {"request": request})

@app.get("/search", response_class=HTMLResponse)
async def search_page(request: Request):
    """
    Render the search page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("search_modern.html", {"request": request})

@app.get("/references", response_class=HTMLResponse)
async def references_page(request: Request):
    """
    Render the references page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("references_modern.html", {"request": request})

@app.get("/settings", response_class=HTMLResponse)
async def settings_page(request: Request):
    """
    Render the settings page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("settings_modern.html", {"request": request})

@app.get("/help", response_class=HTMLResponse)
async def help_page(request: Request):
    """
    Render the help page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("help.html", {"request": request})

@app.get("/fast-test", response_class=HTMLResponse)
async def fast_test(request: Request):
    """
    Render the fast API test page.

    Args:
        request: Request object

    Returns:
        HTML response
    """
    return templates.TemplateResponse("fast_test.html", {"request": request})

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """
    Custom Swagger UI documentation.

    Returns:
        Swagger UI HTML
    """
    return get_swagger_ui_html(
        openapi_url="/openapi.json",
        title=app.title + " - API Documentation",
        swagger_js_url="/static/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui.css",
    )

@app.get("/redoc", include_in_schema=False)
async def redoc_html():
    """
    ReDoc documentation.

    Returns:
        ReDoc HTML
    """
    return get_redoc_html(
        openapi_url="/openapi.json",
        title=app.title + " - API Documentation",
        redoc_js_url="/static/redoc.standalone.js",
    )

@app.get("/openapi.json", include_in_schema=False)
async def get_open_api_endpoint():
    """
    OpenAPI schema endpoint.

    Returns:
        OpenAPI schema
    """
    return get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

@app.get("/health")
async def health_check():
    """
    Health check endpoint.

    Returns:
        Health status
    """
    return {"status": "ok"}

# Import error handling utilities
from utils.error_handling import GraphitiError, handle_exception

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """
    Global exception handler.

    Args:
        request: Request object
        exc: Exception

    Returns:
        JSON response with error details
    """
    http_exception = handle_exception(exc)
    return JSONResponse(
        status_code=http_exception.status_code,
        content={"detail": http_exception.detail}
    )

@app.exception_handler(GraphitiError)
async def graphiti_exception_handler(request: Request, exc: GraphitiError):
    """
    Graphiti exception handler.

    Args:
        request: Request object
        exc: GraphitiError

    Returns:
        JSON response with error details
    """
    return JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.to_dict()}
    )

if __name__ == "__main__":
    # Run the application
    print(f"🚀 Starting Graphiti Knowledge Graph Application")
    # Show user-friendly URLs
    display_host = "localhost" if HOST == "0.0.0.0" else HOST
    print(f"📍 Server: http://{display_host}:{PORT}")
    print(f"📊 Dashboard: http://{display_host}:{PORT}/")
    print(f"📚 API Docs: http://{display_host}:{PORT}/docs")
    print(f"🔧 Settings: http://{display_host}:{PORT}/settings")
    print("=" * 50)

    uvicorn.run(
        "app:app",
        host=HOST,
        port=PORT,
        reload=True
    )
