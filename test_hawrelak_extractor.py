#!/usr/bin/env python3
"""
Test the specialized Hawrelak reference extractor.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_hawrelak_extractor():
    """Test the Hawrelak-specific reference extractor."""
    
    # Check for Mistral API key
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    # Find the Hawrelak document
    uploads_dir = Path("uploads")
    hawrelak_files = list(uploads_dir.glob("*Hawrelak*"))
    if not hawrelak_files:
        print("❌ Hawrelak document not found")
        return
    
    test_file = hawrelak_files[0]
    print(f"📚 Testing Hawrelak-specific extractor on: {test_file.name}")
    
    try:
        # Import required modules
        from utils.mistral_ocr import MistralOCRProcessor
        from services.hawrelak_reference_extractor import HawrelakReferenceExtractor
        
        # Initialize processors
        print("🔧 Initializing Mistral OCR processor...")
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        
        print("🔧 Initializing Hawrelak reference extractor...")
        extractor = HawrelakReferenceExtractor(mistral_ocr)
        
        # Extract references
        print("🚀 Starting Hawrelak-specific reference extraction...")
        result = await extractor.extract_references(str(test_file))
        
        # Display results
        print("\n" + "="*80)
        print("📊 HAWRELAK-SPECIFIC REFERENCE EXTRACTION RESULTS")
        print("="*80)
        
        if result.get('success', False):
            print(f"✅ Success: {result['success']}")
            print(f"📄 Document: {result['filename']}")
            print(f"🔍 Method: {result['extraction_method']}")
            print(f"📚 Total References Found: {result['total_reference_count']}")
            print(f"📝 Text Length: {result.get('extracted_text_length', 0):,} characters")
            
            if result.get('csv_path'):
                print(f"💾 CSV Saved: {result['csv_path']}")
            
            # Show references
            references = result.get('references', [])
            if references:
                # Count by extraction method
                method_counts = {}
                for ref in references:
                    method = ref.get('extraction_method', 'unknown')
                    method_counts[method] = method_counts.get(method, 0) + 1
                
                print(f"\n📋 Extraction Method Breakdown:")
                for method, count in method_counts.items():
                    print(f"   {method}: {count} references")
                
                # Show first 15 references
                print(f"\n📋 First 15 References:")
                print("-" * 80)
                for i, ref in enumerate(references[:15], 1):
                    print(f"\n{i:2d}. Method: {ref.get('extraction_method', 'unknown')}")
                    ref_text = ref['text'][:200] + "..." if len(ref['text']) > 200 else ref['text']
                    print(f"    Text: {ref_text}")
                    if ref.get('metadata'):
                        metadata = ref['metadata']
                        details = []
                        if metadata.get('year'):
                            details.append(f"Year: {metadata['year']}")
                        if metadata.get('first_author'):
                            details.append(f"Author: {metadata['first_author']}")
                        if metadata.get('journal'):
                            details.append(f"Journal: {metadata['journal']}")
                        if metadata.get('doi'):
                            details.append(f"DOI: {metadata['doi']}")
                        if details:
                            print(f"    {' | '.join(details)}")
            
            print(f"\n🎯 TOTAL REFERENCES FOUND: {result['total_reference_count']}")
            
            # Compare with expected count
            expected_count = 100  # You mentioned ~100 references
            found_count = result['total_reference_count']
            
            print(f"\n📊 COMPARISON WITH EXPECTED COUNT:")
            print(f"   Expected: ~{expected_count} references")
            print(f"   Found: {found_count} references")
            print(f"   Percentage: {(found_count/expected_count)*100:.1f}%")
            
            if found_count >= expected_count * 0.95:
                print("🏆 EXCELLENT: Found 95%+ of expected references!")
            elif found_count >= expected_count * 0.85:
                print("✅ VERY GOOD: Found 85%+ of expected references!")
            elif found_count >= expected_count * 0.75:
                print("🟢 GOOD: Found 75%+ of expected references")
            elif found_count >= expected_count * 0.65:
                print("🟡 FAIR: Found 65%+ of expected references")
            else:
                print("🔴 NEEDS IMPROVEMENT: Found fewer references than expected")
            
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")
        
        print("="*80)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def compare_all_hawrelak_methods():
    """Compare all extraction methods on the Hawrelak document."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")
    hawrelak_files = list(uploads_dir.glob("*Hawrelak*"))
    if not hawrelak_files:
        print("❌ Hawrelak document not found")
        return
    
    test_file = hawrelak_files[0]
    print(f"🔄 Comparing all extraction methods on: {test_file.name}")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        from services.hawrelak_reference_extractor import HawrelakReferenceExtractor
        from services.improved_reference_extractor import ImprovedReferenceExtractor
        from services.presentation_reference_extractor import PresentationReferenceExtractor
        
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        
        # Test Hawrelak-specific method
        print("📚 Testing Hawrelak-specific method...")
        hawrelak_extractor = HawrelakReferenceExtractor(mistral_ocr)
        hawrelak_result = await hawrelak_extractor.extract_references(str(test_file))
        
        # Test improved method
        print("🚀 Testing improved method...")
        improved_extractor = ImprovedReferenceExtractor(mistral_ocr)
        improved_result = await improved_extractor.extract_references(str(test_file))
        
        # Test presentation method
        print("🎯 Testing presentation method...")
        presentation_extractor = PresentationReferenceExtractor(mistral_ocr)
        presentation_result = await presentation_extractor.extract_references(str(test_file))
        
        # Compare results
        print("\n" + "="*80)
        print("📊 COMPREHENSIVE HAWRELAK COMPARISON RESULTS")
        print("="*80)
        
        hawrelak_count = hawrelak_result.get('total_reference_count', 0)
        improved_count = improved_result.get('total_reference_count', 0)
        presentation_count = presentation_result.get('total_reference_count', 0)
        
        print(f"📚 Hawrelak-Specific Method: {hawrelak_count} references")
        print(f"🚀 Improved Method: {improved_count} references")
        print(f"🎯 Presentation Method: {presentation_count} references")
        
        best_count = max(hawrelak_count, improved_count, presentation_count)
        
        if hawrelak_count == best_count:
            print("🏆 WINNER: Hawrelak-specific method!")
        elif improved_count == best_count:
            print("🏆 WINNER: Improved method!")
        else:
            print("🏆 WINNER: Presentation method!")
        
        print(f"\n📈 Improvements over previous best (70):")
        print(f"   Hawrelak-specific: {hawrelak_count - 70:+d} references")
        print(f"   Improved: {improved_count - 70:+d} references")
        print(f"   Presentation: {presentation_count - 70:+d} references")
        
        expected = 100
        print(f"\n🎯 Progress toward 100 expected references:")
        print(f"   Hawrelak-specific: {hawrelak_count}/{expected} ({(hawrelak_count/expected)*100:.1f}%)")
        print(f"   Improved: {improved_count}/{expected} ({(improved_count/expected)*100:.1f}%)")
        print(f"   Presentation: {presentation_count}/{expected} ({(presentation_count/expected)*100:.1f}%)")
        
        print("="*80)
        
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("📚 Hawrelak-Specific Reference Extraction Test")
    print("="*60)
    
    # Run Hawrelak-specific test
    print("\n1️⃣ Testing Hawrelak-specific extraction...")
    asyncio.run(test_hawrelak_extractor())
    
    # Run comprehensive comparison
    print("\n2️⃣ Comparing all methods on Hawrelak document...")
    asyncio.run(compare_all_hawrelak_methods())
    
    print("\n✅ Testing complete!")
