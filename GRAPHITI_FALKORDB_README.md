# Graphiti with FalkorDB

This is a clean implementation of the Graphiti knowledge graph project using FalkorDB (RedisGraph) as the database backend.

## Overview

Graphiti is a knowledge graph system that processes PDF documents, extracts entities, and allows for semantic search and question answering based on the extracted information.

This implementation uses FalkorDB (a RedisGraph fork) as the graph database, providing a clean, focused solution without any Neo4j dependencies.

## Components

1. **GraphitiFalkorDBAdapter**: A clean adapter for interacting with FalkorDB
2. **PDF Processor**: Processes PDF documents and adds them to the knowledge graph
3. **Entity Extraction**: Extracts entities from text using OpenAI
4. **Web Interface**: A FastAPI web interface for interacting with the knowledge graph

## Setup

### Prerequisites

1. FalkorDB (Redis with the FalkorDB module)
2. Python 3.8+
3. Required Python packages (see requirements.txt)

### Installation

1. Install FalkorDB:
   ```
   docker run -p 6379:6379 falkordb/falkordb:latest
   ```

2. Install Python dependencies:
   ```
   pip install -r requirements.txt
   ```

3. Set up the FalkorDB environment:
   ```
   python setup_falkordb.py
   ```

4. Configure the `.env` file with your API keys and settings.

## Usage

### Processing PDFs

To process a PDF document:

```
python graphiti_pdf_processor.py path/to/your/document.pdf
```

To process all PDFs in a directory:

```
python graphiti_pdf_processor.py path/to/your/directory
```

### Entity Extraction

To extract entities from facts without entities:

```
python graphiti_entity_extraction.py extract-entities --batch-size 10
```

To extract entities for a specific document:

```
python graphiti_entity_extraction.py extract-document <document_uuid>
```

To get entity statistics:

```
python graphiti_entity_extraction.py stats
```

### Web Interface

To start the web interface:

```
python graphiti_web_interface.py
```

Then open your browser and navigate to http://localhost:8023

## API Endpoints

The web interface provides the following API endpoints:

- `/api/semantic`: Semantic search
- `/api/hybrid`: Hybrid search
- `/api/documents`: Get all documents
- `/api/entities`: Get all entities
- `/api/entities/{uuid}`: Get entity details
- `/api/answer`: Answer questions using the knowledge graph
- `/api/upload`: Upload and process a PDF file

## Migrating from Neo4j

If you're migrating from Neo4j to FalkorDB, follow these steps:

1. Set up the FalkorDB environment:
   ```
   python setup_falkordb.py
   ```

2. Process your PDF documents with the FalkorDB processor:
   ```
   python graphiti_pdf_processor.py uploads
   ```

3. Use the clean FalkorDB web interface:
   ```
   python graphiti_web_interface.py
   ```

## Troubleshooting

If you encounter issues:

1. Check that FalkorDB is running:
   ```
   redis-cli ping
   ```

2. Check that the FalkorDB module is loaded:
   ```
   redis-cli module list
   ```

3. Check the logs for error messages.

4. Make sure your `.env` file is configured correctly.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
