#!/usr/bin/env python3
"""
Examine the actual text content of the pain relief document to understand reference format.
"""

import asyncio
import os
import sys
import re
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def examine_pain_relief_document():
    """Examine the pain relief document text in detail."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if not pain_relief_files:
        print("❌ Pain relief document not found")
        return
    
    test_file = pain_relief_files[0]
    print(f"🔍 Examining: {test_file.name}")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Extract text with Mistral OCR
        print("\n1️⃣ Extracting text with Mistral OCR...")
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        text = await mistral_ocr.extract_text_from_pdf(str(test_file))
        
        if not text:
            print("❌ No text extracted!")
            return
        
        print(f"✅ Extracted {len(text):,} characters")
        
        # Save full text to file for examination
        text_file = Path("pain_relief_full_text.txt")
        with open(text_file, 'w', encoding='utf-8') as f:
            f.write(text)
        print(f"💾 Saved full text to: {text_file}")
        
        # Look for potential reference patterns
        print("\n2️⃣ Looking for potential reference patterns...")
        
        # Split text into lines for analysis
        lines = text.split('\n')
        
        # Look for lines that might be references
        potential_refs = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if len(line) < 20:  # Skip short lines
                continue
                
            # Check for various reference indicators
            indicators = [
                r'\d{4}',  # Year
                r'[Jj]ournal',  # Journal
                r'[Pp]roc',  # Proceedings
                r'[Vv]ol',  # Volume
                r'pp?\.',  # Pages
                r'doi:',  # DOI
                r'PMID:',  # PubMed
                r'http',  # URL
                r'[A-Z][a-z]+,\s*[A-Z]\.',  # Author pattern
                r'et\s+al',  # Et al
                r'[Pp]ublished',  # Published
                r'[Aa]rticle',  # Article
                r'[Rr]eview',  # Review
                r'[Ss]tudy',  # Study
                r'[Rr]esearch',  # Research
            ]
            
            indicator_count = sum(1 for pattern in indicators if re.search(pattern, line))
            
            if indicator_count >= 2:  # Must have at least 2 indicators
                potential_refs.append((i+1, line, indicator_count))
        
        print(f"📚 Found {len(potential_refs)} potential reference lines")
        
        # Show first 20 potential references
        print("\n3️⃣ First 20 potential references:")
        print("-" * 80)
        for i, (line_num, line, score) in enumerate(potential_refs[:20]):
            print(f"{i+1:2d}. Line {line_num:4d} (Score: {score}): {line[:100]}{'...' if len(line) > 100 else ''}")
        
        # Look for numbered lists
        print("\n4️⃣ Looking for numbered lists...")
        numbered_lines = []
        for i, line in enumerate(lines):
            line = line.strip()
            # Look for lines starting with numbers
            if re.match(r'^\d+\.?\s+', line) and len(line) > 20:
                numbered_lines.append((i+1, line))
        
        print(f"🔢 Found {len(numbered_lines)} numbered lines")
        for i, (line_num, line) in enumerate(numbered_lines[:10]):
            print(f"{i+1:2d}. Line {line_num:4d}: {line[:100]}{'...' if len(line) > 100 else ''}")
        
        # Look for specific sections
        print("\n5️⃣ Looking for reference sections...")
        ref_keywords = ['reference', 'bibliography', 'citation', 'literature', 'source']
        
        for keyword in ref_keywords:
            pattern = rf'(?i){keyword}'
            matches = []
            for i, line in enumerate(lines):
                if re.search(pattern, line.strip()):
                    matches.append((i+1, line.strip()))
            
            if matches:
                print(f"\n'{keyword.upper()}' found in {len(matches)} lines:")
                for line_num, line in matches[:5]:
                    print(f"  Line {line_num:4d}: {line[:80]}{'...' if len(line) > 80 else ''}")
        
        # Look for parenthetical citations
        print("\n6️⃣ Looking for parenthetical citations...")
        citation_patterns = [
            r'\([^)]*\d{4}[^)]*\)',  # (Author, 2020)
            r'\([^)]*et\s+al[^)]*\)',  # (Smith et al.)
            r'\(\d+\)',  # (1), (2), etc.
            r'\[\d+\]',  # [1], [2], etc.
        ]
        
        all_citations = []
        for pattern in citation_patterns:
            matches = re.findall(pattern, text)
            all_citations.extend(matches)
        
        print(f"📝 Found {len(all_citations)} potential citations")
        unique_citations = list(set(all_citations))[:20]
        for citation in unique_citations:
            print(f"  {citation}")
        
        # Look for specific text around citations
        print("\n7️⃣ Looking for text around citations...")
        citation_contexts = []
        for match in re.finditer(r'\(\d+\)', text):
            start = max(0, match.start() - 100)
            end = min(len(text), match.end() + 100)
            context = text[start:end].replace('\n', ' ')
            citation_contexts.append(context)
        
        print(f"📍 Found {len(citation_contexts)} citation contexts")
        for i, context in enumerate(citation_contexts[:5]):
            print(f"{i+1}. ...{context}...")
        
        print("\n" + "="*80)
        print("📊 SUMMARY")
        print("="*80)
        print(f"📄 Document: {test_file.name}")
        print(f"📝 Total text length: {len(text):,} characters")
        print(f"📋 Total lines: {len(lines):,}")
        print(f"📚 Potential reference lines: {len(potential_refs)}")
        print(f"🔢 Numbered lines: {len(numbered_lines)}")
        print(f"📝 Citations found: {len(all_citations)}")
        print(f"💾 Full text saved to: {text_file}")
        
        # Suggest improvements
        print("\n💡 SUGGESTIONS FOR IMPROVEMENT:")
        if len(potential_refs) > 10:
            print("✅ Document contains many potential references")
            print("🔧 Consider adjusting patterns to match this document's format")
        else:
            print("⚠️ Few potential references found")
            print("🔧 Document may use non-standard reference format")
        
        if len(numbered_lines) > 10:
            print("✅ Document has numbered lists - could be references")
        
        if len(all_citations) > 10:
            print("✅ Document has in-text citations")
            print("🔧 Try to match citations with reference list")
        
    except Exception as e:
        print(f"❌ Error during examination: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Pain Relief Document Text Examiner")
    print("="*50)
    asyncio.run(examine_pain_relief_document())
