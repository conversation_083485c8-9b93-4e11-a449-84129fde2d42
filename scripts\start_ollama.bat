@echo off
REM Batch script to start Ollama automatically
REM Place this in your Windows Startup folder

echo 🚀 Starting Ollama AI Service...

REM Wait a bit for system to fully boot
timeout /t 10 /nobreak >nul

REM Check if Ollama is already running
tasklist /FI "IMAGENAME eq ollama.exe" 2>NUL | find /I /N "ollama.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo ✅ Ollama is already running
    goto :end
)

REM Start Ollama in background
echo 🔧 Starting Ollama serve...
start /B ollama serve

REM Wait for Ollama to start
timeout /t 5 /nobreak >nul

REM Verify Ollama is running
echo 🔍 Checking Ollama status...
curl -s http://localhost:11434/api/version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama started successfully!
) else (
    echo ⚠️ Ollama may still be starting up...
)

:end
echo 🎯 Ollama startup script completed
REM Keep window open for 3 seconds to see results
timeout /t 3 /nobreak >nul
