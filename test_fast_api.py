#!/usr/bin/env python3
"""
Test the fast API endpoints directly
"""

import requests
import time

def test_fast_api():
    print("🚀 Testing Fast API Endpoints")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:9754"
    
    # Test endpoints with short timeout
    endpoints = [
        ("/api/fast/health", "Health Check"),
        ("/api/fast/graph-stats", "Graph Stats"),
        ("/api/fast/entities?limit=5", "Entities (5)"),
        ("/api/fast/documents?limit=5", "Documents (5)"),
        ("/api/fast/references", "References")
    ]
    
    for endpoint, name in endpoints:
        try:
            print(f"\n🔍 Testing {name}...")
            start_time = time.time()
            
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {name}: Success ({response_time:.2f}s)")
                
                # Show key data
                if 'total_entities' in data:
                    print(f"   Entities: {data.get('total_entities', 0)}")
                    print(f"   Documents: {data.get('total_episodes', 0)}")
                elif 'entities' in data:
                    print(f"   Found {len(data['entities'])} entities")
                    if data['entities']:
                        print(f"   Sample: {data['entities'][0].get('name', 'Unknown')}")
                elif 'documents' in data:
                    print(f"   Found {len(data['documents'])} documents")
                    if data['documents']:
                        print(f"   Sample: {data['documents'][0].get('name', 'Unknown')}")
                elif 'count' in data:
                    print(f"   Count: {data.get('count', 0)}")
                elif 'status' in data:
                    print(f"   Status: {data.get('status', 'unknown')}")
                    
            else:
                print(f"❌ {name}: HTTP {response.status_code}")
                print(f"   Response: {response.text[:200]}...")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {name}: Timeout (>10s)")
        except requests.exceptions.ConnectionError:
            print(f"🔌 {name}: Connection refused")
        except Exception as e:
            print(f"❌ {name}: {e}")
    
    print(f"\n🎯 Fast API Test Complete!")

if __name__ == "__main__":
    test_fast_api()
