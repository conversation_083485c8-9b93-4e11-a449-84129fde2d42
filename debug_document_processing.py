#!/usr/bin/env python3
"""
Debug script to find issues in document processing pipeline.
"""

import asyncio
import tempfile
import os
from pathlib import Path

async def debug_document_processing():
    """Debug the document processing pipeline step by step"""
    print("🔍 Debugging Document Processing Pipeline")
    print("=" * 50)
    
    # Test 1: Check if enhanced document processor can be instantiated
    print("1. Testing Enhanced Document Processor instantiation...")
    try:
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        processor = EnhancedDocumentProcessor()
        print("✅ Enhanced Document Processor created successfully")
    except Exception as e:
        print(f"❌ Failed to create Enhanced Document Processor: {e}")
        return
    
    # Test 2: Check progress tracker
    print("\n2. Testing Progress Tracker...")
    try:
        from utils.progress_tracker import ProgressTracker
        tracker = ProgressTracker(
            total_steps=7,
            document_name="test_doc.txt",
            operation_id="test-123"
        )
        print("✅ Progress Tracker created successfully")
        print(f"   Initial progress: {tracker.get_progress_data()}")
    except Exception as e:
        print(f"❌ Failed to create Progress Tracker: {e}")
        return
    
    # Test 3: Create a simple test file
    print("\n3. Creating test file...")
    test_content = """
    Test Document for Processing Debug
    
    This is a simple test document to debug the processing pipeline.
    
    Health Information:
    Vitamin C is important for immune function.
    Echinacea is an herb used for immune support.
    """
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = Path(f.name)
    
    print(f"✅ Test file created: {temp_file_path}")
    print(f"   File size: {temp_file_path.stat().st_size} bytes")
    
    try:
        # Test 4: Test file category detection
        print("\n4. Testing file category detection...")
        try:
            from utils.file_utils import get_file_category, is_supported_file
            category = get_file_category(temp_file_path.name)
            supported = is_supported_file(temp_file_path.name)
            print(f"✅ File category: {category}")
            print(f"✅ File supported: {supported}")
        except Exception as e:
            print(f"❌ File utils error: {e}")
        
        # Test 5: Test basic document processing
        print("\n5. Testing basic document processing...")
        try:
            result = await processor.process_document(
                file_path=temp_file_path,
                chunk_size=500,
                overlap=0,
                extract_entities=False,  # Start simple
                extract_references=False,
                extract_metadata=True,
                generate_embeddings=False,
                progress_tracker=tracker
            )
            
            print("✅ Document processing completed!")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Chunks: {result.get('chunks', 0)}")
            print(f"   Text length: {len(result.get('text', ''))}")
            print(f"   Entities: {result.get('entities', 0)}")
            print(f"   References: {result.get('references', 0)}")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
                
        except Exception as e:
            print(f"❌ Document processing failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 6: Test with entity extraction
        print("\n6. Testing with entity extraction...")
        try:
            tracker2 = ProgressTracker(
                total_steps=7,
                document_name="test_doc_entities.txt",
                operation_id="test-456"
            )
            
            result = await processor.process_document(
                file_path=temp_file_path,
                chunk_size=500,
                overlap=0,
                extract_entities=True,
                extract_references=False,
                extract_metadata=True,
                generate_embeddings=False,
                progress_tracker=tracker2
            )
            
            print("✅ Document processing with entities completed!")
            print(f"   Success: {result.get('success', False)}")
            print(f"   Entities: {result.get('entities', 0)}")
            
            if result.get('error'):
                print(f"   Error: {result['error']}")
                
        except Exception as e:
            print(f"❌ Entity extraction failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test 7: Check database connection
        print("\n7. Testing database connection...")
        try:
            from database.database_service import get_falkordb_adapter
            adapter = await get_falkordb_adapter()
            
            # Test simple query
            result = adapter.execute_cypher("MATCH (n) RETURN count(n) as total LIMIT 1")
            if result and len(result) > 1:
                total_nodes = result[1][0][0]
                print(f"✅ Database connected - Total nodes: {total_nodes}")
            else:
                print("❌ Database query returned no results")
                
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
        
        # Test 8: Check document service
        print("\n8. Testing document service...")
        try:
            from services.document_processing_service import get_document_processing_service
            doc_service = await get_document_processing_service()
            print("✅ Document processing service created")
            
            # Check if there are any documents in processing
            progress = doc_service.get_processing_progress()
            print(f"   Documents in progress: {len(progress.get('document_progress', {}))}")
            print(f"   Queue size: {progress.get('queue_size', 0)}")
            print(f"   Is processing: {progress.get('is_processing', False)}")
            
        except Exception as e:
            print(f"❌ Document service error: {e}")
    
    finally:
        # Clean up
        if temp_file_path.exists():
            temp_file_path.unlink()
            print(f"\n🧹 Cleaned up test file")

if __name__ == "__main__":
    asyncio.run(debug_document_processing())
