"""
System routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import platform
import psutil
import time
from datetime import datetime, timedelta

from database.database_service import get_falkordb_adapter
from database.redis_service import get_redis_connection
from services.llm_service import test_llm_connection
from services.embedding_service import test_embedding_connection
from services.document_service import get_document_count
from services.entity_service import get_entity_count
from services.reference_service import get_reference_count
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["system"])

# Store application start time
start_time = time.time()

@router.get("/system-status")
async def get_system_status() -> Dict[str, Any]:
    """
    Get system status information.
    
    Returns:
        System status information
    """
    try:
        # Get uptime
        uptime_seconds = time.time() - start_time
        uptime_delta = timedelta(seconds=uptime_seconds)
        days = uptime_delta.days
        hours, remainder = divmod(uptime_delta.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        uptime = f"{days} days, {hours} hours, {minutes} minutes"
        
        # Test database connections
        falkordb_connected = await test_falkordb_connection()
        redis_connected = await test_redis_connection()
        
        # Test API connections
        llm_connected = await test_llm_connection()
        embedding_connected = await test_embedding_connection()
        
        # Get document and entity counts
        try:
            documents_processed = await get_document_count()
        except:
            documents_processed = 0
            
        try:
            entities_extracted = await get_entity_count()
        except:
            entities_extracted = 0
            
        try:
            references_extracted = await get_reference_count()
        except:
            references_extracted = 0
        
        # Get last processing time
        last_processing = await get_last_processing_time()
        
        # Get system information
        system_info = {
            "os": platform.system(),
            "python_version": platform.python_version(),
            "cpu_count": psutil.cpu_count(),
            "memory_total": round(psutil.virtual_memory().total / (1024 * 1024 * 1024), 2),  # GB
            "memory_available": round(psutil.virtual_memory().available / (1024 * 1024 * 1024), 2),  # GB
            "disk_total": round(psutil.disk_usage('/').total / (1024 * 1024 * 1024), 2),  # GB
            "disk_free": round(psutil.disk_usage('/').free / (1024 * 1024 * 1024), 2),  # GB
        }
        
        return {
            "version": "1.0.0",
            "uptime": uptime,
            "falkordb_connected": falkordb_connected,
            "redis_connected": redis_connected,
            "llm_connected": llm_connected,
            "embedding_connected": embedding_connected,
            "documents_processed": documents_processed,
            "entities_extracted": entities_extracted,
            "references_extracted": references_extracted,
            "last_processing": last_processing,
            "system_info": system_info
        }
    
    except Exception as e:
        logger.error(f"Error getting system status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting system status: {str(e)}"
        )

async def test_falkordb_connection() -> bool:
    """
    Test FalkorDB connection.
    
    Returns:
        True if connected, False otherwise
    """
    try:
        adapter = await get_falkordb_adapter()
        result = adapter.execute_cypher("RETURN 1")
        return result is not None
    except Exception as e:
        logger.error(f"Error testing FalkorDB connection: {str(e)}")
        return False

async def test_redis_connection() -> bool:
    """
    Test Redis connection.
    
    Returns:
        True if connected, False otherwise
    """
    try:
        redis = await get_redis_connection()
        result = await redis.ping()
        return result
    except Exception as e:
        logger.error(f"Error testing Redis connection: {str(e)}")
        return False

async def get_last_processing_time() -> str:
    """
    Get the time of the last document processing.
    
    Returns:
        Last processing time as string
    """
    try:
        adapter = await get_falkordb_adapter()
        query = """
        MATCH (d:Document)
        RETURN d.processed_at AS processed_at
        ORDER BY d.processed_at DESC
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1 and len(result[1]) > 0:
            processed_at = result[1][0][0]
            if processed_at:
                # Convert to datetime if it's a string
                if isinstance(processed_at, str):
                    try:
                        dt = datetime.fromisoformat(processed_at)
                        return dt.strftime("%Y-%m-%d %H:%M:%S")
                    except:
                        return processed_at
                return processed_at
        
        return "Never"
    except Exception as e:
        logger.error(f"Error getting last processing time: {str(e)}")
        return "Unknown"
