"""
Script to check the Neo4j database contents.
"""

import os
from neo4j import GraphDatabase
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Connect to Neo4j
driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))

def check_database():
    """Check the contents of the Neo4j database."""
    with driver.session() as session:
        # Check Episodes
        result = session.run("MATCH (e:Episode) RETURN count(e) AS count")
        episode_count = result.single()["count"]
        print(f"Episodes: {episode_count}")
        
        # Check Facts
        result = session.run("MATCH (f:Fact) RETURN count(f) AS count")
        fact_count = result.single()["count"]
        print(f"Facts: {fact_count}")
        
        # Check Entities
        result = session.run("MATCH (e:Entity) RETURN count(e) AS count")
        entity_count = result.single()["count"]
        print(f"Entities: {entity_count}")
        
        # Check Taxonomy
        result = session.run("MATCH (t:Taxonomy) RETURN count(t) AS count")
        taxonomy_count = result.single()["count"]
        print(f"Taxonomy nodes: {taxonomy_count}")
        
        # Check Relationships
        result = session.run("MATCH ()-[r]->() RETURN type(r) AS type, count(r) AS count ORDER BY count DESC")
        print("\nRelationships:")
        for record in result:
            print(f"  {record['type']}: {record['count']}")

if __name__ == "__main__":
    try:
        check_database()
    finally:
        driver.close()
