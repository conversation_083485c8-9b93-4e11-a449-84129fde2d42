import os
import shutil
import platform
from neo4j import GraphDatabase
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

def get_dir_size(path):
    """Get the size of a directory and its subdirectories in bytes"""
    total_size = 0
    for dirpath, dirnames, filenames in os.walk(path):
        for f in filenames:
            fp = os.path.join(dirpath, f)
            if not os.path.islink(fp):  # Skip symbolic links
                total_size += os.path.getsize(fp)
    return total_size

def format_size(size_bytes):
    """Format bytes to human-readable format"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.2f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.2f} PB"

try:
    # Connect to Neo4j
    driver = GraphDatabase.driver(
        neo4j_uri, 
        auth=(neo4j_user, neo4j_password)
    )
    
    # Get database directory
    with driver.session() as session:
        result = session.run("CALL dbms.listConfig() YIELD name, value WHERE name = 'server.directories.data' RETURN value")
        record = result.single()
        if record:
            data_dir = record['value']
            print(f"\nNeo4j Data Directory: {data_dir}")
            
            # Get directory size
            if os.path.exists(data_dir):
                dir_size = get_dir_size(data_dir)
                print(f"Current Database Size: {format_size(dir_size)}")
                
                # Get disk space information
                disk = os.path.splitdrive(data_dir)[0] if platform.system() == 'Windows' else os.path.dirname(data_dir)
                total, used, free = shutil.disk_usage(disk)
                
                print(f"\nDisk Space Information for {disk}:")
                print(f"Total Disk Space: {format_size(total)}")
                print(f"Used Disk Space: {format_size(used)} ({used/total*100:.1f}%)")
                print(f"Free Disk Space: {format_size(free)} ({free/total*100:.1f}%)")
                
                # Neo4j Enterprise Edition limits
                print("\nNeo4j Enterprise Edition Storage Information:")
                print("- No hard storage limit in Enterprise Edition")
                print("- Limited only by available disk space")
                print(f"- Current database using {dir_size/total*100:.2f}% of total disk space")
                
                # Database content information
                result = session.run("MATCH (n) RETURN count(n) as nodeCount")
                node_count = result.single()['nodeCount']
                
                result = session.run("MATCH ()-[r]->() RETURN count(r) as relCount")
                rel_count = result.single()['relCount']
                
                print(f"\nDatabase Contents:")
                print(f"Total Nodes: {node_count}")
                print(f"Total Relationships: {rel_count}")
                
                # Calculate average size per element
                total_elements = node_count + rel_count
                if total_elements > 0:
                    avg_size = dir_size / total_elements
                    print(f"Average size per element (node/relationship): {format_size(avg_size)}")
                    
                    # Estimate capacity
                    print("\nEstimated Storage Capacity:")
                    for multiplier in [10, 100, 1000]:
                        estimated_elements = total_elements * multiplier
                        estimated_size = avg_size * estimated_elements
                        print(f"  {multiplier}x current data ({estimated_elements:,} elements) would require approximately {format_size(estimated_size)}")
            else:
                print(f"Directory {data_dir} does not exist or is not accessible")
    
    # Close the driver
    driver.close()
    
except Exception as e:
    print(f"Error: {e}")
