"""
Advanced example demonstrating how to use Graphiti with Google Gemini
for various knowledge graph operations.
"""

import asyncio
import logging
import os
import json
from datetime import datetime, timezone
from typing import List, Dict, Any

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.nodes import EpisodeType, Fact
from graphiti_core.utils.maintenance.graph_data_operations import clear_data


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )


async def create_knowledge_graph(graphiti: Graphiti):
    """Create a sample knowledge graph with multiple episodes and facts."""
    print("\n=== Creating Knowledge Graph ===")
    
    # Sample data - articles about AI and knowledge graphs
    articles = [
        {
            "title": "Introduction to Knowledge Graphs",
            "content": """
            Knowledge graphs are a powerful way to represent and connect information.
            They consist of nodes (entities) and edges (relationships) that form a graph structure.
            Knowledge graphs are used in many applications, including search engines, recommendation systems,
            and AI assistants. They help machines understand the relationships between different pieces of information.
            """
        },
        {
            "title": "Google Gemini and Large Language Models",
            "content": """
            Google Gemini is a family of large language models (LLMs) developed by Google.
            These models are trained on vast amounts of text data and can generate human-like text,
            translate languages, write different kinds of creative content, and answer questions in an informative way.
            Gemini models can be used for various natural language processing tasks, including text generation,
            summarization, and question answering.
            """
        },
        {
            "title": "Combining Knowledge Graphs with LLMs",
            "content": """
            Combining knowledge graphs with large language models creates powerful AI systems.
            Knowledge graphs provide structured, factual information, while LLMs offer natural language understanding
            and generation capabilities. Together, they can deliver more accurate, contextual, and explainable AI systems.
            This combination helps address some limitations of pure LLMs, such as hallucinations and lack of up-to-date information.
            """
        }
    ]
    
    # Add each article as an episode
    for article in articles:
        print(f"Adding episode: {article['title']}")
        await graphiti.add_episode(
            name=article['title'],
            episode_body=article['content'],
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="Sample Article",
        )
    
    print("Knowledge graph created successfully!")


async def search_knowledge_graph(graphiti: Graphiti):
    """Perform various searches on the knowledge graph."""
    print("\n=== Searching Knowledge Graph ===")
    
    search_queries = [
        "knowledge graphs",
        "Google Gemini capabilities",
        "combining LLMs with knowledge graphs"
    ]
    
    for query in search_queries:
        print(f"\nSearching for: '{query}'")
        search_results = await graphiti.search(query, limit=3)
        
        print(f"Found {len(search_results.edges)} results:")
        for i, result in enumerate(search_results.edges):
            print(f"{i+1}. {result.fact} (Score: {result.score:.4f})")


async def ask_questions(graphiti: Graphiti):
    """Ask questions to the knowledge graph using Gemini."""
    print("\n=== Asking Questions ===")
    
    questions = [
        "What are knowledge graphs used for?",
        "What is Google Gemini?",
        "How do knowledge graphs and LLMs complement each other?"
    ]
    
    for question in questions:
        print(f"\nQuestion: {question}")
        
        # First search for relevant facts
        search_results = await graphiti.search(question, limit=5)
        
        # Extract the facts
        facts = [result.fact for result in search_results.edges]
        
        # Use Gemini to generate an answer based on the facts
        answer = await generate_answer_from_facts(graphiti, question, facts)
        print(f"Answer: {answer}")


async def generate_answer_from_facts(graphiti: Graphiti, question: str, facts: List[str]) -> str:
    """Generate an answer to a question based on retrieved facts using Gemini."""
    # Create a prompt for Gemini
    prompt = f"""
    Question: {question}
    
    Based on the following facts, please provide a concise answer:
    
    {chr(10).join([f"- {fact}" for fact in facts])}
    
    Answer:
    """
    
    # Use the LLM client directly
    llm_client = graphiti.llm_client
    response = await llm_client.generate_response(prompt)
    
    return response


async def main():
    """Main function to run the example."""
    # Load environment variables
    load_dotenv()
    setup_logging()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        print("No Google API key found in environment variables. Please add it to your .env file.")
        return
        
    print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")
    
    try:
        print("Initializing Graphiti with Gemini clients...")
        # Initialize Graphiti with Gemini clients
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=GeminiClient(
                config=LLMConfig(
                    api_key=google_api_key,
                    model="models/gemini-1.5-flash"
                )
            ),
            embedder=GeminiEmbedder(
                config=GeminiEmbedderConfig(
                    api_key=google_api_key,
                    embedding_model="models/embedding-001"
                )
            )
        )
        
        print("Connecting to Neo4j database...")
        
        # Clear existing data (optional - remove this in production)
        print("Clearing existing data...")
        await clear_data(graphiti.driver)
        
        # Set up indices and constraints
        print("Setting up indices and constraints...")
        await graphiti.build_indices_and_constraints()
        
        # Create a sample knowledge graph
        await create_knowledge_graph(graphiti)
        
        # Search the knowledge graph
        await search_knowledge_graph(graphiti)
        
        # Ask questions to the knowledge graph
        await ask_questions(graphiti)
        
        print("\nAdvanced example completed successfully!")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close the driver
        print("Closing Neo4j connection...")
        await graphiti.driver.close()


if __name__ == "__main__":
    asyncio.run(main())
