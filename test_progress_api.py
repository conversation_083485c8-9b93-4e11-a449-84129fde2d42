#!/usr/bin/env python3
"""
Test script to verify the enhanced progress tracking API is working correctly.
"""

import requests
import json
import time

def test_progress_api():
    """Test the enhanced progress API endpoints."""
    base_url = "http://127.0.0.1:9753"
    
    # Test supported types endpoint
    print("Testing supported types endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/supported-types")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Supported types: {len(data['supported_extensions'])} extensions")
        else:
            print(f"✗ Supported types failed: {response.status_code}")
    except Exception as e:
        print(f"✗ Supported types error: {e}")
    
    # Test with a known operation ID (if any)
    operation_id = "a3d57766-18c2-4f59-a73e-b22e8d66ec2e"
    
    print(f"\nTesting progress endpoint with operation ID: {operation_id}")
    try:
        response = requests.get(f"{base_url}/api/enhanced/progress/{operation_id}")
        print(f"Basic progress endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Basic progress data: {json.dumps(data, indent=2)}")
        elif response.status_code == 404:
            print("✓ Operation not found (expected for expired operations)")
        else:
            print(f"✗ Unexpected status: {response.text}")
    except Exception as e:
        print(f"✗ Basic progress error: {e}")
    
    print(f"\nTesting detailed progress endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/progress/{operation_id}/detailed")
        print(f"Detailed progress endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ Detailed progress data: {json.dumps(data, indent=2)}")
        elif response.status_code == 404:
            print("✓ Operation not found (expected for expired operations)")
        else:
            print(f"✗ Unexpected status: {response.text}")
    except Exception as e:
        print(f"✗ Detailed progress error: {e}")

if __name__ == "__main__":
    test_progress_api()
