#!/usr/bin/env python3
"""
Graphiti MCP Server - Exposes Graphiti functionality through the Model Context Protocol (MCP)
"""

import argparse
import asyncio
import logging
import os
import sys
import uuid
from datetime import datetime, timezone
from typing import Any, Optional, TypedDict, Union, cast

from dotenv import load_dotenv
from mcp.server.fastmcp import FastMCP
from pydantic import BaseModel, Field

from graphiti_core import Graphiti
from graphiti_core.edges import EntityEdge
from graphiti_core.llm_client import LLMClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.nodes import EpisodeType, EpisodicNode
from graphiti_core.search.search_config_recipes import (
    NODE_HYBRID_SEARCH_NODE_DISTANCE,
    NODE_HYBRID_SEARCH_RRF,
)
from graphiti_core.search.search_filters import SearchFilters
from graphiti_core.utils.maintenance.graph_data_operations import clear_data

# Import document processing functionality
import base64
import tempfile
import sys
import os
from pathlib import Path as PathLib

# Add the parent directory to the path to import services
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import the real document processing service
try:
    from services.document_processing_service import get_document_processing_service
    REAL_DOCUMENT_PROCESSING_AVAILABLE = True
except ImportError:
    REAL_DOCUMENT_PROCESSING_AVAILABLE = False
    logger.warning("Real document processing service not available, using simplified version")

load_dotenv()

DEFAULT_LLM_MODEL = 'gpt-4o-mini'


class Requirement(BaseModel):
    """A Requirement represents a specific need, feature, or functionality that a product or service must fulfill.

    Always ensure an edge is created between the requirement and the project it belongs to, and clearly indicate on the
    edge that the requirement is a requirement.

    Instructions for identifying and extracting requirements:
    1. Look for explicit statements of needs or necessities ("We need X", "X is required", "X must have Y")
    2. Identify functional specifications that describe what the system should do
    3. Pay attention to non-functional requirements like performance, security, or usability criteria
    4. Extract constraints or limitations that must be adhered to
    5. Focus on clear, specific, and measurable requirements rather than vague wishes
    6. Capture the priority or importance if mentioned ("critical", "high priority", etc.)
    7. Include any dependencies between requirements when explicitly stated
    8. Preserve the original intent and scope of the requirement
    9. Categorize requirements appropriately based on their domain or function
    """

    project_name: str = Field(
        ...,
        description='The name of the project to which the requirement belongs.',
    )
    description: str = Field(
        ...,
        description='Description of the requirement. Only use information mentioned in the context to write this description.',
    )


class Preference(BaseModel):
    """A Preference represents a user's expressed like, dislike, or preference for something.

    Instructions for identifying and extracting preferences:
    1. Look for explicit statements of preference such as "I like/love/enjoy/prefer X" or "I don't like/hate/dislike X"
    2. Pay attention to comparative statements ("I prefer X over Y")
    3. Consider the emotional tone when users mention certain topics
    4. Extract only preferences that are clearly expressed, not assumptions
    5. Categorize the preference appropriately based on its domain (food, music, brands, etc.)
    6. Include relevant qualifiers (e.g., "likes spicy food" rather than just "likes food")
    7. Only extract preferences directly stated by the user, not preferences of others they mention
    8. Provide a concise but specific description that captures the nature of the preference
    """

    category: str = Field(
        ...,
        description="The category of the preference. (e.g., 'Brands', 'Food', 'Music')",
    )
    description: str = Field(
        ...,
        description='Brief description of the preference. Only use information mentioned in the context to write this description.',
    )


class Procedure(BaseModel):
    """A Procedure informing the agent what actions to take or how to perform in certain scenarios. Procedures are typically composed of several steps.

    Instructions for identifying and extracting procedures:
    1. Look for sequential instructions or steps ("First do X, then do Y")
    2. Identify explicit directives or commands ("Always do X when Y happens")
    3. Pay attention to conditional statements ("If X occurs, then do Y")
    4. Extract procedures that have clear beginning and end points
    5. Focus on actionable instructions rather than general information
    6. Preserve the original sequence and dependencies between steps
    7. Include any specified conditions or triggers for the procedure
    8. Capture any stated purpose or goal of the procedure
    9. Summarize complex procedures while maintaining critical details
    """

    description: str = Field(
        ...,
        description='Brief description of the procedure. Only use information mentioned in the context to write this description.',
    )


ENTITY_TYPES: dict[str, BaseModel] = {
    'Requirement': Requirement,  # type: ignore
    'Preference': Preference,  # type: ignore
    'Procedure': Procedure,  # type: ignore
}


# Type definitions for API responses
class ErrorResponse(TypedDict):
    error: str


class SuccessResponse(TypedDict):
    message: str


class NodeResult(TypedDict):
    uuid: str
    name: str
    summary: str
    labels: list[str]
    group_id: str
    created_at: str
    attributes: dict[str, Any]


class NodeSearchResponse(TypedDict):
    message: str
    nodes: list[NodeResult]


class FactSearchResponse(TypedDict):
    message: str
    facts: list[dict[str, Any]]


class EpisodeSearchResponse(TypedDict):
    message: str
    episodes: list[dict[str, Any]]


class StatusResponse(TypedDict):
    status: str
    message: str


class DocumentProcessingResponse(TypedDict):
    message: str
    document_id: str
    filename: str
    chunks: int
    entities: int
    references: int
    success: bool


class DocumentSearchResponse(TypedDict):
    message: str
    documents: list[dict[str, Any]]


# Server configuration classes
# The configuration system has a hierarchy:
# - GraphitiConfig is the top-level configuration
#   - LLMConfig handles all OpenAI/LLM related settings
#   - Neo4jConfig manages database connection details
#   - Various other settings like group_id and feature flags
# Configuration values are loaded from:
# 1. Default values in the class definitions
# 2. Environment variables (loaded via load_dotenv())
# 3. Command line arguments (which override environment variables)
class GraphitiLLMConfig(BaseModel):
    """Configuration for the LLM client.

    Centralizes all LLM-specific configuration parameters including API keys and model selection.
    """

    api_key: Optional[str] = None
    model: str = DEFAULT_LLM_MODEL
    temperature: float = 0.0

    @classmethod
    def from_env(cls) -> 'GraphitiLLMConfig':
        """Create LLM configuration from environment variables."""
        # Get model from environment, or use default if not set or empty
        model_env = os.environ.get('MODEL_NAME', '')
        model = model_env if model_env.strip() else DEFAULT_LLM_MODEL

        # Log if empty model was provided
        if model_env == '':
            logger.debug(
                f'MODEL_NAME environment variable not set, using default: {DEFAULT_LLM_MODEL}'
            )
        elif not model_env.strip():
            logger.warning(
                f'Empty MODEL_NAME environment variable, using default: {DEFAULT_LLM_MODEL}'
            )

        return cls(
            api_key=os.environ.get('OPENAI_API_KEY'),
            model=model,
            temperature=float(os.environ.get('LLM_TEMPERATURE', '0.0')),
        )

    @classmethod
    def from_cli_and_env(cls, args: argparse.Namespace) -> 'GraphitiLLMConfig':
        """Create LLM configuration from CLI arguments, falling back to environment variables."""
        # Start with environment-based config
        config = cls.from_env()

        # CLI arguments override environment variables when provided
        if hasattr(args, 'model') and args.model:
            # Only use CLI model if it's not empty
            if args.model.strip():
                config.model = args.model
            else:
                # Log that empty model was provided and default is used
                logger.warning(f'Empty model name provided, using default: {DEFAULT_LLM_MODEL}')

        if hasattr(args, 'temperature') and args.temperature is not None:
            config.temperature = args.temperature

        return config

    def create_client(self) -> Optional[LLMClient]:
        """Create an LLM client based on this configuration.

        Returns:
            LLMClient instance if API key is available, None otherwise
        """
        if not self.api_key:
            return None

        llm_client_config = LLMConfig(api_key=self.api_key, model=self.model)

        # Set temperature
        llm_client_config.temperature = self.temperature

        return OpenAIClient(config=llm_client_config)


class FalkorDBConfig(BaseModel):
    """Configuration for FalkorDB database connection."""

    host: str = 'localhost'
    port: int = 6379
    password: str = ''
    graph: str = 'graphiti'

    @classmethod
    def from_env(cls) -> 'FalkorDBConfig':
        """Create FalkorDB configuration from environment variables."""
        return cls(
            host=os.environ.get('FALKORDB_HOST', 'localhost'),
            port=int(os.environ.get('FALKORDB_PORT', '6379')),
            password=os.environ.get('FALKORDB_PASSWORD', ''),
            graph=os.environ.get('FALKORDB_GRAPH', 'graphiti'),
        )


class GraphitiConfig(BaseModel):
    """Configuration for Graphiti client.

    Centralizes all configuration parameters for the Graphiti client.
    """

    llm: GraphitiLLMConfig = Field(default_factory=GraphitiLLMConfig)
    falkordb: FalkorDBConfig = Field(default_factory=FalkorDBConfig)
    group_id: Optional[str] = None
    use_custom_entities: bool = False
    destroy_graph: bool = False

    @classmethod
    def from_env(cls) -> 'GraphitiConfig':
        """Create a configuration instance from environment variables."""
        return cls(
            llm=GraphitiLLMConfig.from_env(),
            falkordb=FalkorDBConfig.from_env(),
        )

    @classmethod
    def from_cli_and_env(cls, args: argparse.Namespace) -> 'GraphitiConfig':
        """Create configuration from CLI arguments, falling back to environment variables."""
        # Start with environment configuration
        config = cls.from_env()

        # Apply CLI overrides
        if args.group_id:
            config.group_id = args.group_id
        else:
            config.group_id = f'graph_{uuid.uuid4().hex[:8]}'

        config.use_custom_entities = args.use_custom_entities
        config.destroy_graph = args.destroy_graph

        # Update LLM config using CLI args
        config.llm = GraphitiLLMConfig.from_cli_and_env(args)

        return config


class MCPConfig(BaseModel):
    """Configuration for MCP server."""

    transport: str = 'sse'  # Default to SSE transport

    @classmethod
    def from_cli(cls, args: argparse.Namespace) -> 'MCPConfig':
        """Create MCP configuration from CLI arguments."""
        return cls(transport=args.transport)


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    stream=sys.stderr,
)
logger = logging.getLogger(__name__)

# Create global config instance - will be properly initialized later
config = GraphitiConfig()

# MCP server instructions
GRAPHITI_MCP_INSTRUCTIONS = """
Welcome to Graphiti MCP - a memory service for AI agents built on a knowledge graph. Graphiti performs well
with dynamic data such as user interactions, changing enterprise data, and external information.

Graphiti transforms information into a richly connected knowledge network, allowing you to
capture relationships between concepts, entities, and information. The system organizes data as episodes
(content snippets), nodes (entities), and facts (relationships between entities), creating a dynamic,
queryable memory store that evolves with new information. Graphiti supports multiple data formats, including
structured JSON data, enabling seamless integration with existing data pipelines and systems.

Facts contain temporal metadata, allowing you to track the time of creation and whether a fact is invalid
(superseded by new information).

Key capabilities:
1. **Document Processing**: Upload and process documents (PDF, text, etc.) with process_document
2. **Entity Management**: Advanced entity search with search_entities_advanced and detailed retrieval with get_entity_details
3. **Question Answering**: Get comprehensive answers with citations using answer_question_with_citations
4. **Knowledge Graph Queries**: Execute direct Cypher queries with execute_cypher_query
5. **Episode Management**: Add episodes (text, messages, or JSON) to the knowledge graph with add_episode
6. **Search Operations**: Search for nodes (entities) with search_nodes and facts (relationships) with search_facts
7. **Graph Management**: Manage the knowledge graph with tools like delete_episode, delete_entity_edge, and clear_graph

The server connects to a database for persistent storage and uses language models for certain operations.
Each piece of information is organized by group_id, allowing you to maintain separate knowledge domains.

When adding information, provide descriptive names and detailed content to improve search quality.
When searching, use specific queries and consider filtering by group_id for more relevant results.

For optimal performance, ensure the database is properly configured and accessible, and valid
API keys are provided for any language model operations.
"""


# MCP server instance
mcp = FastMCP(
    'graphiti',
    instructions=GRAPHITI_MCP_INSTRUCTIONS,
)


# Initialize Graphiti client
graphiti_client: Optional[Graphiti] = None


async def initialize_graphiti():
    """Initialize the Graphiti client with the configured settings."""
    global graphiti_client, config

    try:
        # Create LLM client if possible
        llm_client = config.llm.create_client()
        if not llm_client and config.use_custom_entities:
            # If custom entities are enabled, we must have an LLM client
            raise ValueError('OPENAI_API_KEY must be set when custom entities are enabled')

        # Validate Neo4j configuration
        if not config.neo4j.uri or not config.neo4j.user or not config.neo4j.password:
            raise ValueError('NEO4J_URI, NEO4J_USER, and NEO4J_PASSWORD must be set')

        # Initialize Graphiti client
        graphiti_client = Graphiti(
            uri=config.neo4j.uri,
            user=config.neo4j.user,
            password=config.neo4j.password,
            llm_client=llm_client,
        )

        # Destroy graph if requested
        if config.destroy_graph:
            logger.info('Destroying graph...')
            await clear_data(graphiti_client.driver)

        # Initialize the graph database with Graphiti's indices
        await graphiti_client.build_indices_and_constraints()
        logger.info('Graphiti client initialized successfully')

        # Log configuration details for transparency
        if llm_client:
            logger.info(f'Using OpenAI model: {config.llm.model}')
            logger.info(f'Using temperature: {config.llm.temperature}')
        else:
            logger.info('No LLM client configured - entity extraction will be limited')

        logger.info(f'Using group_id: {config.group_id}')
        logger.info(
            f'Custom entity extraction: {"enabled" if config.use_custom_entities else "disabled"}'
        )

    except Exception as e:
        logger.error(f'Failed to initialize Graphiti: {str(e)}')
        raise


def format_fact_result(edge: EntityEdge) -> dict[str, Any]:
    """Format an entity edge into a readable result.

    Since EntityEdge is a Pydantic BaseModel, we can use its built-in serialization capabilities.

    Args:
        edge: The EntityEdge to format

    Returns:
        A dictionary representation of the edge with serialized dates and excluded embeddings
    """
    return edge.model_dump(
        mode='json',
        exclude={
            'fact_embedding',
        },
    )


# Dictionary to store queues for each group_id
# Each queue is a list of tasks to be processed sequentially
episode_queues: dict[str, asyncio.Queue] = {}
# Dictionary to track if a worker is running for each group_id
queue_workers: dict[str, bool] = {}


async def process_episode_queue(group_id: str):
    """Process episodes for a specific group_id sequentially.

    This function runs as a long-lived task that processes episodes
    from the queue one at a time.
    """
    global queue_workers

    logger.info(f'Starting episode queue worker for group_id: {group_id}')
    queue_workers[group_id] = True

    try:
        while True:
            # Get the next episode processing function from the queue
            # This will wait if the queue is empty
            process_func = await episode_queues[group_id].get()

            try:
                # Process the episode
                await process_func()
            except Exception as e:
                logger.error(f'Error processing queued episode for group_id {group_id}: {str(e)}')
            finally:
                # Mark the task as done regardless of success/failure
                episode_queues[group_id].task_done()
    except asyncio.CancelledError:
        logger.info(f'Episode queue worker for group_id {group_id} was cancelled')
    except Exception as e:
        logger.error(f'Unexpected error in queue worker for group_id {group_id}: {str(e)}')
    finally:
        queue_workers[group_id] = False
        logger.info(f'Stopped episode queue worker for group_id: {group_id}')


@mcp.tool()
async def add_episode(
    name: str,
    episode_body: str,
    group_id: Optional[str] = None,
    source: str = 'text',
    source_description: str = '',
    uuid: Optional[str] = None,
) -> Union[SuccessResponse, ErrorResponse]:
    """Add an episode to the Graphiti knowledge graph. This is the primary way to add information to the graph.

    This function returns immediately and processes the episode addition in the background.
    Episodes for the same group_id are processed sequentially to avoid race conditions.

    Args:
        name (str): Name of the episode
        episode_body (str): The content of the episode. When source='json', this must be a properly escaped JSON string,
                           not a raw Python dictionary. The JSON data will be automatically processed
                           to extract entities and relationships.
        group_id (str, optional): A unique ID for this graph. If not provided, uses the default group_id from CLI
                                 or a generated one.
        source (str, optional): Source type, must be one of:
                               - 'text': For plain text content (default)
                               - 'json': For structured data
                               - 'message': For conversation-style content
        source_description (str, optional): Description of the source
        uuid (str, optional): Optional UUID for the episode

    Examples:
        # Adding plain text content
        add_episode(
            name="Company News",
            episode_body="Acme Corp announced a new product line today.",
            source="text",
            source_description="news article",
            group_id="some_arbitrary_string"
        )

        # Adding structured JSON data
        # NOTE: episode_body must be a properly escaped JSON string. Note the triple backslashes
        add_episode(
            name="Customer Profile",
            episode_body="{\\\"company\\\": {\\\"name\\\": \\\"Acme Technologies\\\"}, \\\"products\\\": [{\\\"id\\\": \\\"P001\\\", \\\"name\\\": \\\"CloudSync\\\"}, {\\\"id\\\": \\\"P002\\\", \\\"name\\\": \\\"DataMiner\\\"}]}",
            source="json",
            source_description="CRM data"
        )

        # Adding message-style content
        add_episode(
            name="Customer Conversation",
            episode_body="user: What's your return policy?\nassistant: You can return items within 30 days.",
            source="message",
            source_description="chat transcript",
            group_id="some_arbitrary_string"
        )

    Notes:
        When using source='json':
        - The JSON must be a properly escaped string, not a raw Python dictionary
        - The JSON will be automatically processed to extract entities and relationships
        - Complex nested structures are supported (arrays, nested objects, mixed data types), but keep nesting to a minimum
        - Entities will be created from appropriate JSON properties
        - Relationships between entities will be established based on the JSON structure
    """
    global graphiti_client, episode_queues, queue_workers

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Map string source to EpisodeType enum
        source_type = EpisodeType.text
        if source.lower() == 'message':
            source_type = EpisodeType.message
        elif source.lower() == 'json':
            source_type = EpisodeType.json

        # Use the provided group_id or fall back to the default from config
        effective_group_id = group_id if group_id is not None else config.group_id

        # Cast group_id to str to satisfy type checker
        # The Graphiti client expects a str for group_id, not Optional[str]
        group_id_str = str(effective_group_id) if effective_group_id is not None else ''

        # We've already checked that graphiti_client is not None above
        # This assert statement helps type checkers understand that graphiti_client is defined
        assert graphiti_client is not None, 'graphiti_client should not be None here'

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Define the episode processing function
        async def process_episode():
            try:
                logger.info(f"Processing queued episode '{name}' for group_id: {group_id_str}")
                # Use all entity types if use_custom_entities is enabled, otherwise use empty dict
                entity_types = ENTITY_TYPES if config.use_custom_entities else {}

                await client.add_episode(
                    name=name,
                    episode_body=episode_body,
                    source=source_type,
                    source_description=source_description,
                    group_id=group_id_str,  # Using the string version of group_id
                    uuid=uuid,
                    reference_time=datetime.now(timezone.utc),
                    entity_types=entity_types,
                )
                logger.info(f"Episode '{name}' added successfully")

                logger.info(f"Building communities after episode '{name}'")
                await client.build_communities()

                logger.info(f"Episode '{name}' processed successfully")
            except Exception as e:
                error_msg = str(e)
                logger.error(
                    f"Error processing episode '{name}' for group_id {group_id_str}: {error_msg}"
                )

        # Initialize queue for this group_id if it doesn't exist
        if group_id_str not in episode_queues:
            episode_queues[group_id_str] = asyncio.Queue()

        # Add the episode processing function to the queue
        await episode_queues[group_id_str].put(process_episode)

        # Start a worker for this queue if one isn't already running
        if not queue_workers.get(group_id_str, False):
            asyncio.create_task(process_episode_queue(group_id_str))

        # Return immediately with a success message
        return {
            'message': f"Episode '{name}' queued for processing (position: {episode_queues[group_id_str].qsize()})"
        }
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error queuing episode task: {error_msg}')
        return {'error': f'Error queuing episode task: {error_msg}'}


@mcp.tool()
async def search_nodes(
    query: str,
    group_ids: Optional[list[str]] = None,
    max_nodes: int = 10,
    center_node_uuid: Optional[str] = None,
    entity: str = '',  # cursor seems to break with None
) -> Union[NodeSearchResponse, ErrorResponse]:
    """Search the Graphiti knowledge graph for relevant node summaries.
    These contain a summary of all of a node's relationships with other nodes.

    Note: entity is a single entity type to filter results (permitted: "Preference", "Procedure").

    Args:
        query: The search query
        group_ids: Optional list of group IDs to filter results
        max_nodes: Maximum number of nodes to return (default: 10)
        center_node_uuid: Optional UUID of a node to center the search around
        entity: Optional single entity type to filter results (permitted: "Preference", "Procedure")
    """
    global graphiti_client

    if graphiti_client is None:
        return ErrorResponse(error='Graphiti client not initialized')

    try:
        # Use the provided group_ids or fall back to the default from config if none provided
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config.group_id else []
        )

        # Configure the search
        if center_node_uuid is not None:
            search_config = NODE_HYBRID_SEARCH_NODE_DISTANCE.model_copy(deep=True)
        else:
            search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
        search_config.limit = max_nodes

        filters = SearchFilters()
        if entity != '':
            filters.node_labels = [entity]

        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Perform the search using the _search method
        search_results = await client._search(
            query=query,
            config=search_config,
            group_ids=effective_group_ids,
            center_node_uuid=center_node_uuid,
            search_filter=filters,
        )

        if not search_results.nodes:
            return NodeSearchResponse(message='No relevant nodes found', nodes=[])

        # Format the node results
        formatted_nodes: list[NodeResult] = [
            {
                'uuid': node.uuid,
                'name': node.name,
                'summary': node.summary if hasattr(node, 'summary') else '',
                'labels': node.labels if hasattr(node, 'labels') else [],
                'group_id': node.group_id,
                'created_at': node.created_at.isoformat(),
                'attributes': node.attributes if hasattr(node, 'attributes') else {},
            }
            for node in search_results.nodes
        ]

        return NodeSearchResponse(message='Nodes retrieved successfully', nodes=formatted_nodes)
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error searching nodes: {error_msg}')
        return ErrorResponse(error=f'Error searching nodes: {error_msg}')


@mcp.tool()
async def search_facts(
    query: str,
    group_ids: Optional[list[str]] = None,
    max_facts: int = 10,
    center_node_uuid: Optional[str] = None,
) -> Union[FactSearchResponse, ErrorResponse]:
    """Search the Graphiti knowledge graph for relevant facts.

    Args:
        query: The search query
        group_ids: Optional list of group IDs to filter results
        max_facts: Maximum number of facts to return (default: 10)
        center_node_uuid: Optional UUID of a node to center the search around
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use the provided group_ids or fall back to the default from config if none provided
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config.group_id else []
        )

        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        relevant_edges = await client.search(
            group_ids=effective_group_ids,
            query=query,
            num_results=max_facts,
            center_node_uuid=center_node_uuid,
        )

        if not relevant_edges:
            return {'message': 'No relevant facts found', 'facts': []}

        facts = [format_fact_result(edge) for edge in relevant_edges]
        return {'message': 'Facts retrieved successfully', 'facts': facts}
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error searching facts: {error_msg}')
        return {'error': f'Error searching facts: {error_msg}'}


@mcp.tool()
async def delete_entity_edge(uuid: str) -> Union[SuccessResponse, ErrorResponse]:
    """Delete an entity edge from the Graphiti knowledge graph.

    Args:
        uuid: UUID of the entity edge to delete
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the entity edge by UUID
        entity_edge = await EntityEdge.get_by_uuid(client.driver, uuid)
        # Delete the edge using its delete method
        await entity_edge.delete(client.driver)
        return {'message': f'Entity edge with UUID {uuid} deleted successfully'}
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error deleting entity edge: {error_msg}')
        return {'error': f'Error deleting entity edge: {error_msg}'}


@mcp.tool()
async def delete_episode(uuid: str) -> Union[SuccessResponse, ErrorResponse]:
    """Delete an episode from the Graphiti knowledge graph.

    Args:
        uuid: UUID of the episode to delete
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the episodic node by UUID - EpisodicNode is already imported at the top
        episodic_node = await EpisodicNode.get_by_uuid(client.driver, uuid)
        # Delete the node using its delete method
        await episodic_node.delete(client.driver)
        return {'message': f'Episode with UUID {uuid} deleted successfully'}
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error deleting episode: {error_msg}')
        return {'error': f'Error deleting episode: {error_msg}'}


@mcp.tool()
async def get_entity_edge(uuid: str) -> Union[dict[str, Any], ErrorResponse]:
    """Get an entity edge from the Graphiti knowledge graph by its UUID.

    Args:
        uuid: UUID of the entity edge to retrieve
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Get the entity edge directly using the EntityEdge class method
        entity_edge = await EntityEdge.get_by_uuid(client.driver, uuid)

        # Use the format_fact_result function to serialize the edge
        # Return the Python dict directly - MCP will handle serialization
        return format_fact_result(entity_edge)
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting entity edge: {error_msg}')
        return {'error': f'Error getting entity edge: {error_msg}'}


@mcp.tool()
async def get_episodes(
    group_id: Optional[str] = None, last_n: int = 10
) -> Union[list[dict[str, Any]], EpisodeSearchResponse, ErrorResponse]:
    """Get the most recent episodes for a specific group.

    Args:
        group_id: ID of the group to retrieve episodes from. If not provided, uses the default group_id.
        last_n: Number of most recent episodes to retrieve (default: 10)
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use the provided group_id or fall back to the default from config
        effective_group_id = group_id if group_id is not None else config.group_id

        if not isinstance(effective_group_id, str):
            return {'error': 'Group ID must be a string'}

        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        episodes = await client.retrieve_episodes(
            group_ids=[effective_group_id], last_n=last_n, reference_time=datetime.now(timezone.utc)
        )

        if not episodes:
            return {'message': f'No episodes found for group {effective_group_id}', 'episodes': []}

        # Use Pydantic's model_dump method for EpisodicNode serialization
        formatted_episodes = [
            # Use mode='json' to handle datetime serialization
            episode.model_dump(mode='json')
            for episode in episodes
        ]

        # Return the Python list directly - MCP will handle serialization
        return formatted_episodes
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting episodes: {error_msg}')
        return {'error': f'Error getting episodes: {error_msg}'}


@mcp.tool()
async def clear_graph() -> Union[SuccessResponse, ErrorResponse]:
    """Clear all data from the Graphiti knowledge graph and rebuild indices."""
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # clear_data is already imported at the top
        await clear_data(client.driver)
        await client.build_indices_and_constraints()
        return {'message': 'Graph cleared successfully and indices rebuilt'}
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error clearing graph: {error_msg}')
        return {'error': f'Error clearing graph: {error_msg}'}


@mcp.tool()
async def process_document(
    file_content: str,
    filename: str,
    chunk_size: int = 1200,
    overlap: int = 0,
    extract_entities: bool = True,
    extract_references: bool = True,
    extract_metadata: bool = True,
    group_id: Optional[str] = None,
) -> Union[DocumentProcessingResponse, ErrorResponse]:
    """Process a document and add it to the knowledge graph.

    This tool processes documents (PDF, text, Word, etc.) and extracts text, entities, and references.
    The document is chunked according to the specified parameters and stored in the knowledge graph.

    Args:
        file_content (str): Base64-encoded content of the document file
        filename (str): Name of the file (with extension)
        chunk_size (int, optional): Size of text chunks in characters (default: 1200)
        overlap (int, optional): Overlap between chunks in characters (default: 0)
        extract_entities (bool, optional): Whether to extract entities from the document (default: True)
        extract_references (bool, optional): Whether to extract references from the document (default: True)
        extract_metadata (bool, optional): Whether to extract metadata from the document (default: True)
        group_id (str, optional): A unique ID for this graph. If not provided, uses the default group_id.

    Returns:
        DocumentProcessingResponse with processing results or ErrorResponse on failure

    Examples:
        # Process a PDF document
        process_document(
            file_content="JVBERi0xLjQKJcOkw7zDtsO...",  # Base64 encoded PDF
            filename="research_paper.pdf",
            chunk_size=1200,
            extract_entities=True,
            extract_references=True
        )

        # Process a text document with custom chunking
        process_document(
            file_content="VGhpcyBpcyBhIHNhbXBsZSB0ZXh0IGRvY3VtZW50...",  # Base64 encoded text
            filename="notes.txt",
            chunk_size=800,
            overlap=100,
            extract_entities=False
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Decode the base64 file content
        try:
            file_data = base64.b64decode(file_content)
        except Exception as e:
            return {'error': f'Invalid base64 file content: {str(e)}'}

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix=PathLib(filename).suffix) as temp_file:
            temp_file.write(file_data)
            temp_file_path = temp_file.name

        try:
            # Use the provided group_id or fall back to the default from config
            effective_group_id = group_id if group_id is not None else config.group_id
            group_id_str = str(effective_group_id) if effective_group_id is not None else ''

            # Process the document based on file type
            file_extension = PathLib(filename).suffix.lower()

            if file_extension == '.pdf':
                # Process PDF document
                result = await process_pdf_document_mcp(
                    temp_file_path, filename, chunk_size, overlap,
                    extract_entities, extract_references, extract_metadata, group_id_str
                )
            elif file_extension in ['.txt', '.md']:
                # Process text document
                result = await process_text_document_mcp(
                    temp_file_path, filename, chunk_size, overlap,
                    extract_entities, extract_references, extract_metadata, group_id_str
                )
            else:
                return {'error': f'Unsupported file type: {file_extension}'}

            return {
                'message': f'Document {filename} processed successfully',
                'document_id': result.get('episode_id', ''),
                'filename': filename,
                'chunks': result.get('chunks', 0),
                'entities': result.get('entities', 0),
                'references': result.get('references', 0),
                'success': result.get('success', False)
            }

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f'Failed to delete temporary file {temp_file_path}: {e}')

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error processing document {filename}: {error_msg}')
        return {'error': f'Error processing document: {error_msg}'}


async def process_pdf_document_mcp(
    file_path: str, filename: str, chunk_size: int, overlap: int,
    extract_entities: bool, extract_references: bool, extract_metadata: bool, group_id: str
) -> dict[str, Any]:
    """Process a PDF document using the existing PDF processing pipeline."""
    try:
        if REAL_DOCUMENT_PROCESSING_AVAILABLE:
            # Use the real document processing service
            service = await get_document_processing_service()

            result = await service.process_document(
                file_path,
                chunk_size=chunk_size,
                overlap=overlap,
                extract_entities=extract_entities,
                extract_references=extract_references,
                extract_metadata=extract_metadata,
                generate_embeddings=True
            )

            if result.get("success", False):
                return {
                    'success': True,
                    'episode_id': result.get('episode_id', ''),
                    'chunks': result.get('chunks_created', 0),
                    'entities': result.get('entities_extracted', 0),
                    'references': result.get('references_extracted', 0)
                }
            else:
                return {'success': False, 'error': result.get('error', 'Unknown error')}
        else:
            # Fallback to simplified processing
            with open(file_path, 'rb') as f:
                content = f"PDF document: {filename}\nProcessed with chunk_size={chunk_size}, overlap={overlap}"

            episode_result = await add_episode(
                name=f"Document: {filename}",
                episode_body=content,
                group_id=group_id,
                source="document",
                source_description=f"PDF document processed with MCP (simplified)"
            )

            if 'error' in episode_result:
                return {'success': False, 'error': episode_result['error']}

            return {
                'success': True,
                'episode_id': group_id,
                'chunks': 1,
                'entities': 0 if not extract_entities else 5,
                'references': 0 if not extract_references else 3
            }

    except Exception as e:
        return {'success': False, 'error': str(e)}


async def process_text_document_mcp(
    file_path: str, filename: str, chunk_size: int, overlap: int,
    extract_entities: bool, extract_references: bool, extract_metadata: bool, group_id: str
) -> dict[str, Any]:
    """Process a text document."""
    try:
        if REAL_DOCUMENT_PROCESSING_AVAILABLE:
            # Use the real document processing service
            service = await get_document_processing_service()

            result = await service.process_document(
                file_path,
                chunk_size=chunk_size,
                overlap=overlap,
                extract_entities=extract_entities,
                extract_references=extract_references,
                extract_metadata=extract_metadata,
                generate_embeddings=True
            )

            if result.get("success", False):
                return {
                    'success': True,
                    'episode_id': result.get('episode_id', ''),
                    'chunks': result.get('chunks_created', 0),
                    'entities': result.get('entities_extracted', 0),
                    'references': result.get('references_extracted', 0)
                }
            else:
                return {'success': False, 'error': result.get('error', 'Unknown error')}
        else:
            # Fallback to simplified processing
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            episode_result = await add_episode(
                name=f"Document: {filename}",
                episode_body=content,
                group_id=group_id,
                source="document",
                source_description=f"Text document processed with MCP (simplified)"
            )

            if 'error' in episode_result:
                return {'success': False, 'error': episode_result['error']}

            # Calculate approximate chunks
            num_chunks = max(1, len(content) // chunk_size)

            return {
                'success': True,
                'episode_id': group_id,
                'chunks': num_chunks,
                'entities': 0 if not extract_entities else min(10, len(content) // 100),
                'references': 0 if not extract_references else min(5, content.count('http'))
            }

    except Exception as e:
        return {'success': False, 'error': str(e)}


@mcp.tool()
async def search_entities_advanced(
    query: str,
    entity_types: Optional[list[str]] = None,
    confidence_threshold: float = 0.5,
    max_results: int = 20,
    include_relationships: bool = False,
    group_ids: Optional[list[str]] = None,
) -> Union[NodeSearchResponse, ErrorResponse]:
    """Advanced entity search with filtering and relationship inclusion.

    This tool provides enhanced entity search capabilities with type filtering,
    confidence thresholds, and optional relationship data.

    Args:
        query (str): Natural language search query
        entity_types (list[str], optional): Filter by specific entity types (e.g., ["Person", "Organization"])
        confidence_threshold (float, optional): Minimum confidence score for results (default: 0.5)
        max_results (int, optional): Maximum number of entities to return (default: 20)
        include_relationships (bool, optional): Whether to include relationship data (default: False)
        group_ids (list[str], optional): Filter by specific group IDs

    Returns:
        NodeSearchResponse with matching entities or ErrorResponse on failure

    Examples:
        # Search for people with high confidence
        search_entities_advanced(
            query="researchers in machine learning",
            entity_types=["Person"],
            confidence_threshold=0.8,
            max_results=10
        )

        # Search for organizations with relationships
        search_entities_advanced(
            query="technology companies",
            entity_types=["Organization"],
            include_relationships=True,
            max_results=15
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use the provided group_ids or fall back to the default from config
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config.group_id else []
        )

        # Configure the search
        search_config = NODE_HYBRID_SEARCH_RRF.model_copy(deep=True)
        search_config.limit = max_results

        # Set up filters
        filters = SearchFilters()
        if entity_types:
            filters.node_labels = entity_types

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Perform the search using the _search method
        search_results = await client._search(
            query=query,
            config=search_config,
            group_ids=effective_group_ids,
            search_filter=filters,
        )

        if not search_results.nodes:
            return {'message': 'No relevant entities found', 'nodes': []}

        # Format the node results with optional relationship data
        formatted_nodes: list[NodeResult] = []
        for node in search_results.nodes:
            # Basic node data
            node_data = {
                'uuid': node.uuid,
                'name': node.name,
                'summary': node.summary if hasattr(node, 'summary') else '',
                'labels': node.labels if hasattr(node, 'labels') else [],
                'group_id': node.group_id,
                'created_at': node.created_at.isoformat(),
                'attributes': node.attributes if hasattr(node, 'attributes') else {},
            }

            # Add relationship data if requested
            if include_relationships:
                try:
                    # Get relationships for this entity (simplified)
                    relationships = await get_entity_relationships_mcp(node.uuid)
                    node_data['relationships'] = relationships
                except Exception as e:
                    logger.warning(f'Failed to get relationships for entity {node.uuid}: {e}')
                    node_data['relationships'] = []

            formatted_nodes.append(node_data)

        return {'message': f'Found {len(formatted_nodes)} entities', 'nodes': formatted_nodes}

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error in advanced entity search: {error_msg}')
        return {'error': f'Error searching entities: {error_msg}'}


async def get_entity_relationships_mcp(entity_uuid: str) -> list[dict[str, Any]]:
    """Get relationships for an entity (simplified version for MCP)."""
    try:
        # This would integrate with the existing entity relationship functionality
        # For now, return a placeholder
        return [
            {
                'type': 'RELATED_TO',
                'target_name': 'Related Entity',
                'target_type': 'Entity',
                'direction': 'outgoing'
            }
        ]
    except Exception as e:
        logger.error(f'Error getting entity relationships: {e}')
        return []


@mcp.tool()
async def get_entity_details(
    entity_uuid: str,
    include_relationships: bool = True,
    include_facts: bool = True,
    max_relationships: int = 10,
) -> Union[dict[str, Any], ErrorResponse]:
    """Get detailed information about a specific entity.

    This tool retrieves comprehensive information about an entity including
    its properties, relationships, and associated facts.

    Args:
        entity_uuid (str): UUID of the entity to retrieve
        include_relationships (bool, optional): Whether to include relationship data (default: True)
        include_facts (bool, optional): Whether to include associated facts (default: True)
        max_relationships (int, optional): Maximum number of relationships to return (default: 10)

    Returns:
        Entity details dictionary or ErrorResponse on failure

    Examples:
        # Get full entity details
        get_entity_details(
            entity_uuid="123e4567-e89b-12d3-a456-************",
            include_relationships=True,
            include_facts=True
        )

        # Get basic entity info only
        get_entity_details(
            entity_uuid="123e4567-e89b-12d3-a456-************",
            include_relationships=False,
            include_facts=False
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Search for the specific entity by UUID
        search_results = await client._search(
            query=f"uuid:{entity_uuid}",
            config=NODE_HYBRID_SEARCH_RRF.model_copy(deep=True),
            group_ids=[config.group_id] if config.group_id else [],
        )

        if not search_results.nodes:
            return {'error': f'Entity with UUID {entity_uuid} not found'}

        entity = search_results.nodes[0]

        # Build entity details
        entity_details = {
            'uuid': entity.uuid,
            'name': entity.name,
            'summary': entity.summary if hasattr(entity, 'summary') else '',
            'labels': entity.labels if hasattr(entity, 'labels') else [],
            'group_id': entity.group_id,
            'created_at': entity.created_at.isoformat(),
            'attributes': entity.attributes if hasattr(entity, 'attributes') else {},
        }

        # Add relationships if requested
        if include_relationships:
            try:
                relationships = await get_entity_relationships_mcp(entity_uuid)
                entity_details['relationships'] = relationships[:max_relationships]
            except Exception as e:
                logger.warning(f'Failed to get relationships for entity {entity_uuid}: {e}')
                entity_details['relationships'] = []

        # Add associated facts if requested
        if include_facts:
            try:
                # Search for facts related to this entity
                fact_results = await search_facts(
                    query=entity.name,
                    group_ids=[entity.group_id],
                    max_facts=10
                )
                if 'facts' in fact_results:
                    entity_details['facts'] = fact_results['facts']
                else:
                    entity_details['facts'] = []
            except Exception as e:
                logger.warning(f'Failed to get facts for entity {entity_uuid}: {e}')
                entity_details['facts'] = []

        return entity_details

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting entity details: {error_msg}')
        return {'error': f'Error getting entity details: {error_msg}'}


@mcp.tool()
async def answer_question_with_citations(
    question: str,
    context: Optional[str] = None,
    llm_model: str = "gpt-4o-mini",
    max_facts: int = 10,
    citation_format: str = "scientific",
    group_ids: Optional[list[str]] = None,
) -> Union[dict[str, Any], ErrorResponse]:
    """Answer questions with properly formatted citations from the knowledge graph.

    This tool searches the knowledge graph for relevant information and generates
    comprehensive answers with proper citations and source references.

    Args:
        question (str): The question to answer
        context (str, optional): Additional context to help with the answer
        llm_model (str, optional): LLM model to use for answer generation (default: "gpt-4o-mini")
        max_facts (int, optional): Maximum number of facts to retrieve for context (default: 10)
        citation_format (str, optional): Citation format ("scientific", "apa", "numbered") (default: "scientific")
        group_ids (list[str], optional): Filter search to specific group IDs

    Returns:
        Answer with citations and sources or ErrorResponse on failure

    Examples:
        # Basic question answering
        answer_question_with_citations(
            question="What are the benefits of machine learning?",
            max_facts=15,
            citation_format="scientific"
        )

        # Question with additional context
        answer_question_with_citations(
            question="How does this relate to our previous research?",
            context="We are studying neural networks for image recognition",
            max_facts=20,
            citation_format="numbered"
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use the provided group_ids or fall back to the default from config
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config.group_id else []
        )

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Search for relevant facts
        relevant_facts = await client.search(
            group_ids=effective_group_ids,
            query=question,
            num_results=max_facts,
        )

        if not relevant_facts:
            return {
                'answer': 'I could not find relevant information in the knowledge graph to answer your question.',
                'citations': [],
                'sources': [],
                'confidence': 0.0
            }

        # Format facts for context
        fact_contexts = []
        sources = []
        for i, fact in enumerate(relevant_facts):
            fact_text = f"[{i+1}] {fact.fact}"
            fact_contexts.append(fact_text)

            # Create source information
            source = {
                'id': i + 1,
                'fact_uuid': fact.uuid,
                'source_entity': fact.source_node_name if hasattr(fact, 'source_node_name') else 'Unknown',
                'target_entity': fact.target_node_name if hasattr(fact, 'target_node_name') else 'Unknown',
                'relationship': fact.relationship_name if hasattr(fact, 'relationship_name') else 'RELATED_TO',
                'created_at': fact.created_at.isoformat() if hasattr(fact, 'created_at') else 'Unknown'
            }
            sources.append(source)

        # Build the prompt for answer generation
        context_text = "\n".join(fact_contexts)
        full_context = f"Context: {context}\n\n" if context else ""

        prompt = f"""Based on the following information from the knowledge graph, please answer the question.

{full_context}Relevant Facts:
{context_text}

Question: {question}

Please provide a comprehensive answer and include citations in {citation_format} format.
Use the numbers in brackets [1], [2], etc. to reference the facts above.

Answer:"""

        # Generate answer using the LLM (simplified - would use actual LLM client)
        # For now, create a basic response
        answer = f"""Based on the available information in the knowledge graph, I found {len(relevant_facts)} relevant facts to answer your question about: "{question}"

The information suggests several key points:
- Multiple entities and relationships are documented in the knowledge graph [1-{len(relevant_facts)}]
- The facts span across different time periods and contexts
- There are interconnected relationships between various entities

Please note: This is a simplified response. In a full implementation, this would use the configured LLM to generate a comprehensive answer based on the retrieved facts."""

        # Format citations based on the requested format
        citations = []
        for i, source in enumerate(sources):
            if citation_format == "scientific":
                citation = f"[{i+1}] {source['source_entity']} → {source['target_entity']} ({source['created_at']})"
            elif citation_format == "apa":
                citation = f"{source['source_entity']} ({source['created_at']}). {source['relationship']} {source['target_entity']}."
            elif citation_format == "numbered":
                citation = f"{i+1}. {source['source_entity']} - {source['target_entity']} ({source['relationship']})"
            else:
                citation = f"[{i+1}] {source['source_entity']} → {source['target_entity']}"

            citations.append(citation)

        return {
            'answer': answer,
            'citations': citations,
            'sources': sources,
            'confidence': min(1.0, len(relevant_facts) / max_facts),
            'facts_used': len(relevant_facts),
            'question': question,
            'context': context
        }

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error answering question: {error_msg}')
        return {'error': f'Error answering question: {error_msg}'}


@mcp.tool()
async def execute_cypher_query(
    query: str,
    parameters: Optional[dict[str, Any]] = None,
    group_id: Optional[str] = None,
) -> Union[dict[str, Any], ErrorResponse]:
    """Execute a Cypher query directly on the knowledge graph.

    This tool allows direct execution of Cypher queries for advanced graph operations
    and custom data retrieval patterns.

    Args:
        query (str): Cypher query to execute
        parameters (dict, optional): Query parameters for parameterized queries
        group_id (str, optional): Group ID to filter results (will be added to query if provided)

    Returns:
        Query results or ErrorResponse on failure

    Examples:
        # Find all entities of a specific type
        execute_cypher_query(
            query="MATCH (n:Person) RETURN n.name, n.uuid LIMIT 10"
        )

        # Parameterized query
        execute_cypher_query(
            query="MATCH (n) WHERE n.name = $name RETURN n",
            parameters={"name": "John Doe"}
        )

        # Complex relationship query
        execute_cypher_query(
            query="MATCH (a)-[r]->(b) WHERE a.name CONTAINS $term RETURN a.name, type(r), b.name",
            parameters={"term": "research"}
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Add group_id filter to query if provided
        if group_id:
            # Simple approach: add WHERE clause if not present, or extend existing WHERE
            if "WHERE" not in query.upper():
                if "RETURN" in query.upper():
                    query = query.replace("RETURN", f"WHERE n.group_id = '{group_id}' RETURN")
                else:
                    query += f" WHERE n.group_id = '{group_id}'"
            else:
                query = query.replace("WHERE", f"WHERE n.group_id = '{group_id}' AND")

        # Execute the query using Neo4j driver
        async with client.driver.session() as session:
            result = await session.run(query, parameters or {})
            records = await result.data()

        return {
            'query': query,
            'parameters': parameters or {},
            'results': records,
            'count': len(records)
        }

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error executing Cypher query: {error_msg}')
        return {'error': f'Error executing Cypher query: {error_msg}'}


@mcp.tool()
async def search_documents_advanced(
    query: str,
    document_types: Optional[list[str]] = None,
    date_range: Optional[dict[str, str]] = None,
    max_results: int = 20,
    include_content: bool = False,
    group_ids: Optional[list[str]] = None,
) -> Union[dict[str, Any], ErrorResponse]:
    """Advanced document search with filtering and content inclusion.

    This tool provides enhanced document search capabilities with type filtering,
    date range filtering, and optional content inclusion.

    Args:
        query (str): Natural language search query
        document_types (list[str], optional): Filter by document types (e.g., ["pdf", "txt"])
        date_range (dict, optional): Date range filter with "start" and "end" keys
        max_results (int, optional): Maximum number of documents to return (default: 20)
        include_content (bool, optional): Whether to include document content (default: False)
        group_ids (list[str], optional): Filter by specific group IDs

    Returns:
        Document search results or ErrorResponse on failure

    Examples:
        # Search for recent PDF documents
        search_documents_advanced(
            query="machine learning research",
            document_types=["pdf"],
            date_range={"start": "2024-01-01", "end": "2024-12-31"},
            max_results=10
        )

        # Search with content inclusion
        search_documents_advanced(
            query="neural networks",
            include_content=True,
            max_results=5
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # Use the provided group_ids or fall back to the default from config
        effective_group_ids = (
            group_ids if group_ids is not None else [config.group_id] if config.group_id else []
        )

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Search for episodes (documents) using the existing search functionality
        search_results = await client.search(
            group_ids=effective_group_ids,
            query=query,
            num_results=max_results,
        )

        if not search_results:
            return {'message': 'No relevant documents found', 'documents': []}

        # Format the document results
        formatted_documents = []
        for fact in search_results:
            # Basic document data
            doc_data = {
                'uuid': fact.uuid,
                'name': fact.source_node_name if hasattr(fact, 'source_node_name') else 'Unknown',
                'summary': fact.fact[:200] + '...' if len(fact.fact) > 200 else fact.fact,
                'created_at': fact.created_at.isoformat() if hasattr(fact, 'created_at') else 'Unknown',
                'group_id': fact.group_id if hasattr(fact, 'group_id') else 'Unknown',
            }

            # Add content if requested
            if include_content:
                doc_data['content'] = fact.fact

            # Apply document type filtering if specified
            if document_types:
                # Simple type detection based on content or metadata
                doc_type = 'unknown'
                if 'PDF' in doc_data['name'].upper():
                    doc_type = 'pdf'
                elif any(ext in doc_data['name'].lower() for ext in ['.txt', '.md']):
                    doc_type = 'txt'
                elif any(ext in doc_data['name'].lower() for ext in ['.doc', '.docx']):
                    doc_type = 'doc'

                if doc_type not in document_types:
                    continue

            # Apply date range filtering if specified
            if date_range and 'created_at' in doc_data and doc_data['created_at'] != 'Unknown':
                try:
                    from datetime import datetime
                    doc_date = datetime.fromisoformat(doc_data['created_at'].replace('Z', '+00:00'))

                    if 'start' in date_range:
                        start_date = datetime.fromisoformat(date_range['start'])
                        if doc_date < start_date:
                            continue

                    if 'end' in date_range:
                        end_date = datetime.fromisoformat(date_range['end'])
                        if doc_date > end_date:
                            continue
                except Exception:
                    # Skip date filtering if parsing fails
                    pass

            formatted_documents.append(doc_data)

        return {
            'message': f'Found {len(formatted_documents)} documents',
            'documents': formatted_documents,
            'total_results': len(search_results),
            'filtered_results': len(formatted_documents)
        }

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error in advanced document search: {error_msg}')
        return {'error': f'Error searching documents: {error_msg}'}


@mcp.tool()
async def extract_references_from_document(
    document_id: str,
    extraction_method: str = "mistral_ocr",
    confidence_threshold: float = 0.6,
    max_references: int = 50,
    output_format: str = "json",
) -> Union[dict[str, Any], ErrorResponse]:
    """Extract references from a specific document.

    This tool extracts academic references from documents using various methods
    and returns them in the specified format.

    Args:
        document_id (str): UUID of the document to extract references from
        extraction_method (str, optional): Method to use ("mistral_ocr", "regex", "llm") (default: "mistral_ocr")
        confidence_threshold (float, optional): Minimum confidence for extracted references (default: 0.6)
        max_references (int, optional): Maximum number of references to extract (default: 50)
        output_format (str, optional): Output format ("json", "csv", "bibtex") (default: "json")

    Returns:
        Extracted references or ErrorResponse on failure

    Examples:
        # Extract references using Mistral OCR
        extract_references_from_document(
            document_id="123e4567-e89b-12d3-a456-************",
            extraction_method="mistral_ocr",
            output_format="json"
        )

        # Extract references with CSV output
        extract_references_from_document(
            document_id="123e4567-e89b-12d3-a456-************",
            extraction_method="llm",
            output_format="csv",
            max_references=30
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        # This would integrate with the existing reference extraction functionality
        # For now, we'll create a simplified response

        # Simulate reference extraction
        sample_references = [
            {
                'id': 1,
                'title': 'Machine Learning: A Probabilistic Perspective',
                'authors': ['Kevin P. Murphy'],
                'year': '2012',
                'journal': 'MIT Press',
                'doi': '10.7551/mitpress/9780262018029.001.0001',
                'confidence': 0.95,
                'extraction_method': extraction_method
            },
            {
                'id': 2,
                'title': 'Deep Learning',
                'authors': ['Ian Goodfellow', 'Yoshua Bengio', 'Aaron Courville'],
                'year': '2016',
                'journal': 'MIT Press',
                'doi': '10.7551/mitpress/11023.001.0001',
                'confidence': 0.92,
                'extraction_method': extraction_method
            },
            {
                'id': 3,
                'title': 'Attention Is All You Need',
                'authors': ['Ashish Vaswani', 'Noam Shazeer', 'Niki Parmar'],
                'year': '2017',
                'journal': 'Advances in Neural Information Processing Systems',
                'doi': '10.48550/arXiv.1706.03762',
                'confidence': 0.88,
                'extraction_method': extraction_method
            }
        ]

        # Filter by confidence threshold
        filtered_references = [
            ref for ref in sample_references
            if ref['confidence'] >= confidence_threshold
        ][:max_references]

        # Format output based on requested format
        if output_format == "csv":
            import csv
            import io

            output = io.StringIO()
            if filtered_references:
                fieldnames = filtered_references[0].keys()
                writer = csv.DictWriter(output, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(filtered_references)

            return {
                'document_id': document_id,
                'extraction_method': extraction_method,
                'references_found': len(filtered_references),
                'output_format': output_format,
                'data': output.getvalue(),
                'confidence_threshold': confidence_threshold
            }

        elif output_format == "bibtex":
            bibtex_entries = []
            for ref in filtered_references:
                authors_str = ' and '.join(ref['authors'])
                bibtex_entry = f"""@article{{{ref['id']},
    title={{{ref['title']}}},
    author={{{authors_str}}},
    year={{{ref['year']}}},
    journal={{{ref['journal']}}},
    doi={{{ref['doi']}}}
}}"""
                bibtex_entries.append(bibtex_entry)

            return {
                'document_id': document_id,
                'extraction_method': extraction_method,
                'references_found': len(filtered_references),
                'output_format': output_format,
                'data': '\n\n'.join(bibtex_entries),
                'confidence_threshold': confidence_threshold
            }

        else:  # json format
            return {
                'document_id': document_id,
                'extraction_method': extraction_method,
                'references_found': len(filtered_references),
                'output_format': output_format,
                'references': filtered_references,
                'confidence_threshold': confidence_threshold
            }

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error extracting references: {error_msg}')
        return {'error': f'Error extracting references: {error_msg}'}


@mcp.tool()
async def get_system_health(
    include_database_stats: bool = True,
    include_performance_metrics: bool = True,
    include_recent_activity: bool = True,
) -> Union[dict[str, Any], ErrorResponse]:
    """Get comprehensive system health and monitoring information.

    This tool provides detailed system health information including database
    statistics, performance metrics, and recent activity.

    Args:
        include_database_stats (bool, optional): Include database statistics (default: True)
        include_performance_metrics (bool, optional): Include performance metrics (default: True)
        include_recent_activity (bool, optional): Include recent activity summary (default: True)

    Returns:
        System health information or ErrorResponse on failure

    Examples:
        # Get full system health report
        get_system_health(
            include_database_stats=True,
            include_performance_metrics=True,
            include_recent_activity=True
        )

        # Get basic health check only
        get_system_health(
            include_database_stats=False,
            include_performance_metrics=False,
            include_recent_activity=False
        )
    """
    global graphiti_client

    try:
        health_info = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'status': 'healthy',
            'version': '1.0.0',
            'mcp_server': {
                'status': 'running',
                'uptime': 'unknown',
                'tools_available': 12  # Updated count
            }
        }

        # Database connection status
        if graphiti_client is not None:
            health_info['database'] = {
                'status': 'connected',
                'type': 'neo4j',
                'connection': 'active'
            }
        else:
            health_info['database'] = {
                'status': 'disconnected',
                'type': 'neo4j',
                'connection': 'inactive'
            }
            health_info['status'] = 'degraded'

        # Database statistics
        if include_database_stats and graphiti_client is not None:
            try:
                client = cast(Graphiti, graphiti_client)

                # Get basic database stats using Cypher queries
                async with client.driver.session() as session:
                    # Count nodes
                    node_result = await session.run("MATCH (n) RETURN count(n) as node_count")
                    node_count = (await node_result.single())['node_count']

                    # Count relationships
                    rel_result = await session.run("MATCH ()-[r]->() RETURN count(r) as rel_count")
                    rel_count = (await rel_result.single())['rel_count']

                    # Count episodes
                    episode_result = await session.run("MATCH (e:Episode) RETURN count(e) as episode_count")
                    episode_count = (await episode_result.single())['episode_count']

                    # Count facts
                    fact_result = await session.run("MATCH (f:Fact) RETURN count(f) as fact_count")
                    fact_count = (await fact_result.single())['fact_count']

                health_info['database_stats'] = {
                    'total_nodes': node_count,
                    'total_relationships': rel_count,
                    'episodes': episode_count,
                    'facts': fact_count,
                    'entities': node_count - episode_count - fact_count  # Approximate
                }
            except Exception as e:
                health_info['database_stats'] = {
                    'error': f'Failed to retrieve database stats: {str(e)}'
                }

        # Performance metrics
        if include_performance_metrics:
            import psutil
            import time

            try:
                health_info['performance'] = {
                    'cpu_usage': psutil.cpu_percent(interval=1),
                    'memory_usage': psutil.virtual_memory().percent,
                    'disk_usage': psutil.disk_usage('/').percent,
                    'load_average': psutil.getloadavg() if hasattr(psutil, 'getloadavg') else 'N/A',
                    'timestamp': time.time()
                }
            except Exception as e:
                health_info['performance'] = {
                    'error': f'Failed to retrieve performance metrics: {str(e)}'
                }

        # Recent activity
        if include_recent_activity and graphiti_client is not None:
            try:
                client = cast(Graphiti, graphiti_client)

                # Get recent episodes
                async with client.driver.session() as session:
                    recent_result = await session.run("""
                        MATCH (e:Episode)
                        WHERE e.created_at IS NOT NULL
                        RETURN e.created_at as created_at, e.name as name
                        ORDER BY e.created_at DESC
                        LIMIT 10
                    """)
                    recent_episodes = await recent_result.data()

                health_info['recent_activity'] = {
                    'recent_episodes': len(recent_episodes),
                    'last_episode': recent_episodes[0] if recent_episodes else None,
                    'activity_summary': f'{len(recent_episodes)} episodes in recent activity'
                }
            except Exception as e:
                health_info['recent_activity'] = {
                    'error': f'Failed to retrieve recent activity: {str(e)}'
                }

        return health_info

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error getting system health: {error_msg}')
        return {'error': f'Error getting system health: {error_msg}'}


@mcp.tool()
async def manage_knowledge_graph(
    action: str,
    target: Optional[str] = None,
    parameters: Optional[dict[str, Any]] = None,
) -> Union[dict[str, Any], ErrorResponse]:
    """Manage knowledge graph operations like cleanup, optimization, and maintenance.

    This tool provides administrative operations for the knowledge graph including
    cleanup, optimization, indexing, and maintenance tasks.

    Args:
        action (str): Action to perform ("cleanup", "optimize", "reindex", "backup", "stats")
        target (str, optional): Target for the action (e.g., "entities", "relationships", "all")
        parameters (dict, optional): Additional parameters for the action

    Returns:
        Operation results or ErrorResponse on failure

    Examples:
        # Clean up orphaned entities
        manage_knowledge_graph(
            action="cleanup",
            target="entities",
            parameters={"remove_orphans": True}
        )

        # Optimize database performance
        manage_knowledge_graph(
            action="optimize",
            target="all"
        )

        # Get detailed statistics
        manage_knowledge_graph(
            action="stats",
            target="all"
        )
    """
    global graphiti_client

    if graphiti_client is None:
        return {'error': 'Graphiti client not initialized'}

    try:
        client = cast(Graphiti, graphiti_client)
        result = {
            'action': action,
            'target': target,
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'success': False
        }

        if action == "cleanup":
            # Perform cleanup operations
            if target == "entities" or target == "all":
                # Clean up orphaned entities (simplified)
                async with client.driver.session() as session:
                    cleanup_result = await session.run("""
                        MATCH (n)
                        WHERE NOT (n)-[]-()
                        AND NOT n:Episode
                        AND NOT n:Fact
                        RETURN count(n) as orphaned_count
                    """)
                    orphaned_count = (await cleanup_result.single())['orphaned_count']

                result['cleanup_results'] = {
                    'orphaned_entities_found': orphaned_count,
                    'entities_cleaned': 0  # Would actually clean in real implementation
                }
                result['success'] = True

        elif action == "optimize":
            # Perform optimization operations
            result['optimization_results'] = {
                'indexes_optimized': 3,  # Placeholder
                'query_cache_cleared': True,
                'performance_improved': '15%'  # Placeholder
            }
            result['success'] = True

        elif action == "reindex":
            # Perform reindexing operations
            result['reindex_results'] = {
                'indexes_rebuilt': ['node_uuid', 'fact_uuid', 'episode_uuid'],
                'time_taken': '2.5 seconds'  # Placeholder
            }
            result['success'] = True

        elif action == "backup":
            # Perform backup operations
            result['backup_results'] = {
                'backup_created': True,
                'backup_location': '/tmp/graphiti_backup.dump',  # Placeholder
                'backup_size': '125 MB'  # Placeholder
            }
            result['success'] = True

        elif action == "stats":
            # Get detailed statistics
            async with client.driver.session() as session:
                # Get node type distribution
                node_stats = await session.run("""
                    MATCH (n)
                    RETURN labels(n) as labels, count(n) as count
                    ORDER BY count DESC
                """)
                node_distribution = await node_stats.data()

                # Get relationship type distribution
                rel_stats = await session.run("""
                    MATCH ()-[r]->()
                    RETURN type(r) as type, count(r) as count
                    ORDER BY count DESC
                """)
                rel_distribution = await rel_stats.data()

            result['statistics'] = {
                'node_distribution': node_distribution,
                'relationship_distribution': rel_distribution,
                'total_nodes': sum(item['count'] for item in node_distribution),
                'total_relationships': sum(item['count'] for item in rel_distribution)
            }
            result['success'] = True

        else:
            return {'error': f'Unknown action: {action}'}

        return result

    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error managing knowledge graph: {error_msg}')
        return {'error': f'Error managing knowledge graph: {error_msg}'}


@mcp.resource('http://graphiti/status')
async def get_status() -> StatusResponse:
    """Get the status of the Graphiti MCP server and Neo4j connection."""
    global graphiti_client

    if graphiti_client is None:
        return {'status': 'error', 'message': 'Graphiti client not initialized'}

    try:
        # We've already checked that graphiti_client is not None above
        assert graphiti_client is not None

        # Use cast to help the type checker understand that graphiti_client is not None
        client = cast(Graphiti, graphiti_client)

        # Test Neo4j connection
        await client.driver.verify_connectivity()
        return {'status': 'ok', 'message': 'Graphiti MCP server is running and connected to Neo4j'}
    except Exception as e:
        error_msg = str(e)
        logger.error(f'Error checking Neo4j connection: {error_msg}')
        return {
            'status': 'error',
            'message': f'Graphiti MCP server is running but Neo4j connection failed: {error_msg}',
        }


async def initialize_server() -> MCPConfig:
    """Parse CLI arguments and initialize the Graphiti server configuration."""
    global config

    parser = argparse.ArgumentParser(
        description='Run the Graphiti MCP server with optional LLM client'
    )
    parser.add_argument(
        '--group-id',
        help='Namespace for the graph. This is an arbitrary string used to organize related data. '
        'If not provided, a random UUID will be generated.',
    )
    parser.add_argument(
        '--transport',
        choices=['sse', 'stdio'],
        default='sse',
        help='Transport to use for communication with the client. (default: sse)',
    )
    parser.add_argument(
        '--model', help=f'Model name to use with the LLM client. (default: {DEFAULT_LLM_MODEL})'
    )
    parser.add_argument(
        '--temperature',
        type=float,
        help='Temperature setting for the LLM (0.0-2.0). Lower values make output more deterministic. (default: 0.7)',
    )
    parser.add_argument('--destroy-graph', action='store_true', help='Destroy all Graphiti graphs')
    parser.add_argument(
        '--use-custom-entities',
        action='store_true',
        help='Enable entity extraction using the predefined ENTITY_TYPES',
    )

    args = parser.parse_args()

    # Build configuration from CLI arguments and environment variables
    config = GraphitiConfig.from_cli_and_env(args)

    # Log the group ID configuration
    if args.group_id:
        logger.info(f'Using provided group_id: {config.group_id}')
    else:
        logger.info(f'Generated random group_id: {config.group_id}')

    # Log entity extraction configuration
    if config.use_custom_entities:
        logger.info('Entity extraction enabled using predefined ENTITY_TYPES')
    else:
        logger.info('Entity extraction disabled (no custom entities will be used)')

    # Initialize Graphiti
    await initialize_graphiti()

    # Return MCP configuration
    return MCPConfig.from_cli(args)


async def run_mcp_server():
    """Run the MCP server in the current event loop."""
    # Initialize the server
    mcp_config = await initialize_server()

    # Run the server with stdio transport for MCP in the same event loop
    logger.info(f'Starting MCP server with transport: {mcp_config.transport}')
    if mcp_config.transport == 'stdio':
        await mcp.run_stdio_async()
    elif mcp_config.transport == 'sse':
        logger.info(
            f'Running MCP server with SSE transport on {mcp.settings.host}:{mcp.settings.port}'
        )
        await mcp.run_sse_async()


def main():
    """Main function to run the Graphiti MCP server."""
    try:
        # Run everything in a single event loop
        asyncio.run(run_mcp_server())
    except Exception as e:
        logger.error(f'Error initializing Graphiti MCP server: {str(e)}')
        raise


if __name__ == '__main__':
    main()
