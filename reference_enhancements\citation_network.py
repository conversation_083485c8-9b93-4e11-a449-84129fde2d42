"""
Citation Network Analysis Module

Builds and analyzes citation networks from references in the knowledge graph.
"""

import asyncio
import logging
import json
from typing import List, Dict, Any, Tu<PERSON>, Optional
from pathlib import Path
from datetime import datetime
import re

# Network analysis imports
try:
    import networkx as nx
    import matplotlib.pyplot as plt
    import matplotlib
    matplotlib.use('Agg')  # Use non-interactive backend
    NETWORKX_AVAILABLE = True
except ImportError:
    NETWORKX_AVAILABLE = False

# Add the parent directory to the path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.falkordb_adapter import FalkorDBAdapter

logger = logging.getLogger(__name__)

class CitationNetworkAnalyzer:
    """
    Builds and analyzes citation networks from references.
    """
    
    def __init__(self):
        """Initialize the citation network analyzer."""
        self.db = FalkorDBAdapter()
        self.network = None
        
        if not NETWORKX_AVAILABLE:
            logger.warning("⚠️ NetworkX not available. Some features will be disabled.")
    
    async def build_citation_network(self) -> Dict[str, Any]:
        """
        Build a citation network from references in the knowledge graph.
        
        Returns:
            Dictionary with network statistics and data
        """
        logger.info("🕸️ Building citation network from references")
        
        try:
            # Get all references and documents
            references = await self._get_all_references()
            documents = await self._get_all_documents()
            
            logger.info(f"📚 Found {len(references)} references from {len(documents)} documents")
            
            if not NETWORKX_AVAILABLE:
                return await self._build_simple_network(references, documents)
            
            # Create NetworkX graph
            self.network = nx.DiGraph()
            
            # Add document nodes
            for doc in documents:
                self.network.add_node(doc['name'], 
                                    type='document',
                                    title=doc.get('title', doc['name']),
                                    reference_count=doc.get('reference_count', 0))
            
            # Add reference nodes and citation edges
            citations_created = 0
            
            for ref in references:
                ref_id = f"ref_{ref['node_id']}"
                source_doc = ref.get('source_document', '')
                
                # Add reference node
                self.network.add_node(ref_id,
                                    type='reference',
                                    title=ref.get('title', ''),
                                    authors=ref.get('authors', ''),
                                    year=ref.get('year', ''),
                                    journal=ref.get('journal', ''))
                
                # Create edge from source document to reference
                if source_doc and source_doc in [d['name'] for d in documents]:
                    self.network.add_edge(source_doc, ref_id, type='contains_reference')
                
                # Try to find citation relationships
                cited_docs = await self._find_cited_documents(ref)
                for cited_doc in cited_docs:
                    if cited_doc in [d['name'] for d in documents]:
                        self.network.add_edge(source_doc, cited_doc, type='cites')
                        citations_created += 1
            
            # Calculate network statistics
            stats = await self._calculate_network_statistics()
            
            logger.info(f"✅ Built citation network with {self.network.number_of_nodes()} nodes and {self.network.number_of_edges()} edges")
            logger.info(f"🔗 Created {citations_created} citation relationships")
            
            return {
                "success": True,
                "network_statistics": stats,
                "nodes": self.network.number_of_nodes(),
                "edges": self.network.number_of_edges(),
                "citations_created": citations_created,
                "documents": len(documents),
                "references": len(references)
            }
            
        except Exception as e:
            logger.error(f"❌ Error building citation network: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "network_statistics": {},
                "nodes": 0,
                "edges": 0
            }
    
    async def get_most_cited_documents(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get the most cited documents using PageRank or simple citation counting.
        
        Args:
            limit: Maximum number of documents to return
            
        Returns:
            List of most cited documents
        """
        logger.info(f"📊 Finding top {limit} most cited documents")
        
        try:
            if not self.network and NETWORKX_AVAILABLE:
                await self.build_citation_network()
            
            if NETWORKX_AVAILABLE and self.network:
                # Use PageRank for sophisticated ranking
                pagerank_scores = nx.pagerank(self.network)
                
                # Filter to document nodes only
                doc_scores = {node: score for node, score in pagerank_scores.items() 
                            if self.network.nodes[node].get('type') == 'document'}
                
                # Sort by PageRank score
                sorted_docs = sorted(doc_scores.items(), key=lambda x: x[1], reverse=True)[:limit]
                
                most_cited = []
                for doc_name, score in sorted_docs:
                    node_data = self.network.nodes[doc_name]
                    most_cited.append({
                        'document': doc_name,
                        'title': node_data.get('title', doc_name),
                        'pagerank_score': score,
                        'citation_count': len([edge for edge in self.network.in_edges(doc_name) 
                                             if self.network.edges[edge].get('type') == 'cites']),
                        'reference_count': node_data.get('reference_count', 0)
                    })
                
            else:
                # Fallback: simple citation counting
                most_cited = await self._get_most_cited_simple(limit)
            
            logger.info(f"✅ Found {len(most_cited)} most cited documents")
            return most_cited
            
        except Exception as e:
            logger.error(f"❌ Error getting most cited documents: {e}", exc_info=True)
            return []
    
    async def find_citation_path(self, source_doc: str, target_doc: str) -> Dict[str, Any]:
        """
        Find citation path between two documents.
        
        Args:
            source_doc: Source document name
            target_doc: Target document name
            
        Returns:
            Dictionary with path information
        """
        logger.info(f"🔍 Finding citation path from '{source_doc}' to '{target_doc}'")
        
        try:
            if not self.network and NETWORKX_AVAILABLE:
                await self.build_citation_network()
            
            if not NETWORKX_AVAILABLE or not self.network:
                return {
                    "success": False,
                    "error": "NetworkX not available for path analysis",
                    "path": []
                }
            
            # Check if both documents exist in the network
            if source_doc not in self.network.nodes or target_doc not in self.network.nodes:
                return {
                    "success": False,
                    "error": "One or both documents not found in citation network",
                    "path": []
                }
            
            try:
                # Find shortest path
                path = nx.shortest_path(self.network, source_doc, target_doc)
                path_length = len(path) - 1
                
                # Get path details
                path_details = []
                for i in range(len(path) - 1):
                    edge_data = self.network.edges[path[i], path[i+1]]
                    path_details.append({
                        'from': path[i],
                        'to': path[i+1],
                        'relationship': edge_data.get('type', 'unknown')
                    })
                
                logger.info(f"✅ Found citation path of length {path_length}")
                
                return {
                    "success": True,
                    "path": path,
                    "path_length": path_length,
                    "path_details": path_details
                }
                
            except nx.NetworkXNoPath:
                return {
                    "success": True,
                    "path": [],
                    "path_length": -1,
                    "message": "No citation path found between documents"
                }
            
        except Exception as e:
            logger.error(f"❌ Error finding citation path: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "path": []
            }
    
    def save_network_visualization(self, output_path: str, figsize: Tuple[int, int] = (12, 8)) -> bool:
        """
        Save network visualization to file.
        
        Args:
            output_path: Path to save the visualization
            figsize: Figure size (width, height)
            
        Returns:
            True if successful, False otherwise
        """
        if not NETWORKX_AVAILABLE or not self.network:
            logger.warning("⚠️ Cannot create visualization: NetworkX not available or network not built")
            return False
        
        try:
            plt.figure(figsize=figsize)
            
            # Create layout
            pos = nx.spring_layout(self.network, k=1, iterations=50)
            
            # Separate document and reference nodes
            doc_nodes = [node for node, data in self.network.nodes(data=True) if data.get('type') == 'document']
            ref_nodes = [node for node, data in self.network.nodes(data=True) if data.get('type') == 'reference']
            
            # Draw nodes
            nx.draw_networkx_nodes(self.network, pos, nodelist=doc_nodes, 
                                 node_color='lightblue', node_size=300, alpha=0.8, label='Documents')
            nx.draw_networkx_nodes(self.network, pos, nodelist=ref_nodes, 
                                 node_color='lightcoral', node_size=100, alpha=0.6, label='References')
            
            # Draw edges
            citation_edges = [(u, v) for u, v, d in self.network.edges(data=True) if d.get('type') == 'cites']
            reference_edges = [(u, v) for u, v, d in self.network.edges(data=True) if d.get('type') == 'contains_reference']
            
            nx.draw_networkx_edges(self.network, pos, edgelist=citation_edges, 
                                 edge_color='red', alpha=0.6, arrows=True, arrowsize=20, label='Citations')
            nx.draw_networkx_edges(self.network, pos, edgelist=reference_edges, 
                                 edge_color='gray', alpha=0.3, arrows=True, arrowsize=10)
            
            # Add labels for document nodes only
            doc_labels = {node: node[:15] + '...' if len(node) > 15 else node for node in doc_nodes}
            nx.draw_networkx_labels(self.network, pos, labels=doc_labels, font_size=8)
            
            plt.title("Citation Network Visualization")
            plt.legend()
            plt.axis('off')
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"✅ Saved network visualization to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving network visualization: {e}")
            return False
    
    async def export_network_data(self, output_path: str) -> bool:
        """Export network data to JSON file."""
        if not self.network:
            logger.warning("⚠️ No network to export")
            return False
        
        try:
            # Convert network to JSON-serializable format
            network_data = {
                "nodes": [
                    {
                        "id": node,
                        "type": data.get('type', 'unknown'),
                        "title": data.get('title', ''),
                        "authors": data.get('authors', ''),
                        "year": data.get('year', ''),
                        "journal": data.get('journal', ''),
                        "reference_count": data.get('reference_count', 0)
                    }
                    for node, data in self.network.nodes(data=True)
                ],
                "edges": [
                    {
                        "source": source,
                        "target": target,
                        "type": data.get('type', 'unknown')
                    }
                    for source, target, data in self.network.edges(data=True)
                ],
                "statistics": await self._calculate_network_statistics() if NETWORKX_AVAILABLE else {},
                "exported_at": datetime.now().isoformat()
            }
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(network_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ Exported network data to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting network data: {e}")
            return False
    
    async def _get_all_references(self) -> List[Dict[str, Any]]:
        """Get all references from the database."""
        query = """
        MATCH (r:Reference)
        RETURN r.reference_text as reference_text,
               r.authors as authors,
               r.title as title,
               r.year as year,
               r.journal as journal,
               r.source_document as source_document,
               id(r) as node_id
        """
        
        result = self.db.execute_cypher(query)
        
        if len(result) > 1 and result[1]:
            references = []
            for row in result[1]:
                ref_dict = {
                    'reference_text': row[0] if row[0] else '',
                    'authors': row[1] if row[1] else '',
                    'title': row[2] if row[2] else '',
                    'year': row[3] if row[3] else '',
                    'journal': row[4] if row[4] else '',
                    'source_document': row[5] if row[5] else '',
                    'node_id': row[6] if len(row) > 6 else None
                }
                references.append(ref_dict)
            return references
        
        return []
    
    async def _get_all_documents(self) -> List[Dict[str, Any]]:
        """Get all documents from the database."""
        query = """
        MATCH (d:Document)
        OPTIONAL MATCH (d)-[:HAS_REFERENCE]->(r:Reference)
        RETURN d.name as name,
               d.title as title,
               count(r) as reference_count
        """
        
        result = self.db.execute_cypher(query)
        
        if len(result) > 1 and result[1]:
            documents = []
            for row in result[1]:
                doc_dict = {
                    'name': row[0] if row[0] else '',
                    'title': row[1] if row[1] else row[0],
                    'reference_count': row[2] if len(row) > 2 else 0
                }
                documents.append(doc_dict)
            return documents
        
        return []
    
    async def _find_cited_documents(self, reference: Dict[str, Any]) -> List[str]:
        """Find documents that might be cited by this reference."""
        # This is a simplified approach - could be enhanced with better matching
        cited_docs = []
        
        ref_title = reference.get('title', '').lower()
        ref_authors = reference.get('authors', '').lower()
        
        if not ref_title and not ref_authors:
            return cited_docs
        
        # Get all documents and check for matches
        documents = await self._get_all_documents()
        
        for doc in documents:
            doc_name = doc['name'].lower()
            doc_title = doc.get('title', '').lower()
            
            # Simple matching - could be improved
            if ref_title and (ref_title in doc_title or doc_title in ref_title):
                cited_docs.append(doc['name'])
            elif ref_authors and ref_authors in doc_name:
                cited_docs.append(doc['name'])
        
        return cited_docs
    
    async def _calculate_network_statistics(self) -> Dict[str, Any]:
        """Calculate network statistics."""
        if not NETWORKX_AVAILABLE or not self.network:
            return {}
        
        try:
            stats = {
                "nodes": self.network.number_of_nodes(),
                "edges": self.network.number_of_edges(),
                "density": nx.density(self.network),
                "is_connected": nx.is_weakly_connected(self.network),
                "number_of_components": nx.number_weakly_connected_components(self.network)
            }
            
            # Add more statistics if network is not empty
            if self.network.number_of_nodes() > 0:
                stats["average_degree"] = sum(dict(self.network.degree()).values()) / self.network.number_of_nodes()
                
                if nx.is_weakly_connected(self.network):
                    stats["diameter"] = nx.diameter(self.network.to_undirected())
                    stats["average_shortest_path_length"] = nx.average_shortest_path_length(self.network.to_undirected())
            
            return stats
            
        except Exception as e:
            logger.error(f"❌ Error calculating network statistics: {e}")
            return {}
    
    async def _build_simple_network(self, references: List[Dict], documents: List[Dict]) -> Dict[str, Any]:
        """Build a simple network representation without NetworkX."""
        logger.info("📊 Building simple citation network (NetworkX not available)")
        
        # Create simple network structure
        network_data = {
            "documents": documents,
            "references": references,
            "citation_relationships": [],
            "statistics": {
                "documents": len(documents),
                "references": len(references),
                "citations": 0
            }
        }
        
        # Try to find simple citation relationships
        citations = 0
        for ref in references:
            cited_docs = await self._find_cited_documents(ref)
            for cited_doc in cited_docs:
                network_data["citation_relationships"].append({
                    "source": ref.get('source_document', ''),
                    "target": cited_doc,
                    "reference": ref.get('title', ref.get('reference_text', '')[:100])
                })
                citations += 1
        
        network_data["statistics"]["citations"] = citations
        
        return {
            "success": True,
            "network_data": network_data,
            "nodes": len(documents) + len(references),
            "edges": citations,
            "message": "Simple network built (NetworkX not available for advanced analysis)"
        }
    
    async def _get_most_cited_simple(self, limit: int) -> List[Dict[str, Any]]:
        """Get most cited documents using simple counting."""
        # Count citations per document
        citation_counts = {}
        
        query = """
        MATCH (d:Document)
        OPTIONAL MATCH (r:Reference)-[:CITES]->(d)
        RETURN d.name as document, d.title as title, count(r) as citation_count
        ORDER BY citation_count DESC
        LIMIT $limit
        """
        
        result = self.db.execute_cypher(query, {'limit': limit})
        
        most_cited = []
        if len(result) > 1 and result[1]:
            for row in result[1]:
                most_cited.append({
                    'document': row[0],
                    'title': row[1] if row[1] else row[0],
                    'citation_count': row[2],
                    'pagerank_score': 0.0  # Not available without NetworkX
                })
        
        return most_cited
