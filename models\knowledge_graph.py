"""
Knowledge graph data models for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field

class Node(BaseModel):
    """Node model"""
    uuid: str
    type: str
    name: str
    body: Optional[str] = None
    properties: Optional[Dict[str, Any]] = None

class Edge(BaseModel):
    """Edge model"""
    source: str
    target: str
    type: str
    properties: Optional[Dict[str, Any]] = None

class GraphData(BaseModel):
    """Graph data model"""
    nodes: List[Node]
    relationships: List[Edge]

class GraphQuery(BaseModel):
    """Graph query model"""
    query: str
    params: Optional[Dict[str, Any]] = None

class GraphQueryResult(BaseModel):
    """Graph query result model"""
    headers: List[str]
    data: List[List[Any]]
    summary: Optional[Dict[str, Any]] = None

class SearchQuery(BaseModel):
    """Search query model"""
    query: str
    entity_types: Optional[List[str]] = None
    relationship_types: Optional[List[str]] = None
    min_confidence: Optional[float] = None
    limit: int = 100

class SearchResult(BaseModel):
    """Search result model"""
    entities: List[Dict[str, Any]]
    facts: List[Dict[str, Any]]
    relationships: List[Dict[str, Any]]

class TaxonomyNode(BaseModel):
    """Taxonomy node model"""
    uuid: str
    name: str
    type: str
    children: List["TaxonomyNode"] = []

class Taxonomy(BaseModel):
    """Taxonomy model"""
    root_nodes: List[TaxonomyNode]
