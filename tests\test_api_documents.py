"""
Integration tests for the document API endpoints.
"""

import os
import sys
import pytest
import io
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

def test_get_documents_endpoint(test_client, mock_db):
    """
    Test the get documents endpoint.
    
    Args:
        test_client: FastAPI test client
        mock_db: Mocked database adapter
    """
    # Mock the database response
    mock_db.execute_cypher.return_value = [
        ["uuid", "name", "file_path", "processed_at"],
        [
            ["doc1", "Document 1", "/path/to/doc1.pdf", "2023-01-01T00:00:00"],
            ["doc2", "Document 2", "/path/to/doc2.pdf", "2023-01-02T00:00:00"]
        ]
    ]
    
    # Make a request to the documents endpoint
    response = test_client.get("/api/documents")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "documents" in data
    assert "total" in data
    assert "page" in data
    assert "page_size" in data
    
    # Check the documents
    assert len(data["documents"]) == 2
    assert data["documents"][0]["uuid"] == "doc1"
    assert data["documents"][1]["uuid"] == "doc2"

def test_get_documents_endpoint_error(test_client, mock_db):
    """
    Test the get documents endpoint with an error.
    
    Args:
        test_client: FastAPI test client
        mock_db: Mocked database adapter
    """
    # Mock the database to raise an exception
    mock_db.execute_cypher.side_effect = Exception("Test error")
    
    # Make a request to the documents endpoint
    response = test_client.get("/api/documents")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting documents" in response.json()["detail"]

def test_get_document_endpoint(test_client, mock_db):
    """
    Test the get document endpoint.
    
    Args:
        test_client: FastAPI test client
        mock_db: Mocked database adapter
    """
    # Mock the database response
    mock_db.execute_cypher.return_value = [
        ["uuid", "name", "file_path", "processed_at", "chunks", "entities"],
        [["doc1", "Document 1", "/path/to/doc1.pdf", "2023-01-01T00:00:00", 10, 20]]
    ]
    
    # Make a request to the document endpoint
    response = test_client.get("/api/documents/doc1")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert data["uuid"] == "doc1"
    assert data["name"] == "Document 1"
    assert data["file_path"] == "/path/to/doc1.pdf"
    assert data["chunks"] == 10
    assert data["entities"] == 20

def test_get_document_endpoint_error(test_client, mock_db):
    """
    Test the get document endpoint with an error.
    
    Args:
        test_client: FastAPI test client
        mock_db: Mocked database adapter
    """
    # Mock the database to raise an exception
    mock_db.execute_cypher.side_effect = Exception("Test error")
    
    # Make a request to the document endpoint
    response = test_client.get("/api/documents/doc1")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting document" in response.json()["detail"]

@patch("services.document_service.process_document")
def test_upload_document_endpoint(mock_process_document, test_client):
    """
    Test the upload document endpoint.
    
    Args:
        mock_process_document: Mocked process_document function
        test_client: FastAPI test client
    """
    # Mock the process_document function
    mock_process_document.return_value = {
        "success": True,
        "file_id": "doc1",
        "file_path": "/path/to/doc1.pdf",
        "chunks": 10,
        "ocr_provider": "pypdf2"
    }
    
    # Create a test file
    file_content = b"Test file content"
    file = io.BytesIO(file_content)
    
    # Make a request to the upload endpoint
    response = test_client.post(
        "/api/upload",
        files={"file": ("test.pdf", file, "application/pdf")},
        data={
            "chunk_size": 1200,
            "overlap": 0,
            "extract_entities": True,
            "extract_references": True,
            "extract_metadata": True
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert data["success"] == True
    assert data["id"] == "doc1"
    assert data["file_type"] == "document"
    assert data["chunks"] == 10

@patch("services.document_service.process_document")
def test_upload_document_endpoint_error(mock_process_document, test_client):
    """
    Test the upload document endpoint with an error.
    
    Args:
        mock_process_document: Mocked process_document function
        test_client: FastAPI test client
    """
    # Mock the process_document function to raise an exception
    mock_process_document.side_effect = Exception("Test error")
    
    # Create a test file
    file_content = b"Test file content"
    file = io.BytesIO(file_content)
    
    # Make a request to the upload endpoint
    response = test_client.post(
        "/api/upload",
        files={"file": ("test.pdf", file, "application/pdf")},
        data={
            "chunk_size": 1200,
            "overlap": 0,
            "extract_entities": True,
            "extract_references": True,
            "extract_metadata": True
        }
    )
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error processing file" in response.json()["detail"]
