/**
 * Graphiti UI - Main JavaScript file for the Graphiti Knowledge Graph UI
 *
 * This file handles the core UI functionality for all tabs in the Graphiti application.
 * It initializes the UI components, sets up event listeners, and manages the tab switching.
 */

// Global variables
let currentTab = 'chat';
let entityData = {};
let documentData = {};
let knowledgeGraphData = {};

// Initialize the UI when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("Graphiti UI initialized");

    // Initialize all tabs
    initializeTabs();

    // Load initial data
    loadInitialData();

    // Set up global event listeners
    setupGlobalEventListeners();
});

/**
 * Initialize all tabs
 */
function initializeTabs() {
    // Get all tab buttons
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

    // Add event listener for tab changes
    tabButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            const tabId = this.getAttribute('data-bs-target').replace('#', '');
            handleTabChange(tabId);
        });
    });

    // Initialize the active tab
    const activeTab = document.querySelector('.nav-link.active');
    if (activeTab) {
        const tabId = activeTab.getAttribute('data-bs-target').replace('#', '');
        handleTabChange(tabId);
    }
}

/**
 * Handle tab change
 *
 * @param {string} tabId - The ID of the tab to switch to
 */
function handleTabChange(tabId) {
    console.log(`Switching to tab: ${tabId}`);
    currentTab = tabId;

    // Load data for the selected tab
    switch (tabId) {
        case 'chat':
            // Chat tab is already initialized by qa_interface.js
            break;
        case 'search':
            initializeSearchTab();
            break;
        case 'entities':
            initializeEntitiesTab();
            break;
        case 'upload':
            // Upload tab is already initialized by flask_upload.js
            break;
        case 'metadata':
            initializeMetadataTab();
            break;
        case 'references':
            initializeReferencesTab();
            break;
        case 'enhancements':
            initializeEnhancementsTab();
            break;
        case 'graph':
            initializeGraphTab();
            break;
        case 'settings':
            initializeSettingsTab();
            break;
        default:
            console.log(`Unknown tab: ${tabId}`);
    }
}

/**
 * Load initial data for the application
 */
function loadInitialData() {
    // Load entity types for filters
    loadEntityTypes();

    // Load graph statistics
    loadGraphStatistics();
}

/**
 * Set up global event listeners
 */
function setupGlobalEventListeners() {
    // Add event listener for the entity search input
    const entitySearchInput = document.getElementById('entity-search-input');
    if (entitySearchInput) {
        entitySearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchEntities();
            }
        });
    }

    // Add event listener for the entity search button
    const entitySearchButton = document.getElementById('entity-search-button');
    if (entitySearchButton) {
        entitySearchButton.addEventListener('click', function() {
            searchEntities();
        });
    }

    // Add event listener for the entity type filter
    const entityTypeFilter = document.getElementById('entity-type-filter');
    if (entityTypeFilter) {
        entityTypeFilter.addEventListener('change', function() {
            filterEntitiesByType();
        });
    }
}

/**
 * Load entity types for filters
 */
function loadEntityTypes() {
    fetch('/api/entity-types')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Populate entity type filter
            const entityTypeFilter = document.getElementById('entity-type-filter');
            if (entityTypeFilter) {
                // Clear existing options except the first one
                while (entityTypeFilter.options.length > 1) {
                    entityTypeFilter.remove(1);
                }

                // Add entity types
                data.entity_types.forEach(type => {
                    const option = document.createElement('option');
                    option.value = type;
                    option.textContent = type;
                    entityTypeFilter.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading entity types:', error);
        });
}

/**
 * Load graph statistics
 */
function loadGraphStatistics() {
    fetch('/api/fast/graph-stats')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store graph statistics
            knowledgeGraphData.statistics = data;

            // Update UI elements with statistics
            updateGraphStatistics(data);
        })
        .catch(error => {
            console.error('Error loading graph statistics:', error);
        });
}

/**
 * Update UI elements with graph statistics
 *
 * @param {Object} stats - Graph statistics
 */
function updateGraphStatistics(stats) {
    // Update entity count
    const entityCountElement = document.getElementById('entity-count');
    if (entityCountElement) {
        entityCountElement.textContent = stats.entity_count;
    }

    // Update document count
    const documentCountElement = document.getElementById('document-count');
    if (documentCountElement) {
        documentCountElement.textContent = stats.document_count;
    }

    // Update relationship count
    const relationshipCountElement = document.getElementById('relationship-count');
    if (relationshipCountElement) {
        relationshipCountElement.textContent = stats.relationship_count;
    }
}

/**
 * Initialize the Entities tab
 */
function initializeEntitiesTab() {
    console.log("Initializing Entities tab");

    // Show loading spinner
    const loadingSpinner = document.getElementById('entities-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Load entities
    loadEntities();
}

/**
 * Load entities from the API
 */
function loadEntities() {
    // Get filter values
    const entityType = document.getElementById('entity-type-filter')?.value || '';
    const minMentions = document.getElementById('min-mentions-filter')?.value || 0;

    // Build URL
    let url = `/api/entities?limit=100&offset=0`;
    if (entityType) {
        url += `&entity_type=${encodeURIComponent(entityType)}`;
    }
    if (minMentions > 0) {
        url += `&min_mentions=${minMentions}`;
    }

    // Fetch entities
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store entity data
            entityData.list = data.entities;
            entityData.typeCounts = data.type_counts;

            // Render entities
            renderEntities(data);

            // Hide loading spinner
            const loadingSpinner = document.getElementById('entities-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading entities:', error);

            // Hide loading spinner
            const loadingSpinner = document.getElementById('entities-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const entitiesList = document.getElementById('entities-list');
            if (entitiesList) {
                entitiesList.innerHTML = `<div class="alert alert-danger">Error loading entities: ${error.message}</div>`;
            }
        });
}

/**
 * Render entities in the UI
 *
 * @param {Object} data - Entity data from the API
 */
function renderEntities(data) {
    const entitiesList = document.getElementById('entities-list');
    if (!entitiesList) {
        console.error("Entities list element not found");
        return;
    }

    // Clear existing content
    entitiesList.innerHTML = '';

    // If no entities, show message
    if (!data.entities || data.entities.length === 0) {
        entitiesList.innerHTML = '<div class="alert alert-info">No entities found.</div>';
        return;
    }

    // Group entities by type
    const entitiesByType = {};
    data.entities.forEach(entity => {
        const type = entity.type || 'Unknown';
        if (!entitiesByType[type]) {
            entitiesByType[type] = [];
        }
        entitiesByType[type].push(entity);
    });

    // Create entity type sections
    Object.keys(entitiesByType).sort().forEach(type => {
        const entities = entitiesByType[type];
        const count = entities.length;

        // Create type header
        const typeHeader = document.createElement('h4');
        typeHeader.className = 'mt-4 mb-3';
        typeHeader.textContent = `${type} (${count})`;
        entitiesList.appendChild(typeHeader);

        // Create entity cards container
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'row row-cols-1 row-cols-md-3 g-4';

        // Add entity cards
        entities.forEach(entity => {
            const card = document.createElement('div');
            card.className = 'col';

            card.innerHTML = `
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">${entity.name}</h5>
                        <p class="card-text">
                            <span class="badge bg-info">${entity.type}</span>
                            <span class="badge bg-secondary">${entity.mention_count} mentions</span>
                        </p>
                    </div>
                    <div class="card-footer">
                        <a href="/entity-detail?uuid=${entity.uuid}" class="btn btn-sm btn-primary">View Details</a>
                    </div>
                </div>
            `;

            cardsContainer.appendChild(card);
        });

        entitiesList.appendChild(cardsContainer);
    });

    // Add pagination if available
    if (data.pagination) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'mt-4 d-flex justify-content-center';

        const pagination = document.createElement('nav');
        pagination.setAttribute('aria-label', 'Entity pagination');

        const paginationList = document.createElement('ul');
        paginationList.className = 'pagination';

        // Previous page button
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${data.pagination.has_prev ? '' : 'disabled'}`;

        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (data.pagination.has_prev) {
                loadEntitiesPage(data.pagination.page - 1);
            }
        });

        prevItem.appendChild(prevLink);
        paginationList.appendChild(prevItem);

        // Page numbers
        for (let i = 1; i <= data.pagination.total_pages; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === data.pagination.page ? 'active' : ''}`;

            const pageLink = document.createElement('a');
            pageLink.className = 'page-link';
            pageLink.href = '#';
            pageLink.textContent = i;
            pageLink.addEventListener('click', function(e) {
                e.preventDefault();
                loadEntitiesPage(i);
            });

            pageItem.appendChild(pageLink);
            paginationList.appendChild(pageItem);
        }

        // Next page button
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${data.pagination.has_next ? '' : 'disabled'}`;

        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (data.pagination.has_next) {
                loadEntitiesPage(data.pagination.page + 1);
            }
        });

        nextItem.appendChild(nextLink);
        paginationList.appendChild(nextItem);

        pagination.appendChild(paginationList);
        paginationContainer.appendChild(pagination);
        entitiesList.appendChild(paginationContainer);
    }
}

/**
 * Load a specific page of entities
 *
 * @param {number} page - Page number to load
 */
function loadEntitiesPage(page) {
    // Get filter values
    const entityType = document.getElementById('entity-type-filter')?.value || '';
    const minMentions = document.getElementById('min-mentions-filter')?.value || 0;
    const pageSize = 100;

    // Calculate offset
    const offset = (page - 1) * pageSize;

    // Build URL
    let url = `/api/entities?limit=${pageSize}&offset=${offset}`;
    if (entityType) {
        url += `&entity_type=${encodeURIComponent(entityType)}`;
    }
    if (minMentions > 0) {
        url += `&min_mentions=${minMentions}`;
    }

    // Show loading spinner
    const loadingSpinner = document.getElementById('entities-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch entities
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store entity data
            entityData.list = data.entities;
            entityData.typeCounts = data.type_counts;

            // Render entities
            renderEntities(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading entities page:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const entitiesList = document.getElementById('entities-list');
            if (entitiesList) {
                entitiesList.innerHTML = `<div class="alert alert-danger">Error loading entities: ${error.message}</div>`;
            }
        });
}

/**
 * Filter entities by type
 */
function filterEntitiesByType() {
    // Get filter values
    const entityType = document.getElementById('entity-type-filter')?.value || '';

    // Show loading spinner
    const loadingSpinner = document.getElementById('entities-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Build URL
    let url = `/api/entities?limit=100&offset=0`;
    if (entityType) {
        url += `&entity_type=${encodeURIComponent(entityType)}`;
    }

    // Fetch entities
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store entity data
            entityData.list = data.entities;
            entityData.typeCounts = data.type_counts;

            // Render entities
            renderEntities(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error filtering entities:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const entitiesList = document.getElementById('entities-list');
            if (entitiesList) {
                entitiesList.innerHTML = `<div class="alert alert-danger">Error filtering entities: ${error.message}</div>`;
            }
        });
}

/**
 * Search entities by name
 */
function searchEntities() {
    // Get search query
    const searchQuery = document.getElementById('entity-search-input')?.value || '';

    if (!searchQuery) {
        // If no search query, load all entities
        loadEntities();
        return;
    }

    // Show loading spinner
    const loadingSpinner = document.getElementById('entities-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Build URL
    let url = `/api/entities?limit=100&offset=0&search=${encodeURIComponent(searchQuery)}`;

    // Fetch entities
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store entity data
            entityData.list = data.entities;
            entityData.typeCounts = data.type_counts;

            // Render entities
            renderEntities(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error searching entities:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const entitiesList = document.getElementById('entities-list');
            if (entitiesList) {
                entitiesList.innerHTML = `<div class="alert alert-danger">Error searching entities: ${error.message}</div>`;
            }
        });
}
}
