"""
<PERSON><PERSON><PERSON> to set up relationship types in the Neo4j database.
"""

import os
import asyncio
import logging
from neo4j import AsyncGraphDatabase
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Define relationship types
RELATIONSHIP_TYPES = [
    {
        "name": "IS_A",
        "description": "Indicates that the source entity is a type or subclass of the target entity",
        "example": "Vitamin C IS_A Nutrient"
    },
    {
        "name": "PART_OF",
        "description": "Indicates that the source entity is a component or part of the target entity",
        "example": "Flavonoids PART_OF Green Tea"
    },
    {
        "name": "TREATS",
        "description": "Indicates that the source entity is used to treat or manage the target entity (usually a disease or condition)",
        "example": "Echinacea TREATS Common Cold"
    },
    {
        "name": "CAUSES",
        "description": "Indicates that the source entity causes or contributes to the target entity",
        "example": "Smoking CAUSES Lung Cancer"
    },
    {
        "name": "PREVENTS",
        "description": "Indicates that the source entity helps prevent or reduce the risk of the target entity",
        "example": "Vitamin D PREVENTS Rickets"
    },
    {
        "name": "ASSOCIATED_WITH",
        "description": "Indicates a general association between the source and target entities",
        "example": "Turmeric ASSOCIATED_WITH Anti-inflammatory Properties"
    },
    {
        "name": "STUDIED_BY",
        "description": "Indicates that the source entity was studied by the target entity (usually a researcher or organization)",
        "example": "Ginseng STUDIED_BY Dr. Smith"
    },
    {
        "name": "CONTAINS",
        "description": "Indicates that the source entity contains the target entity as a component or ingredient",
        "example": "Milk Thistle CONTAINS Silymarin"
    },
    {
        "name": "INTERACTS_WITH",
        "description": "Indicates that the source entity interacts with the target entity, potentially affecting its function",
        "example": "St. John's Wort INTERACTS_WITH Warfarin"
    },
    {
        "name": "CONTRAINDICATES",
        "description": "Indicates that the source entity is contraindicated for the target entity",
        "example": "Ginkgo Biloba CONTRAINDICATES Blood Thinners"
    },
    {
        "name": "INCREASES",
        "description": "Indicates that the source entity increases or enhances the target entity",
        "example": "Exercise INCREASES Metabolism"
    },
    {
        "name": "DECREASES",
        "description": "Indicates that the source entity decreases or reduces the target entity",
        "example": "Meditation DECREASES Stress"
    },
    {
        "name": "NEEDS",
        "description": "Indicates that the source entity requires the target entity",
        "example": "Bone Formation NEEDS Vitamin D"
    },
    {
        "name": "INHIBITS",
        "description": "Indicates that the source entity inhibits or suppresses the target entity",
        "example": "Curcumin INHIBITS Inflammation"
    },
    {
        "name": "ACTIVATES",
        "description": "Indicates that the source entity activates or stimulates the target entity",
        "example": "Sunlight ACTIVATES Vitamin D Production"
    },
    {
        "name": "REGULATES",
        "description": "Indicates that the source entity regulates or controls the target entity",
        "example": "Insulin REGULATES Blood Sugar"
    },
    {
        "name": "CONVERTS_TO",
        "description": "Indicates that the source entity is converted to the target entity",
        "example": "Beta Carotene CONVERTS_TO Vitamin A"
    },
    {
        "name": "DERIVED_FROM",
        "description": "Indicates that the source entity is derived from the target entity",
        "example": "Resveratrol DERIVED_FROM Grapes"
    },
    {
        "name": "USED_FOR",
        "description": "Indicates that the source entity is used for the target entity (usually a purpose or application)",
        "example": "Aloe Vera USED_FOR Skin Healing"
    },
    {
        "name": "MEASURED_BY",
        "description": "Indicates that the source entity is measured by the target entity",
        "example": "Vitamin B12 Status MEASURED_BY Serum Homocysteine"
    }
]

async def get_neo4j_driver():
    """Get a Neo4j driver."""
    driver = AsyncGraphDatabase.driver(
        neo4j_uri,
        auth=(neo4j_user, neo4j_password)
    )
    return driver

async def setup_relationship_types():
    """Set up relationship types in the Neo4j database."""
    logger.info("Setting up relationship types")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            # Create constraint for RelationshipType nodes
            try:
                await session.run(
                    """
                    CREATE CONSTRAINT relationship_type_name_constraint IF NOT EXISTS
                    FOR (r:RelationshipType)
                    REQUIRE r.name IS UNIQUE
                    """
                )
                logger.info("Created constraint for RelationshipType nodes")
            except Exception as e:
                logger.warning(f"Error creating constraint: {e}")
            
            # Create relationship types
            for rel_type in RELATIONSHIP_TYPES:
                result = await session.run(
                    """
                    MERGE (r:RelationshipType {name: $name})
                    ON CREATE SET
                        r.description = $description,
                        r.example = $example
                    ON MATCH SET
                        r.description = $description,
                        r.example = $example
                    RETURN r.name AS name
                    """,
                    rel_type
                )
                
                record = await result.single()
                logger.info(f"Created/updated relationship type: {record['name']}")
                
            # Get count of relationship types
            result = await session.run(
                """
                MATCH (r:RelationshipType)
                RETURN count(r) AS count
                """
            )
            
            record = await result.single()
            logger.info(f"Total relationship types: {record['count']}")
            
    except Exception as e:
        logger.error(f"Error setting up relationship types: {e}")
    finally:
        await driver.close()

async def main():
    """Main function to set up relationship types."""
    await setup_relationship_types()

if __name__ == "__main__":
    asyncio.run(main())
