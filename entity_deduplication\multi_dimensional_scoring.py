"""
Multi-dimensional entity similarity scoring.

This module implements comprehensive similarity scoring that combines:
- Semantic embeddings (vector similarity)
- Syntactic similarity (string matching)
- Contextual relevance (document, domain context)
- Domain-specific attributes
- Confidence weighting
"""

import logging
import math
import re
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from collections import Counter

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.utils import normalize_text, calculate_string_similarity

logger = logging.getLogger(__name__)


@dataclass
class SimilarityComponents:
    """Components of multi-dimensional similarity scoring."""
    semantic_score: float = 0.0
    syntactic_score: float = 0.0
    contextual_score: float = 0.0
    attribute_score: float = 0.0
    confidence_score: float = 0.0
    overall_score: float = 0.0
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary for logging/debugging."""
        return {
            'semantic': self.semantic_score,
            'syntactic': self.syntactic_score,
            'contextual': self.contextual_score,
            'attribute': self.attribute_score,
            'confidence': self.confidence_score,
            'overall': self.overall_score
        }


class MultiDimensionalScorer:
    """
    Multi-dimensional entity similarity scorer.
    
    Combines multiple similarity measures with configurable weights
    to provide more accurate entity matching.
    """
    
    def __init__(self, weights: Optional[Dict[str, float]] = None):
        """
        Initialize the multi-dimensional scorer.
        
        Args:
            weights: Dictionary of weights for different similarity components
        """
        # Default weights - can be tuned based on domain and performance
        self.weights = weights or {
            'semantic': 0.35,      # Vector embedding similarity
            'syntactic': 0.25,     # String/name similarity  
            'contextual': 0.20,    # Document/domain context
            'attribute': 0.15,     # Domain-specific attributes
            'confidence': 0.05     # Entity confidence weighting
        }
        
        # Ensure weights sum to 1.0
        total_weight = sum(self.weights.values())
        if abs(total_weight - 1.0) > 0.01:
            logger.warning(f"Weights sum to {total_weight}, normalizing to 1.0")
            for key in self.weights:
                self.weights[key] /= total_weight
    
    def calculate_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> SimilarityComponents:
        """
        Calculate multi-dimensional similarity between two entities.
        
        Args:
            entity1: First entity
            entity2: Second entity
            
        Returns:
            SimilarityComponents with detailed scoring breakdown
        """
        components = SimilarityComponents()
        
        # 1. Semantic similarity (vector embeddings)
        components.semantic_score = self._calculate_semantic_similarity(entity1, entity2)
        
        # 2. Syntactic similarity (string matching)
        components.syntactic_score = self._calculate_syntactic_similarity(entity1, entity2)
        
        # 3. Contextual similarity (document/domain context)
        components.contextual_score = self._calculate_contextual_similarity(entity1, entity2)
        
        # 4. Attribute similarity (domain-specific attributes)
        components.attribute_score = self._calculate_attribute_similarity(entity1, entity2)
        
        # 5. Confidence weighting
        components.confidence_score = self._calculate_confidence_weighting(entity1, entity2)
        
        # 6. Calculate overall weighted score
        components.overall_score = (
            components.semantic_score * self.weights['semantic'] +
            components.syntactic_score * self.weights['syntactic'] +
            components.contextual_score * self.weights['contextual'] +
            components.attribute_score * self.weights['attribute'] +
            components.confidence_score * self.weights['confidence']
        )
        
        return components
    
    def _calculate_semantic_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate semantic similarity using vector embeddings."""
        if not entity1.embedding or not entity2.embedding:
            return 0.0
        
        try:
            from numpy import dot
            from numpy.linalg import norm
            
            # Cosine similarity
            cosine_sim = dot(entity1.embedding, entity2.embedding) / (
                norm(entity1.embedding) * norm(entity2.embedding)
            )
            
            # Normalize to 0-1 range (cosine similarity can be -1 to 1)
            return max(0.0, (cosine_sim + 1.0) / 2.0)
            
        except Exception as e:
            logger.warning(f"Error calculating semantic similarity: {e}")
            return 0.0
    
    def _calculate_syntactic_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate syntactic similarity using multiple string matching techniques."""
        name1 = entity1.name
        name2 = entity2.name
        
        if not name1 or not name2:
            return 0.0
        
        # Multiple syntactic similarity measures
        similarities = []
        
        # 1. Basic string similarity
        basic_sim = calculate_string_similarity(name1, name2)
        similarities.append(basic_sim)
        
        # 2. Normalized string similarity (remove common words, punctuation)
        norm_sim = self._calculate_normalized_similarity(name1, name2)
        similarities.append(norm_sim)
        
        # 3. Token-based similarity (Jaccard similarity of words)
        token_sim = self._calculate_token_similarity(name1, name2)
        similarities.append(token_sim)
        
        # 4. Phonetic similarity (for names)
        if entity1.type == "Person" or entity2.type == "Person":
            phonetic_sim = self._calculate_phonetic_similarity(name1, name2)
            similarities.append(phonetic_sim)
        
        # 5. Abbreviation similarity
        abbrev_sim = self._calculate_abbreviation_similarity(name1, name2)
        similarities.append(abbrev_sim)
        
        # Return weighted average with emphasis on highest scores
        similarities.sort(reverse=True)
        if len(similarities) >= 3:
            # Weight the top 3 scores: 50%, 30%, 20%
            return (similarities[0] * 0.5 + similarities[1] * 0.3 + similarities[2] * 0.2)
        else:
            return sum(similarities) / len(similarities)
    
    def _calculate_normalized_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity after normalizing names."""
        # Remove common words and normalize
        common_words = {'the', 'of', 'and', 'in', 'at', 'for', 'on', 'with', 'by'}
        
        def normalize_name(name: str) -> str:
            # Convert to lowercase, remove punctuation, split into words
            words = re.findall(r'\b\w+\b', name.lower())
            # Remove common words
            words = [w for w in words if w not in common_words]
            return ' '.join(words)
        
        norm1 = normalize_name(name1)
        norm2 = normalize_name(name2)
        
        if not norm1 or not norm2:
            return 0.0
        
        return calculate_string_similarity(norm1, norm2)
    
    def _calculate_token_similarity(self, name1: str, name2: str) -> float:
        """Calculate Jaccard similarity of word tokens."""
        tokens1 = set(re.findall(r'\b\w+\b', name1.lower()))
        tokens2 = set(re.findall(r'\b\w+\b', name2.lower()))
        
        if not tokens1 or not tokens2:
            return 0.0
        
        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))
        
        return intersection / union if union > 0 else 0.0
    
    def _calculate_phonetic_similarity(self, name1: str, name2: str) -> float:
        """Calculate phonetic similarity (simplified soundex-like approach)."""
        def simple_soundex(name: str) -> str:
            """Simplified soundex algorithm."""
            if not name:
                return ""
            
            # Keep first letter, remove vowels, group similar consonants
            name = name.upper()
            result = name[0]
            
            # Mapping of similar sounds
            sound_map = {
                'B': '1', 'F': '1', 'P': '1', 'V': '1',
                'C': '2', 'G': '2', 'J': '2', 'K': '2', 'Q': '2', 'S': '2', 'X': '2', 'Z': '2',
                'D': '3', 'T': '3',
                'L': '4',
                'M': '5', 'N': '5',
                'R': '6'
            }
            
            for char in name[1:]:
                if char in sound_map:
                    code = sound_map[char]
                    if result[-1] != code:  # Avoid duplicates
                        result += code
            
            return result[:4].ljust(4, '0')  # Pad to 4 characters
        
        soundex1 = simple_soundex(name1)
        soundex2 = simple_soundex(name2)
        
        if soundex1 == soundex2:
            return 1.0
        
        # Calculate similarity of soundex codes
        return calculate_string_similarity(soundex1, soundex2)
    
    def _calculate_abbreviation_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity considering abbreviations."""
        def get_abbreviations(name: str) -> Set[str]:
            """Extract possible abbreviations from a name."""
            abbrevs = set()
            words = re.findall(r'\b\w+\b', name)
            
            # First letters of words
            if len(words) > 1:
                abbrevs.add(''.join(w[0].upper() for w in words))
            
            # Common abbreviation patterns
            for word in words:
                if len(word) > 3:
                    # First 3 letters
                    abbrevs.add(word[:3].upper())
                    # Remove vowels
                    consonants = ''.join(c for c in word if c.lower() not in 'aeiou')
                    if len(consonants) >= 2:
                        abbrevs.add(consonants.upper())
            
            return abbrevs
        
        abbrevs1 = get_abbreviations(name1)
        abbrevs2 = get_abbreviations(name2)
        
        # Check if one name could be an abbreviation of the other
        name1_clean = re.sub(r'[^\w\s]', '', name1).upper()
        name2_clean = re.sub(r'[^\w\s]', '', name2).upper()
        
        # Direct abbreviation check
        if name1_clean in abbrevs2 or name2_clean in abbrevs1:
            return 0.8
        
        # Abbreviation overlap
        if abbrevs1 and abbrevs2:
            overlap = len(abbrevs1.intersection(abbrevs2))
            total = len(abbrevs1.union(abbrevs2))
            return overlap / total if total > 0 else 0.0
        
        return 0.0
    
    def _calculate_contextual_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate contextual similarity based on document and domain context."""
        context_score = 0.0
        factors = 0
        
        # 1. Same document context (entities from same document are less likely to be duplicates)
        if entity1.source_document_id and entity2.source_document_id:
            if entity1.source_document_id == entity2.source_document_id:
                context_score += 0.2  # Lower score for same document
            else:
                context_score += 0.8  # Higher score for different documents
            factors += 1
        
        # 2. Description similarity
        if entity1.description and entity2.description:
            desc_sim = self._calculate_description_similarity(entity1.description, entity2.description)
            context_score += desc_sim
            factors += 1
        
        # 3. Entity type consistency
        if entity1.type == entity2.type:
            context_score += 0.9
        else:
            # Check if types are related (e.g., Person and Author)
            related_types = self._get_related_types(entity1.type, entity2.type)
            context_score += related_types
        factors += 1
        
        return context_score / factors if factors > 0 else 0.0
    
    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity between entity descriptions."""
        if not desc1 or not desc2:
            return 0.0
        
        # Tokenize and calculate overlap
        tokens1 = set(re.findall(r'\b\w+\b', desc1.lower()))
        tokens2 = set(re.findall(r'\b\w+\b', desc2.lower()))
        
        if not tokens1 or not tokens2:
            return 0.0
        
        intersection = len(tokens1.intersection(tokens2))
        union = len(tokens1.union(tokens2))
        
        return intersection / union if union > 0 else 0.0
    
    def _get_related_types(self, type1: str, type2: str) -> float:
        """Get similarity score for related entity types."""
        # Define type relationships
        type_relationships = {
            ('Person', 'Author'): 0.7,
            ('Person', 'Researcher'): 0.7,
            ('Organization', 'Institution'): 0.8,
            ('Organization', 'Company'): 0.8,
            ('Location', 'Place'): 0.9,
            ('Medication', 'Drug'): 0.9,
            ('Disease', 'Condition'): 0.8,
            ('Nutrient', 'Supplement'): 0.7,
        }
        
        # Check both directions
        key1 = (type1, type2)
        key2 = (type2, type1)
        
        return type_relationships.get(key1, type_relationships.get(key2, 0.0))
    
    def _calculate_attribute_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate similarity based on domain-specific attributes."""
        if not entity1.attributes or not entity2.attributes:
            return 0.0
        
        # Get common attributes
        attrs1 = entity1.attributes
        attrs2 = entity2.attributes
        
        common_keys = set(attrs1.keys()).intersection(set(attrs2.keys()))
        if not common_keys:
            return 0.0
        
        similarities = []
        for key in common_keys:
            val1 = attrs1[key]
            val2 = attrs2[key]
            
            if val1 == val2:
                similarities.append(1.0)
            elif isinstance(val1, str) and isinstance(val2, str):
                similarities.append(calculate_string_similarity(val1, val2))
            elif isinstance(val1, (int, float)) and isinstance(val2, (int, float)):
                # Numerical similarity
                max_val = max(abs(val1), abs(val2))
                if max_val > 0:
                    similarities.append(1.0 - abs(val1 - val2) / max_val)
                else:
                    similarities.append(1.0)
            else:
                similarities.append(0.0)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def _calculate_confidence_weighting(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate confidence-based weighting factor."""
        conf1 = entity1.confidence or 0.5
        conf2 = entity2.confidence or 0.5
        
        # Higher confidence entities should have higher similarity scores
        avg_confidence = (conf1 + conf2) / 2
        
        # Scale confidence to similarity score (0.5 confidence = 0.5 score)
        return avg_confidence


# Global instance
_multi_dimensional_scorer = None


def get_multi_dimensional_scorer(weights: Optional[Dict[str, float]] = None) -> MultiDimensionalScorer:
    """Get the global multi-dimensional scorer instance."""
    global _multi_dimensional_scorer
    if _multi_dimensional_scorer is None or weights is not None:
        _multi_dimensional_scorer = MultiDimensionalScorer(weights)
    return _multi_dimensional_scorer
