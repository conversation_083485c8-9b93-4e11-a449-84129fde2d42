<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help - Graphiti Knowledge Graph</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    
    <style>
        .help-section {
            margin-bottom: 30px;
        }
        
        .help-section-header {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .help-card {
            height: 100%;
            transition: transform 0.3s;
        }
        
        .help-card:hover {
            transform: translateY(-5px);
        }
        
        .help-icon {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #0d6efd;
        }
        
        .faq-item {
            margin-bottom: 20px;
        }
        
        .faq-question {
            font-weight: 500;
            cursor: pointer;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        
        .faq-answer {
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 0 0 5px 5px;
            margin-top: 1px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Help & Documentation</h1>
        
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Help</li>
            </ol>
        </nav>
        
        <!-- Help Overview -->
        <div class="help-section">
            <div class="help-section-header">
                <h3>Getting Started with Graphiti</h3>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-upload"></i>
                            </div>
                            <h5 class="card-title">Upload Documents</h5>
                            <p class="card-text">Learn how to upload and process documents to extract knowledge.</p>
                            <a href="#upload-documents" class="btn btn-primary">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-diagram-3"></i>
                            </div>
                            <h5 class="card-title">Explore Knowledge Graph</h5>
                            <p class="card-text">Discover how to navigate and explore the knowledge graph.</p>
                            <a href="#explore-graph" class="btn btn-primary">Learn More</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-chat-dots"></i>
                            </div>
                            <h5 class="card-title">Ask Questions</h5>
                            <p class="card-text">Learn how to ask questions and get answers from your knowledge base.</p>
                            <a href="#ask-questions" class="btn btn-primary">Learn More</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Upload Documents -->
        <div class="help-section" id="upload-documents">
            <div class="help-section-header">
                <h3>Upload Documents</h3>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>Supported Document Types</h4>
                    <p>Graphiti supports the following document types:</p>
                    <ul>
                        <li><strong>PDF</strong> - Portable Document Format files</li>
                        <li><strong>TXT</strong> - Plain text files</li>
                        <li><strong>DOCX</strong> - Microsoft Word documents</li>
                        <li><strong>HTML</strong> - Web pages</li>
                        <li><strong>MD</strong> - Markdown files</li>
                    </ul>
                    
                    <h4>Document Size Limits</h4>
                    <p>The maximum file size for document uploads is 50MB by default. This can be adjusted in the Settings page.</p>
                    
                    <h4>Batch Upload</h4>
                    <p>To upload multiple documents at once:</p>
                    <ol>
                        <li>Navigate to the <a href="/batch-upload">Batch Upload</a> page</li>
                        <li>Drag and drop files into the upload area or click to select files</li>
                        <li>Click the "Upload" button to start processing</li>
                        <li>Monitor the progress of each document in the processing queue</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Document Processing</h5>
                        </div>
                        <div class="card-body">
                            <p>When you upload a document, Graphiti performs the following steps:</p>
                            <ol>
                                <li><strong>Text Extraction</strong> - Extracts text content from the document</li>
                                <li><strong>Chunking</strong> - Divides the text into manageable chunks (default: 1200 characters)</li>
                                <li><strong>Embedding Generation</strong> - Creates vector embeddings for each chunk</li>
                                <li><strong>Entity Extraction</strong> - Identifies entities like people, organizations, diseases, etc.</li>
                                <li><strong>Relationship Extraction</strong> - Identifies relationships between entities</li>
                                <li><strong>Reference Extraction</strong> - Extracts bibliographic references</li>
                                <li><strong>Knowledge Graph Integration</strong> - Adds the extracted information to the knowledge graph</li>
                            </ol>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> Processing time depends on document size and complexity. Large documents may take several minutes to process.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Explore Knowledge Graph -->
        <div class="help-section" id="explore-graph">
            <div class="help-section-header">
                <h3>Explore Knowledge Graph</h3>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>Knowledge Graph Explorer</h4>
                    <p>The Knowledge Graph Explorer allows you to visualize and interact with the knowledge graph:</p>
                    <ul>
                        <li>View entities and their relationships</li>
                        <li>Zoom in/out and pan the visualization</li>
                        <li>Click on nodes to see detailed information</li>
                        <li>Filter by entity type</li>
                        <li>Search for specific entities</li>
                    </ul>
                    
                    <h4>Graph Search</h4>
                    <p>The Graph Search feature allows you to find paths between entities:</p>
                    <ol>
                        <li>Select a start node</li>
                        <li>Select an end node</li>
                        <li>Set the maximum path length</li>
                        <li>Optionally filter by relationship types</li>
                        <li>Click "Find Paths" to see all paths between the selected nodes</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>Entity Types</h5>
                        </div>
                        <div class="card-body">
                            <p>Graphiti extracts the following entity types:</p>
                            <ul>
                                <li><strong>Person</strong> - Individual people</li>
                                <li><strong>Organization</strong> - Companies, institutions, agencies</li>
                                <li><strong>Location</strong> - Geographic locations</li>
                                <li><strong>Disease</strong> - Medical conditions and diseases</li>
                                <li><strong>Symptom</strong> - Signs and symptoms of diseases</li>
                                <li><strong>Treatment</strong> - Medical treatments and procedures</li>
                                <li><strong>Medication</strong> - Drugs and medications</li>
                                <li><strong>Food</strong> - Food items and categories</li>
                                <li><strong>Herb</strong> - Medicinal herbs and plants</li>
                                <li><strong>Nutrient</strong> - Vitamins, minerals, and other nutrients</li>
                                <li><strong>Process</strong> - Biological or chemical processes</li>
                                <li><strong>Research</strong> - Studies, experiments, and research topics</li>
                            </ul>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> Each entity type is color-coded in the knowledge graph visualization for easy identification.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Ask Questions -->
        <div class="help-section" id="ask-questions">
            <div class="help-section-header">
                <h3>Ask Questions</h3>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>Q&A Interface</h4>
                    <p>The Q&A interface allows you to ask natural language questions about your documents:</p>
                    <ol>
                        <li>Navigate to the <a href="/qa">Q&A</a> page</li>
                        <li>Type your question in the input field</li>
                        <li>Click "Ask" or press Enter</li>
                        <li>View the answer with supporting references</li>
                    </ol>
                    
                    <h4>Tips for Effective Questions</h4>
                    <ul>
                        <li>Be specific and clear in your questions</li>
                        <li>Include key terms that are likely to be in your documents</li>
                        <li>Ask one question at a time for best results</li>
                        <li>Follow up with clarifying questions if needed</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h5>How It Works</h5>
                        </div>
                        <div class="card-body">
                            <p>When you ask a question, Graphiti performs the following steps:</p>
                            <ol>
                                <li><strong>Query Analysis</strong> - Analyzes your question to understand its intent</li>
                                <li><strong>Semantic Search</strong> - Finds relevant document chunks using vector similarity</li>
                                <li><strong>Graph Search</strong> - Identifies relevant entities and relationships in the knowledge graph</li>
                                <li><strong>Answer Generation</strong> - Uses an LLM to generate a comprehensive answer based on the retrieved information</li>
                                <li><strong>Reference Linking</strong> - Adds citations to the source documents</li>
                            </ol>
                            
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> Answers are generated based on the content of your documents. If the information is not in your documents, Graphiti will indicate that it doesn't have enough information to answer.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- FAQ -->
        <div class="help-section">
            <div class="help-section-header">
                <h3>Frequently Asked Questions</h3>
            </div>
            
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faqOne">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="false" aria-controls="collapseOne">
                            What types of documents work best with Graphiti?
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Graphiti works best with scientific and technical documents, especially in the health, nutrition, and medical domains. Research papers, medical journals, textbooks, and technical reports typically contain well-structured information with many entities and relationships that Graphiti can extract effectively.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faqTwo">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                            How can I improve the quality of entity extraction?
                        </button>
                    </h2>
                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            To improve entity extraction quality:
                            <ul>
                                <li>Use high-quality, well-formatted documents</li>
                                <li>Adjust the LLM model in Settings (more powerful models generally provide better extraction)</li>
                                <li>Manually review and edit entities after extraction</li>
                                <li>Use domain-specific documents that contain clear terminology</li>
                                <li>Consider pre-processing documents to remove irrelevant sections</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faqThree">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                            Can I export data from Graphiti?
                        </button>
                    </h2>
                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Yes, Graphiti provides several export options:
                            <ul>
                                <li>Export references to CSV format</li>
                                <li>Export search results to CSV</li>
                                <li>Export the knowledge graph in various formats (JSON, GraphML, CSV)</li>
                                <li>Export entity lists with their properties</li>
                                <li>Save Q&A conversations for future reference</li>
                            </ul>
                            Look for export buttons in the respective interfaces.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faqFour">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                            How do I configure FalkorDB and Redis?
                        </button>
                    </h2>
                    <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="faqFour" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            FalkorDB and Redis configuration can be adjusted in the Settings page. For optimal performance:
                            <ul>
                                <li>Ensure both services are running before using Graphiti</li>
                                <li>Use the "Test Connection" button to verify connectivity</li>
                                <li>For large knowledge graphs, allocate more memory to FalkorDB</li>
                                <li>For vector search, ensure Redis has the RediSearch module installed</li>
                                <li>Use different ports for FalkorDB and Redis to avoid conflicts</li>
                            </ul>
                            Default settings work well for most use cases.
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faqFive">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFive" aria-expanded="false" aria-controls="collapseFive">
                            What should I do if document processing fails?
                        </button>
                    </h2>
                    <div id="collapseFive" class="accordion-collapse collapse" aria-labelledby="faqFive" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            If document processing fails:
                            <ol>
                                <li>Check the document format and ensure it's supported</li>
                                <li>Verify the document isn't corrupted or password-protected</li>
                                <li>Check if the document exceeds the maximum file size limit</li>
                                <li>Look for error messages in the processing log</li>
                                <li>Try processing a smaller portion of the document</li>
                                <li>Ensure all services (FalkorDB, Redis) are running</li>
                                <li>Restart the application if issues persist</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Support -->
        <div class="help-section">
            <div class="help-section-header">
                <h3>Additional Support</h3>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-book"></i>
                            </div>
                            <h5 class="card-title">Documentation</h5>
                            <p class="card-text">Access comprehensive documentation for Graphiti.</p>
                            <a href="https://github.com/yourusername/graphiti/docs" class="btn btn-primary" target="_blank">View Docs</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-github"></i>
                            </div>
                            <h5 class="card-title">GitHub Repository</h5>
                            <p class="card-text">View source code, report issues, and contribute.</p>
                            <a href="https://github.com/yourusername/graphiti" class="btn btn-primary" target="_blank">Visit GitHub</a>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="card help-card">
                        <div class="card-body text-center">
                            <div class="help-icon">
                                <i class="bi bi-envelope"></i>
                            </div>
                            <h5 class="card-title">Contact Support</h5>
                            <p class="card-text">Get help from our support team.</p>
                            <a href="mailto:<EMAIL>" class="btn btn-primary">Email Support</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
