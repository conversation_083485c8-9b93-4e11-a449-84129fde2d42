/**
 * Enhanced reference display for Graphiti Knowledge Graph
 *
 * This script provides improved reference display in the UI, including:
 * 1. Better formatting of reference metadata
 * 2. Support for different citation styles
 * 3. Improved filtering and search
 * 4. Enhanced visualization
 */

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeReferenceDisplay();
});

function initializeReferenceDisplay() {
    // Reference elements
    const referencesList = document.getElementById('references-list');
    const referencesTab = document.getElementById('references-tab');
    const referencesLoading = document.getElementById('references-loading');
    const searchInput = document.getElementById('reference-search-input');
    const searchButton = document.getElementById('search-references-button');
    const documentSelector = document.getElementById('document-selector');
    const exportButton = document.getElementById('export-references-button');
    const visualizeButton = document.getElementById('visualize-references-button');

    // If references tab is not active, don't initialize
    if (!referencesList && !referencesTab) return;

    // Reference data
    let allReferences = [];
    let filteredReferences = [];
    let currentCitationStyle = 'apa'; // Default citation style

    // Create citation style selector
    createCitationStyleSelector();

    // Initialize search and filters
    if (searchInput) {
        searchInput.addEventListener('input', filterReferences);
        // Also trigger search on Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                filterReferences();
            }
        });
    }

    if (searchButton) {
        searchButton.addEventListener('click', filterReferences);
    }

    if (documentSelector) {
        documentSelector.addEventListener('change', loadReferences);
        populateDocumentSelector();
    }

    // Initialize export button
    if (exportButton) {
        exportButton.addEventListener('click', exportReferences);
    }

    // Initialize visualize button
    if (visualizeButton) {
        visualizeButton.addEventListener('click', function() {
            visualizeReferences(filteredReferences);
        });
    }

    // Load references on page load
    loadReferences();

    /**
     * Create citation style selector
     */
    function createCitationStyleSelector() {
        // Check if we have a container for the selector
        const container = document.getElementById('reference-filters');
        if (!container) return;

        // Create the selector div
        const selectorDiv = document.createElement('div');
        selectorDiv.className = 'col-md-4';

        // Create the label
        const label = document.createElement('label');
        label.htmlFor = 'citation-style-selector';
        label.className = 'form-label';
        label.textContent = 'Citation Style';

        // Create the select element
        const select = document.createElement('select');
        select.id = 'citation-style-selector';
        select.className = 'form-select';

        // Add options
        const options = [
            { value: 'apa', text: 'APA' },
            { value: 'mla', text: 'MLA' },
            { value: 'chicago', text: 'Chicago' },
            { value: 'raw', text: 'Raw Text' }
        ];

        options.forEach(option => {
            const optionElement = document.createElement('option');
            optionElement.value = option.value;
            optionElement.textContent = option.text;
            select.appendChild(optionElement);
        });

        // Add event listener
        select.addEventListener('change', function() {
            currentCitationStyle = this.value;
            displayReferences(filteredReferences);
        });

        // Add to container
        selectorDiv.appendChild(label);
        selectorDiv.appendChild(select);
        container.appendChild(selectorDiv);
    }

    /**
     * Populate document selector with available documents
     */
    async function populateDocumentSelector() {
        if (!documentSelector) return;

        try {
            // Get both documents and references to match them properly
            // Fetch more documents to ensure we get all of them (including Cocoa documents at the end)
            const [docsResponse, refsResponse] = await Promise.all([
                fetch('/api/documents?page_size=100'),  // Increase limit to get all documents
                fetch('/api/references')
            ]);

            const docsData = await docsResponse.json();
            const refsData = await refsResponse.json();

            if (docsResponse.ok && docsData.documents && refsResponse.ok && refsData.references) {
                // Get unique source documents from references (CSV data)
                const sourceDocuments = new Set(refsData.references.map(ref => ref.source_document));

                // Add options for documents that have references in the CSV
                docsData.documents.forEach(doc => {
                    // Check if this document has references in the CSV (direct filename match)
                    if (sourceDocuments.has(doc.filename)) {
                        const option = document.createElement('option');
                        // Use the document filename as value to match references
                        option.value = doc.filename;
                        // Display clean title by removing UUID prefix for better UX
                        const displayTitle = doc.title || doc.filename.replace(/^[a-f0-9-]+_/, '') || 'Unknown Document';
                        option.textContent = displayTitle;
                        documentSelector.appendChild(option);
                    }
                });
            }
        } catch (error) {
            console.error('Error loading documents:', error);
        }
    }

    /**
     * Load references from the API
     */
    async function loadReferences() {
        if (!referencesList) return;

        try {
            // Show loading indicator
            if (referencesLoading) referencesLoading.style.display = 'flex';

            // Build query parameters
            const params = new URLSearchParams();

            // Add filter parameters
            if (documentSelector && documentSelector.value) {
                params.append('source_document', documentSelector.value);
            }

            if (searchInput && searchInput.value) {
                params.append('search_query', searchInput.value);
            }

            const response = await fetch(`/api/references?${params.toString()}`);
            const data = await response.json();

            if (response.ok) {
                allReferences = data.references || [];
                filteredReferences = [...allReferences];

                // Enable export button if references exist
                if (exportButton) exportButton.disabled = allReferences.length === 0;
                if (visualizeButton) visualizeButton.disabled = allReferences.length === 0;

                // Display references
                displayReferences(filteredReferences);

                // Update statistics if available
                updateReferenceStatistics(data.statistics || {
                    total_references: allReferences.length,
                    total_documents: new Set(allReferences.map(r => r.source_document)).size,
                    total_journals: new Set(allReferences.map(r => r.journal).filter(Boolean)).size,
                    total_authors: new Set(allReferences.map(r => r.authors).filter(Boolean)).size
                });
            } else {
                showError('Error loading references: ' + (data.detail || 'Unknown error'));
            }
        } catch (error) {
            showError('Error: ' + error.message);
        } finally {
            if (referencesLoading) referencesLoading.style.display = 'none';
        }
    }

    /**
     * Filter references based on search input
     */
    function filterReferences() {
        if (!searchInput || !allReferences) return;

        const searchTerm = searchInput.value.toLowerCase();

        if (!searchTerm) {
            filteredReferences = [...allReferences];
        } else {
            filteredReferences = allReferences.filter(ref => {
                return (
                    (ref.title && ref.title.toLowerCase().includes(searchTerm)) ||
                    (ref.authors && ref.authors.toLowerCase().includes(searchTerm)) ||
                    (ref.journal && ref.journal.toLowerCase().includes(searchTerm)) ||
                    (ref.year && ref.year.toString().includes(searchTerm)) ||
                    (ref.reference_text && ref.reference_text.toLowerCase().includes(searchTerm))
                );
            });
        }

        displayReferences(filteredReferences);
    }

    /**
     * Display references in the UI
     *
     * @param {Array} references - Array of reference objects
     */
    function displayReferences(references) {
        if (!referencesList) return;

        // Create references container
        const container = document.createElement('div');
        container.className = 'references-container';

        // Add reference count
        const countInfo = document.createElement('p');
        countInfo.className = 'text-muted mb-3';
        countInfo.textContent = `Showing ${references.length} references`;
        container.appendChild(countInfo);

        // Create references list
        const refsContainer = document.createElement('div');
        refsContainer.className = 'list-group';

        // Add each reference
        references.forEach(ref => {
            const refItem = createReferenceItem(ref);
            refsContainer.appendChild(refItem);
        });

        container.appendChild(refsContainer);
        referencesList.innerHTML = '';
        referencesList.appendChild(container);
    }

    /**
     * Create a reference item
     *
     * @param {Object} ref - Reference object
     * @returns {HTMLElement} Reference item element
     */
    function createReferenceItem(ref) {
        const refItem = document.createElement('div');
        refItem.className = 'list-group-item';

        // Get citation based on selected style
        let citation = '';
        if (currentCitationStyle === 'apa' && ref.citation_apa) {
            citation = ref.citation_apa;
        } else if (currentCitationStyle === 'mla' && ref.citation_mla) {
            citation = ref.citation_mla;
        } else if (currentCitationStyle === 'chicago' && ref.citation_chicago) {
            citation = ref.citation_chicago;
        } else if (currentCitationStyle === 'raw' && ref.reference_text) {
            citation = ref.reference_text;
        } else {
            // Fallback to constructing citation from components
            citation = constructCitation(ref);
        }

        // Create citation element
        const citationElement = document.createElement('p');
        citationElement.className = 'mb-1';
        citationElement.innerHTML = citation;

        // Create metadata section
        const metadataSection = document.createElement('div');
        metadataSection.className = 'reference-metadata small text-muted mt-2';

        // Add metadata fields
        const metadataFields = [];

        if (ref.doi) {
            metadataFields.push(`<strong>DOI:</strong> <a href="https://doi.org/${ref.doi}" target="_blank">${ref.doi}</a>`);
        }

        if (ref.pmid) {
            metadataFields.push(`<strong>PMID:</strong> <a href="https://pubmed.ncbi.nlm.nih.gov/${ref.pmid}" target="_blank">${ref.pmid}</a>`);
        }

        if (ref.source_document) {
            metadataFields.push(`<strong>Source:</strong> ${ref.source_document}`);
        }

        if (ref.extraction_method) {
            const method = ref.extraction_method.charAt(0).toUpperCase() + ref.extraction_method.slice(1);
            metadataFields.push(`<strong>Extraction:</strong> ${method}`);
        }

        metadataSection.innerHTML = metadataFields.join(' | ');

        // Add elements to item
        refItem.appendChild(citationElement);
        refItem.appendChild(metadataSection);

        return refItem;
    }

    /**
     * Construct a citation from reference components
     *
     * @param {Object} ref - Reference object
     * @returns {string} Formatted citation
     */
    function constructCitation(ref) {
        let citation = '';

        // Authors
        if (ref.authors) {
            citation += `${ref.authors}`;
        }

        // Year
        if (ref.year) {
            citation += ` (${ref.year}).`;
        }

        // Title
        if (ref.title) {
            citation += ` ${ref.title}.`;
        }

        // Journal
        if (ref.journal) {
            citation += ` <em>${ref.journal}</em>`;
        }

        // Volume and issue
        if (ref.volume) {
            citation += `, ${ref.volume}`;
            if (ref.issue) {
                citation += `(${ref.issue})`;
            }
        }

        // Pages
        if (ref.pages) {
            citation += `, ${ref.pages}`;
        }

        // DOI
        if (ref.doi) {
            citation += `. https://doi.org/${ref.doi}`;
        }

        return citation || ref.reference_text || 'Unknown reference';
    }

    /**
     * Update reference statistics in the UI
     *
     * @param {Object} statistics - Reference statistics
     */
    function updateReferenceStatistics(statistics) {
        if (!statistics) return;

        // Update count elements if they exist
        const referencesCount = document.getElementById('references-count');
        const documentsCount = document.getElementById('documents-count');
        const journalsCount = document.getElementById('journals-count');
        const authorsCount = document.getElementById('authors-count');

        if (referencesCount) referencesCount.textContent = statistics.total_references || 0;
        if (documentsCount) documentsCount.textContent = statistics.total_documents || 0;
        if (journalsCount) journalsCount.textContent = statistics.total_journals || 0;
        if (authorsCount) authorsCount.textContent = statistics.total_authors || 0;
    }

    /**
     * Export references to CSV
     */
    function exportReferences() {
        if (filteredReferences.length === 0) return;

        // Build query parameters for export
        const params = new URLSearchParams();
        params.append('format', 'csv');

        // Add filter parameters
        if (documentSelector && documentSelector.value) {
            params.append('source_document', documentSelector.value);
        }

        if (searchInput && searchInput.value) {
            params.append('search_query', searchInput.value);
        }

        // Open export URL in new window
        window.open(`/api/references/export?${params.toString()}`, '_blank');
    }

    /**
     * Show error message
     *
     * @param {string} message - Error message
     */
    function showError(message) {
        if (!referencesList) return;

        const errorAlert = document.createElement('div');
        errorAlert.className = 'alert alert-danger';
        errorAlert.textContent = message;

        referencesList.innerHTML = '';
        referencesList.appendChild(errorAlert);
    }
}
