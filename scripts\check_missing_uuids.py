"""
<PERSON><PERSON><PERSON> to check for missing UUIDs across all node and relationship types in the database.

This script:
1. Checks Episode nodes (documents) for missing UUIDs
2. Checks Fact nodes (document chunks) for missing UUIDs
3. Checks Entity nodes for missing UUIDs
4. Checks all relationship types for missing UUIDs

Usage:
    python scripts/check_missing_uuids.py [--verbose]
"""

import os
import asyncio
import logging
import argparse
from typing import Dict, List, Any, Optional, Tuple

import sys
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from database.database_service import get_falkordb_adapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def check_episode_nodes() -> Tuple[int, int]:
    """
    Check Episode nodes for missing UUIDs.

    Returns:
        Tuple of (total_nodes, missing_uuid_count)
    """
    adapter = await get_falkordb_adapter()

    # Count all Episode nodes
    count_query = """
    MATCH (e:Episode)
    RETURN count(e) as total
    """

    count_result = adapter.execute_cypher(count_query)
    total_nodes = 0

    if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
        total_nodes = count_result[1][0][0]

    # Count Episode nodes without UUIDs
    missing_query = """
    MATCH (e:Episode)
    WHERE e.uuid IS NULL
    RETURN count(e) as missing
    """

    missing_result = adapter.execute_cypher(missing_query)
    missing_uuid_count = 0

    if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
        missing_uuid_count = missing_result[1][0][0]

    return total_nodes, missing_uuid_count

async def check_fact_nodes() -> Tuple[int, int]:
    """
    Check Fact nodes for missing UUIDs.

    Returns:
        Tuple of (total_nodes, missing_uuid_count)
    """
    adapter = await get_falkordb_adapter()

    # Count all Fact nodes
    count_query = """
    MATCH (f:Fact)
    RETURN count(f) as total
    """

    count_result = adapter.execute_cypher(count_query)
    total_nodes = 0

    if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
        total_nodes = count_result[1][0][0]

    # Count Fact nodes without UUIDs
    missing_query = """
    MATCH (f:Fact)
    WHERE f.uuid IS NULL
    RETURN count(f) as missing
    """

    missing_result = adapter.execute_cypher(missing_query)
    missing_uuid_count = 0

    if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
        missing_uuid_count = missing_result[1][0][0]

    return total_nodes, missing_uuid_count

async def check_entity_nodes() -> Tuple[int, int]:
    """
    Check Entity nodes for missing UUIDs.

    Returns:
        Tuple of (total_nodes, missing_uuid_count)
    """
    adapter = await get_falkordb_adapter()

    # Count all Entity nodes
    count_query = """
    MATCH (e:Entity)
    RETURN count(e) as total
    """

    count_result = adapter.execute_cypher(count_query)
    total_nodes = 0

    if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
        total_nodes = count_result[1][0][0]

    # Count Entity nodes without UUIDs
    missing_query = """
    MATCH (e:Entity)
    WHERE e.uuid IS NULL
    RETURN count(e) as missing
    """

    missing_result = adapter.execute_cypher(missing_query)
    missing_uuid_count = 0

    if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
        missing_uuid_count = missing_result[1][0][0]

    return total_nodes, missing_uuid_count

async def check_relationships() -> Tuple[int, int]:
    """
    Check relationships for missing UUIDs.

    Returns:
        Tuple of (total_relationships, missing_uuid_count)
    """
    adapter = await get_falkordb_adapter()

    # Count all relationships
    count_query = """
    MATCH ()-[r]->()
    RETURN count(r) as total
    """

    count_result = adapter.execute_cypher(count_query)
    total_relationships = 0

    if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
        total_relationships = count_result[1][0][0]

    # Count relationships without UUIDs
    missing_query = """
    MATCH ()-[r]->()
    WHERE r.uuid IS NULL
    RETURN count(r) as missing
    """

    missing_result = adapter.execute_cypher(missing_query)
    missing_uuid_count = 0

    if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
        missing_uuid_count = missing_result[1][0][0]

    return total_relationships, missing_uuid_count

async def check_merged_entities() -> List[Dict[str, Any]]:
    """
    Check for potential issues with merged entities.

    Returns:
        List of potential issues
    """
    adapter = await get_falkordb_adapter()
    issues = []

    # Check for relationships pointing to non-existent entities
    orphaned_query = """
    MATCH ()-[r]->(e:Entity)
    WHERE NOT EXISTS(e.uuid)
    RETURN count(r) as orphaned
    """

    orphaned_result = adapter.execute_cypher(orphaned_query)
    orphaned_count = 0

    if orphaned_result and len(orphaned_result) > 1 and len(orphaned_result[1]) > 0:
        orphaned_count = orphaned_result[1][0][0]

    if orphaned_count > 0:
        issues.append({
            "issue_type": "orphaned_relationships",
            "count": orphaned_count,
            "description": f"Found {orphaned_count} relationships pointing to entities without UUIDs"
        })

    return issues

async def main():
    """Main function."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Check for missing UUIDs in the database")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    try:
        # Check Episode nodes
        episode_total, episode_missing = await check_episode_nodes()
        logger.info(f"Episodes: {episode_missing} out of {episode_total} missing UUIDs")

        # Check Fact nodes
        fact_total, fact_missing = await check_fact_nodes()
        logger.info(f"Facts: {fact_missing} out of {fact_total} missing UUIDs")

        # Check Entity nodes
        entity_total, entity_missing = await check_entity_nodes()
        logger.info(f"Entities: {entity_missing} out of {entity_total} missing UUIDs")

        # Check relationships
        rel_total, rel_missing = await check_relationships()
        logger.info(f"Relationships: {rel_missing} out of {rel_total} missing UUIDs")

        # Check merged entities
        merged_issues = await check_merged_entities()
        if merged_issues:
            logger.info("Issues with merged entities:")
            for issue in merged_issues:
                logger.info(f"  - {issue['description']}")
        else:
            logger.info("No issues found with merged entities")

        # Summary
        total_missing = episode_missing + fact_missing + entity_missing + rel_missing
        total_nodes = episode_total + fact_total + entity_total + rel_total

        logger.info(f"Summary: {total_missing} out of {total_nodes} elements missing UUIDs")

        if total_missing > 0:
            logger.info("Run 'python scripts/fix_missing_uuids.py' to fix missing UUIDs")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
