#!/usr/bin/env python3
"""
Test script to check entity detail API.
"""

import requests
import asyncio
from database.database_service import get_falkordb_adapter

async def get_andrographis_uuid():
    """Get the UUID for Andrographis entity"""
    adapter = await get_falkordb_adapter()
    
    query = """
    MATCH (e:Entity)
    WHERE e.name = 'Andrographis' AND e.type = 'Herb'
    RETURN e.uuid as uuid, e.name as name, e.type as type
    LIMIT 1
    """
    
    result = adapter.execute_cypher(query)
    if result and len(result) > 1 and len(result[1]) > 0:
        return result[1][0][0]  # Return the UUID
    return None

def test_entity_detail_api():
    """Test entity detail API"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entity Detail API")
    print("=" * 40)
    
    # First get Andrographis UUID
    print("1. Getting Andrographis UUID...")
    try:
        uuid = asyncio.run(get_andrographis_uuid())
        if not uuid:
            print("❌ Could not find Andrographis entity")
            return
        
        print(f"✅ Found Andrographis UUID: {uuid}")
        
        # Test the entity detail API
        print(f"\n2. Testing /api/entity/{uuid}")
        response = requests.get(f"{base_url}/api/entity/{uuid}", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            if 'entity' in data:
                entity = data['entity']
                print(f"Entity data:")
                print(f"  Name: {entity.get('name', 'N/A')}")
                print(f"  Type: {entity.get('type', 'N/A')}")
                print(f"  UUID: {entity.get('uuid', 'N/A')}")
                print(f"  Confidence: {entity.get('confidence', 'N/A')}")
                print(f"  Mention count: {entity.get('mention_count', 'N/A')}")
            else:
                print("❌ No 'entity' key in response")
                print(f"Full response: {data}")
                
            if 'relationships' in data:
                relationships = data['relationships']
                print(f"Relationships: {len(relationships)} found")
            
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_entity_detail_api()
