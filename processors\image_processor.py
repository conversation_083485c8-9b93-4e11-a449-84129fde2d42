"""
Image Document Processor
Handles image files with OCR text extraction.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ImageProcessor:
    """
    Processor for image documents (.jpg, .jpeg, .png, .gif, .tiff, .bmp).
    """
    
    def __init__(self):
        """Initialize the image processor."""
        self.supported_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.tiff', '.tif', '.bmp']
        
        # Initialize available OCR methods
        self.mistral_ocr = self._initialize_mistral_ocr()
        self.pillow_available = self._check_pillow_availability()
    
    def _initialize_mistral_ocr(self):
        """Initialize Mistral OCR if available."""
        try:
            from utils.mistral_ocr import MistralOCRProcessor
            return MistralOCRProcessor()
        except Exception as e:
            logger.warning(f"Mistral OCR not available: {e}")
            return None
    
    def _check_pillow_availability(self) -> bool:
        """Check if Pillow is available."""
        try:
            from PIL import Image
            return True
        except ImportError:
            logger.warning("Pillow not available. Install with: pip install Pillow")
            return False
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from an image file using OCR.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Try Mistral OCR first (best quality)
            if self.mistral_ocr:
                try:
                    logger.info(f"Extracting text from {file_path} using Mistral OCR")
                    text = await self.mistral_ocr.extract_text_from_image(str(file_path))
                    
                    if text and len(text.strip()) > 0:
                        metadata = await self._extract_metadata_mistral(file_path)
                        return {
                            'success': True,
                            'text': text.strip(),
                            'metadata': metadata,
                            'ocr_provider': 'mistral-ocr',
                            'extraction_method': 'mistral_ocr'
                        }
                    else:
                        logger.warning(f"Mistral OCR returned empty text for {file_path}")
                except Exception as e:
                    logger.warning(f"Mistral OCR failed for {file_path}: {e}")
            
            # If OCR fails or returns no text, still provide image metadata
            metadata = await self._extract_image_metadata(file_path)
            
            return {
                'success': True,
                'text': '',  # No text extracted
                'metadata': metadata,
                'ocr_provider': 'none',
                'extraction_method': 'metadata_only',
                'note': 'No text extracted from image. Only metadata available.'
            }
            
        except Exception as e:
            logger.error(f"Error processing image file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata_mistral(self, file_path: Path) -> Dict[str, Any]:
        """Extract metadata when using Mistral OCR."""
        try:
            # Get basic file metadata
            metadata = await self._extract_image_metadata(file_path)
            metadata['extraction_method'] = 'mistral_ocr'
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting Mistral metadata: {e}")
            return {
                'title': file_path.stem,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    async def _extract_image_metadata(self, file_path: Path) -> Dict[str, Any]:
        """Extract image metadata using Pillow."""
        try:
            stat = file_path.stat()
            
            metadata = {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'image_metadata'
            }
            
            # Try to get image dimensions and other info using Pillow
            if self.pillow_available:
                try:
                    from PIL import Image
                    from PIL.ExifTags import TAGS
                    
                    with Image.open(file_path) as img:
                        metadata.update({
                            'width': img.width,
                            'height': img.height,
                            'mode': img.mode,
                            'format': img.format,
                            'has_transparency': img.mode in ('RGBA', 'LA') or 'transparency' in img.info
                        })
                        
                        # Extract EXIF data if available
                        if hasattr(img, '_getexif') and img._getexif():
                            exif_data = {}
                            exif = img._getexif()
                            
                            for tag_id, value in exif.items():
                                tag = TAGS.get(tag_id, tag_id)
                                exif_data[tag] = str(value)
                            
                            metadata['exif_data'] = exif_data
                            
                            # Extract common EXIF fields
                            if 'DateTime' in exif_data:
                                metadata['date_taken'] = exif_data['DateTime']
                            if 'Artist' in exif_data:
                                metadata['author'] = exif_data['Artist']
                            if 'ImageDescription' in exif_data:
                                metadata['description'] = exif_data['ImageDescription']
                            if 'Software' in exif_data:
                                metadata['software'] = exif_data['Software']
                            if 'Make' in exif_data and 'Model' in exif_data:
                                metadata['camera'] = f"{exif_data['Make']} {exif_data['Model']}"
                
                except Exception as e:
                    logger.warning(f"Error extracting image metadata with Pillow: {e}")
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting image metadata: {e}")
            return {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the image content.
        
        Args:
            file_path: Path to the image file
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars] if text else "[Image file - no text extracted]"
            
            if text and len(text) > max_chars:
                preview_text += "... [truncated]"
            
            # Add image-specific preview info
            metadata = result['metadata']
            preview_info = {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text) if text else 0,
                'metadata': metadata,
                'extraction_method': result.get('extraction_method', 'unknown'),
                'is_image': True
            }
            
            # Add image dimensions if available
            if 'width' in metadata and 'height' in metadata:
                preview_info['dimensions'] = f"{metadata['width']} x {metadata['height']}"
            
            if 'format' in metadata:
                preview_info['image_format'] = metadata['format']
            
            return preview_info
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
    
    async def get_image_info(self, file_path: Path) -> Dict[str, Any]:
        """
        Get detailed image information without text extraction.
        
        Args:
            file_path: Path to the image file
            
        Returns:
            Dictionary containing image information
        """
        try:
            metadata = await self._extract_image_metadata(file_path)
            
            return {
                'success': True,
                'metadata': metadata,
                'is_image': True
            }
            
        except Exception as e:
            logger.error(f"Error getting image info for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'is_image': True
            }
