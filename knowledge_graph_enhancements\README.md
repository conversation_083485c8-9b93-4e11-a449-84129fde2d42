# Knowledge Graph Enhancements

This directory contains scripts for enhancing the Graphiti knowledge graph with advanced features:

## Scripts

### 1. setup_relationship_types.py

Sets up relationship types in the Neo4j database with descriptions and examples.

**Features:**
- Creates 20+ relationship types (IS_A, PART_OF, TREATS, etc.)
- Adds descriptions and examples for each relationship type
- Creates a constraint for unique relationship type names

**Usage:**
```bash
python setup_relationship_types.py
```

### 2. enhanced_relationship_extraction.py

Extracts relationships between entities with confidence scores and evidence.

**Features:**
- Identifies relationships between entities in text
- Assigns confidence scores to relationships
- Tracks evidence for each relationship
- Uses APOC procedures for reliable relationship creation

**Usage:**
```bash
# This script is called by the main processing pipeline
# It can also be imported and used programmatically
```

### 3. domain_specific_attributes.py

Adds domain-specific attributes to entities using LLM extraction.

**Features:**
- Extracts attributes for different entity types (Herb, Nutrient, etc.)
- Uses OpenAI API for attribute extraction
- Supports batch processing of entities
- Provides command-line interface for processing and retrieval

**Usage:**
```bash
# Process entities of a specific type
python domain_specific_attributes.py process --type Herb --batch-size 5 --max-entities 10

# Get attributes for a specific entity
python domain_specific_attributes.py get --name "Entity Name"
```

### 4. advanced_search.py

Implements advanced search functionality for the knowledge graph.

**Features:**
- Search by entity type
- Search by relationship type
- Filter by confidence score
- Keyword search across entities and relationships
- Combined filtering for precise results

**Usage:**
```bash
# Get all entity types
python advanced_search.py entity-types

# Get all relationship types
python advanced_search.py relationship-types

# Search for entities by type
python advanced_search.py entity-search --entity-type Herb --limit 10

# Search for relationships by type
python advanced_search.py relationship-search --relationship-type TREATS --limit 10

# Search by keyword
python advanced_search.py keyword-search --query "Vitamin E" --limit 10

# Advanced search with multiple filters
python advanced_search.py advanced-search --query "cancer" --entity-type "Herb" --relationship-type "TREATS" --min-confidence 0.7 --limit 10
```

## Integration

These enhancements are integrated with the main Graphiti system through:

1. **Web Interface**: The enhanced features are accessible through the web interface
2. **API Endpoints**: Advanced search and entity attributes are available via API
3. **Processing Pipeline**: Relationship extraction is part of the document processing pipeline
4. **Knowledge Graph Structure**: Relationship types and entity attributes are stored in the Neo4j database

## Dependencies

- Neo4j 5.x Enterprise Edition
- APOC procedures installed in Neo4j
- OpenAI API key for attribute extraction
- Python 3.9+ with required packages
