#!/usr/bin/env python3
"""
Test script for type-specific entity deduplication rules.

This script demonstrates the specialized rules for different entity types.
"""

import sys
import os
import logging
from typing import List

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.type_specific_rules import get_type_rule_engine

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_person_test_entities() -> List[EntityForDeduplication]:
    """Create test entities for Person type."""
    return [
        EntityForDeduplication(uuid="p1", name="<PERSON>. <PERSON>", type="Person", confidence=0.9),
        EntityForDeduplication(uuid="p2", name="<PERSON>", type="Person", confidence=0.85),
        EntityForDeduplication(uuid="p3", name="<PERSON><PERSON>", type="Person", confidence=0.7),
        EntityForDeduplication(uuid="p4", name="<PERSON>, <PERSON>", type="Person", confidence=0.88),
        EntityForDeduplication(uuid="p5", name="Prof. <PERSON>", type="Person", confidence=0.92),
        EntityForDeduplication(uuid="p6", name="Smith, John", type="Person", confidence=0.8),
        EntityForDeduplication(uuid="p7", name="John Doe", type="Person", confidence=0.9),
    ]


def create_organization_test_entities() -> List[EntityForDeduplication]:
    """Create test entities for Organization type."""
    return [
        EntityForDeduplication(uuid="o1", name="Harvard Medical School", type="Organization", confidence=0.95),
        EntityForDeduplication(uuid="o2", name="Harvard Med School", type="Organization", confidence=0.8),
        EntityForDeduplication(uuid="o3", name="HMS", type="Organization", confidence=0.7),
        EntityForDeduplication(uuid="o4", name="Google Inc.", type="Organization", confidence=0.9),
        EntityForDeduplication(uuid="o5", name="Google LLC", type="Organization", confidence=0.88),
        EntityForDeduplication(uuid="o6", name="Google", type="Organization", confidence=0.85),
        EntityForDeduplication(uuid="o7", name="Massachusetts Institute of Technology", type="Organization", confidence=0.95),
        EntityForDeduplication(uuid="o8", name="MIT", type="Organization", confidence=0.9),
    ]


def create_location_test_entities() -> List[EntityForDeduplication]:
    """Create test entities for Location type."""
    return [
        EntityForDeduplication(uuid="l1", name="Boston", type="Location", confidence=0.9),
        EntityForDeduplication(uuid="l2", name="Boston, MA", type="Location", confidence=0.92),
        EntityForDeduplication(uuid="l3", name="Boston, Massachusetts", type="Location", confidence=0.95),
        EntityForDeduplication(uuid="l4", name="New York City", type="Location", confidence=0.9),
        EntityForDeduplication(uuid="l5", name="NYC", type="Location", confidence=0.8),
        EntityForDeduplication(uuid="l6", name="St. Louis", type="Location", confidence=0.9),
        EntityForDeduplication(uuid="l7", name="Saint Louis", type="Location", confidence=0.88),
        EntityForDeduplication(uuid="l8", name="Mt. Vernon", type="Location", confidence=0.85),
        EntityForDeduplication(uuid="l9", name="Mount Vernon", type="Location", confidence=0.9),
    ]


def test_person_rules():
    """Test person-specific deduplication rules."""
    logger.info("=== Testing Person Entity Rules ===")
    
    entities = create_person_test_entities()
    rule_engine = get_type_rule_engine()
    
    # Test cases for person matching
    test_cases = [
        (entities[0], entities[1], "Dr. John Smith vs John A. Smith"),
        (entities[0], entities[2], "Dr. John Smith vs J. Smith"),
        (entities[0], entities[3], "Dr. John Smith vs John Smith, MD"),
        (entities[1], entities[4], "John A. Smith vs Prof. John Alexander Smith"),
        (entities[0], entities[6], "Dr. John Smith vs John Doe (different person)"),
    ]
    
    for entity1, entity2, description in test_cases:
        logger.info(f"\n{description}")
        
        match_result = rule_engine.calculate_type_specific_similarity(entity1, entity2)
        if match_result:
            logger.info(f"  Similarity: {match_result.similarity_score:.3f}")
            logger.info(f"  Match Type: {match_result.match_type}")
            logger.info(f"  Confidence: {match_result.confidence:.3f}")
            logger.info(f"  Reasoning: {match_result.reasoning}")
            
            factors = match_result.type_specific_factors
            logger.info(f"  Last Name Sim: {factors.get('last_name_similarity', 0):.3f}")
            logger.info(f"  First Name Sim: {factors.get('first_name_similarity', 0):.3f}")


def test_organization_rules():
    """Test organization-specific deduplication rules."""
    logger.info("\n=== Testing Organization Entity Rules ===")
    
    entities = create_organization_test_entities()
    rule_engine = get_type_rule_engine()
    
    # Test cases for organization matching
    test_cases = [
        (entities[0], entities[1], "Harvard Medical School vs Harvard Med School"),
        (entities[0], entities[2], "Harvard Medical School vs HMS"),
        (entities[3], entities[4], "Google Inc. vs Google LLC"),
        (entities[4], entities[5], "Google LLC vs Google"),
        (entities[6], entities[7], "Massachusetts Institute of Technology vs MIT"),
    ]
    
    for entity1, entity2, description in test_cases:
        logger.info(f"\n{description}")
        
        match_result = rule_engine.calculate_type_specific_similarity(entity1, entity2)
        if match_result:
            logger.info(f"  Similarity: {match_result.similarity_score:.3f}")
            logger.info(f"  Match Type: {match_result.match_type}")
            logger.info(f"  Confidence: {match_result.confidence:.3f}")
            logger.info(f"  Reasoning: {match_result.reasoning}")
            
            factors = match_result.type_specific_factors
            logger.info(f"  Basic Sim: {factors.get('basic_similarity', 0):.3f}")
            logger.info(f"  Abbrev Sim: {factors.get('abbreviation_similarity', 0):.3f}")


def test_location_rules():
    """Test location-specific deduplication rules."""
    logger.info("\n=== Testing Location Entity Rules ===")
    
    entities = create_location_test_entities()
    rule_engine = get_type_rule_engine()
    
    # Test cases for location matching
    test_cases = [
        (entities[0], entities[1], "Boston vs Boston, MA"),
        (entities[1], entities[2], "Boston, MA vs Boston, Massachusetts"),
        (entities[3], entities[4], "New York City vs NYC"),
        (entities[5], entities[6], "St. Louis vs Saint Louis"),
        (entities[7], entities[8], "Mt. Vernon vs Mount Vernon"),
    ]
    
    for entity1, entity2, description in test_cases:
        logger.info(f"\n{description}")
        
        match_result = rule_engine.calculate_type_specific_similarity(entity1, entity2)
        if match_result:
            logger.info(f"  Similarity: {match_result.similarity_score:.3f}")
            logger.info(f"  Match Type: {match_result.match_type}")
            logger.info(f"  Confidence: {match_result.confidence:.3f}")
            logger.info(f"  Reasoning: {match_result.reasoning}")
            
            factors = match_result.type_specific_factors
            logger.info(f"  Primary Sim: {factors.get('primary_similarity', 0):.3f}")
            logger.info(f"  Abbrev Sim: {factors.get('abbreviation_similarity', 0):.3f}")


def test_entity_variants():
    """Test entity variant generation."""
    logger.info("\n=== Testing Entity Variants ===")
    
    rule_engine = get_type_rule_engine()
    
    test_entities = [
        EntityForDeduplication(uuid="v1", name="Dr. John Smith", type="Person"),
        EntityForDeduplication(uuid="v2", name="Harvard Medical School", type="Organization"),
        EntityForDeduplication(uuid="v3", name="St. Louis, Missouri", type="Location"),
    ]
    
    for entity in test_entities:
        logger.info(f"\nVariants for {entity.type}: '{entity.name}'")
        variants = rule_engine.get_entity_variants(entity)
        for i, variant in enumerate(sorted(variants), 1):
            logger.info(f"  {i}. {variant}")


def test_name_normalization():
    """Test entity name normalization."""
    logger.info("\n=== Testing Name Normalization ===")
    
    rule_engine = get_type_rule_engine()
    
    test_cases = [
        ("dr. john smith", "Person"),
        ("HARVARD MEDICAL SCHOOL", "Organization"),
        ("new york, ny", "Location"),
        ("prof. mary jane watson-parker", "Person"),
        ("google, inc.", "Organization"),
        ("st. petersburg, florida", "Location"),
    ]
    
    for name, entity_type in test_cases:
        entity = EntityForDeduplication(uuid="norm", name=name, type=entity_type)
        normalized = rule_engine.normalize_entity_name(entity)
        logger.info(f"{entity_type:12} | {name:30} -> {normalized}")


def test_cross_type_comparison():
    """Test behavior when comparing entities of different types."""
    logger.info("\n=== Testing Cross-Type Comparison ===")
    
    rule_engine = get_type_rule_engine()
    
    person = EntityForDeduplication(uuid="cross1", name="John Smith", type="Person")
    org = EntityForDeduplication(uuid="cross2", name="John Smith Inc.", type="Organization")
    
    logger.info("Comparing Person 'John Smith' vs Organization 'John Smith Inc.'")
    
    match_result = rule_engine.calculate_type_specific_similarity(person, org)
    if match_result:
        logger.info(f"  Result: {match_result}")
    else:
        logger.info("  No type-specific rules applied (different types)")


def test_performance_comparison():
    """Compare type-specific rules with generic similarity."""
    logger.info("\n=== Performance Comparison ===")
    
    rule_engine = get_type_rule_engine()
    
    # Test cases where type-specific rules should perform better
    test_cases = [
        (EntityForDeduplication(uuid="perf1", name="Dr. John Smith", type="Person"),
         EntityForDeduplication(uuid="perf2", name="J. Smith", type="Person"),
         "Person with initials"),
        
        (EntityForDeduplication(uuid="perf3", name="Harvard Medical School", type="Organization"),
         EntityForDeduplication(uuid="perf4", name="HMS", type="Organization"),
         "Organization with acronym"),
        
        (EntityForDeduplication(uuid="perf5", name="St. Louis", type="Location"),
         EntityForDeduplication(uuid="perf6", name="Saint Louis", type="Location"),
         "Location with abbreviation"),
    ]
    
    for entity1, entity2, description in test_cases:
        logger.info(f"\n{description}:")
        
        # Type-specific similarity
        type_result = rule_engine.calculate_type_specific_similarity(entity1, entity2)
        if type_result:
            logger.info(f"  Type-specific: {type_result.similarity_score:.3f} ({type_result.match_type})")
        
        # Generic similarity (for comparison)
        from entity_deduplication.utils import calculate_string_similarity
        generic_sim = calculate_string_similarity(entity1.name, entity2.name)
        logger.info(f"  Generic string: {generic_sim:.3f}")
        
        if type_result and type_result.similarity_score > generic_sim:
            improvement = type_result.similarity_score - generic_sim
            logger.info(f"  Improvement: +{improvement:.3f}")


def main():
    """Run all tests."""
    logger.info("Starting Type-Specific Rules Tests")
    logger.info("=" * 60)
    
    try:
        test_person_rules()
        test_organization_rules()
        test_location_rules()
        test_entity_variants()
        test_name_normalization()
        test_cross_type_comparison()
        test_performance_comparison()
        
        logger.info("\n" + "=" * 60)
        logger.info("All type-specific rules tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
