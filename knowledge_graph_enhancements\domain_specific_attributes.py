"""
<PERSON><PERSON>t to add domain-specific attributes to entities based on their types.
"""

import os
import asyncio
import logging
import json
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase
import openai
from typing import Dict, List, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Get OpenAI API key
openai_api_key = os.environ.get('OPENAI_API_KEY')
openai.api_key = openai_api_key

# Define attribute schemas for different entity types
ATTRIBUTE_SCHEMAS = {
    "Herb": {
        "active_compounds": "List of active compounds in the herb",
        "traditional_uses": "Traditional medicinal uses of the herb",
        "dosage_range": "Typical dosage range for the herb",
        "contraindications": "Conditions where the herb should not be used",
        "side_effects": "Common side effects of the herb",
        "interactions": "Known interactions with medications or other herbs",
        "preparation_methods": "Common methods of preparation (e.g., tea, tincture, capsule)"
    },
    "Nutrient": {
        "rda": "Recommended Daily Allowance",
        "food_sources": "Common food sources of the nutrient",
        "deficiency_symptoms": "Symptoms of deficiency",
        "toxicity_symptoms": "Symptoms of toxicity or excess",
        "bioavailability": "Factors affecting bioavailability",
        "functions": "Primary biological functions"
    },
    "Medication": {
        "drug_class": "Pharmacological class of the medication",
        "mechanism_of_action": "How the medication works in the body",
        "half_life": "Half-life of the medication",
        "dosage_forms": "Available forms (e.g., tablet, injection, topical)",
        "common_dosages": "Typical dosage ranges",
        "side_effects": "Common side effects",
        "contraindications": "Conditions where the medication should not be used",
        "interactions": "Known interactions with other medications, herbs, or foods"
    },
    "Disease": {
        "symptoms": "Common symptoms of the disease",
        "causes": "Known causes or risk factors",
        "diagnostic_criteria": "Criteria used for diagnosis",
        "conventional_treatments": "Standard medical treatments",
        "complementary_treatments": "Complementary or alternative treatments",
        "prevention": "Prevention strategies",
        "prognosis": "Typical prognosis or outcome"
    }
}

async def get_neo4j_driver():
    """Get a Neo4j driver."""
    driver = AsyncGraphDatabase.driver(
        neo4j_uri,
        auth=(neo4j_user, neo4j_password)
    )
    return driver

async def get_entities_by_type(entity_type: str, batch_size: int = 10, skip: int = 0) -> List[Dict[str, Any]]:
    """Get entities of a specific type from the Neo4j database."""
    driver = await get_neo4j_driver()

    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (e:Entity)
                WHERE e.type = $type
                RETURN e.name AS name, e.uuid AS uuid, e.type AS type
                SKIP $skip
                LIMIT $batch_size
                """,
                {"type": entity_type, "batch_size": batch_size, "skip": skip}
            )

            entities = [{"name": record["name"], "uuid": record["uuid"], "type": record["type"]}
                       async for record in result]

            return entities
    finally:
        await driver.close()

def extract_entity_attributes(entity_name: str, entity_type: str) -> Dict[str, Any]:
    """Extract domain-specific attributes for an entity using OpenAI."""
    if entity_type not in ATTRIBUTE_SCHEMAS:
        logger.warning(f"No attribute schema defined for entity type: {entity_type}")
        return {}

    schema = ATTRIBUTE_SCHEMAS[entity_type]
    attributes_prompt = ", ".join([f"{attr}: {desc}" for attr, desc in schema.items()])

    try:
        client = openai.OpenAI(api_key=openai_api_key)
        response = client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {"role": "system", "content": f"You are a knowledgeable assistant specializing in medical and health information. Extract accurate attributes for the {entity_type} named '{entity_name}' based on the following schema. If you don't know an attribute, respond with 'Unknown' for that attribute. Format your response as a JSON object with the attribute names as keys."},
                {"role": "user", "content": f"Extract the following attributes for the {entity_type} named '{entity_name}':\n\n{attributes_prompt}"}
            ],
            response_format={"type": "json_object"}
        )

        attributes = json.loads(response.choices[0].message.content)
        return attributes

    except Exception as e:
        logger.error(f"Error extracting attributes for {entity_name} ({entity_type}): {e}")
        return {}

async def update_entity_attributes(entity_uuid: str, attributes: Dict[str, Any]) -> bool:
    """Update the attributes of an entity in the Neo4j database."""
    if not attributes:
        return False

    driver = await get_neo4j_driver()

    try:
        async with driver.session() as session:
            # Convert attributes to a JSON string
            attributes_json = json.dumps(attributes)

            result = await session.run(
                """
                MATCH (e:Entity {uuid: $uuid})
                SET e.attributes = $attributes
                RETURN e
                """,
                {"uuid": entity_uuid, "attributes": attributes_json}
            )

            record = await result.single()
            return record is not None

    except Exception as e:
        logger.error(f"Error updating attributes for entity {entity_uuid}: {e}")
        return False

    finally:
        await driver.close()

async def process_entities_with_attributes(entity_type: str, batch_size: int = 10, max_entities: int = 100) -> int:
    """Process entities of a specific type and add domain-specific attributes."""
    processed_count = 0
    skip = 0

    while processed_count < max_entities:
        # Get a batch of entities
        entities = await get_entities_by_type(entity_type, batch_size, skip)

        if not entities:
            logger.info(f"No more {entity_type} entities to process")
            break

        for entity in entities:
            logger.info(f"Processing {entity_type}: {entity['name']}")

            # Extract attributes for the entity (non-async function)
            attributes = extract_entity_attributes(entity['name'], entity_type)

            if attributes:
                # Update the entity with the extracted attributes
                success = await update_entity_attributes(entity['uuid'], attributes)

                if success:
                    logger.info(f"Successfully updated attributes for {entity['name']}")
                    processed_count += 1
                else:
                    logger.warning(f"Failed to update attributes for {entity['name']}")

            # Check if we've reached the maximum number of entities to process
            if processed_count >= max_entities:
                break

        skip += batch_size

    return processed_count

async def get_entity_with_attributes(entity_name: str) -> Optional[Dict[str, Any]]:
    """Get an entity with its attributes from the Neo4j database."""
    driver = await get_neo4j_driver()

    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (e:Entity)
                WHERE e.name = $name
                RETURN e.name AS name, e.type AS type, e.attributes AS attributes
                """,
                {"name": entity_name}
            )

            record = await result.single()

            if record:
                entity = {
                    "name": record["name"],
                    "type": record["type"],
                    "attributes": json.loads(record["attributes"]) if record["attributes"] else {}
                }
                return entity

            return None

    except Exception as e:
        logger.error(f"Error getting entity {entity_name}: {e}")
        return None

    finally:
        await driver.close()

async def get_entities_with_attributes(entity_type: str = None, limit: int = 10) -> List[Dict[str, Any]]:
    """Get entities with attributes from the Neo4j database."""
    driver = await get_neo4j_driver()

    try:
        async with driver.session() as session:
            query = """
                MATCH (e:Entity)
                WHERE e.attributes IS NOT NULL
                """

            if entity_type:
                query += "AND e.type = $type "

            query += """
                RETURN e.name AS name, e.type AS type, e.attributes AS attributes
                LIMIT $limit
                """

            params = {"limit": limit}
            if entity_type:
                params["type"] = entity_type

            result = await session.run(query, params)

            entities = []
            async for record in result:
                entity = {
                    "name": record["name"],
                    "type": record["type"],
                    "attributes": json.loads(record["attributes"]) if record["attributes"] else {}
                }
                entities.append(entity)

            return entities

    except Exception as e:
        logger.error(f"Error getting entities with attributes: {e}")
        return []

    finally:
        await driver.close()

async def main():
    """Main function to add domain-specific attributes to entities."""
    import argparse

    parser = argparse.ArgumentParser(description='Add domain-specific attributes to entities.')
    parser.add_argument('command', choices=['process', 'get', 'list'],
                        help='Command to execute: process (add attributes), get (get entity), list (list entities)')
    parser.add_argument('--type', type=str, choices=list(ATTRIBUTE_SCHEMAS.keys()),
                        help='Entity type to process')
    parser.add_argument('--name', type=str, help='Entity name to get')
    parser.add_argument('--batch-size', type=int, default=5, help='Batch size for processing entities')
    parser.add_argument('--max-entities', type=int, default=10, help='Maximum number of entities to process')
    parser.add_argument('--limit', type=int, default=10, help='Limit for listing entities')

    args = parser.parse_args()

    if args.command == 'process':
        if not args.type:
            logger.error("Entity type is required for 'process' command")
            return

        logger.info(f"Processing {args.type} entities with batch size {args.batch_size} and max {args.max_entities}")
        processed_count = await process_entities_with_attributes(args.type, args.batch_size, args.max_entities)
        logger.info(f"Processed {processed_count} {args.type} entities")

    elif args.command == 'get':
        if not args.name:
            logger.error("Entity name is required for 'get' command")
            return

        logger.info(f"Getting entity: {args.name}")
        entity = await get_entity_with_attributes(args.name)

        if entity:
            print(f"Entity: {entity['name']} ({entity['type']})")
            print("Attributes:")
            for attr, value in entity['attributes'].items():
                print(f"  {attr}: {value}")
        else:
            print(f"Entity not found: {args.name}")

    elif args.command == 'list':
        logger.info(f"Listing entities with attributes" + (f" of type {args.type}" if args.type else ""))
        entities = await get_entities_with_attributes(args.type, args.limit)

        print(f"Found {len(entities)} entities with attributes:")
        for entity in entities:
            print(f"Entity: {entity['name']} ({entity['type']})")
            print("Attributes:")
            for attr, value in entity['attributes'].items():
                print(f"  {attr}: {value}")
            print()

if __name__ == "__main__":
    asyncio.run(main())
