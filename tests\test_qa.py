"""
Test script for Q&A functionality using the Neo4j database directly.
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase
import google.generativeai as genai

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Get Google Gemini API key
google_api_key = os.environ.get('GOOGLE_API_KEY')
if not google_api_key:
    # Try to get it from the environment variable directly
    google_api_key = os.environ.get('AIzaSyBsw8WAQoGkh0XIrgiqy4dNHLvuqvbk754')

# Configure Google Gemini
genai.configure(api_key=google_api_key)

# Connect to Neo4j
async def get_neo4j_driver():
    driver = AsyncGraphDatabase.driver(
        neo4j_uri,
        auth=(neo4j_user, neo4j_password)
    )
    return driver

# Extract keywords from a query
def extract_keywords(query):
    """Extract meaningful keywords from a query"""
    # Convert to lowercase
    query = query.lower()

    # Remove common stop words and question words
    stop_words = [
        "a", "an", "the", "and", "or", "but", "if", "because", "as", "what",
        "when", "where", "which", "who", "whom", "whose", "why", "how",
        "about", "tell", "explain", "describe", "information", "know", "understand",
        "me", "my", "i", "we", "our", "us", "you", "your", "he", "she", "it", "they", "them",
        "this", "that", "these", "those", "is", "are", "was", "were", "be", "been", "being",
        "have", "has", "had", "do", "does", "did", "can", "could", "will", "would", "shall", "should",
        "may", "might", "must", "for", "of", "with", "without", "in", "on", "at", "by", "to", "from"
    ]

    # Split into words and filter out stop words
    words = query.split()
    keywords = [word for word in words if word not in stop_words and len(word) > 2]

    # If no keywords found, use the original query words
    if not keywords:
        keywords = [word for word in words if len(word) > 2]

    # If still no keywords, use the whole query
    if not keywords:
        keywords = [query]

    return keywords

# Search for facts related to a query
async def search_facts(query):
    """Search for facts related to a query"""
    try:
        # Connect to Neo4j
        driver = await get_neo4j_driver()

        try:
            # Extract keywords from the query
            keywords = extract_keywords(query)
            logger.info(f"Keywords: {keywords}")

            # Search for facts using keywords
            facts = []
            async with driver.session() as session:
                # Create a Cypher query that searches for any of the keywords
                cypher_query = """
                MATCH (e:Episode)-[:CONTAINS]->(f:Fact)
                WHERE """

                conditions = []
                for i, keyword in enumerate(keywords):
                    conditions.append(f"toLower(f.body) CONTAINS toLower($keyword{i})")

                cypher_query += " OR ".join(conditions)
                cypher_query += """
                WITH f, e, 1.0 AS score
                ORDER BY size(f.body) DESC
                LIMIT 10
                RETURN f.body AS text, score, e.name AS document, f.chunk_num AS chunk_num
                """

                # Create parameters for the query
                params = {}
                for i, keyword in enumerate(keywords):
                    params[f"keyword{i}"] = keyword

                logger.info(f"Executing query: {cypher_query}")
                logger.info(f"Parameters: {params}")

                result = await session.run(cypher_query, params)
                records = await result.values()

                # Format results for context
                sources = []
                context_parts = []

                if records:
                    logger.info(f"Found {len(records)} facts")
                    for i, record in enumerate(records):
                        text, score, document, chunk_num = record
                        sources.append({
                            "preview": text[:200] + "..." if len(text) > 200 else text,
                            "score": score,
                            "document": document,
                            "chunk_num": chunk_num
                        })

                        context_parts.append(f"{i+1} (from {document}):\n{text}")
                        facts.append({
                            "body": text,
                            "document": document,
                            "chunk_num": chunk_num
                        })
                else:
                    logger.info("No facts found")

                # If no facts found, try to find facts that mention entities related to the query
                if not facts:
                    logger.info("Trying to find entities related to the query")
                    # Look for entities related to the query
                    entity_query = """
                    MATCH (e:Entity)
                    WHERE """

                    entity_conditions = []
                    for i, keyword in enumerate(keywords):
                        entity_conditions.append(f"toLower(e.name) CONTAINS toLower($keyword{i})")

                    entity_query += " OR ".join(entity_conditions)
                    entity_query += """
                    RETURN e.uuid AS uuid, e.name AS name, e.type AS type
                    LIMIT 5
                    """

                    logger.info(f"Executing entity query: {entity_query}")
                    result = await session.run(entity_query, params)
                    entity_records = await result.values()

                    logger.info(f"Found {len(entity_records)} entities")

                    # For each entity, find facts that mention it
                    for entity_record in entity_records:
                        entity_uuid, entity_name, entity_type = entity_record
                        logger.info(f"Found entity: {entity_name} ({entity_type})")

                        fact_query = """
                        MATCH (e:Entity {uuid: $entity_uuid})<-[:MENTIONS]-(f:Fact)<-[:CONTAINS]-(ep:Episode)
                        RETURN f.body AS text, 0.8 AS score, ep.name AS document, f.chunk_num AS chunk_num
                        LIMIT 5
                        """

                        result = await session.run(fact_query, {"entity_uuid": entity_uuid})
                        fact_records = await result.values()

                        logger.info(f"Found {len(fact_records)} facts mentioning entity {entity_name}")

                        for i, record in enumerate(fact_records):
                            text, score, document, chunk_num = record
                            if any(f["body"] == text for f in facts):
                                continue  # Skip duplicates

                            sources.append({
                                "preview": text[:200] + "..." if len(text) > 200 else text,
                                "score": score,
                                "document": document,
                                "chunk_num": chunk_num
                            })

                            context_parts.append(f"{len(context_parts)+1} (from {document}, mentions {entity_name}):\n{text}")
                            facts.append({
                                "body": text,
                                "document": document,
                                "chunk_num": chunk_num
                            })

                # If still no facts, get some random facts
                if not facts:
                    logger.info("No facts found, getting random facts")
                    result = await session.run(
                        """
                        MATCH (e:Episode)-[:CONTAINS]->(f:Fact)
                        WHERE f.body IS NOT NULL
                        RETURN f.body AS text, 0.5 AS score, e.name AS document, f.chunk_num AS chunk_num
                        ORDER BY rand()
                        LIMIT 3
                        """
                    )

                    random_records = await result.values()
                    logger.info(f"Found {len(random_records)} random facts")

                    for i, record in enumerate(random_records):
                        text, score, document, chunk_num = record
                        sources.append({
                            "preview": text[:200] + "..." if len(text) > 200 else text,
                            "score": score,
                            "document": document,
                            "chunk_num": chunk_num
                        })

                        context_parts.append(f"{i+1} (from {document}):\n{text}")
                        facts.append({
                            "body": text,
                            "document": document,
                            "chunk_num": chunk_num
                        })

                context_text = "\n\n".join(context_parts)
                return context_text, sources, facts

        finally:
            await driver.close()
    except Exception as e:
        logger.error(f"Error searching facts: {e}")
        return "", [], []

# Generate an answer using Google Gemini
async def generate_answer(query, context_text):
    """Generate an answer using Google Gemini"""
    try:
        # System prompt for the LLM
        system_prompt = """# AI Assistant Prompt

# Your Name is Avery

# Role:
You are a highly intelligent personal assistant named Avery specializing in natural medicine and health.

# Instructions
1. Provide detailed, comprehensive answers based on the context provided.
2. Always cite your sources by referencing the number in parentheses, e.g., (1), (2), etc.
3. Include specific information from the context rather than generic statements.
4. If multiple sources provide information, synthesize them into a coherent answer.
5. If the context contains contradictory information, acknowledge this and present both perspectives.
6. Use a professional but engaging tone.
7. Format your response with clear sections and bullet points when appropriate.
8. If the context doesn't contain enough information, clearly state this limitation.

# Expertise Areas
- Natural Medicine
- Herbal Medicine
- Nutritional Medicine
- Integrative Medicine
- Pathology
- Psychology
- Dietetics

IMPORTANT: Your answers should be thorough and detailed, drawing specifically from the provided context. Always use the number references (1), (2), etc. when citing information. This allows users to click on these references to see the source. If the context doesn't contain relevant information, say 'I don't have enough specific information in my knowledge base to answer this question thoroughly.'"""

        user_prompt = f"Context:\n{context_text}\n\nQuestion: {query}"

        # Use Google Gemini to generate an answer
        logger.info("Using Google Gemini to generate an answer")
        model = genai.GenerativeModel('gemini-1.5-pro')
        chat = model.start_chat(history=[])
        
        # Add system prompt as the first message
        chat.send_message(system_prompt)
        
        # Send the user prompt
        response = chat.send_message(user_prompt)
        
        answer = response.text
        logger.info(f"Generated answer: {answer}")
        
        return answer
    except Exception as e:
        logger.error(f"Error generating answer: {e}")
        return f"Error generating answer: {e}"

# Main function
async def main():
    """Main function"""
    # Test queries
    queries = [
        "Tell me about antioxidants",
        "What is chemotherapy?",
        "How does vitamin C work?",
        "What are the benefits of turmeric?"
    ]

    for query in queries:
        logger.info(f"\n\n=== Testing query: {query} ===")
        
        # Search for facts
        context_text, sources, facts = await search_facts(query)
        
        # Log the context
        logger.info(f"Context: {context_text}")
        
        # Generate an answer
        if context_text:
            answer = await generate_answer(query, context_text)
            logger.info(f"Answer: {answer}")
        else:
            logger.info("No context found, cannot generate an answer")

# Run the main function
if __name__ == "__main__":
    asyncio.run(main())
