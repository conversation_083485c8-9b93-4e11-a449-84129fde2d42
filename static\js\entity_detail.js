/**
 * Entity detail view for Graphiti Knowledge Graph
 *
 * This script handles the display of detailed information about a specific entity,
 * including its relationships, associated facts, and source documents.
 */

// Global variables
let entityData = null;
let networkInstance = null;

// DOM elements - initialize these after the DOM is loaded
let loadingSpinner;
let errorMessage;
let errorText;
let entityDetail;
let entityName;
let entityType;
let entityConfidence;
let relationshipsList;
let factsList;
let documentsList;
let relationshipNetwork;
let editEntityBtn;
let editForm;
let editName;
let editType;
let editConfidence;
let cancelEdit;
let entityEditForm;

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    console.log("Entity detail script loaded");

    // Initialize DOM elements
    loadingSpinner = document.getElementById('loadingSpinner');
    errorMessage = document.getElementById('errorMessage');
    errorText = document.getElementById('errorText');
    entityDetail = document.getElementById('entityDetail');
    entityName = document.getElementById('entityName');
    entityType = document.getElementById('entityType');
    entityConfidence = document.getElementById('entityConfidence');
    relationshipsList = document.getElementById('relationshipsList');
    factsList = document.getElementById('factsList');
    documentsList = document.getElementById('documentsList');
    relationshipNetwork = document.getElementById('relationshipNetwork');
    editEntityBtn = document.getElementById('editEntityBtn');
    editForm = document.getElementById('editForm');
    editName = document.getElementById('editName');
    editType = document.getElementById('editType');
    editConfidence = document.getElementById('editConfidence');
    cancelEdit = document.getElementById('cancelEdit');
    entityEditForm = document.getElementById('entityEditForm');

    // Check if required elements exist
    if (!loadingSpinner || !errorMessage || !entityDetail) {
        console.error("Required DOM elements not found");
        return;
    }

    // Get entity UUID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const entityUuid = urlParams.get('uuid');

    if (!entityUuid) {
        showError('No entity UUID provided');
        return;
    }

    console.log(`Entity UUID from URL: ${entityUuid}`);

    // Load entity data
    loadEntityData(entityUuid);

    // Set up event listeners
    setupEventListeners(entityUuid);
});

/**
 * Set up event listeners for the page
 */
function setupEventListeners(entityUuid) {
    // Edit button
    if (editEntityBtn) {
        editEntityBtn.addEventListener('click', () => {
            // Show edit form
            if (editForm && entityData && entityData.entity) {
                editForm.style.display = 'block';

                // Populate form with current values
                if (editName) editName.value = entityData.entity.name;
                if (editType) editType.value = entityData.entity.type;
                if (editConfidence) editConfidence.value = entityData.entity.confidence;
            }
        });
    }

    // Cancel edit button
    if (cancelEdit) {
        cancelEdit.addEventListener('click', () => {
            // Hide edit form
            if (editForm) {
                editForm.style.display = 'none';
            }
        });
    }

    // Edit form submission
    if (entityEditForm) {
        entityEditForm.addEventListener('submit', (event) => {
            event.preventDefault();

            // Get form values
            const name = editName ? editName.value : '';
            const type = editType ? editType.value : '';
            const confidence = editConfidence ? parseFloat(editConfidence.value) : 1.0;

            // Update entity
            updateEntity(entityUuid, name, type, confidence);
        });
    }

    // Tab change event for visualization
    const visualizationTab = document.getElementById('visualization-tab');
    if (visualizationTab) {
        visualizationTab.addEventListener('click', () => {
            // Initialize network visualization if not already done
            if (!networkInstance && entityData) {
                initializeNetworkVisualization(entityData);
            }
        });
    }
}

/**
 * Load entity data from the API
 */
async function loadEntityData(entityUuid) {
    // Show loading spinner
    if (loadingSpinner) loadingSpinner.style.display = 'flex';
    if (entityDetail) entityDetail.style.display = 'none';
    if (errorMessage) errorMessage.style.display = 'none';

    try {
        // Fetch entity data
        console.log(`Fetching entity data for UUID: ${entityUuid}`);

        // Make sure we have a valid UUID
        if (!entityUuid) {
            throw new Error("No entity UUID provided");
        }

        // Make the API request
        const response = await fetch(`/api/entity/${entityUuid}`);

        if (!response.ok) {
            const errorText = await response.text();
            console.error(`API error response: ${errorText}`);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Parse the JSON response
        try {
            entityData = await response.json();
            console.log("Entity data received:", entityData);
        } catch (jsonError) {
            console.error("Failed to parse JSON response:", jsonError);
            throw new Error("Invalid response format from server");
        }

        // Check if entity data is valid
        if (!entityData || !entityData.entity) {
            console.error("Invalid entity data structure:", entityData);
            throw new Error("Invalid entity data received");
        }

        // Render entity data
        renderEntityData();

        // Hide loading spinner
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        if (entityDetail) entityDetail.style.display = 'block';
    } catch (error) {
        console.error('Error loading entity data:', error);
        showError(`Failed to load entity data: ${error.message}`);
    }
}

/**
 * Format entity name by converting LaTeX notation to Unicode
 */
function formatEntityName(name) {
    if (!name) return 'Unknown';

    // Convert LaTeX-style notation to Unicode
    const replacements = {
        '$eta$': 'β',
        '$alpha$': 'α',
        '$beta$': 'β',
        '$gamma$': 'γ',
        '$delta$': 'δ',
        '$epsilon$': 'ε',
        '$zeta$': 'ζ',
        '$theta$': 'θ',
        '$lambda$': 'λ',
        '$mu$': 'μ',
        '$nu$': 'ν',
        '$pi$': 'π',
        '$rho$': 'ρ',
        '$sigma$': 'σ',
        '$tau$': 'τ',
        '$phi$': 'φ',
        '$chi$': 'χ',
        '$psi$': 'ψ',
        '$omega$': 'ω'
    };

    let formattedName = name;
    for (const [latex, unicode] of Object.entries(replacements)) {
        formattedName = formattedName.replace(new RegExp(latex.replace('$', '\\$'), 'gi'), unicode);
    }

    return formattedName;
}

/**
 * Render entity data
 */
function renderEntityData() {
    if (!entityData || !entityData.entity) {
        console.error("No entity data to render");
        return;
    }

    const entity = entityData.entity;
    const formattedName = formatEntityName(entity.name);

    // Set entity details
    if (entityName) entityName.textContent = formattedName;
    if (entityType) entityType.textContent = entity.type || "Unknown";
    if (entityConfidence) entityConfidence.textContent = (entity.confidence || 0).toFixed(2);

    // Update page title and breadcrumb
    document.title = `${formattedName} - Entity Detail - Graphiti Knowledge Graph`;
    const breadcrumbItem = document.getElementById('entity-breadcrumb');
    if (breadcrumbItem) {
        breadcrumbItem.textContent = formattedName;
    }

    // Render relationships
    renderRelationships();

    // Render facts (if available)
    if (entityData.facts && factsList) {
        renderFacts();
    } else if (factsList) {
        factsList.innerHTML = '<div class="alert alert-info">No facts available for this entity.</div>';
    }

    // Render documents (if available)
    if (entityData.documents && documentsList) {
        renderDocuments();
    } else if (documentsList) {
        documentsList.innerHTML = '<div class="alert alert-info">No source documents available for this entity.</div>';
    }
}

/**
 * Render entity relationships
 */
function renderRelationships() {
    if (!relationshipsList) {
        console.error("Relationships list element not found");
        return;
    }

    if (!entityData || !entityData.relationships || entityData.relationships.length === 0) {
        relationshipsList.innerHTML = '<div class="alert alert-info">No relationships found for this entity.</div>';
        return;
    }

    const relationships = entityData.relationships;

    // Group relationships by type
    const relationshipsByType = {};
    relationships.forEach(rel => {
        const type = rel.type || "Unknown";
        if (!relationshipsByType[type]) {
            relationshipsByType[type] = [];
        }
        relationshipsByType[type].push(rel);
    });

    // Clear relationships list
    relationshipsList.innerHTML = '';

    // Render relationships by type
    Object.keys(relationshipsByType).sort().forEach(type => {
        const rels = relationshipsByType[type];

        // Create type header
        const typeHeader = document.createElement('h5');
        typeHeader.className = 'mt-3';
        typeHeader.textContent = `${type} (${rels.length})`;
        relationshipsList.appendChild(typeHeader);

        // Create relationship cards
        rels.forEach(rel => {
            const card = document.createElement('div');
            card.className = 'card relationship-card';

            // Determine if this is an outgoing or incoming relationship
            const isOutgoing = rel.target_uuid !== undefined;
            const targetName = isOutgoing ? (rel.target_name || "Unknown") : (rel.source_name || "Unknown");
            const targetType = isOutgoing ? (rel.target_type || "Unknown") : (rel.source_type || "Unknown");
            const targetUuid = isOutgoing ? rel.target_uuid : rel.source_uuid;
            const direction = isOutgoing ? 'to' : 'from';

            if (targetUuid) {
                card.innerHTML = `
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-secondary">${direction}</span>
                                <strong>${targetName}</strong>
                                <span class="badge bg-info">${targetType}</span>
                            </div>
                            <a href="/entity-detail?uuid=${targetUuid}" class="btn btn-sm btn-outline-primary">View</a>
                        </div>
                    </div>
                `;
            } else {
                card.innerHTML = `
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-secondary">${direction}</span>
                                <strong>${targetName}</strong>
                                <span class="badge bg-info">${targetType}</span>
                            </div>
                            <span class="text-muted">No link available</span>
                        </div>
                    </div>
                `;
            }

            relationshipsList.appendChild(card);
        });
    });
}

/**
 * Initialize network visualization
 */
function initializeNetworkVisualization(data) {
    const entity = data.entity;
    const relationships = data.relationships;

    if (!relationships || relationships.length === 0) {
        relationshipNetwork.innerHTML = '<div class="alert alert-info">No relationships to visualize.</div>';
        return;
    }

    // Create nodes and edges arrays for visualization
    const nodes = [];
    const edges = [];

    // Add current entity as central node
    nodes.push({
        id: entity.uuid,
        label: entity.name,
        title: `${entity.type}: ${entity.name}`,
        group: entity.type,
        shape: 'dot',
        size: 20,
        color: {
            background: '#6c757d',
            border: '#495057',
            highlight: {
                background: '#343a40',
                border: '#212529'
            }
        }
    });

    // Add related entities as nodes
    relationships.forEach(rel => {
        // Determine if this is an outgoing or incoming relationship
        const isOutgoing = rel.target_uuid !== undefined;
        const targetId = isOutgoing ? rel.target_uuid : rel.source_uuid;
        const targetName = isOutgoing ? rel.target_name : rel.source_name;
        const targetType = isOutgoing ? rel.target_type : rel.source_type;

        // Add node if not already added
        if (!nodes.some(node => node.id === targetId)) {
            nodes.push({
                id: targetId,
                label: targetName,
                title: `${targetType}: ${targetName}`,
                group: targetType,
                shape: 'dot',
                size: 15
            });
        }

        // Add edge
        edges.push({
            from: isOutgoing ? entity.uuid : targetId,
            to: isOutgoing ? targetId : entity.uuid,
            label: rel.type,
            arrows: isOutgoing ? 'to' : 'from',
            title: rel.type
        });
    });

    // Create network
    const container = relationshipNetwork;
    const data = {
        nodes: new vis.DataSet(nodes),
        edges: new vis.DataSet(edges)
    };
    const options = {
        nodes: {
            font: {
                size: 12
            }
        },
        edges: {
            font: {
                size: 10,
                align: 'middle'
            },
            smooth: {
                type: 'continuous'
            }
        },
        physics: {
            stabilization: true,
            barnesHut: {
                gravitationalConstant: -2000,
                centralGravity: 0.3,
                springLength: 150,
                springConstant: 0.04,
                damping: 0.09
            }
        },
        groups: {
            // Define colors for different entity types
            Person: { color: { background: '#007bff', border: '#0056b3' } },
            Organization: { color: { background: '#28a745', border: '#145523' } },
            Location: { color: { background: '#17a2b8', border: '#0c525d' } },
            Disease: { color: { background: '#dc3545', border: '#a71d2a' } },
            Symptom: { color: { background: '#fd7e14', border: '#c96a0a' } },
            Treatment: { color: { background: '#6f42c1', border: '#4e2d89' } },
            Herb: { color: { background: '#20c997', border: '#158765' } },
            Nutrient: { color: { background: '#6c757d', border: '#495057' } }
        }
    };

    // Initialize network
    networkInstance = new vis.Network(container, data, options);

    // Add click event
    networkInstance.on('click', function(params) {
        if (params.nodes.length > 0) {
            const nodeId = params.nodes[0];
            if (nodeId !== entity.uuid) {
                window.location.href = `/entity-detail?uuid=${nodeId}`;
            }
        }
    });
}

/**
 * Update entity
 */
async function updateEntity(entityUuid, name, type, confidence) {
    try {
        console.log(`Updating entity ${entityUuid} with name=${name}, type=${type}, confidence=${confidence}`);

        const response = await fetch(`/api/entity/${entityUuid}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                name,
                type,
                confidence
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Reload entity data
        loadEntityData(entityUuid);

        // Hide edit form
        if (editForm) {
            editForm.style.display = 'none';
        }
    } catch (error) {
        console.error('Error updating entity:', error);
        alert('Failed to update entity: ' + error.message);
    }
}

/**
 * Show error message
 */
function showError(message) {
    console.error("Error:", message);

    if (errorText) errorText.textContent = message;
    if (errorMessage) errorMessage.style.display = 'block';
    if (loadingSpinner) loadingSpinner.style.display = 'none';
    if (entityDetail) entityDetail.style.display = 'none';

    // Update the page title and breadcrumb to show error
    document.title = "Entity Not Found - Graphiti Knowledge Graph";

    // Update breadcrumb
    const breadcrumbItems = document.querySelectorAll('.breadcrumb-item');
    if (breadcrumbItems.length > 0) {
        const lastItem = breadcrumbItems[breadcrumbItems.length - 1];
        if (lastItem) {
            lastItem.textContent = "Entity Not Found";
        }
    }
}
