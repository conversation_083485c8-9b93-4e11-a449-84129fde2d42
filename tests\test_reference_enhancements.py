"""
Test script for reference enhancements
"""

import os
import asyncio
import logging
import json
import argparse
from pathlib import Path

from reference_deduplication import ReferenceDeduplcator
from citation_network import Citation<PERSON>workAnalyzer
from bibliographic_enrichment import Bibliographic<PERSON><PERSON><PERSON>er
from reference_enhancement_suite import ReferenceEnhancementSuite

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_enhancements.log')
    ]
)
logger = logging.getLogger(__name__)

# Create output directory
OUTPUT_DIR = Path("test_output/enhancements")
OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

async def test_deduplication():
    """Test reference deduplication"""
    logger.info("Testing reference deduplication")
    
    # Initialize deduplicator
    deduplicator = ReferenceDeduplcator()
    
    try:
        # Run deduplication
        result = await deduplicator.deduplicate_references()
        
        # Save result
        output_path = OUTPUT_DIR / "deduplication_result.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2)
        
        # Print summary
        print("\n=== Reference Deduplication Results ===")
        if result["success"]:
            print(f"Total references: {result['total_references']}")
            print(f"Duplicate groups: {result['duplicate_groups']}")
            print(f"Duplicate references: {result['duplicate_references']}")
            print(f"Relationships created: {result['relationships_created']}")
        else:
            print(f"Error: {result['error']}")
        
        print(f"\nResults saved to: {output_path}")
        
        return result
    
    finally:
        await deduplicator.close()

async def test_citation_network():
    """Test citation network analysis"""
    logger.info("Testing citation network analysis")
    
    # Initialize analyzer
    analyzer = CitationNetworkAnalyzer()
    
    try:
        # Build citation network
        result = await analyzer.build_citation_network()
        
        # Save result
        output_path = OUTPUT_DIR / "citation_network_result.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2)
        
        # Generate visualization
        viz_path = OUTPUT_DIR / "citation_network.png"
        analyzer.save_network_visualization(str(viz_path))
        
        # Export network data
        export_path = OUTPUT_DIR / "citation_network.json"
        await analyzer.export_network_data(str(export_path))
        
        # Get most cited documents
        most_cited = await analyzer.get_most_cited_documents(limit=10)
        
        # Print summary
        print("\n=== Citation Network Analysis Results ===")
        if result["success"]:
            print(f"Documents: {result['documents']}")
            print(f"References: {result['references']}")
            print(f"Citations: {result['citations']}")
            
            print("\nNetwork Statistics:")
            for key, value in result["statistics"].items():
                print(f"  {key}: {value}")
            
            print("\nMost Cited Documents:")
            for i, doc in enumerate(most_cited):
                title = doc["document"].get("metadata_title", doc["document"]["name"])
                count = doc["citation_count"]
                print(f"  {i+1}. {title} - {count} citations")
        else:
            print(f"Error: {result['error']}")
        
        print(f"\nResults saved to: {output_path}")
        print(f"Visualization saved to: {viz_path}")
        print(f"Network data exported to: {export_path}")
        
        return result
    
    finally:
        await analyzer.close()

async def test_bibliographic_enrichment(batch_size: int = 10, max_references: int = 20):
    """Test bibliographic enrichment"""
    logger.info(f"Testing bibliographic enrichment (batch_size={batch_size}, max_references={max_references})")
    
    # Initialize enricher
    enricher = BibliographicEnricher()
    
    try:
        # Enrich references
        result = await enricher.enrich_all_references(
            batch_size=batch_size,
            max_references=max_references
        )
        
        # Save result
        output_path = OUTPUT_DIR / "enrichment_result.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2)
        
        # Export enriched references
        export_path = OUTPUT_DIR / "enriched_references.json"
        await enricher.export_enriched_references(str(export_path))
        
        # Print summary
        print("\n=== Bibliographic Enrichment Results ===")
        if result["success"]:
            print(f"Total references processed: {result['total_processed']}")
            print(f"Total references enriched: {result['total_enriched']}")
        else:
            print(f"Error: {result['error']}")
        
        print(f"\nResults saved to: {output_path}")
        print(f"Enriched references exported to: {export_path}")
        
        return result
    
    finally:
        await enricher.close()

async def test_all_enhancements(batch_size: int = 10, max_references: int = 20):
    """Test all reference enhancements"""
    logger.info(f"Testing all reference enhancements (batch_size={batch_size}, max_references={max_references})")
    
    # Initialize suite
    suite = ReferenceEnhancementSuite()
    
    try:
        # Run all enhancements
        result = await suite.run_all_enhancements(
            deduplicate=True,
            build_network=True,
            enrich=True,
            batch_size=batch_size,
            max_references=max_references,
            output_dir=str(OUTPUT_DIR)
        )
        
        # Save result
        output_path = OUTPUT_DIR / "all_enhancements_result.json"
        with open(output_path, "w", encoding="utf-8") as f:
            json.dump(result, f, indent=2)
        
        # Print summary
        print("\n=== All Reference Enhancements Results ===")
        if result["success"]:
            # Deduplication results
            if result["results"]["deduplication"]:
                dedup = result["results"]["deduplication"]
                print("\nDeduplication:")
                print(f"  Total references: {dedup.get('total_references', 0)}")
                print(f"  Duplicate groups: {dedup.get('duplicate_groups', 0)}")
                print(f"  Duplicate references: {dedup.get('duplicate_references', 0)}")
                print(f"  Relationships created: {dedup.get('relationships_created', 0)}")
            
            # Citation network results
            if result["results"]["citation_network"]:
                network = result["results"]["citation_network"]
                print("\nCitation Network:")
                print(f"  Documents: {network.get('documents', 0)}")
                print(f"  References: {network.get('references', 0)}")
                print(f"  Citations: {network.get('citations', 0)}")
                
                if network.get("statistics"):
                    stats = network["statistics"]
                    print("  Network Statistics:")
                    print(f"    Nodes: {stats.get('nodes', 0)}")
                    print(f"    Edges: {stats.get('edges', 0)}")
                    print(f"    Connected components: {stats.get('connected_components', 0)}")
                    print(f"    Max in-degree: {stats.get('max_in_degree', 0)}")
            
            # Enrichment results
            if result["results"]["enrichment"]:
                enrich = result["results"]["enrichment"]
                print("\nBibliographic Enrichment:")
                print(f"  Total references processed: {enrich.get('total_processed', 0)}")
                print(f"  Total references enriched: {enrich.get('total_enriched', 0)}")
        else:
            print(f"Error: {result['error']}")
        
        print(f"\nResults saved to: {output_path}")
        print(f"All output files saved to: {OUTPUT_DIR}")
        
        return result
    
    finally:
        await suite.close()

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test reference enhancements")
    parser.add_argument("--test", choices=["deduplication", "citation-network", "enrichment", "all"], default="all", help="Test to run")
    parser.add_argument("--batch-size", type=int, default=10, help="Batch size for enrichment")
    parser.add_argument("--max-references", type=int, default=20, help="Maximum number of references to process for enrichment")
    
    args = parser.parse_args()
    
    # Run tests
    if args.test == "deduplication" or args.test == "all":
        await test_deduplication()
    
    if args.test == "citation-network" or args.test == "all":
        await test_citation_network()
    
    if args.test == "enrichment" or args.test == "all":
        await test_bibliographic_enrichment(args.batch_size, args.max_references)
    
    if args.test == "all":
        await test_all_enhancements(args.batch_size, args.max_references)

if __name__ == "__main__":
    asyncio.run(main())
