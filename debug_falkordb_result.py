#!/usr/bin/env python3
"""
Debug FalkorDB result format.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.falkordb_adapter import FalkorDBAdapter

def debug_result():
    """Debug the FalkorDB result format."""
    
    print("🔍 DEBUGGING FALKORDB RESULT FORMAT")
    print("=" * 60)
    
    try:
        db = FalkorDBAdapter()
        
        # Get a few entities
        query = 'MATCH (n) RETURN n.name as name, n.type as type, n.description as description LIMIT 5'
        result = db.execute_cypher(query)
        
        print(f"Result type: {type(result)}")
        print(f"Result length: {len(result)}")
        print()
        
        for i, record in enumerate(result):
            print(f"Record {i}:")
            print(f"  Type: {type(record)}")
            print(f"  Content: {record}")
            print(f"  Length: {len(record) if hasattr(record, '__len__') else 'N/A'}")
            
            if isinstance(record, list):
                for j, item in enumerate(record):
                    print(f"    Item {j}: {type(item)} = {item}")
            elif isinstance(record, dict):
                for key, value in record.items():
                    print(f"    {key}: {type(value)} = {value}")
            print()
        
        # Try a simpler query
        print("Trying simpler query...")
        simple_query = 'MATCH (n) RETURN n LIMIT 3'
        simple_result = db.execute_cypher(simple_query)
        
        print(f"Simple result type: {type(simple_result)}")
        print(f"Simple result length: {len(simple_result)}")
        
        for i, record in enumerate(simple_result):
            print(f"Simple Record {i}: {type(record)} = {record}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_result()
