#!/usr/bin/env python3
"""
Test script for enhanced citation system.

This script demonstrates the improved reference citations in Q&A responses.
"""

import sys
import os
import logging
from typing import List, Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.enhanced_citation_service import EnhancedCitationService, get_citation_service

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_qa_response() -> Dict[str, Any]:
    """Create a mock Q&A response with references."""
    answer_text = """
Antioxidants play a crucial role in reducing oxidative stress and protecting against cellular damage [1]. 
Research has shown that dietary antioxidants, particularly those found in fruits and vegetables, can help 
prevent various chronic diseases including cancer and cardiovascular disease [2][3].

However, the use of antioxidant supplements during cancer treatment requires careful consideration. Some 
studies suggest that high-dose antioxidant supplementation may interfere with chemotherapy and radiation 
therapy effectiveness [4]. The timing and dosage of antioxidant intake should be discussed with healthcare 
professionals, especially for cancer patients [5].

For general health maintenance, obtaining antioxidants through whole foods is recommended over isolated 
supplements, as whole foods provide synergistic effects and better bioavailability [1][3].
"""

    source_facts = [
        {
            "fact": {
                "uuid": "fact-1",
                "name": "Antioxidants and Oxidative Stress",
                "body": "Antioxidants are compounds that neutralize free radicals and reduce oxidative stress in the body. They include vitamins C and E, beta-carotene, and various phytochemicals found in plant foods.",
                "summary": "Overview of antioxidants and their role in reducing oxidative stress",
                "properties": {
                    "title": "Antioxidants in Human Health and Disease",
                    "authors": "Smith, J.A., Johnson, M.B., Williams, K.C.",
                    "year": "2023",
                    "journal": "Journal of Nutritional Biochemistry",
                    "volume": "45",
                    "issue": "3",
                    "pages": "123-145",
                    "doi": "10.1016/j.jnutbio.2023.01.015"
                }
            },
            "document_name": "Antioxidants_Review_2023.pdf",
            "document_id": "doc-001",
            "confidence": 0.95
        },
        {
            "fact": {
                "uuid": "fact-2", 
                "name": "Dietary Antioxidants and Disease Prevention",
                "body": "Epidemiological studies consistently show that diets rich in antioxidant-containing foods are associated with reduced risk of chronic diseases including cancer, cardiovascular disease, and neurodegenerative disorders.",
                "summary": "Evidence for disease prevention through dietary antioxidants",
                "properties": {
                    "title": "Dietary Antioxidants and Chronic Disease Prevention: A Systematic Review",
                    "authors": "Brown, L.M., Davis, R.K., Thompson, S.J.",
                    "year": "2022",
                    "journal": "American Journal of Clinical Nutrition",
                    "volume": "116",
                    "issue": "2",
                    "pages": "456-478",
                    "doi": "10.1093/ajcn/nqac089",
                    "pmid": "35234567"
                }
            },
            "document_name": "Dietary_Antioxidants_Meta_Analysis.pdf",
            "document_id": "doc-002",
            "confidence": 0.92
        },
        {
            "fact": {
                "uuid": "fact-3",
                "name": "Antioxidant-Rich Foods",
                "body": "Berries, leafy greens, nuts, and colorful vegetables are excellent sources of natural antioxidants. These foods provide not only antioxidants but also fiber, minerals, and other beneficial compounds that work synergistically.",
                "summary": "Food sources of antioxidants and their benefits",
                "properties": {
                    "title": "Whole Food Sources of Antioxidants: Nutritional and Health Benefits",
                    "authors": "Garcia, M.A., Lee, H.S., Patel, N.K.",
                    "year": "2023",
                    "journal": "Nutrients",
                    "volume": "15",
                    "issue": "8",
                    "pages": "1892",
                    "doi": "10.3390/nu15081892",
                    "url": "https://www.mdpi.com/2072-6643/15/8/1892"
                }
            },
            "document_name": "Whole_Foods_Antioxidants.pdf",
            "document_id": "doc-003",
            "confidence": 0.88
        },
        {
            "fact": {
                "uuid": "fact-4",
                "name": "Antioxidants and Cancer Treatment",
                "body": "The interaction between antioxidant supplements and cancer treatments is complex. While some antioxidants may protect healthy cells from treatment-related damage, they might also protect cancer cells from therapeutic interventions.",
                "summary": "Complex interactions between antioxidants and cancer therapy",
                "properties": {
                    "title": "Antioxidant Supplementation During Cancer Treatment: Benefits and Risks",
                    "authors": "Wilson, K.R., Martinez, C.L., Anderson, P.M.",
                    "year": "2023",
                    "journal": "Cancer Treatment Reviews",
                    "volume": "89",
                    "pages": "102-115",
                    "doi": "10.1016/j.ctrv.2023.02.008",
                    "pmid": "36789012"
                }
            },
            "document_name": "Antioxidants_Cancer_Treatment.pdf",
            "document_id": "doc-004",
            "confidence": 0.90
        },
        {
            "fact": {
                "uuid": "fact-5",
                "name": "Clinical Guidelines for Antioxidant Use",
                "body": "Current clinical guidelines recommend discussing antioxidant supplementation with healthcare providers, particularly for patients undergoing cancer treatment. Timing, dosage, and type of antioxidants should be carefully considered.",
                "summary": "Clinical recommendations for antioxidant supplementation",
                "properties": {
                    "title": "Clinical Practice Guidelines for Antioxidant Use in Oncology",
                    "authors": "Taylor, R.J., Kim, S.H., Roberts, A.B.",
                    "year": "2023",
                    "journal": "Journal of Clinical Oncology",
                    "volume": "41",
                    "issue": "12",
                    "pages": "2234-2248",
                    "doi": "10.1200/JCO.22.01456",
                    "pmid": "36912345",
                    "abstract": "This comprehensive review provides evidence-based guidelines for the use of antioxidant supplements in cancer patients, addressing timing, dosage, and potential interactions with standard treatments."
                }
            },
            "document_name": "Clinical_Guidelines_Antioxidants.pdf",
            "document_id": "doc-005",
            "confidence": 0.93
        }
    ]

    return {
        "answer_text": answer_text,
        "source_facts": source_facts
    }


def test_enhanced_citation_formatting():
    """Test enhanced citation formatting in different styles."""
    logger.info("=== Testing Enhanced Citation Formatting ===")
    
    citation_service = get_citation_service()
    mock_response = create_mock_qa_response()
    
    # Test different citation styles
    styles = ["apa", "mla", "chicago"]
    
    for style in styles:
        logger.info(f"\n{style.upper()} Style Citations:")
        logger.info("=" * 50)
        
        enhanced_response = citation_service.enhance_qa_response_with_citations(
            mock_response["answer_text"],
            mock_response["source_facts"],
            citation_style=style
        )
        
        logger.info("Enhanced Answer:")
        logger.info(enhanced_response["answer"][:200] + "...")
        
        logger.info("\nCitation Section:")
        logger.info(enhanced_response["citations"][:500] + "...")
        
        logger.info(f"\nTotal References: {enhanced_response['total_references']}")


def test_reference_metadata_extraction():
    """Test reference metadata extraction from facts."""
    logger.info("\n=== Testing Reference Metadata Extraction ===")
    
    citation_service = get_citation_service()
    mock_response = create_mock_qa_response()
    
    for i, fact in enumerate(mock_response["source_facts"]):
        logger.info(f"\nFact {i+1}: {fact['fact']['name']}")
        
        enhanced_ref = citation_service._create_enhanced_reference(fact, i+1)
        
        logger.info(f"  Title: {enhanced_ref.title}")
        logger.info(f"  Authors: <AUTHORS>
        logger.info(f"  Year: {enhanced_ref.year}")
        logger.info(f"  Journal: {enhanced_ref.journal}")
        logger.info(f"  DOI: {enhanced_ref.doi}")
        logger.info(f"  PMID: {enhanced_ref.pmid}")
        logger.info(f"  Confidence: {enhanced_ref.confidence}")


def test_clickable_links():
    """Test clickable link generation."""
    logger.info("\n=== Testing Clickable Links ===")
    
    citation_service = get_citation_service()
    mock_response = create_mock_qa_response()
    
    # Test with a fact that has DOI and PMID
    fact_with_links = mock_response["source_facts"][1]  # Has both DOI and PMID
    enhanced_ref = citation_service._create_enhanced_reference(fact_with_links, 1)
    
    # Test APA citation with links
    apa_citation = enhanced_ref.citation_apa
    enhanced_citation = citation_service._add_clickable_links(apa_citation, enhanced_ref)
    
    logger.info("Original Citation:")
    logger.info(apa_citation)
    logger.info("\nEnhanced Citation with Links:")
    logger.info(enhanced_citation)


def test_reference_summary():
    """Test reference summary generation."""
    logger.info("\n=== Testing Reference Summary ===")
    
    citation_service = get_citation_service()
    mock_response = create_mock_qa_response()
    
    # Create enhanced references
    enhanced_refs = []
    for i, fact in enumerate(mock_response["source_facts"]):
        enhanced_ref = citation_service._create_enhanced_reference(fact, i+1)
        enhanced_refs.append(enhanced_ref)
    
    # Generate summary
    summary = citation_service.generate_reference_summary(enhanced_refs)
    
    logger.info("Reference Summary:")
    logger.info(f"Total: {summary['total']}")
    logger.info(f"Journal articles: {summary['journal_count']}")
    logger.info(f"With DOI: {summary['doi_count']}")
    logger.info(f"Recent (2020+): {summary['recent_count']}")
    logger.info(f"Journals: {summary['journals']}")
    logger.info("\nSummary Text:")
    logger.info(summary['summary'])


def test_complete_enhanced_response():
    """Test the complete enhanced Q&A response."""
    logger.info("\n=== Testing Complete Enhanced Response ===")
    
    citation_service = get_citation_service()
    mock_response = create_mock_qa_response()
    
    enhanced_response = citation_service.enhance_qa_response_with_citations(
        mock_response["answer_text"],
        mock_response["source_facts"],
        citation_style="apa"
    )
    
    logger.info("Complete Enhanced Response Structure:")
    logger.info(f"- Answer length: {len(enhanced_response['answer'])} characters")
    logger.info(f"- Citations length: {len(enhanced_response['citations'])} characters")
    logger.info(f"- Number of references: {len(enhanced_response['references'])}")
    logger.info(f"- Citation style: {enhanced_response['citation_style']}")
    logger.info(f"- Total references: {enhanced_response['total_references']}")
    
    # Show first reference in detail
    if enhanced_response['references']:
        first_ref = enhanced_response['references'][0]
        logger.info("\nFirst Reference Details:")
        for key, value in first_ref.items():
            if value and key not in ['raw_text']:  # Skip empty and verbose fields
                logger.info(f"  {key}: {value}")


def test_comparison_with_original():
    """Compare enhanced citations with original format."""
    logger.info("\n=== Comparison: Enhanced vs Original ===")
    
    mock_response = create_mock_qa_response()
    
    logger.info("ORIGINAL FORMAT:")
    logger.info("Sources:")
    for i, fact in enumerate(mock_response["source_facts"]):
        logger.info(f"{i+1}. Document {i+1}. Document {i+2}. Document {i+3}.... please improve significantly")
    
    logger.info("\nENHANCED FORMAT:")
    citation_service = get_citation_service()
    enhanced_response = citation_service.enhance_qa_response_with_citations(
        mock_response["answer_text"],
        mock_response["source_facts"],
        citation_style="apa"
    )
    
    # Show just the citation section
    citations = enhanced_response["citations"]
    logger.info(citations[:800] + "..." if len(citations) > 800 else citations)


def main():
    """Run all tests."""
    logger.info("Starting Enhanced Citation System Tests")
    logger.info("=" * 60)
    
    try:
        test_enhanced_citation_formatting()
        test_reference_metadata_extraction()
        test_clickable_links()
        test_reference_summary()
        test_complete_enhanced_response()
        test_comparison_with_original()
        
        logger.info("\n" + "=" * 60)
        logger.info("All enhanced citation tests completed successfully!")
        logger.info("\nKey Improvements:")
        logger.info("✅ Detailed academic citations (APA, MLA, Chicago)")
        logger.info("✅ Clickable DOI and PMID links")
        logger.info("✅ Reference abstracts and metadata")
        logger.info("✅ Document source links")
        logger.info("✅ Reference summaries and statistics")
        logger.info("✅ Proper academic formatting")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
