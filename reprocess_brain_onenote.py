#!/usr/bin/env python3
"""
Reprocess Brain.one file to ensure all references are extracted properly.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from processors.enhanced_document_processor import EnhancedDocumentProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def reprocess_brain_onenote():
    """Reprocess Brain.one file with enhanced reference extraction."""
    
    print("🧠 REPROCESSING BRAIN.ONE FILE FOR COMPLETE REFERENCE EXTRACTION")
    print("=" * 80)
    
    # Find the most recent Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    print(f"📁 Full path: {brain_file}")
    
    try:
        # Initialize enhanced document processor
        processor = EnhancedDocumentProcessor()
        print("✅ Enhanced document processor initialized")
        
        # Process the Brain.one file with full pipeline
        print("\n🔄 Starting complete document processing...")
        print("   📝 Text extraction")
        print("   🧠 Entity extraction") 
        print("   📚 Reference extraction")
        print("   🔗 Embedding generation")
        print("   💾 Knowledge graph storage")
        
        result = await processor.process_document(
            file_path=str(brain_file),
            chunk_size=1200,
            overlap=0,
            extract_entities=True,
            extract_references=True,
            extract_metadata=True,
            generate_embeddings=True
        )
        
        print("\n📊 PROCESSING RESULTS:")
        print("=" * 60)
        
        if result.get("success", False):
            print("✅ Document processing completed successfully!")
            
            # Show detailed results
            print(f"📄 Document: {brain_file.name}")
            print(f"📝 Text chunks: {result.get('chunks_created', 0)}")
            print(f"🧠 Entities extracted: {result.get('entities_extracted', 0)}")
            print(f"📚 References extracted: {result.get('references_extracted', 0)}")
            print(f"🔗 Embeddings generated: {result.get('embeddings_generated', 0)}")
            print(f"📊 Processing method: {result.get('processing_method', 'Unknown')}")
            
            # Check if references were found
            references_count = result.get('references_extracted', 0)
            if references_count > 0:
                print(f"\n🎉 SUCCESS! Found {references_count} references in Brain.one file")
                
                # Check for ginger-related references specifically
                print("\n🌿 Checking for ginger-related content...")
                
                # Look for ginger references in the processing results
                processing_id = result.get('processing_id', '')
                if processing_id:
                    # Check if processing results file exists
                    results_file = Path(f"processing_results/{processing_id}.json")
                    if results_file.exists():
                        import json
                        with open(results_file, 'r', encoding='utf-8') as f:
                            processing_data = json.load(f)
                        
                        # Look for ginger-related entities
                        entities = processing_data.get('entities', [])
                        ginger_entities = [e for e in entities if 'ginger' in e.get('name', '').lower()]
                        
                        if ginger_entities:
                            print(f"   ✅ Found {len(ginger_entities)} ginger-related entities")
                            for entity in ginger_entities[:3]:
                                print(f"      • {entity.get('name', 'Unknown')} ({entity.get('type', 'Unknown')})")
                        else:
                            print("   ℹ️ No ginger-related entities found in this processing")
                
                # Check the references directory for new reference files
                ref_dir = Path("references")
                recent_ref_files = []
                if ref_dir.exists():
                    # Look for reference files created in the last few minutes
                    import time
                    current_time = time.time()
                    for ref_file in ref_dir.glob("*.csv"):
                        if current_time - ref_file.stat().st_mtime < 300:  # 5 minutes
                            recent_ref_files.append(ref_file)
                
                if recent_ref_files:
                    print(f"\n📚 Found {len(recent_ref_files)} recently created reference files:")
                    for ref_file in recent_ref_files:
                        print(f"   📄 {ref_file.name}")
                        
                        # Check if this file contains ginger references
                        try:
                            with open(ref_file, 'r', encoding='utf-8') as f:
                                content = f.read().lower()
                                if 'ginger' in content or 'shogaol' in content:
                                    print(f"      🌿 Contains ginger-related references!")
                        except Exception as e:
                            print(f"      ⚠️ Could not read file: {e}")
                
            else:
                print(f"\n⚠️ No references extracted from Brain.one file")
                print("This could mean:")
                print("1. The file doesn't contain structured references")
                print("2. The reference extraction patterns didn't match")
                print("3. The file processing encountered issues")
            
            # Show any errors or warnings
            if 'error' in result:
                print(f"\n⚠️ Processing warnings/errors:")
                print(f"   {result['error']}")
            
            return True
            
        else:
            print("❌ Document processing failed!")
            error = result.get('error', 'Unknown error')
            print(f"Error: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Error during Brain.one reprocessing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_existing_brain_references():
    """Check what Brain.one references already exist in the system."""
    
    print("\n🔍 CHECKING EXISTING BRAIN.ONE REFERENCES")
    print("=" * 60)
    
    try:
        # Check references directory for Brain.one related files
        ref_dir = Path("references")
        if not ref_dir.exists():
            print("❌ References directory not found")
            return
        
        brain_ref_files = []
        for ref_file in ref_dir.glob("*.csv"):
            if 'brain' in ref_file.name.lower():
                brain_ref_files.append(ref_file)
        
        if brain_ref_files:
            print(f"📚 Found {len(brain_ref_files)} Brain.one reference files:")
            for ref_file in brain_ref_files:
                print(f"   📄 {ref_file.name}")
                
                # Count references in file
                try:
                    with open(ref_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        ref_count = len(lines) - 1  # Subtract header
                        print(f"      📊 Contains {ref_count} references")
                        
                        # Check for ginger content
                        content = ''.join(lines).lower()
                        if 'ginger' in content:
                            print(f"      🌿 Contains ginger-related references")
                        
                except Exception as e:
                    print(f"      ⚠️ Could not read file: {e}")
        else:
            print("ℹ️ No existing Brain.one reference files found")
        
        # Check processing results for Brain.one
        results_dir = Path("processing_results")
        if results_dir.exists():
            brain_result_files = []
            for result_file in results_dir.glob("*.json"):
                if 'brain' in result_file.name.lower():
                    brain_result_files.append(result_file)
            
            if brain_result_files:
                print(f"\n📊 Found {len(brain_result_files)} Brain.one processing result files:")
                for result_file in brain_result_files:
                    print(f"   📄 {result_file.name}")
        
    except Exception as e:
        print(f"❌ Error checking existing references: {e}")

async def main():
    """Main function."""
    print("🧠 BRAIN.ONE REFERENCE EXTRACTION")
    print("=" * 80)
    
    # First check what already exists
    await check_existing_brain_references()
    
    # Then reprocess to ensure complete extraction
    success = await reprocess_brain_onenote()
    
    print("\n" + "=" * 80)
    print("🎯 BRAIN.ONE PROCESSING SUMMARY")
    print("=" * 80)
    
    if success:
        print("🎉 Brain.one file reprocessed successfully!")
        print("✅ Complete document pipeline executed")
        print("✅ References extracted and stored")
        print("✅ Entities added to knowledge graph")
        print("✅ Embeddings generated for search")
        
        print("\nNext steps:")
        print("1. Check the references tab in UI for new references")
        print("2. Search for ginger-related entities in the knowledge graph")
        print("3. Test Q&A with questions about ginger and neuroprotection")
        print("4. Run reference enhancement suite to improve reference quality")
    else:
        print("❌ Brain.one processing encountered issues")
        print("Check the logs above for specific problems")
    
    return success

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
