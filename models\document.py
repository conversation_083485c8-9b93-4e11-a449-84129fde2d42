"""
Document-related data models for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union, Annotated
from pydantic import BaseModel, Field, field_validator, model_validator, HttpUrl, PositiveInt, NonNegativeInt
from datetime import datetime
from enum import Enum
import re

class DocumentType(str, Enum):
    """Document type enumeration"""
    PDF = "pdf"
    TEXT = "text"
    WORD = "word"
    HTML = "html"
    SPREADSHEET = "spreadsheet"
    PRESENTATION = "presentation"
    EBOOK = "ebook"
    IMAGE = "image"
    OTHER = "other"

class OCRProvider(str, Enum):
    """OCR provider enumeration"""
    MISTRAL = "mistral"
    PYPDF2 = "pypdf2"
    TESSERACT = "tesseract"
    NONE = "none"

class DocumentMetadata(BaseModel):
    """Document metadata model"""
    title: Optional[str] = Field(None, description="Document title")
    authors: Optional[List[str]] = Field(None, description="List of authors")
    publication_date: Optional[datetime] = Field(None, description="Publication date")
    publisher: Optional[str] = Field(None, description="Publisher name")
    doi: Optional[str] = Field(None, description="Digital Object Identifier")
    isbn: Optional[str] = Field(None, description="International Standard Book Number")
    keywords: Optional[List[str]] = Field(None, description="List of keywords")
    abstract: Optional[str] = Field(None, description="Document abstract")
    language: Optional[str] = Field(None, description="Document language", min_length=2, max_length=3)
    pages: Optional[PositiveInt] = Field(None, description="Number of pages")
    source_url: Optional[HttpUrl] = Field(None, description="Source URL")
    custom_metadata: Optional[Dict[str, Any]] = Field(None, description="Custom metadata")

    @field_validator('doi')
    @classmethod
    def validate_doi(cls, v):
        if v is None:
            return v
        if not re.match(r"^10\.\d{4,9}/[-._;()/:A-Z0-9]+$", v, re.IGNORECASE):
            raise ValueError("Invalid DOI format. Must match pattern: 10.NNNN/XXXXX")
        return v

    @field_validator('isbn')
    @classmethod
    def validate_isbn(cls, v):
        if v is None:
            return v
        # Simple ISBN-10 or ISBN-13 validation
        isbn = v.replace("-", "").replace(" ", "")
        if len(isbn) == 10 and isbn[:9].isdigit() and (isbn[9].isdigit() or isbn[9].upper() == 'X'):
            return v
        elif len(isbn) == 13 and isbn.isdigit() and isbn.startswith(('978', '979')):
            return v
        raise ValueError("Invalid ISBN format. Must be a valid ISBN-10 or ISBN-13")

class DocumentChunk(BaseModel):
    """Document chunk model"""
    uuid: str = Field(..., description="Unique identifier for the chunk")
    document_id: str = Field(..., description="Document identifier this chunk belongs to")
    chunk_num: PositiveInt = Field(..., description="Chunk number within the document")
    body: str = Field(..., description="Text content of the chunk")
    embedding: Optional[List[float]] = Field(None, description="Vector embedding of the chunk")
    start_char: Optional[NonNegativeInt] = Field(None, description="Starting character position in the document")
    end_char: Optional[NonNegativeInt] = Field(None, description="Ending character position in the document")
    page_num: Optional[PositiveInt] = Field(None, description="Page number in the document")

    @model_validator(mode='after')
    def validate_char_positions(self):
        if self.start_char is not None and self.end_char is not None:
            if self.end_char <= self.start_char:
                raise ValueError("end_char must be greater than start_char")
        return self

class DocumentProcessingOptions(BaseModel):
    """Document processing options"""
    chunk_size: int = Field(1200, description="Size of text chunks in characters", ge=100, le=10000)
    overlap: int = Field(0, description="Overlap between chunks in characters", ge=0, le=5000)
    extract_entities: bool = Field(True, description="Whether to extract entities from the document")
    extract_references: bool = Field(True, description="Whether to extract references from the document")
    extract_metadata: bool = Field(True, description="Whether to extract metadata from the document")
    ocr_provider: OCRProvider = Field(OCRProvider.MISTRAL, description="OCR provider to use for PDF documents")

    @model_validator(mode='after')
    def validate_overlap(self):
        if self.overlap >= self.chunk_size:
            raise ValueError("overlap must be less than chunk_size")
        return self

class DocumentUploadResponse(BaseModel):
    """Document upload response"""
    filename: str = Field(..., description="Original filename")
    id: str = Field(..., description="Unique identifier for the document")
    file_type: str = Field(..., description="Type of the document")
    chunks: NonNegativeInt = Field(0, description="Number of chunks created")
    entities: NonNegativeInt = Field(0, description="Number of entities extracted")
    references: NonNegativeInt = Field(0, description="Number of references extracted")
    success: bool = Field(True, description="Whether the upload was successful")
    entity_extraction_running: bool = Field(False, description="Whether entity extraction is still running")
    reference_extraction_running: bool = Field(False, description="Whether reference extraction is still running")
    ocr_provider: str = Field("pypdf2", description="OCR provider used for processing")
    error: Optional[str] = Field(None, description="Error message if upload failed")

class DocumentSummary(BaseModel):
    """Document summary model"""
    id: str = Field(..., description="Unique identifier for the document")
    filename: str = Field(..., description="Original filename")
    file_type: DocumentType = Field(..., description="Type of the document")
    upload_date: datetime = Field(..., description="Date and time when the document was uploaded")
    chunks: NonNegativeInt = Field(..., description="Number of chunks created")
    entities: NonNegativeInt = Field(..., description="Number of entities extracted")
    references: NonNegativeInt = Field(..., description="Number of references extracted")
    metadata: Optional[DocumentMetadata] = Field(None, description="Document metadata")

class DocumentDetails(BaseModel):
    """Document details model"""
    uuid: str = Field(..., description="Unique identifier for the document")
    name: str = Field(..., description="Document name")
    file_path: Optional[str] = Field(None, description="Path to the document file")
    processed_at: Optional[str] = Field(None, description="ISO-formatted date when the document was processed")
    chunks: NonNegativeInt = Field(0, description="Number of chunks created")
    entities: NonNegativeInt = Field(0, description="Number of entities extracted")
    references: NonNegativeInt = Field(0, description="Number of references extracted")
    file_type: DocumentType = Field(DocumentType.OTHER, description="Type of the document")

    @field_validator('processed_at')
    @classmethod
    def validate_processed_at(cls, v):
        if v is None:
            return v
        try:
            datetime.fromisoformat(v)
        except ValueError:
            raise ValueError("processed_at must be a valid ISO-formatted date string")
        return v

class DocumentList(BaseModel):
    """Document list model"""
    documents: List[DocumentSummary] = Field(..., description="List of documents")
    total: NonNegativeInt = Field(..., description="Total number of documents")
    page: PositiveInt = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of documents per page", ge=1, le=100)

class DocumentProgressStatus(BaseModel):
    """Document processing progress status"""
    document_id: str = Field(..., description="Document ID")
    status: str = Field(..., description="Processing status")
    progress_percentage: int = Field(0, description="Progress percentage", ge=0, le=100)
    step_name: str = Field(..., description="Current processing step")
    details: Dict[str, Any] = Field({}, description="Additional details")

class ProcessingQueueStatus(BaseModel):
    """Processing queue status"""
    is_processing: bool = Field(..., description="Whether the queue is currently processing documents")
    queue_length: NonNegativeInt = Field(..., description="Number of documents in the queue")
    current_document: Optional[str] = Field(None, description="ID of the document currently being processed")
    current_progress: Optional[DocumentProgressStatus] = Field(None, description="Progress of the current document")
    documents_progress: Dict[str, DocumentProgressStatus] = Field({}, description="Progress of all documents in the queue")

class BatchDocumentUploadResponse(BaseModel):
    """Batch document upload response"""
    total_files: NonNegativeInt = Field(..., description="Total number of files in the batch")
    successful_uploads: NonNegativeInt = Field(..., description="Number of successful uploads")
    failed_uploads: NonNegativeInt = Field(..., description="Number of failed uploads")
    documents: List[DocumentUploadResponse] = Field(..., description="List of document upload responses")
    success: bool = Field(True, description="Whether the batch upload was successful overall")
    error: Optional[str] = Field(None, description="Error message if batch upload failed")

    @model_validator(mode='after')
    def validate_counts(self):
        if self.successful_uploads + self.failed_uploads != self.total_files:
            raise ValueError("successful_uploads + failed_uploads must equal total_files")
        return self
