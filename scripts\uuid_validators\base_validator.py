"""
Base validator class for UUID validation tasks.
"""

import logging
from datetime import datetime, timezone
from typing import Dict, Any
from database.database_service import get_falkordb_adapter
from utils.uuid_validation import generate_uuid

logger = logging.getLogger(__name__)


class BaseValidator:
    """Base class for UUID validators."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        """
        Initialize the validator.

        Args:
            fix: Whether to fix issues
            verbose: Whether to enable verbose logging
        """
        self.fix = fix
        self.verbose = verbose
        self.adapter = None
        self.stats = {}

    async def get_adapter(self):
        """Get the database adapter."""
        if not self.adapter:
            self.adapter = await get_falkordb_adapter()
        return self.adapter

    def log_info(self, message: str):
        """Log info message if verbose mode is enabled."""
        if self.verbose:
            logger.info(message)

    def get_timestamp(self) -> str:
        """Get current timestamp in ISO format."""
        return datetime.now(timezone.utc).isoformat()

    def generate_new_uuid(self) -> str:
        """Generate a new UUID."""
        return generate_uuid()

    async def validate(self):
        """Override in subclasses to implement validation logic."""
        raise NotImplementedError("Subclasses must implement validate method")

    def get_stats(self) -> Dict[str, Any]:
        """Get validation statistics."""
        return self.stats.copy()
