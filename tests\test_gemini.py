"""
Simple script to test if Google Gemini is working correctly.
"""

import os
import sys
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Google Gemini API key
google_api_key = os.environ.get('GOOGLE_API_KEY')
if not google_api_key:
    # Try to get it from the environment variable directly
    google_api_key = os.environ.get('AIzaSyBsw8WAQoGkh0XIrgiqy4dNHLvuqvbk754')

if not google_api_key:
    print("Error: Google API key not found in environment variables.")
    sys.exit(1)

print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-4:]}")

try:
    # Configure the Gemini API
    genai.configure(api_key=google_api_key)

    # List available models
    print("Available models:")
    for model in genai.list_models():
        if "generateContent" in model.supported_generation_methods:
            print(f"- {model.name}")

    # Test simple completion with Gemini
    print("\nSending test request to Google Gemini API...")

    model = genai.GenerativeModel('gemini-1.5-pro')
    response = model.generate_content("Tell me about antioxidants in 2-3 sentences.")

    print("\nResponse from Google Gemini:")
    print(f"Answer: {response.text}")
    print("\nAPI test successful!")

except Exception as e:
    print(f"\nError using Google Gemini API: {e}")
    print("\nAPI test failed.")
