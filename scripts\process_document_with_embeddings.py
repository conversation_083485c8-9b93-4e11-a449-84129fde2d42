"""
<PERSON><PERSON><PERSON> to process a document and generate embeddings in one step
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timezone
import argparse
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
import openai
from openai import OpenAI

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pdf_processor import process_pdf
from database.falkordb_adapter import GraphitiFalkorDBAdapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def generate_embeddings(api_key: str, texts: List[str], model: str = "text-embedding-3-small") -> List[List[float]]:
    """
    Generate embeddings for a list of texts using OpenAI.
    
    Args:
        api_key: OpenAI API key
        texts: List of texts to embed
        model: Embedding model to use
        
    Returns:
        List of embedding vectors
    """
    logger.info(f"Generating embeddings for {len(texts)} texts using model {model}")
    
    try:
        client = OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=texts,
            model=model
        )
        
        embeddings = [data.embedding for data in response.data]
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise

async def create_vector_index(adapter: GraphitiFalkorDBAdapter) -> bool:
    """
    Create a vector index in FalkorDB for Fact nodes.
    
    Args:
        adapter: FalkorDB adapter
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Creating vector index for Fact nodes")
    
    try:
        # Check if the index already exists
        check_index_query = """
        CALL db.indexes() YIELD name, label, properties, type
        WHERE name = 'fact_embedding_index' AND type = 'VECTOR'
        RETURN count(*) > 0 AS exists
        """
        
        result = adapter.execute_cypher(check_index_query)
        
        if result and len(result) > 1 and result[1] and result[1][0][0]:
            logger.info("Vector index 'fact_embedding_index' already exists")
            return True
        
        # Create the vector index
        create_index_query = """
        CREATE VECTOR INDEX fact_embedding_index
        FOR (f:Fact)
        ON (f.embedding)
        OPTIONS {
          dimension: 1536,
          similarity: 'cosine'
        }
        """
        
        adapter.execute_cypher(create_index_query)
        logger.info("Created vector index 'fact_embedding_index'")
        return True
    except Exception as e:
        logger.error(f"Error creating vector index: {e}")
        return False

async def get_facts_for_episode(adapter: GraphitiFalkorDBAdapter, episode_id: str) -> List[Dict[str, Any]]:
    """
    Get all facts for a specific episode.
    
    Args:
        adapter: FalkorDB adapter
        episode_id: Episode UUID
        
    Returns:
        List of facts
    """
    logger.info(f"Getting facts for episode {episode_id}")
    
    try:
        query = f"""
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid AS uuid, f.body AS body
        """
        
        result = adapter.execute_cypher(query)
        
        facts = []
        if result and len(result) > 1:
            for row in result[1]:
                facts.append({
                    "uuid": row[0],
                    "body": row[1]
                })
        
        logger.info(f"Found {len(facts)} facts for episode {episode_id}")
        return facts
    except Exception as e:
        logger.error(f"Error getting facts for episode: {e}")
        return []

async def update_facts_with_embeddings(adapter: GraphitiFalkorDBAdapter, facts: List[Dict[str, Any]], embeddings: List[List[float]]) -> bool:
    """
    Update Fact nodes with their embeddings.
    
    Args:
        adapter: FalkorDB adapter
        facts: List of facts to update
        embeddings: List of embedding vectors
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Updating {len(facts)} Fact nodes with embeddings")
    
    try:
        for i, fact in enumerate(facts):
            # Convert embedding to JSON string to store in FalkorDB
            embedding_json = json.dumps(embeddings[i])
            timestamp = datetime.now(timezone.utc).isoformat()
            
            query = f"""
            MATCH (f:Fact {{uuid: '{fact["uuid"]}'}})
            SET f.embedding = '{embedding_json}',
                f.embedding_model = 'text-embedding-3-small',
                f.embedding_updated_at = '{timestamp}'
            RETURN f.uuid
            """
            
            result = adapter.execute_cypher(query)
            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"Failed to update fact {fact['uuid']} with embedding")
        
        logger.info(f"Updated {len(facts)} Fact nodes with embeddings")
        return True
    except Exception as e:
        logger.error(f"Error updating Facts with embeddings: {e}")
        return False

async def process_document_with_embeddings(file_path: str, chunk_size: int = 1200, overlap: int = 0, max_pages: Optional[int] = None) -> Dict[str, Any]:
    """
    Process a document and generate embeddings in one step.
    
    Args:
        file_path: Path to the document file
        chunk_size: Size of text chunks in characters
        overlap: Overlap between chunks in characters
        max_pages: Maximum number of pages to process
        
    Returns:
        Result dictionary
    """
    # Load environment variables
    load_dotenv()
    
    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return {"success": False, "error": "OpenAI API key not found"}
    
    try:
        # Step 1: Process the document
        logger.info(f"Processing document: {file_path}")
        result = await process_pdf(file_path, chunk_size, overlap, max_pages)
        
        if not result["success"]:
            logger.error(f"Failed to process document: {result.get('error', 'Unknown error')}")
            return result
        
        episode_id = result["episode_id"]
        logger.info(f"Document processed successfully. Episode ID: {episode_id}")
        
        # Step 2: Connect to FalkorDB
        adapter = GraphitiFalkorDBAdapter()
        
        # Step 3: Create vector index if it doesn't exist
        if not await create_vector_index(adapter):
            logger.error("Failed to create vector index")
            return {"success": False, "error": "Failed to create vector index", **result}
        
        # Step 4: Get facts for the episode
        facts = await get_facts_for_episode(adapter, episode_id)
        
        if not facts:
            logger.error(f"No facts found for episode {episode_id}")
            return {"success": False, "error": "No facts found for episode", **result}
        
        # Step 5: Generate embeddings for the facts
        texts = [fact["body"] for fact in facts]
        embeddings = await generate_embeddings(openai_api_key, texts)
        
        # Step 6: Update facts with embeddings
        if not await update_facts_with_embeddings(adapter, facts, embeddings):
            logger.error("Failed to update facts with embeddings")
            return {"success": False, "error": "Failed to update facts with embeddings", **result}
        
        logger.info(f"Successfully added embeddings to {len(facts)} facts")
        
        # Close FalkorDB connection
        adapter.close()
        
        return {
            "success": True,
            "episode_id": episode_id,
            "chunks": result["chunks"],
            "embeddings_added": len(facts),
            "ocr_provider": result["ocr_provider"]
        }
    except Exception as e:
        logger.error(f"Error processing document with embeddings: {e}", exc_info=True)
        return {"success": False, "error": str(e)}

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Process a document and generate embeddings in one step.')
    parser.add_argument('file_path', help='Path to the document file')
    parser.add_argument('--chunk-size', type=int, default=1200, help='Size of text chunks in characters')
    parser.add_argument('--overlap', type=int, default=0, help='Overlap between chunks in characters')
    parser.add_argument('--max-pages', type=int, default=None, help='Maximum number of pages to process')
    
    args = parser.parse_args()
    
    # Process the document with embeddings
    result = await process_document_with_embeddings(args.file_path, args.chunk_size, args.overlap, args.max_pages)
    
    if result["success"]:
        logger.info(f"Successfully processed document: {args.file_path}")
        logger.info(f"Episode ID: {result['episode_id']}")
        logger.info(f"Chunks: {result['chunks']}")
        logger.info(f"Embeddings added: {result['embeddings_added']}")
        logger.info(f"OCR Provider: {result['ocr_provider']}")
    else:
        logger.error(f"Failed to process document: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    asyncio.run(main())
