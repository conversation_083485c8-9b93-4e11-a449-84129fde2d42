"""
Base classes and enums for the worker system.

This module provides the base classes and enums used by the worker system.
"""

from enum import Enum
from typing import Dict, List, Any, Optional, Union, Callable
import asyncio
import time

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

class WorkerType(Enum):
    """Types of workers in the document processing pipeline."""
    DOCUMENT_PROCESSOR = "document_processor"  # Handles initial document processing and text extraction
    ENTITY_EXTRACTOR = "entity_extractor"      # Extracts entities from text chunks
    ENTITY_DEDUPLICATOR = "entity_deduplicator" # Deduplicates entities in the knowledge graph
    REFERENCE_EXTRACTOR = "reference_extractor" # Extracts references from documents
    EMBEDDING_GENERATOR = "embedding_generator" # Generates embeddings for text chunks
    DATABASE_WRITER = "database_writer"        # Writes data to the database

class WorkerStatus(Enum):
    """Status of a worker."""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    TERMINATED = "terminated"

class WorkerStats:
    """Statistics for the worker system."""

    def __init__(self):
        """Initialize worker statistics."""
        self.documents_processed = 0
        self.entities_extracted = 0
        self.references_extracted = 0
        self.embeddings_generated = 0
        self.errors = 0
        self.start_time = None
        self.end_time = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert statistics to a dictionary."""
        return {
            "documents_processed": self.documents_processed,
            "entities_extracted": self.entities_extracted,
            "references_extracted": self.references_extracted,
            "embeddings_generated": self.embeddings_generated,
            "errors": self.errors,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "elapsed_time": self.elapsed_time
        }

    @property
    def elapsed_time(self) -> Optional[float]:
        """Get the elapsed time in seconds."""
        if self.start_time is None:
            return None

        end_time = self.end_time or time.time()
        return end_time - self.start_time

    def start(self):
        """Start timing."""
        self.start_time = time.time()

    def stop(self):
        """Stop timing."""
        self.end_time = time.time()

    def increment_documents(self, count: int = 1):
        """Increment the number of documents processed."""
        self.documents_processed += count

    def increment_entities(self, count: int = 1):
        """Increment the number of entities extracted."""
        self.entities_extracted += count

    def increment_references(self, count: int = 1):
        """Increment the number of references extracted."""
        self.references_extracted += count

    def increment_embeddings(self, count: int = 1):
        """Increment the number of embeddings generated."""
        self.embeddings_generated += count

    def increment_errors(self, count: int = 1):
        """Increment the number of errors."""
        self.errors += count
