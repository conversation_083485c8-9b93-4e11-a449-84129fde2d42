"""
Dependency injection utilities for the Graphiti application.

This module provides FastAPI dependencies that can be injected into route handlers.
"""

from typing import Callable, Dict, Any, Optional
from fastapi import Depends, HTTPException, status

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from database.database_service import get_falkordb_adapter
from utils.config import get_config
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def get_db():
    """
    Get a database adapter instance.
    
    This dependency provides a database adapter instance that can be injected
    into route handlers.
    
    Returns:
        FalkorDB adapter instance
    """
    try:
        adapter = await get_falkordb_adapter()
        if not adapter.is_connected():
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Database connection failed"
            )
        return adapter
    except Exception as e:
        logger.error(f"Error getting database adapter: {e}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=f"Database connection failed: {str(e)}"
        )

async def get_settings():
    """
    Get application settings.
    
    This dependency provides application settings that can be injected
    into route handlers.
    
    Returns:
        Application settings
    """
    try:
        config = get_config()
        return config
    except Exception as e:
        logger.error(f"Error getting settings: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting settings: {str(e)}"
        )

def get_llm_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get LLM configuration.
    
    This dependency provides LLM configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        LLM configuration
    """
    return settings.get("llm", {})

def get_embedding_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get embedding configuration.
    
    This dependency provides embedding configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        Embedding configuration
    """
    return settings.get("embedding", {})

def get_database_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get database configuration.
    
    This dependency provides database configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        Database configuration
    """
    return settings.get("falkordb", {})

def get_ocr_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get OCR configuration.
    
    This dependency provides OCR configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        OCR configuration
    """
    return settings.get("ocr", {})

def get_paths_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get paths configuration.
    
    This dependency provides paths configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        Paths configuration
    """
    return settings.get("paths", {})

def get_server_config(settings: Dict[str, Any] = Depends(get_settings)):
    """
    Get server configuration.
    
    This dependency provides server configuration that can be injected
    into route handlers.
    
    Args:
        settings: Application settings
        
    Returns:
        Server configuration
    """
    return settings.get("server", {})
