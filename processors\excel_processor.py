"""
Microsoft Excel Spreadsheet Processor
Handles .xls, .xlsx, and .csv files with enhanced data extraction.
"""

import os
import csv
import asyncio
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class ExcelProcessor:
    """
    Processor for Excel spreadsheets (.xls, .xlsx, .csv).
    """
    
    def __init__(self):
        """Initialize the Excel processor."""
        self.supported_extensions = ['.xls', '.xlsx', '.csv']
        
        # Try to import required libraries
        self.pandas_available = self._check_pandas_availability()
        self.openpyxl_available = self._check_openpyxl_availability()
        self.xlrd_available = self._check_xlrd_availability()
    
    def _check_pandas_availability(self) -> bool:
        """Check if pandas is available."""
        try:
            import pandas as pd
            return True
        except ImportError:
            logger.warning("pandas not available. Install with: pip install pandas")
            return False
    
    def _check_openpyxl_availability(self) -> bool:
        """Check if openpyxl is available."""
        try:
            import openpyxl
            return True
        except ImportError:
            logger.warning("openpyxl not available. Install with: pip install openpyxl")
            return False
    
    def _check_xlrd_availability(self) -> bool:
        """Check if xlrd is available."""
        try:
            import xlrd
            return True
        except ImportError:
            logger.warning("xlrd not available. Install with: pip install xlrd")
            return False
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text and data from a spreadsheet file.
        
        Args:
            file_path: Path to the spreadsheet file
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Handle CSV files
            if file_ext == '.csv':
                return await self._extract_from_csv(file_path)
            
            # Handle Excel files (.xls, .xlsx)
            elif file_ext in ['.xls', '.xlsx']:
                # Try pandas first (most comprehensive)
                if self.pandas_available:
                    try:
                        return await self._extract_with_pandas(file_path)
                    except Exception as e:
                        logger.warning(f"Pandas extraction failed for {file_path}: {e}")
                
                # Fallback to openpyxl for .xlsx
                if file_ext == '.xlsx' and self.openpyxl_available:
                    try:
                        return await self._extract_with_openpyxl(file_path)
                    except Exception as e:
                        logger.warning(f"openpyxl extraction failed for {file_path}: {e}")
                
                # Fallback to xlrd for .xls
                if file_ext == '.xls' and self.xlrd_available:
                    try:
                        return await self._extract_with_xlrd(file_path)
                    except Exception as e:
                        logger.warning(f"xlrd extraction failed for {file_path}: {e}")
            
            return {
                'success': False,
                'error': f"No suitable extraction method available for {file_path}",
                'text': '',
                'metadata': {}
            }
            
        except Exception as e:
            logger.error(f"Error processing spreadsheet {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_from_csv(self, file_path: Path) -> Dict[str, Any]:
        """Extract data from CSV file."""
        try:
            rows = []
            encoding = 'utf-8'
            
            # Try different encodings
            for enc in ['utf-8', 'utf-8-sig', 'latin1', 'cp1252']:
                try:
                    with open(file_path, 'r', encoding=enc, newline='') as csvfile:
                        # Detect delimiter
                        sample = csvfile.read(1024)
                        csvfile.seek(0)
                        
                        sniffer = csv.Sniffer()
                        delimiter = sniffer.sniff(sample).delimiter
                        
                        reader = csv.reader(csvfile, delimiter=delimiter)
                        rows = list(reader)
                        encoding = enc
                        break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"Error reading CSV with encoding {enc}: {e}")
                    continue
            
            if not rows:
                return {
                    'success': False,
                    'error': f"Could not read CSV file {file_path}",
                    'text': '',
                    'metadata': {}
                }
            
            # Convert to text format
            text_lines = []
            headers = rows[0] if rows else []
            
            # Add headers
            if headers:
                text_lines.append("Headers: " + " | ".join(str(h) for h in headers))
                text_lines.append("-" * 50)
            
            # Add data rows (limit to prevent huge files)
            max_rows = 1000
            data_rows = rows[1:max_rows+1] if len(rows) > 1 else []
            
            for i, row in enumerate(data_rows):
                row_text = " | ".join(str(cell) for cell in row)
                text_lines.append(f"Row {i+1}: {row_text}")
            
            if len(rows) > max_rows + 1:
                text_lines.append(f"... and {len(rows) - max_rows - 1} more rows")
            
            text = "\n".join(text_lines)
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'encoding': encoding,
                'total_rows': len(rows),
                'total_columns': len(headers) if headers else 0,
                'headers': headers[:10],  # First 10 headers
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'csv_reader'
            }
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'csv_reader',
                'extraction_method': 'csv_reader'
            }
            
        except Exception as e:
            logger.error(f"Error extracting from CSV: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_pandas(self, file_path: Path) -> Dict[str, Any]:
        """Extract data using pandas."""
        try:
            import pandas as pd
            
            file_ext = file_path.suffix.lower()
            
            # Read the file
            if file_ext == '.csv':
                df = pd.read_csv(file_path, encoding='utf-8')
            elif file_ext == '.xlsx':
                # Read all sheets
                excel_file = pd.ExcelFile(file_path)
                sheets_data = {}
                
                for sheet_name in excel_file.sheet_names:
                    try:
                        sheets_data[sheet_name] = pd.read_excel(file_path, sheet_name=sheet_name)
                    except Exception as e:
                        logger.warning(f"Error reading sheet {sheet_name}: {e}")
                        continue
                
                # Combine all sheets
                if sheets_data:
                    df = pd.concat(sheets_data.values(), ignore_index=True)
                else:
                    df = pd.DataFrame()
            elif file_ext == '.xls':
                df = pd.read_excel(file_path)
            else:
                raise ValueError(f"Unsupported file extension: {file_ext}")
            
            if df.empty:
                return {
                    'success': False,
                    'error': f"No data found in {file_path}",
                    'text': '',
                    'metadata': {}
                }
            
            # Convert to text
            text_lines = []
            
            # Add column information
            text_lines.append(f"Columns ({len(df.columns)}): " + " | ".join(df.columns.astype(str)))
            text_lines.append("-" * 50)
            
            # Add data summary
            text_lines.append(f"Data Summary:")
            text_lines.append(f"Total rows: {len(df)}")
            text_lines.append(f"Total columns: {len(df.columns)}")
            text_lines.append("-" * 50)
            
            # Add sample data (first 100 rows)
            max_rows = min(100, len(df))
            for i in range(max_rows):
                row_data = df.iloc[i].astype(str).tolist()
                row_text = " | ".join(row_data)
                text_lines.append(f"Row {i+1}: {row_text}")
            
            if len(df) > max_rows:
                text_lines.append(f"... and {len(df) - max_rows} more rows")
            
            text = "\n".join(text_lines)
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'total_rows': len(df),
                'total_columns': len(df.columns),
                'columns': df.columns.tolist()[:20],  # First 20 columns
                'data_types': df.dtypes.astype(str).to_dict(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'pandas'
            }
            
            # Add sheet information for Excel files
            if file_ext in ['.xlsx', '.xls']:
                try:
                    excel_file = pd.ExcelFile(file_path)
                    metadata['sheet_names'] = excel_file.sheet_names
                    metadata['total_sheets'] = len(excel_file.sheet_names)
                except:
                    pass
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'pandas',
                'extraction_method': 'pandas'
            }
            
        except Exception as e:
            logger.error(f"Error extracting with pandas: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_openpyxl(self, file_path: Path) -> Dict[str, Any]:
        """Extract data using openpyxl (for .xlsx files)."""
        try:
            from openpyxl import load_workbook
            
            workbook = load_workbook(file_path, read_only=True)
            
            text_lines = []
            total_rows = 0
            total_columns = 0
            
            # Process each worksheet
            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                
                text_lines.append(f"\n=== Sheet: {sheet_name} ===")
                
                # Get data from sheet
                rows_data = []
                for row in worksheet.iter_rows(values_only=True):
                    if any(cell is not None for cell in row):  # Skip empty rows
                        rows_data.append(row)
                
                if rows_data:
                    # Add headers if available
                    if len(rows_data) > 0:
                        headers = [str(cell) if cell is not None else '' for cell in rows_data[0]]
                        text_lines.append("Headers: " + " | ".join(headers))
                        text_lines.append("-" * 30)
                    
                    # Add data rows (limit to prevent huge output)
                    max_rows = min(50, len(rows_data))
                    for i, row in enumerate(rows_data[1:max_rows]):
                        row_text = " | ".join(str(cell) if cell is not None else '' for cell in row)
                        text_lines.append(f"Row {i+1}: {row_text}")
                    
                    if len(rows_data) > max_rows:
                        text_lines.append(f"... and {len(rows_data) - max_rows} more rows")
                    
                    total_rows += len(rows_data)
                    total_columns = max(total_columns, len(rows_data[0]) if rows_data else 0)
            
            text = "\n".join(text_lines)
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'total_rows': total_rows,
                'total_columns': total_columns,
                'sheet_names': workbook.sheetnames,
                'total_sheets': len(workbook.sheetnames),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'openpyxl'
            }
            
            workbook.close()
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'openpyxl',
                'extraction_method': 'openpyxl'
            }
            
        except Exception as e:
            logger.error(f"Error extracting with openpyxl: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_xlrd(self, file_path: Path) -> Dict[str, Any]:
        """Extract data using xlrd (for .xls files)."""
        try:
            import xlrd
            
            workbook = xlrd.open_workbook(file_path)
            
            text_lines = []
            total_rows = 0
            total_columns = 0
            
            # Process each worksheet
            for sheet_idx in range(workbook.nsheets):
                worksheet = workbook.sheet_by_index(sheet_idx)
                sheet_name = workbook.sheet_names()[sheet_idx]
                
                text_lines.append(f"\n=== Sheet: {sheet_name} ===")
                
                if worksheet.nrows > 0:
                    # Add headers if available
                    if worksheet.nrows > 0:
                        headers = [str(worksheet.cell_value(0, col)) for col in range(worksheet.ncols)]
                        text_lines.append("Headers: " + " | ".join(headers))
                        text_lines.append("-" * 30)
                    
                    # Add data rows (limit to prevent huge output)
                    max_rows = min(50, worksheet.nrows)
                    for row_idx in range(1, max_rows):
                        row_data = [str(worksheet.cell_value(row_idx, col)) for col in range(worksheet.ncols)]
                        row_text = " | ".join(row_data)
                        text_lines.append(f"Row {row_idx}: {row_text}")
                    
                    if worksheet.nrows > max_rows:
                        text_lines.append(f"... and {worksheet.nrows - max_rows} more rows")
                    
                    total_rows += worksheet.nrows
                    total_columns = max(total_columns, worksheet.ncols)
            
            text = "\n".join(text_lines)
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'total_rows': total_rows,
                'total_columns': total_columns,
                'sheet_names': workbook.sheet_names(),
                'total_sheets': workbook.nsheets,
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'xlrd'
            }
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'xlrd',
                'extraction_method': 'xlrd'
            }
            
        except Exception as e:
            logger.error(f"Error extracting with xlrd: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the spreadsheet content.
        
        Args:
            file_path: Path to the spreadsheet
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
