"""
Merged entity validator for orphaned and inconsistent relationships.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class MergeValidator(BaseValidator):
    """Validator for issues with merged entities."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "orphaned_relationships": 0,
            "orphaned_relationships_fixed": 0,
            "inconsistent_relationships": 0,
            "inconsistent_relationships_fixed": 0
        }

    async def validate(self):
        """Check for issues with merged entities."""
        await self._check_orphaned_relationships()
        await self._check_relationship_consistency()

    async def _check_orphaned_relationships(self):
        """Check for orphaned relationships after entity merges."""
        adapter = await self.get_adapter()

        # Find relationships pointing to non-existent entities
        query = """
        MATCH ()-[r]->(e:Entity)
        WHERE NOT EXISTS(e.uuid)
        RETURN id(r) as rel_id, type(r) as rel_type
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        orphaned_relationships = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                orphaned_relationships.append({
                    "rel_id": row[0],
                    "rel_type": row[1]
                })

            self.stats["orphaned_relationships"] = len(orphaned_relationships)
            self.log_info(f"Found {len(orphaned_relationships)} orphaned relationships")

            if self.fix:
                fixed_count = await self._fix_orphaned_relationships(orphaned_relationships)
                self.stats["orphaned_relationships_fixed"] = fixed_count

    async def _fix_orphaned_relationships(self, orphaned_relationships: List[Dict[str, Any]]) -> int:
        """
        Fix orphaned relationships by removing them.

        Args:
            orphaned_relationships: List of orphaned relationships

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in orphaned_relationships:
            # Delete the orphaned relationship
            query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['rel_id']}
            DELETE r
            """

            result = adapter.execute_cypher(query)

            if result:
                fixed_count += 1
                self.log_info(f"Deleted orphaned relationship of type {rel['rel_type']}")

        return fixed_count

    async def _check_relationship_consistency(self):
        """Check for inconsistent relationships after entity merges."""
        adapter = await self.get_adapter()

        # Find relationships with inconsistent UUIDs
        query = """
        MATCH (source)-[r]->(target:Entity)
        WHERE EXISTS(r.target_uuid) AND r.target_uuid <> target.uuid
        RETURN id(r) as rel_id, type(r) as rel_type,
               source.uuid as source_uuid, target.uuid as target_uuid,
               r.target_uuid as stored_target_uuid
        LIMIT 100
        """

        result = adapter.execute_cypher(query)
        inconsistent_relationships = []

        if result and len(result) > 1 and len(result[1]) > 0:
            for row in result[1]:
                inconsistent_relationships.append({
                    "rel_id": row[0],
                    "rel_type": row[1],
                    "source_uuid": row[2],
                    "target_uuid": row[3],
                    "stored_target_uuid": row[4]
                })

            self.stats["inconsistent_relationships"] = len(inconsistent_relationships)
            self.log_info(f"Found {len(inconsistent_relationships)} inconsistent relationships")

            if self.fix:
                fixed_count = await self._fix_relationship_consistency(inconsistent_relationships)
                self.stats["inconsistent_relationships_fixed"] = fixed_count

    async def _fix_relationship_consistency(self, inconsistent_relationships: List[Dict[str, Any]]) -> int:
        """
        Fix inconsistent relationships by updating the stored UUID.

        Args:
            inconsistent_relationships: List of inconsistent relationships

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in inconsistent_relationships:
            # Update the relationship to use the correct target UUID
            query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['rel_id']}
            SET r.target_uuid = '{rel['target_uuid']}',
                r.updated_at = '{self.get_timestamp()}'
            """

            result = adapter.execute_cypher(query)

            if result:
                fixed_count += 1
                self.log_info(f"Fixed inconsistent relationship of type {rel['rel_type']}")

        return fixed_count
