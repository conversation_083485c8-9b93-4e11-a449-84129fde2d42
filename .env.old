# FalkorDB Configuration
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_PASSWORD=

# Redis Vector Search Configuration
REDIS_VECTOR_SEARCH_HOST=localhost
REDIS_VECTOR_SEARCH_PORT=6380
REDIS_VECTOR_SEARCH_PASSWORD=

# Web Server Configuration
HOST=0.0.0.0
PORT=9753

# UNIFIED LAUNCH SYSTEM (Updated June 2025)
# Primary: python start.py ui (or just python start.py)
# Alternative: python app.py
# Docker: python start.py docker
# MCP: python start.py mcp
# Health Check: python start.py health
# See LAUNCH_GUIDE.md for complete instructions

# OpenAI API Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************

# LLM Models Configuration
# Primary LLM for question answering (Avery assistant)
USE_LOCAL_LLM=false
QA_LLM_PROVIDER=openrouter
QA_LLM_MODEL=thedrummer/valkyrie-49b-v1

# LLM Parameters Configuration
QA_LLM_TEMPERATURE=0.3
QA_LLM_MAX_TOKENS=1000
QA_LLM_TOP_K=40
QA_LLM_TOP_P=0.9

# Entity Extraction Configuration
USE_LOCAL_LLM=false
ENTITY_EXTRACTION_PROVIDER=openrouter
ENTITY_EXTRACTION_MODEL=meta-llama/llama-4-maverick

# Reference Extraction Configuration

# Worker Configuration
WORKER_PORT=

# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434

# Embedding Models Configuration
# Used for semantic search and vector similarity
USE_LOCAL_EMBEDDINGS=true
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=snowflake-arctic-embed2

# Chunking Configuration
CHUNK_SIZE=1200
CHUNK_OVERLAP=0
RECURSIVE_CHUNKING=true

# Alternative LLM Providers
# Google Gemini API key (alternative LLM and embeddings)
GOOGLE_API_KEY=AIzaSyBsw8WAQoGkh0XIrgiqy4dNHLvuqvbk754

# OpenRouter API KEY
OPEN_ROUTER_API_KEY=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
OPENROUTER_API_KEY=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
# OpenRouter Models
OPEN_ROUTER_MODELS="qwen3-4b,qwen3-30b-a3b,meta-llama/llama-4-maverick,google/gemma-3-27b-it,mistralai/mistral-nemo,huggingfaceh4/zephyr-7b-beta,nvidia/llama-3.3-nemotron-super-49b-v1:free,anthropic/claude-3-opus-20240229,anthropic/claude-3-sonnet-20240229,google/gemini-1.5-pro-latest,mistralai/mistral-large-latest"

# Mistral API key (alternative LLM and OCR)
MISTRAL_API_KEY=d8b5P0AE6NpGlMCw4F8wTVBwo9zjRsHK

# OCR Configuration
USE_MISTRAL_OCR=true
MISTRAL_OCR_MODEL=mistral-ocr-latest

# Reference Deduplication settings
TITLE_SIMILARITY_THRESHOLD=0.85
AUTHOR_SIMILARITY_THRESHOLD=0.75
YEAR_MATCH_REQUIRED=true
MIN_OVERALL_SIMILARITY=0.8

# Entity Deduplication settings
ENTITY_NAME_SIMILARITY_THRESHOLD=0.85
ENTITY_TYPE_MATCH_REQUIRED=true
ENTITY_MIN_OVERALL_SIMILARITY=0.8
ENTITY_DEDUP_BATCH_SIZE=100
ENTITY_DEDUP_FETCH_BATCH_SIZE=5000
ENTITY_DEDUP_MAX_PER_REQUEST=0
USE_LLM_FOR_ENTITY_DEDUPLICATION=true
ENTITY_DEDUP_LLM_PROVIDER=openrouter
ENTITY_DEDUP_LLM_MODEL=meta-llama/llama-4-maverick
ENTITY_DEDUP_VERBOSE_LOGGING=false

# External API keys (optional)
CROSSREF_EMAIL=<EMAIL>
PUBMED_API_KEY=0f4e874dd117d508097d5c7318515a866b08

# LLM Usage Map
# Question Answering: MedLLaMA3 v2.0 (Avery assistant with natural medicine expertise)
# Entity Extraction: meta-llama/llama-4-maverick (for identifying entities in text)
# Relationship Extraction: meta-llama/llama-4-maverick (for identifying relationships between entities)
# Embeddings: Ollama snowflake-arctic-embed2:latestpenAI text-embedding-3-small (for vector search and similarity)
# OCR: Mistral OCR (for processing PDF documents with OCR)

# Available Ollama Models
OLLAMA_MODELS="ahmgam/medllama3-v20:latest,meditron:latest,mistral-nemo:latest,bge-m3:latest,mistral:latest,snowflake-arctic-embed2:latest,llama2:latest,nomic-embed-text:latest,gemma3-modified:latest,gemma3:latest,gemma:latest,llama3.2:latest,llama3.3:latest"
