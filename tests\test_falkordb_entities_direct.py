"""
Test FalkorDB entities directly
"""

import logging
from graphiti_falkordb_adapter import GraphitiFalkorDBAdapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_entities():
    """Test getting entities from FalkorDB"""
    # Create FalkorDB adapter
    adapter = GraphitiFalkorDBAdapter(graph_name="graphiti")
    
    try:
        # Get all entities
        result = adapter.get_all_entities()
        
        # Print the result
        logger.info(f"Result: {result}")
        
        # Check if there are any entities
        if result and "entities" in result:
            logger.info(f"Found {len(result['entities'])} entities")
            
            # Print the first few entities
            for i, entity in enumerate(result["entities"][:10]):
                logger.info(f"Entity {i+1}: {entity}")
        else:
            logger.info("No entities found")
    
    finally:
        adapter.close()

if __name__ == "__main__":
    test_entities()
