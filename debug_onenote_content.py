#!/usr/bin/env python3
"""
Debug script to examine what's actually being extracted from OneNote files.

This script will help us understand why Mistral OCR is getting empty PDFs.
"""

import sys
import os
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


def debug_onenote_extraction():
    """Debug OneNote extraction to see what content is actually available."""
    
    # Find the most recent OneNote file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return
    
    # Use the most recent one
    onenote_file = onenote_files[-1]  # Get the last one (most recent)
    print(f"🔍 Examining OneNote file: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from one_extract import OneNoteExtractor, OneNoteExtractorError
        
        # Read the OneNote file
        with open(onenote_file, 'rb') as f:
            file_data = f.read()
        
        print(f"📄 Raw file data length: {len(file_data):,} bytes")
        
        # Initialize OneNote extractor
        try:
            extractor = OneNoteExtractor(data=file_data)
            print("✅ OneNote extractor initialized successfully")
        except OneNoteExtractorError as e:
            print(f"❌ OneNote extractor failed: {e}")
            return
        
        # Extract metadata
        print("\n🔍 EXTRACTING METADATA:")
        meta_objects = list(extractor.extract_meta())
        print(f"📊 Found {len(meta_objects)} metadata objects")
        
        for i, meta_obj in enumerate(meta_objects):
            print(f"\n--- Metadata Object {i+1} ---")
            print(f"  Object ID: {getattr(meta_obj, 'object_id', 'N/A')}")
            print(f"  Title: {getattr(meta_obj, 'title', 'N/A')}")
            print(f"  Creation Date: {getattr(meta_obj, 'creation_date', 'N/A')}")
            print(f"  Last Modified: {getattr(meta_obj, 'last_modification_date', 'N/A')}")
            
            # Try to get more attributes
            for attr in dir(meta_obj):
                if not attr.startswith('_') and attr not in ['object_id', 'title', 'creation_date', 'last_modification_date']:
                    try:
                        value = getattr(meta_obj, attr)
                        if value and str(value).strip():
                            print(f"  {attr}: {value}")
                    except Exception:
                        pass
        
        # Extract embedded files
        print("\n🔍 EXTRACTING EMBEDDED FILES:")
        embedded_files = list(extractor.extract_files())
        print(f"📎 Found {len(embedded_files)} embedded files")
        
        total_embedded_text = ""
        for i, file_data in enumerate(embedded_files):
            print(f"\n--- Embedded File {i+1} ---")
            print(f"  Size: {len(file_data):,} bytes")
            
            # Try to decode as text
            try:
                text_content = file_data.decode('utf-8', errors='ignore')
                if text_content.strip():
                    print(f"  Text content length: {len(text_content)} characters")
                    print(f"  First 200 chars: {text_content[:200]}")
                    total_embedded_text += text_content + "\n\n"
                else:
                    print("  No readable text content")
            except Exception as e:
                print(f"  Could not decode as text: {e}")
                
                # Try other encodings
                for encoding in ['latin-1', 'cp1252', 'ascii']:
                    try:
                        text_content = file_data.decode(encoding, errors='ignore')
                        if text_content.strip():
                            print(f"  Text content ({encoding}): {len(text_content)} characters")
                            break
                    except Exception:
                        continue
        
        print(f"\n📝 Total embedded text extracted: {len(total_embedded_text)} characters")
        
        # Try to extract any other content
        print("\n🔍 CHECKING EXTRACTOR METHODS:")
        for method_name in dir(extractor):
            if not method_name.startswith('_') and 'extract' in method_name.lower():
                print(f"  Available method: {method_name}")
                try:
                    method = getattr(extractor, method_name)
                    if callable(method):
                        result = method()
                        if hasattr(result, '__iter__') and not isinstance(result, (str, bytes)):
                            result_list = list(result)
                            print(f"    Returns: {len(result_list)} items")
                        else:
                            print(f"    Returns: {type(result)} - {str(result)[:100]}")
                except Exception as e:
                    print(f"    Error calling {method_name}: {e}")
        
        # Summary
        print(f"\n📊 EXTRACTION SUMMARY:")
        print(f"  Metadata objects: {len(meta_objects)}")
        print(f"  Embedded files: {len(embedded_files)}")
        print(f"  Total embedded text: {len(total_embedded_text)} characters")
        
        if len(total_embedded_text) < 100:
            print("❌ CRITICAL: Very little text content extracted!")
            print("   This explains why Mistral OCR is getting empty PDFs")
        else:
            print("✅ Reasonable amount of text content found")
        
        # Save extracted content for inspection
        if total_embedded_text:
            debug_file = Path("debug_onenote_extracted_content.txt")
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write("=== ONENOTE EXTRACTED CONTENT DEBUG ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Metadata objects: {len(meta_objects)}\n")
                f.write(f"Embedded files: {len(embedded_files)}\n\n")
                f.write("=== EXTRACTED TEXT ===\n\n")
                f.write(total_embedded_text)
            print(f"💾 Saved extracted content to: {debug_file}")
        
    except ImportError:
        print("❌ one-extract library not available")
    except Exception as e:
        print(f"❌ Error during OneNote debugging: {e}")
        import traceback
        traceback.print_exc()


def debug_pdf_generation():
    """Debug the PDF generation process."""
    print("\n" + "="*60)
    print("🔍 DEBUGGING PDF GENERATION")
    print("="*60)
    
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph
        from reportlab.lib.styles import getSampleStyleSheet
        import tempfile
        
        # Test basic PDF creation
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            pdf_path = temp_file.name
        
        try:
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            styles = getSampleStyleSheet()
            
            # Create a test PDF with substantial content
            story = [
                Paragraph("Test OneNote Content", styles['Title']),
                Paragraph("This is a test to see if PDF generation works properly.", styles['Normal']),
                Paragraph("Section 1: Brain Health", styles['Heading2']),
                Paragraph("Ginger has neuroprotective effects and can help with brain health. It contains compounds like gingerol that have anti-inflammatory properties.", styles['Normal']),
                Paragraph("Section 2: Bioactive Compounds", styles['Heading2']),
                Paragraph("Baicalein is a flavonoid with anti-inflammatory properties. It can inhibit TNF and NFK pathways.", styles['Normal']),
                Paragraph("Section 3: Neurotransmitters", styles['Heading2']),
                Paragraph("Neurotransmitters are chemical messengers that transmit signals across synapses. NFG (Nerve Growth Factor) supports neuron growth and survival.", styles['Normal']),
            ]
            
            doc.build(story)
            
            # Check the generated PDF
            pdf_size = Path(pdf_path).stat().st_size
            print(f"✅ Test PDF created: {pdf_size:,} bytes")
            
            # Try to process it with Mistral OCR
            print("🔍 Testing Mistral OCR on generated PDF...")
            
            from utils.mistral_ocr import MistralOCRProcessor
            import asyncio
            
            async def test_mistral_ocr():
                mistral_ocr = MistralOCRProcessor()
                result = await mistral_ocr.extract_text_from_document(pdf_path)
                return result
            
            extracted_text = asyncio.run(test_mistral_ocr())
            
            if extracted_text:
                print(f"✅ Mistral OCR extracted {len(extracted_text)} characters")
                print(f"First 200 chars: {extracted_text[:200]}")
            else:
                print("❌ Mistral OCR returned no text from test PDF")
            
        finally:
            # Clean up
            try:
                Path(pdf_path).unlink()
            except Exception:
                pass
                
    except Exception as e:
        print(f"❌ Error testing PDF generation: {e}")
        import traceback
        traceback.print_exc()


def main():
    """Run OneNote debugging."""
    print("🔍 ONENOTE CONTENT DEBUGGING")
    print("="*60)
    
    debug_onenote_extraction()
    debug_pdf_generation()
    
    print("\n" + "="*60)
    print("🎯 DEBUGGING COMPLETE")
    print("="*60)


if __name__ == "__main__":
    main()
