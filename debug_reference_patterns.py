#!/usr/bin/env python3
"""
Debug reference extraction patterns
"""

import asyncio
import re
from processors.pdf_processor import PDFProcessor
from pathlib import Path

async def debug_reference_patterns():
    # Get the text first
    processor = PDFProcessor()
    file_path = Path('uploads/4e8bb988-26e9-4e75-a50d-53e7bcb4835d_Co Q10 - Athletes.pdf')
    
    result = await processor.extract_text(file_path)
    if not result.get('success'):
        print("Failed to extract text")
        return
    
    text = result.get('text', '')
    print(f"Text length: {len(text)}")
    
    # Find the reference context
    ref_start = text.find('Journal of the International Society')
    if ref_start == -1:
        print("Reference not found in text!")
        return
    
    # Get context around the reference
    context_start = max(0, ref_start - 200)
    context_end = min(len(text), ref_start + 400)
    context = text[context_start:context_end]
    
    print(f"\nReference context:")
    print("=" * 80)
    print(context)
    print("=" * 80)
    
    # Test specific patterns
    patterns = [
        (r'\*([^*]+Journal[^*]+)\*', "Italics journal pattern"),
        (r'"([^"]+Journal[^"]+)"', "Quoted journal pattern"),
        (r'_([^_]+Journal[^_]+)_', "Underlined journal pattern"),
        (r'(?:published\s+in\s+|in\s+the\s+)([A-Z][^.,]{10,}(?:Journal|Review|Magazine|Proceedings)[^.,]{0,50})', "Published in pattern"),
        (r'Journal[^.,]{0,100}', "Simple journal pattern"),
        (r'\*[^*]+\*', "Any italics pattern"),
    ]
    
    print(f"\nTesting patterns:")
    for pattern, description in patterns:
        matches = re.findall(pattern, context, re.MULTILINE | re.IGNORECASE)
        print(f"{description}: {len(matches)} matches")
        for i, match in enumerate(matches[:3], 1):
            if isinstance(match, tuple):
                match = match[0] if match[0] else match[1] if len(match) > 1 else ""
            print(f"  {i}. {match}")
    
    # Test the validation function
    print(f"\nTesting validation:")
    test_refs = [
        "Journal of the International Society of Sports Nutrition",
        "*Journal of the International Society of Sports Nutrition*",
        "published in *Journal of the International Society of Sports Nutrition*",
    ]
    
    for ref in test_refs:
        # Simulate the validation function
        is_valid = len(ref) >= 10 and 'journal' in ref.lower()
        print(f"'{ref}' -> Valid: {is_valid}")

if __name__ == "__main__":
    asyncio.run(debug_reference_patterns())
