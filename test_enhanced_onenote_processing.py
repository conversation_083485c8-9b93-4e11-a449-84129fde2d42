#!/usr/bin/env python3
"""
Test script for enhanced OneNote processing with rich content extraction.

This script tests the new rich content extraction method that properly processes
the substantial content found in OneNote files.
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_enhanced_onenote_processing():
    """Test the enhanced OneNote processing with real file."""
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Testing enhanced processing on: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        
        # Initialize processor
        processor = OneNoteProcessor()
        print(f"✅ Initialized: {processor.processor_name}")
        
        # Test the enhanced processing
        print("\n🚀 Starting enhanced OneNote processing...")
        result = await processor.extract_text(str(onenote_file))
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            text = result.get('text', '')
            metadata = result.get('metadata', {})
            
            print(f"✅ Text extracted: {len(text):,} characters")
            print(f"✅ Word count: {metadata.get('word_count', 0):,}")
            print(f"✅ Processing method: {metadata.get('processing_method', 'unknown')}")
            print(f"✅ OCR provider: {result.get('ocr_provider', 'unknown')}")
            
            # Show metadata details
            print(f"\n📋 METADATA DETAILS:")
            print(f"Pages: {metadata.get('page_count', 0)}")
            print(f"Sections: {metadata.get('section_count', 0)}")
            print(f"File type: {metadata.get('file_type', 'unknown')}")
            
            # Show first part of extracted text
            print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
            print("=" * 60)
            print(text[:1000])
            if len(text) > 1000:
                print(f"\n... [Content continues for {len(text)-1000:,} more characters]")
            print("=" * 60)
            
            # Save full content for inspection
            output_file = Path("enhanced_onenote_extraction_result.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== ENHANCED ONENOTE EXTRACTION RESULT ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Processing method: {metadata.get('processing_method', 'unknown')}\n")
                f.write(f"OCR provider: {result.get('ocr_provider', 'unknown')}\n")
                f.write(f"Text length: {len(text):,} characters\n")
                f.write(f"Word count: {metadata.get('word_count', 0):,}\n\n")
                f.write("=== FULL EXTRACTED TEXT ===\n\n")
                f.write(text)
            
            print(f"💾 Saved full extraction result to: {output_file}")
            
            # Compare with previous results
            if len(text) > 1000:
                print(f"\n🎉 MASSIVE IMPROVEMENT!")
                print(f"Previous extraction: ~521 characters")
                print(f"Enhanced extraction: {len(text):,} characters")
                print(f"Improvement factor: {len(text)/521:.1f}x more content!")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ Processing failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Error during enhanced processing test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_processing_methods():
    """Test different processing methods to compare results."""
    
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found")
        return
    
    onenote_file = onenote_files[-1]
    print(f"\n🔬 COMPARING PROCESSING METHODS")
    print(f"File: {onenote_file.name}")
    print("=" * 60)
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        
        processor = OneNoteProcessor()
        
        # Test rich content extraction directly
        print("\n1️⃣ Testing Rich Content Extraction...")
        result1 = await processor._process_with_rich_content_extraction(Path(onenote_file))
        
        if result1.get('success'):
            text1 = result1.get('text', '')
            print(f"✅ Rich content: {len(text1):,} characters")
        else:
            print(f"❌ Rich content failed: {result1.get('error', 'Unknown')}")
        
        # Test one-extract fallback
        print("\n2️⃣ Testing One-Extract Fallback...")
        result2 = await processor._process_with_one_extract(Path(onenote_file))
        
        if result2.get('success'):
            text2 = result2.get('text', '')
            print(f"✅ One-extract: {len(text2):,} characters")
        else:
            print(f"❌ One-extract failed: {result2.get('error', 'Unknown')}")
        
        # Compare results
        if result1.get('success') and result2.get('success'):
            text1 = result1.get('text', '')
            text2 = result2.get('text', '')
            
            print(f"\n📊 COMPARISON:")
            print(f"Rich content extraction: {len(text1):,} characters")
            print(f"One-extract fallback: {len(text2):,} characters")
            print(f"Improvement: {len(text1)/len(text2):.1f}x more content")
            
            if len(text1) > len(text2):
                print("🎉 Rich content extraction is significantly better!")
            else:
                print("ℹ️ Methods produced similar results")
        
    except Exception as e:
        print(f"❌ Error comparing methods: {e}")


def main():
    """Run enhanced OneNote processing tests."""
    print("🚀 ENHANCED ONENOTE PROCESSING TESTS")
    print("=" * 60)
    
    async def run_tests():
        success1 = await test_enhanced_onenote_processing()
        await test_processing_methods()
        return success1
    
    success = asyncio.run(run_tests())
    
    print("\n" + "=" * 60)
    print("🎯 TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 Enhanced OneNote processing is working!")
        print("✅ Rich content extraction successful")
        print("✅ Substantial content extracted from OneNote file")
        print("✅ Major improvement over previous processing")
        print("\nThe OneNote processor now properly extracts:")
        print("- Page titles and section headers")
        print("- Embedded file content")
        print("- Image file information")
        print("- Reference URLs")
        print("- Comprehensive document structure")
    else:
        print("❌ Enhanced processing needs further work")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
