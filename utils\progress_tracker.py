"""
Enhanced progress tracking for document processing.
"""

import time
import asyncio
from datetime import datetime, timezone
from typing import Dict, Any, Optional, Callable

class ProgressTracker:
    """
    Enhanced progress tracking for document processing.

    This class provides improved progress tracking for document processing,
    including detailed step information, timestamps, and statistics.
    """

    def __init__(self, document_id: str = None, filename: str = None,
                 total_steps: int = None, document_name: str = None,
                 operation_id: str = None, websocket_manager=None):
        """
        Initialize the progress tracker.

        Args:
            document_id: The ID of the document being processed (legacy parameter)
            filename: The filename of the document (legacy parameter)
            total_steps: Total number of processing steps (new parameter)
            document_name: The name of the document (new parameter)
            operation_id: The operation ID (new parameter)
            websocket_manager: WebSocket manager for real-time updates
        """
        # Support both old and new parameter styles
        if operation_id is not None:
            # New style: operation_id, document_name, total_steps
            self.document_id = operation_id
            self.filename = document_name or "unknown"
            self.operation_id = operation_id
            self.total_steps = total_steps or 7
        elif document_id is not None:
            # Legacy style: document_id, filename
            self.document_id = document_id
            self.filename = filename or "unknown"
            self.operation_id = document_id
            self.total_steps = total_steps or 5
        else:
            raise ValueError("Either document_id or operation_id must be provided")

        self.start_time = datetime.now(timezone.utc)
        self.last_update_time = self.start_time
        self.current_step = 0
        self.step_name = "Starting document processing"
        self.progress_percentage = 0
        self.status = "processing"
        self.details = {
            "start_time": self.start_time.isoformat(),
            "facts_count": 0,
            "entities_count": 0,
            "references_count": 0,
            "embeddings_count": 0,
            "embeddings_in_redis_count": 0,
            "processing_time": 0
        }
        self.step_history = []

        # WebSocket integration
        self.websocket_manager = websocket_manager
        if self.websocket_manager:
            # Store operation metadata
            self.websocket_manager.store_operation_metadata(self.operation_id, {
                "filename": self.filename,
                "total_steps": self.total_steps,
                "start_time": self.start_time.isoformat()
            })

        # Persistent state management
        from utils.operation_state_manager import get_operation_state_manager
        self.state_manager = get_operation_state_manager()

        # Create persistent operation record
        self.state_manager.create_operation(
            operation_id=self.operation_id,
            filename=self.filename,
            total_steps=self.total_steps
        )

    def update_progress(self, step: int, step_name: str, progress_percentage: int,
                        status: str = "processing", details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Update the progress of document processing.

        Args:
            step: The current processing step (1-5)
            step_name: The name of the current step
            progress_percentage: The progress percentage (0-100)
            status: The processing status (processing, completed, failed)
            details: Additional details to include in the progress data

        Returns:
            The updated progress data
        """
        self.last_update_time = datetime.now(timezone.utc)
        self.current_step = step
        self.step_name = step_name
        self.progress_percentage = progress_percentage
        self.status = status

        # Calculate processing time
        processing_time = (self.last_update_time - self.start_time).total_seconds()
        self.details["processing_time"] = processing_time

        # Add step to history if it's new
        if not self.step_history or self.step_history[-1]["step"] != step:
            self.step_history.append({
                "step": step,
                "step_name": step_name,
                "progress_percentage": progress_percentage,
                "timestamp": self.last_update_time.isoformat()
            })

        # Update details if provided
        if details:
            self.details.update(details)

        # Update persistent state
        if hasattr(self, 'state_manager'):
            try:
                self.state_manager.update_operation(
                    operation_id=self.operation_id,
                    status=status,
                    progress_percentage=progress_percentage,
                    current_step=step,
                    current_step_name=step_name,
                    details=self.details,
                    statistics={
                        'start_time': self.start_time.isoformat(),
                        'elapsed_time': processing_time,
                        'estimated_remaining_time': self._calculate_estimated_remaining_time()
                    }
                )
            except Exception as e:
                import logging
                logging.getLogger(__name__).warning(f"Failed to update persistent state: {e}")

        # Send WebSocket update if manager is available
        if self.websocket_manager:
            progress_data = self.get_progress_data()
            # Use asyncio to send the update without blocking
            try:
                import asyncio
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # Schedule the coroutine to run
                    asyncio.create_task(
                        self.websocket_manager.send_progress_update(self.operation_id, progress_data)
                    )
                else:
                    # Run the coroutine directly
                    loop.run_until_complete(
                        self.websocket_manager.send_progress_update(self.operation_id, progress_data)
                    )
            except Exception as e:
                # Don't let WebSocket errors break the progress tracking
                import logging
                logging.getLogger(__name__).warning(f"Failed to send WebSocket update: {e}")

        return self.get_progress_data()

    def is_cancelled(self) -> bool:
        """
        Check if the operation has been cancelled.

        Returns:
            True if the operation has been cancelled, False otherwise
        """
        if self.websocket_manager:
            return self.websocket_manager.is_cancelled(self.operation_id)
        return False

    def check_cancellation(self) -> None:
        """
        Check for cancellation and raise an exception if cancelled.

        Raises:
            RuntimeError: If the operation has been cancelled
        """
        if self.is_cancelled():
            raise RuntimeError(f"Operation {self.operation_id} has been cancelled")

    def update_facts_count(self, count: int) -> None:
        """Update the facts count."""
        self.details["facts_count"] = count

    def update_entities_count(self, count: int) -> None:
        """Update the entities count."""
        self.details["entities_count"] = count

    def update_references_count(self, count: int) -> None:
        """Update the references count."""
        self.details["references_count"] = count

    def update_embeddings_count(self, count: int) -> None:
        """Update the embeddings count."""
        self.details["embeddings_count"] = count

    def update_embeddings_in_redis_count(self, count: int) -> None:
        """Update the embeddings in Redis count."""
        self.details["embeddings_in_redis_count"] = count

    def complete(self, details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Mark the document processing as complete.

        Args:
            details: Additional details to include in the progress data

        Returns:
            The updated progress data
        """
        self.last_update_time = datetime.now(timezone.utc)
        self.current_step = self.total_steps
        self.step_name = "Processing complete"
        self.progress_percentage = 100
        self.status = "completed"

        # Calculate processing time
        processing_time = (self.last_update_time - self.start_time).total_seconds()
        self.details["processing_time"] = processing_time
        self.details["completion_time"] = self.last_update_time.isoformat()

        # Add final step to history
        self.step_history.append({
            "step": self.current_step,
            "step_name": self.step_name,
            "progress_percentage": self.progress_percentage,
            "timestamp": self.last_update_time.isoformat()
        })

        # Update details if provided
        if details:
            self.details.update(details)

        # Save final results to persistent storage
        self._save_final_results()

        # Update persistent state
        if hasattr(self, 'state_manager'):
            try:
                self.state_manager.update_operation(
                    operation_id=self.operation_id,
                    status='completed',
                    progress_percentage=100,
                    current_step=self.total_steps,
                    current_step_name='Processing complete',
                    completion_time=self.last_update_time.isoformat(),
                    details=self.details,
                    statistics={
                        'start_time': self.start_time.isoformat(),
                        'completion_time': self.last_update_time.isoformat(),
                        'elapsed_time': processing_time,
                        'estimated_remaining_time': 0
                    }
                )
            except Exception as e:
                import logging
                logging.getLogger(__name__).warning(f"Failed to update persistent completion state: {e}")

        # Send WebSocket completion notification
        if self.websocket_manager:
            progress_data = self.get_progress_data()
            try:
                import asyncio
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(
                        self.websocket_manager.send_completion(self.operation_id, progress_data)
                    )
                else:
                    loop.run_until_complete(
                        self.websocket_manager.send_completion(self.operation_id, progress_data)
                    )
            except Exception as e:
                import logging
                logging.getLogger(__name__).warning(f"Failed to send WebSocket completion: {e}")

        return self.get_progress_data()

    def fail(self, error: str, details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Mark the document processing as failed.

        Args:
            error: The error message
            details: Additional details to include in the progress data

        Returns:
            The updated progress data
        """
        self.last_update_time = datetime.now(timezone.utc)
        self.step_name = "Processing failed"
        self.status = "failed"

        # Calculate processing time
        processing_time = (self.last_update_time - self.start_time).total_seconds()
        self.details["processing_time"] = processing_time
        self.details["error"] = error
        self.details["failure_time"] = self.last_update_time.isoformat()

        # Add failure to history
        self.step_history.append({
            "step": self.current_step,
            "step_name": self.step_name,
            "progress_percentage": self.progress_percentage,
            "timestamp": self.last_update_time.isoformat(),
            "error": error
        })

        # Update details if provided
        if details:
            self.details.update(details)

        # Update persistent state
        if hasattr(self, 'state_manager'):
            try:
                self.state_manager.update_operation(
                    operation_id=self.operation_id,
                    status='failed',
                    current_step_name='Processing failed',
                    error_message=error,
                    details=self.details,
                    statistics={
                        'start_time': self.start_time.isoformat(),
                        'failure_time': self.last_update_time.isoformat(),
                        'elapsed_time': processing_time,
                        'estimated_remaining_time': 0
                    }
                )
            except Exception as e:
                import logging
                logging.getLogger(__name__).warning(f"Failed to update persistent failure state: {e}")

        # Send WebSocket error notification
        if self.websocket_manager:
            try:
                import asyncio
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    asyncio.create_task(
                        self.websocket_manager.send_error(self.operation_id, error, details)
                    )
                else:
                    loop.run_until_complete(
                        self.websocket_manager.send_error(self.operation_id, error, details)
                    )
            except Exception as e:
                import logging
                logging.getLogger(__name__).warning(f"Failed to send WebSocket error: {e}")

        return self.get_progress_data()

    def _calculate_estimated_remaining_time(self) -> float:
        """Calculate estimated remaining time based on current progress."""
        if self.progress_percentage > 0 and self.status == "processing":
            elapsed_time = (datetime.now(timezone.utc) - self.start_time).total_seconds()
            estimated_total_time = elapsed_time * (100 / self.progress_percentage)
            return max(0, estimated_total_time - elapsed_time)
        return 0

    def get_progress_data(self) -> Dict[str, Any]:
        """
        Get the current progress data.

        Returns:
            The current progress data
        """
        # Calculate estimated remaining time
        if self.progress_percentage > 0 and self.status == "processing":
            elapsed_time = (datetime.now(timezone.utc) - self.start_time).total_seconds()
            estimated_total_time = elapsed_time * (100 / self.progress_percentage)
            estimated_remaining_time = max(0, estimated_total_time - elapsed_time)
        else:
            estimated_remaining_time = 0.0

        return {
            "document_id": self.document_id,
            "filename": self.filename,
            "document_name": self.filename,  # Alias for enhanced routes
            "total_steps": self.total_steps,
            "current_step": self.current_step,
            "step_name": self.step_name,
            "current_step_name": self.step_name,  # Alias for enhanced routes
            "progress_percentage": self.progress_percentage,
            "status": self.status,
            "error_message": self.details.get("error"),  # For enhanced routes
            "details": self.details,
            "step_history": self.step_history,
            "statistics": {
                "estimated_remaining_time": estimated_remaining_time,
                "elapsed_time": (datetime.now(timezone.utc) - self.start_time).total_seconds(),
                "start_time": self.start_time.isoformat()
            }
        }

    def _save_final_results(self) -> None:
        """Save final processing results to persistent storage."""
        try:
            # Import here to avoid circular imports
            from utils.processing_results_store import get_results_store

            # Only save if we have an operation_id and processing is complete
            if hasattr(self, 'operation_id') and self.operation_id and self.status == "completed":
                results_store = get_results_store()

                # Prepare final results data
                final_results = {
                    "operation_id": self.operation_id,
                    "document_name": self.filename,
                    "status": self.status,
                    "progress_percentage": self.progress_percentage,
                    "processing_time": self.details.get("processing_time", 0),
                    "facts_count": self.details.get("facts_count", 0),
                    "entities_count": self.details.get("entities_count", 0),
                    "references_count": self.details.get("references_count", 0),
                    "embeddings_count": self.details.get("embeddings_count", 0),
                    "embeddings_in_redis_count": self.details.get("embeddings_in_redis_count", 0),
                    "start_time": self.start_time.isoformat(),
                    "completion_time": self.details.get("completion_time"),
                    "step_history": self.step_history,
                    "all_details": self.details
                }

                # Save to persistent storage
                results_store.save_results(self.operation_id, final_results)

        except Exception as e:
            # Don't fail the processing if saving results fails
            import logging
            logging.getLogger(__name__).warning(f"Failed to save final results: {e}")
