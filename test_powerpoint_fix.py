#!/usr/bin/env python3
"""
Test script to verify PowerPoint processing fixes.
"""

import asyncio
import tempfile
from pathlib import Path

async def test_powerpoint_fix():
    """Test PowerPoint processing fixes"""
    print("🔍 Testing PowerPoint Processing Fixes")
    print("=" * 40)
    
    # Test 1: Check if MistralOCRProcessor has the new method
    print("1. Testing MistralOCRProcessor method availability...")
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        processor = MistralOCRProcessor()
        
        # Check if the new method exists
        if hasattr(processor, 'extract_text_from_document'):
            print("✅ extract_text_from_document method found")
        else:
            print("❌ extract_text_from_document method missing")
            
        if hasattr(processor, 'encode_document'):
            print("✅ encode_document method found")
        else:
            print("❌ encode_document method missing")
            
    except Exception as e:
        print(f"❌ MistralOCRProcessor error: {e}")
    
    # Test 2: Check PowerPoint processor initialization
    print("\n2. Testing PowerPoint processor initialization...")
    try:
        from processors.powerpoint_processor import PowerPointProcessor
        
        ppt_processor = PowerPointProcessor()
        print("✅ PowerPoint processor initialized successfully")
        print(f"   Supported extensions: {ppt_processor.supported_extensions}")
        print(f"   PPTX available: {ppt_processor.pptx_available}")
        print(f"   Mistral OCR available: {ppt_processor.mistral_ocr is not None}")
        
    except Exception as e:
        print(f"❌ PowerPoint processor error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Test with a simple text file (to simulate document processing)
    print("\n3. Testing document processing pipeline...")
    try:
        # Create a simple test file
        test_content = "Test PowerPoint Content\nSlide 1: Introduction\nSlide 2: Main Points"
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(test_content)
            temp_file_path = Path(f.name)
        
        try:
            # Test the enhanced document processor
            from processors.enhanced_document_processor import EnhancedDocumentProcessor
            
            enhanced_processor = EnhancedDocumentProcessor()
            
            # Test file category detection
            from utils.file_utils import get_file_category, is_supported_file
            
            # Test with .pptx extension
            test_pptx_name = "test.pptx"
            category = get_file_category(test_pptx_name)
            supported = is_supported_file(test_pptx_name)
            
            print(f"✅ File category for .pptx: {category}")
            print(f"✅ .pptx file supported: {supported}")
            
        finally:
            # Clean up
            if temp_file_path.exists():
                temp_file_path.unlink()
                
    except Exception as e:
        print(f"❌ Document processing test error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 4: Check if the upload system recognizes PowerPoint files
    print("\n4. Testing upload system PowerPoint support...")
    try:
        import requests
        
        # Test supported types endpoint
        response = requests.get("http://127.0.0.1:9753/api/enhanced/supported-types", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            supported_extensions = data.get('supported_extensions', [])
            
            ppt_supported = '.ppt' in supported_extensions
            pptx_supported = '.pptx' in supported_extensions
            
            print(f"✅ .ppt supported: {ppt_supported}")
            print(f"✅ .pptx supported: {pptx_supported}")
            
            if ppt_supported and pptx_supported:
                print("✅ PowerPoint files are supported by upload system")
            else:
                print("❌ PowerPoint files not fully supported")
        else:
            print(f"❌ Supported types API error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Upload system test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_powerpoint_fix())
