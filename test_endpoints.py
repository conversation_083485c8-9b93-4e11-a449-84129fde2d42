#!/usr/bin/env python3
"""
Test script to check if API endpoints are working correctly.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent))

from database.database_service import get_falkordb_adapter
from routes.entity_routes import get_graph_statistics
from routes.fast_api_routes import get_fast_graph_stats, get_fast_entities, get_fast_documents
from routes.system_routes import get_system_status


async def test_database_connection():
    """Test if we can connect to the database."""
    print("Testing database connection...")
    try:
        adapter = await get_falkordb_adapter()
        print("✓ FalkorDB connection successful")
        
        # Test a simple query
        result = adapter.execute_cypher("MATCH (n) RETURN count(n) as total_nodes LIMIT 1")
        if result and len(result) > 1:
            node_count = result[1][0][0] if len(result[1]) > 0 else 0
            print(f"✓ Database query successful - Total nodes: {node_count}")
        else:
            print("⚠ Database query returned no results")
            
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False
    return True


async def test_graph_stats():
    """Test the graph statistics endpoint."""
    print("\nTesting graph statistics endpoint...")
    try:
        stats = await get_graph_statistics()
        print(f"✓ Graph stats endpoint working")
        print(f"  - Episodes: {stats.get('total_episodes', 0)}")
        print(f"  - Entities: {stats.get('total_entities', 0)}")
        print(f"  - Relationships: {stats.get('total_relationships', 0)}")
        return True
    except Exception as e:
        print(f"✗ Graph stats endpoint failed: {e}")
        return False


async def test_fast_endpoints():
    """Test the fast API endpoints."""
    print("\nTesting fast API endpoints...")
    
    # Test fast graph stats
    try:
        stats = await get_fast_graph_stats()
        print(f"✓ Fast graph stats working")
        print(f"  - Episodes: {stats.get('total_episodes', 0)}")
        print(f"  - Entities: {stats.get('total_entities', 0)}")
    except Exception as e:
        print(f"✗ Fast graph stats failed: {e}")
    
    # Test fast entities
    try:
        entities_data = await get_fast_entities(limit=5)
        print(f"✓ Fast entities working")
        print(f"  - Count: {entities_data.get('count', 0)}")
        print(f"  - Entities: {len(entities_data.get('entities', []))}")
    except Exception as e:
        print(f"✗ Fast entities failed: {e}")
    
    # Test fast documents
    try:
        docs_data = await get_fast_documents(limit=5)
        print(f"✓ Fast documents working")
        print(f"  - Count: {docs_data.get('count', 0)}")
        print(f"  - Documents: {len(docs_data.get('documents', []))}")
    except Exception as e:
        print(f"✗ Fast documents failed: {e}")


async def test_system_status():
    """Test the system status endpoint."""
    print("\nTesting system status endpoint...")
    try:
        status = await get_system_status()
        print(f"✓ System status endpoint working")
        print(f"  - FalkorDB: {'Connected' if status.get('falkordb_connected') else 'Disconnected'}")
        print(f"  - Redis: {'Connected' if status.get('redis_connected') else 'Disconnected'}")
        print(f"  - Documents: {status.get('documents_processed', 0)}")
        print(f"  - Entities: {status.get('entities_extracted', 0)}")
        return True
    except Exception as e:
        print(f"✗ System status endpoint failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🔍 Testing API Endpoints")
    print("=" * 50)
    
    # Test database connection first
    db_ok = await test_database_connection()
    
    if db_ok:
        await test_graph_stats()
        await test_fast_endpoints()
        await test_system_status()
    else:
        print("\n❌ Database connection failed - skipping endpoint tests")
        print("\nPossible issues:")
        print("1. FalkorDB is not running")
        print("2. Database configuration is incorrect")
        print("3. Database is empty (no data)")
    
    print("\n" + "=" * 50)
    print("✅ Endpoint testing complete")


if __name__ == "__main__":
    asyncio.run(main())
