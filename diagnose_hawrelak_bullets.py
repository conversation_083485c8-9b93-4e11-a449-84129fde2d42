#!/usr/bin/env python3
"""
Diagnose bullet point issues in Hawrelak document.
"""

import asyncio
import os
import sys
import re
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def diagnose_bullet_points():
    """Diagnose bullet point detection in Hawrelak document."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")
    hawrelak_files = list(uploads_dir.glob("*Hawrelak*"))
    if not hawrelak_files:
        print("❌ Hawrelak document not found")
        return
    
    test_file = hawrelak_files[0]
    print(f"🔍 Diagnosing bullet points in: {test_file.name}")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Extract text with Mistral OCR
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        text = await mistral_ocr.extract_text_from_pdf(str(test_file))
        
        if not text:
            print("❌ No text extracted!")
            return
        
        print(f"📝 Extracted {len(text):,} characters")
        
        # Save text for examination
        with open("hawrelak_text_sample.txt", "w", encoding="utf-8") as f:
            f.write(text)
        print("💾 Saved text to hawrelak_text_sample.txt")
        
        # Look for different bullet point characters
        bullet_chars = ['•', '●', '○', '▪', '▫', '■', '□', '◦', '‣', '-', '*']
        
        print("\n🔍 Searching for bullet point characters:")
        for char in bullet_chars:
            count = text.count(char)
            if count > 0:
                print(f"   '{char}': {count} occurrences")
                
                # Show first few occurrences
                positions = []
                start = 0
                for _ in range(min(5, count)):
                    pos = text.find(char, start)
                    if pos != -1:
                        positions.append(pos)
                        start = pos + 1
                
                for i, pos in enumerate(positions):
                    context_start = max(0, pos - 50)
                    context_end = min(len(text), pos + 100)
                    context = text[context_start:context_end].replace('\n', '\\n')
                    print(f"      {i+1}. Position {pos}: ...{context}...")
        
        # Look for reference-like patterns
        print("\n📚 Looking for reference patterns:")
        
        # Pattern 1: Author, Initial format
        author_pattern = r'[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.'
        author_matches = re.findall(author_pattern, text)
        print(f"   Author patterns (Name, I. I.): {len(author_matches)}")
        for match in author_matches[:5]:
            print(f"      {match}")
        
        # Pattern 2: Year in parentheses
        year_pattern = r'\(\d{4}\)'
        year_matches = re.findall(year_pattern, text)
        print(f"   Year patterns (YYYY): {len(year_matches)}")
        unique_years = sorted(set(year_matches))[:10]
        print(f"      Sample years: {', '.join(unique_years)}")
        
        # Pattern 3: DOI patterns
        doi_pattern = r'doi:\s*10\.\d+/[^\s]+'
        doi_matches = re.findall(doi_pattern, text, re.IGNORECASE)
        print(f"   DOI patterns: {len(doi_matches)}")
        for match in doi_matches[:3]:
            print(f"      {match}")
        
        # Look for lines that might be references
        print("\n📋 Analyzing lines for reference characteristics:")
        lines = text.split('\n')
        potential_ref_lines = []
        
        for i, line in enumerate(lines):
            line = line.strip()
            if len(line) < 30:  # Skip short lines
                continue
            
            # Check for reference indicators
            has_author = bool(re.search(r'[A-Z][a-z]+\s*,\s*[A-Z]\.', line))
            has_year = bool(re.search(r'\(\d{4}\)', line))
            has_doi = bool(re.search(r'doi:', line, re.IGNORECASE))
            has_journal = bool(re.search(r'[Jj]ournal|[Rr]ev|[Cc]lin|[Mm]ed', line))
            
            score = sum([has_author, has_year, has_doi, has_journal])
            
            if score >= 2:
                potential_ref_lines.append((i+1, line, score))
        
        print(f"   Found {len(potential_ref_lines)} potential reference lines")
        
        # Show first 10 potential reference lines
        print("\n📋 First 10 potential reference lines:")
        for i, (line_num, line, score) in enumerate(potential_ref_lines[:10]):
            print(f"{i+1:2d}. Line {line_num:4d} (Score: {score}): {line[:100]}{'...' if len(line) > 100 else ''}")
        
        # Look specifically for the examples you provided
        print("\n🎯 Looking for your specific examples:")
        examples = [
            "Charalambous",
            "Chen, H. L.",
            "Cryan, J. F.",
            "Dao, M. C.",
            "Davis LMG"
        ]
        
        for example in examples:
            if example in text:
                pos = text.find(example)
                context_start = max(0, pos - 100)
                context_end = min(len(text), pos + 300)
                context = text[context_start:context_end]
                print(f"   ✅ Found '{example}':")
                print(f"      {context.replace(chr(10), ' ')[:200]}...")
            else:
                print(f"   ❌ Not found: '{example}'")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total text length: {len(text):,} characters")
        print(f"   Total lines: {len(lines):,}")
        print(f"   Potential reference lines: {len(potential_ref_lines)}")
        print(f"   Author patterns found: {len(author_matches)}")
        print(f"   Year patterns found: {len(year_matches)}")
        print(f"   DOI patterns found: {len(doi_matches)}")
        
    except Exception as e:
        print(f"❌ Error during diagnosis: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Hawrelak Bullet Point Diagnostic")
    print("="*40)
    asyncio.run(diagnose_bullet_points())
