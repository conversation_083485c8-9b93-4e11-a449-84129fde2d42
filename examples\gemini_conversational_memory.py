"""
Example demonstrating how to use Graphiti with Google Gemini
for conversational memory and chat history.
"""

import asyncio
import logging
import os
import json
from datetime import datetime, timezone
from typing import List, Dict, Any

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiC<PERSON>, LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.nodes import EpisodeType, Fact
from graphiti_core.utils.maintenance.graph_data_operations import clear_data


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )


class ConversationManager:
    """Manages conversations using Graphiti for memory."""
    
    def __init__(self, graphiti: Graphiti):
        self.graphiti = graphiti
        self.conversation_episodes = {}  # Maps conversation_id to episode_id
    
    async def start_conversation(self, conversation_id: str, user_name: str):
        """Start a new conversation."""
        print(f"\n=== Starting Conversation: {conversation_id} ===")
        
        # Create an episode for this conversation
        episode_id = await self.graphiti.add_episode(
            name=f"Conversation with {user_name}",
            episode_body=f"Conversation started with {user_name} at {datetime.now(timezone.utc).isoformat()}",
            source=EpisodeType.text,
            reference_time=datetime.now(timezone.utc),
            source_description="Chat Conversation",
        )
        
        self.conversation_episodes[conversation_id] = episode_id
        return episode_id
    
    async def add_message(self, conversation_id: str, sender: str, message: str):
        """Add a message to the conversation."""
        if conversation_id not in self.conversation_episodes:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        episode_id = self.conversation_episodes[conversation_id]
        
        # Add the message as a fact
        fact_id = await self.graphiti.add_fact(
            fact_body=message,
            episode_id=episode_id,
            metadata={"sender": sender, "timestamp": datetime.now(timezone.utc).isoformat()}
        )
        
        return fact_id
    
    async def get_conversation_history(self, conversation_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get the conversation history."""
        if conversation_id not in self.conversation_episodes:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        episode_id = self.conversation_episodes[conversation_id]
        
        # Get all facts for this episode
        facts = await self.graphiti.get_facts_by_episode_id(episode_id)
        
        # Convert to a list of messages
        messages = []
        for fact in facts:
            metadata = fact.metadata or {}
            messages.append({
                "sender": metadata.get("sender", "Unknown"),
                "message": fact.fact_body,
                "timestamp": metadata.get("timestamp", "")
            })
        
        # Sort by timestamp
        messages.sort(key=lambda x: x["timestamp"])
        
        # Return the most recent messages
        return messages[-limit:] if limit > 0 else messages
    
    async def generate_response(self, conversation_id: str, user_message: str) -> str:
        """Generate a response to a user message using conversation history."""
        # Get conversation history
        history = await self.get_conversation_history(conversation_id)
        
        # Create a prompt with the conversation history
        prompt = "You are a helpful AI assistant. Please respond to the user's latest message based on the conversation history.\n\n"
        prompt += "Conversation history:\n"
        
        for msg in history:
            prompt += f"{msg['sender']}: {msg['message']}\n"
        
        prompt += f"\nUser: {user_message}\n"
        prompt += "Assistant: "
        
        # Generate a response
        response = await self.graphiti.llm_client.generate_response(prompt)
        
        return response


async def simulate_conversation(conversation_manager: ConversationManager, conversation_id: str, user_name: str):
    """Simulate a conversation with the AI."""
    # Start a new conversation
    await conversation_manager.start_conversation(conversation_id, user_name)
    
    # Define a sequence of user messages
    user_messages = [
        "Hi there! My name is Alice. Can you help me learn about knowledge graphs?",
        "That sounds interesting. How are knowledge graphs different from regular databases?",
        "Can you give me an example of how knowledge graphs are used in the real world?",
        "How would I get started with creating my own knowledge graph?",
        "Thank you for all your help!"
    ]
    
    # Simulate the conversation
    for message in user_messages:
        print(f"\nUser: {message}")
        
        # Add user message to conversation
        await conversation_manager.add_message(conversation_id, "User", message)
        
        # Generate and add assistant response
        assistant_response = await conversation_manager.generate_response(conversation_id, message)
        print(f"Assistant: {assistant_response}")
        
        await conversation_manager.add_message(conversation_id, "Assistant", assistant_response)
    
    # Retrieve and display the full conversation history
    print("\n=== Full Conversation History ===")
    history = await conversation_manager.get_conversation_history(conversation_id, limit=0)
    
    for msg in history:
        print(f"{msg['sender']}: {msg['message']}")


async def main():
    """Main function to run the example."""
    # Load environment variables
    load_dotenv()
    setup_logging()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        print("No Google API key found in environment variables. Please add it to your .env file.")
        return
        
    print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")
    
    try:
        print("Initializing Graphiti with Gemini clients...")
        # Initialize Graphiti with Gemini clients
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=GeminiClient(
                config=LLMConfig(
                    api_key=google_api_key,
                    model="models/gemini-1.5-pro"  # Using Pro model for better conversation
                )
            ),
            embedder=GeminiEmbedder(
                config=GeminiEmbedderConfig(
                    api_key=google_api_key,
                    embedding_model="models/embedding-001"
                )
            )
        )
        
        print("Connecting to Neo4j database...")
        
        # Clear existing data (optional - remove this in production)
        print("Clearing existing data...")
        await clear_data(graphiti.driver)
        
        # Set up indices and constraints
        print("Setting up indices and constraints...")
        await graphiti.build_indices_and_constraints()
        
        # Create a conversation manager
        conversation_manager = ConversationManager(graphiti)
        
        # Simulate a conversation
        await simulate_conversation(conversation_manager, "conv-001", "Alice")
        
        print("\nConversational memory example completed successfully!")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close the driver
        print("Closing Neo4j connection...")
        await graphiti.driver.close()


if __name__ == "__main__":
    asyncio.run(main())
