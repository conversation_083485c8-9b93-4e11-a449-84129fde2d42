"""
Script to update existing entities with UUIDs
"""

import os
import asyncio
import logging
import uuid
from datetime import datetime, timezone

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def update_entities_with_uuids(driver):
    """Update existing entities with UUIDs."""
    logger.info("Updating existing entities with UUIDs")
    
    try:
        async with driver.session() as session:
            # Find entities without UUIDs
            result = await session.run(
                """
                MATCH (e:Entity)
                WHERE e.uuid IS NULL
                RETURN e.name AS name, e.type AS type
                """
            )
            
            entities = []
            async for record in result:
                entities.append({
                    "name": record["name"],
                    "type": record["type"]
                })
            
            logger.info(f"Found {len(entities)} entities without UUIDs")
            
            # Update each entity with a UUID
            for entity in entities:
                entity_uuid = str(uuid.uuid4())
                
                result = await session.run(
                    """
                    MATCH (e:Entity {name: $name, type: $type})
                    SET e.uuid = $uuid,
                        e.updated_at = datetime($timestamp)
                    RETURN e.name AS name, e.type AS type, e.uuid AS uuid
                    """,
                    {
                        "name": entity["name"],
                        "type": entity["type"],
                        "uuid": entity_uuid,
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
                
                record = await result.single()
                logger.info(f"Updated entity {record['name']} ({record['type']}) with UUID {record['uuid']}")
            
            logger.info(f"Updated {len(entities)} entities with UUIDs")
            return len(entities)
    except Exception as e:
        logger.error(f"Error updating entities with UUIDs: {e}")
        return 0

async def main():
    """Main function to update entities with UUIDs."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        # Update entities with UUIDs
        updated_count = await update_entities_with_uuids(driver)
        logger.info(f"Updated {updated_count} entities with UUIDs")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
