#!/usr/bin/env python3
"""
Test script to check entity type counts from entities API.
"""

import requests

def test_entities_type_counts():
    """Test the entities API for type counts"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entities API Type Counts")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/entities?limit=5", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            
            if 'type_counts' in data:
                type_counts = data['type_counts']
                print(f"Type counts available: {len(type_counts)} types")
                print(f"Sample type counts:")
                
                # Show top 10 types by count
                sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)
                for i, (type_name, count) in enumerate(sorted_types[:10]):
                    print(f"  {i+1}. {type_name}: {count}")
                
                print(f"\nTotal types: {len(type_counts)}")
            else:
                print("No type_counts in response")
                print(f"Available keys: {list(data.keys())}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entities_type_counts()
