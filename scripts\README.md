# 🛠️ Graphiti Utility Scripts

This directory contains utility scripts for Graphiti maintenance and operations.

## 📋 **Available Scripts**

### **Database Management**
- `setup_database.py` - Initialize FalkorDB with proper schema
- `backup_database.py` - Create database backups
- `restore_database.py` - Restore from database backups

### **Data Processing**
- `bulk_import.py` - Import large datasets
- `data_migration.py` - Migrate data between formats
- `cleanup_duplicates.py` - Remove duplicate entities and references

### **Maintenance**
- `health_check.py` - Comprehensive system health check
- `performance_monitor.py` - Monitor system performance
- `log_analyzer.py` - Analyze application logs

### **Development**
- `test_runner.py` - Run comprehensive tests
- `dev_setup.py` - Set up development environment
- `code_quality.py` - Check code quality and standards

## 🚀 **Usage**

All scripts should be run from the project root directory:

```bash
# Example usage
python scripts/health_check.py
python scripts/setup_database.py --reset
python scripts/bulk_import.py --file data.json
```

## 📝 **Notes**

- Scripts use the same configuration as the main application (`.env` file)
- All scripts include proper error handling and logging
- Use `--help` flag with any script for detailed usage information
- Scripts are designed to be safe and include confirmation prompts for destructive operations
