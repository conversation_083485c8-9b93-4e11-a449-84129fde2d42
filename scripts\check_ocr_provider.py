"""
<PERSON><PERSON><PERSON> to check the OCR provider for each episode.
"""

import os
from neo4j import GraphDatabase
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Connect to Neo4j
driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))

def check_ocr_provider():
    """Check the OCR provider for each episode."""
    with driver.session() as session:
        # Get all episodes
        result = session.run(
            """
            MATCH (e:Episode)
            RETURN e.name AS name, e.ocr_provider AS ocr_provider, e.total_chunks AS total_chunks
            """
        )
        
        print("Episodes and OCR providers:")
        for record in result:
            name = record["name"]
            ocr_provider = record["ocr_provider"] if record["ocr_provider"] else "unknown"
            total_chunks = record["total_chunks"] if record["total_chunks"] else 0
            print(f"  {name}: {ocr_provider} ({total_chunks} chunks)")

if __name__ == "__main__":
    try:
        check_ocr_provider()
    finally:
        driver.close()
