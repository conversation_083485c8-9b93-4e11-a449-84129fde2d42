# Using FalkorDB with Graphiti

This guide provides instructions for using FalkorDB as the database for your Graphiti project, replacing Neo4j while maintaining all functionality including natural language Q&A.

## Current Status (Updated May 2, 2025)

We've successfully implemented FalkorDB as an alternative database for the Graphiti knowledge graph project. Here's what we've accomplished:

1. **FalkorDB Setup**:
   - Created Docker container for FalkorDB
   - Implemented connection adapter in `graphiti_falkordb_adapter.py`
   - Verified connection and basic functionality

2. **PDF Processing**:
   - Successfully processed a PDF document (ThymoDream whitepaper)
   - Extracted text and chunked it into facts
   - Stored facts in FalkorDB
   - Extracted entities from the facts
   - Created relationships between facts and entities

3. **Web Interface**:
   - Created a web interface for FalkorDB at port 8023
   - Implemented API endpoints for entities and entity details
   - Added test entities to demonstrate functionality

## Next Steps

Here's what you can do to continue working with FalkorDB:

1. **Start the Docker containers**:
   ```
   docker start falkordb graphiti-neo4j-1
   ```

2. **Start the web interface**:
   ```
   python graphiti_web_interface.py
   ```

3. **Process more PDFs**:
   ```
   python graphiti_pdf_processor.py "path/to/your/pdf"
   ```

4. **Improve relationship extraction**:
   - Enhance the relationship extraction between entities
   - Add more relationship types
   - Improve the visualization of relationships in the UI

## Issues Addressed

1. **Entity Display**: Fixed the UI to correctly display entities from the API
2. **Entity Details**: Entity details page is now working correctly
3. **Entity Relationships**: Need to improve relationship extraction and display (still in progress)

## What is FalkorDB?

FalkorDB is a high-performance graph database that supports the OpenCypher query language and is compatible with Neo4j clients. It offers several advantages:

- Better performance for AI/ML workloads
- Built-in vector search capabilities
- Multi-tenant support
- GraphRAG optimizations

## Setup Instructions

### 1. Start FalkorDB

```powershell
# Run the PowerShell script to start FalkorDB
.\start_falkordb.ps1
```

This will:
- Start a FalkorDB container using Docker
- Configure it to use port 6379 for Redis protocol
- Configure it to use port 7688 for BOLT protocol (mapped from 7687 internally)
- Test the connection to ensure it's working properly

### 2. Start the Graphiti Web Interface for FalkorDB

```bash
# Start the web interface with FalkorDB
python graphiti_web_interface.py
```

## How It Works

The implementation uses a combination of approaches to integrate FalkorDB with Graphiti:

1. **FalkorDB Adapter**: A Python adapter that translates Cypher queries to FalkorDB commands using the Redis protocol.

2. **BOLT Protocol Support**: FalkorDB supports the BOLT protocol, which allows it to be used with Neo4j drivers. However, this support is experimental, so we primarily use the Redis protocol.

3. **Redis Protocol**: FalkorDB is built on Redis and supports the Redis protocol for executing Cypher queries using the `GRAPH.QUERY` command.

## Files Created/Modified

- `graphiti_falkordb_adapter.py`: FalkorDB adapter for Graphiti
- `graphiti_pdf_processor.py`: PDF processing pipeline for FalkorDB
- `graphiti_entity_extraction.py`: Entity extraction for FalkorDB
- `graphiti_web_interface.py`: Web interface for FalkorDB
- `check_falkordb_entities.py`: Script to check entities in FalkorDB
- `check_falkordb_documents.py`: Script to check documents in FalkorDB
- `test_falkordb_entities_direct.py`: Script to test entity retrieval from FalkorDB
- `test_entities_api.py`: Script to test the entities API endpoint

## Testing

You can test the FalkorDB connection using the provided test scripts:

```bash
# Test the Redis connection
python test_falkordb_redis.py

# Test the FalkorDB adapter
python test_falkordb_entities_direct.py

# Test the entities API
python test_entities_api.py
```

## Troubleshooting

### Connection Issues

If you encounter connection issues:

1. Ensure the FalkorDB container is running:
   ```bash
   docker ps
   ```

2. Check FalkorDB logs:
   ```bash
   docker logs falkordb
   ```

3. Verify Redis port is accessible:
   ```bash
   telnet localhost 6379
   ```

### Query Issues

If you encounter issues with Cypher queries:

1. Test the query using the FalkorDB adapter:
   ```python
   from graphiti_falkordb_adapter import GraphitiFalkorDBAdapter
   adapter = GraphitiFalkorDBAdapter(graph_name="graphiti")
   result = adapter.execute_cypher("YOUR_QUERY_HERE")
   print(result)
   ```

2. Check the FalkorDB documentation for any syntax differences between OpenCypher and Neo4j's Cypher.

## Additional Resources

- [FalkorDB Documentation](https://docs.falkordb.com/)
- [FalkorDB GitHub Repository](https://github.com/FalkorDB/FalkorDB)
- [OpenCypher Documentation](https://opencypher.org/)
