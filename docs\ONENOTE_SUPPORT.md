# Microsoft OneNote (.one) File Support

## Overview

The Graphiti document ingestion system now supports Microsoft OneNote (.one) files with **Mistral AI OCR as the primary processing method**. OneNote files are converted to PDF format and processed by Mistral OCR for superior table, image, and complex content interpretation, allowing users to upload and process OneNote notebooks through the same unified pipeline used for other document types.

## Features

### ✅ **Complete Integration**
- **File Type Detection**: Automatic recognition of `.one` files
- **Upload Validation**: OneNote files accepted through all upload routes
- **UI Support**: Drag & drop and file selection support in the web interface
- **Processing Pipeline**: Full integration with the enhanced document processor

### ✅ **OneNote-Specific Capabilities**
- **Primary: Mistral AI OCR Processing**: Convert OneNote to PDF and process with Mistral OCR for superior content interpretation
- **Advanced Content Extraction**: Tables, images, complex layouts, and visual content processed by Mistral OCR
- **PDF Conversion**: Professional PDF generation from OneNote content using reportlab library
- **Text Extraction**: Extract text content from OneNote pages and sections
- **Metadata Extraction**: Extract page titles, creation dates, modification dates
- **Embedded File Support**: Process embedded files and objects within OneNote documents
- **Password Protection**: Support for password-protected OneNote files
- **Section Structure**: Preserve page and section organization from OneNote
- **Multi-Method Pipeline**: PDF conversion → HTML conversion → one-extract fallback

### ✅ **Processing Features**
- **Entity Extraction**: Extract entities from OneNote content using the same NLP pipeline
- **Reference Extraction**: Identify and extract references from OneNote pages
- **Knowledge Graph Integration**: Store OneNote content in the knowledge graph
- **Vector Embeddings**: Generate embeddings for semantic search
- **Chunking**: Split OneNote content into manageable chunks for processing

## Technical Implementation

### Dependencies
- **Mistral AI OCR**: Primary processing engine for superior content interpretation
- **reportlab**: Professional PDF generation from OneNote content
- **one-extract**: Python library for extracting objects from OneNote files (fallback)
- **msoffcrypto-tool**: For handling encrypted OneNote files
- **cryptography**: Cryptographic operations for password-protected files

### File Processing Workflow

1. **File Upload & Validation**
   ```
   User uploads .one file → File type validation → MIME type detection
   ```

2. **OneNote Processing (Multi-Method Pipeline)**
   ```
   Method 1: OneNote → PDF Conversion → Mistral AI OCR → Superior content extraction
   Method 2: OneNote → HTML Conversion → Mistral AI OCR → Content extraction
   Method 3: OneNote → one-extract library → Basic text extraction (fallback)
   ```

3. **Content Processing**
   ```
   Extracted content → Text chunking → Entity extraction → Reference extraction
   ```

4. **Storage & Indexing**
   ```
   Knowledge graph storage → Vector embeddings → Search indexing
   ```

### Supported File Extensions
- `.one` - Microsoft OneNote files
- Case-insensitive matching (`.ONE`, `.One`, etc.)

### MIME Type
- `application/onenote`

## Usage

### Web Interface Upload
1. Navigate to the Upload page
2. Drag & drop OneNote files or click "Select Files"
3. OneNote files are automatically recognized and accepted
4. Processing follows the same workflow as other document types

### API Upload
```bash
curl -X POST "http://localhost:9753/api/enhanced/upload" \
  -F "file=@notebook.one" \
  -F "chunk_size=1200" \
  -F "extract_entities=true" \
  -F "extract_references=true"
```

### Password-Protected Files
For password-protected OneNote files, the system will detect the encryption and prompt for a password. Currently, password handling is implemented in the processor but may require additional UI integration for seamless user experience.

## Processing Results

### Extracted Content
- **Text Content**: All readable text from OneNote pages
- **Page Structure**: Hierarchical organization of sections and pages
- **Embedded Objects**: Text content from embedded files where possible
- **Metadata**: Creation dates, modification dates, page titles

### Generated Metadata
```json
{
  "title": "notebook_name",
  "filename": "notebook.one",
  "file_type": "OneNote",
  "page_count": 5,
  "section_count": 3,
  "pages": [
    {
      "object_id": 123,
      "title": "Meeting Notes",
      "creation_date": "2024-01-15T10:30:00Z",
      "last_modification_date": "2024-01-16T14:20:00Z"
    }
  ],
  "processor": "OneNote Processor",
  "password_protected": false
}
```

## Limitations

### Current Limitations
1. **Text-Only Extraction**: Currently focuses on text content; images and drawings are not processed
2. **Complex Formatting**: Advanced OneNote formatting may not be fully preserved
3. **Embedded Media**: Images, audio, and video embedded in OneNote are not extracted
4. **Handwriting Recognition**: Handwritten notes are not converted to text

### Future Enhancements
- **OCR Integration**: Process images and handwritten content using Mistral OCR
- **Rich Media Support**: Extract and process embedded images and media
- **Advanced Formatting**: Better preservation of OneNote's rich formatting
- **Collaborative Features**: Support for shared notebooks and version history

## Error Handling

### Common Issues
1. **Invalid OneNote File**: Files that appear to be .one but are corrupted or invalid
2. **Password Required**: Encrypted files without provided password
3. **Library Dependencies**: Missing one-extract library installation

### Error Messages
- `"Invalid OneNote file encountered"` - File is not a valid OneNote format
- `"Password required for encrypted OneNote file"` - File is password-protected
- `"one-extract library not installed"` - Required dependency missing

## Installation Requirements

### Required Dependencies
```bash
pip install one-extract==0.1.3
```

This automatically installs:
- `msoffcrypto-tool~=5.0`
- `olefile>=0.46`
- `cryptography>=39.0`

### Verification
Run the OneNote support test to verify installation:
```bash
python test_onenote_support.py
```

## Integration with Existing Features

### Knowledge Graph
OneNote content is stored in the knowledge graph with the same structure as other documents:
- **Facts**: Text chunks from OneNote pages
- **Entities**: Extracted entities from OneNote content
- **Relationships**: Connections between entities and concepts
- **Documents**: OneNote files tracked as document nodes

### Search & Q&A
- **Semantic Search**: OneNote content is searchable through vector embeddings
- **Question Answering**: Q&A system can reference OneNote content
- **Citation**: Proper citations back to specific OneNote pages and sections

### Reference Extraction
The reference extraction system works with OneNote content to identify:
- **Academic References**: Citations within OneNote pages
- **Web Links**: URLs and web references
- **Document References**: References to other documents

## Best Practices

### File Organization
- **Descriptive Names**: Use clear, descriptive names for OneNote files
- **Section Structure**: Organize content in clear sections and pages
- **Regular Updates**: Keep OneNote files updated for better processing

### Content Preparation
- **Text-Heavy Content**: OneNote files with more text content process better
- **Clear Structure**: Well-organized sections and pages improve entity extraction
- **Minimal Formatting**: Simple formatting translates better during processing

### Processing Optimization
- **Chunk Size**: Use appropriate chunk sizes (1200 characters recommended)
- **Entity Extraction**: Enable entity extraction for better knowledge graph integration
- **Reference Extraction**: Enable reference extraction for academic content

## Troubleshooting

### Common Solutions
1. **Install Dependencies**: Ensure one-extract library is properly installed
2. **File Validation**: Verify OneNote files are not corrupted
3. **Password Handling**: Provide passwords for encrypted files
4. **Memory Usage**: Large OneNote files may require more memory

### Debug Information
Enable debug logging to see detailed processing information:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Support and Feedback

OneNote support is actively maintained and improved. For issues or feature requests:
1. Check the error logs for specific error messages
2. Verify all dependencies are properly installed
3. Test with the provided test script
4. Report issues with sample files (non-sensitive content)

The OneNote integration follows the same high-quality standards as other document processors in the Graphiti system, ensuring reliable and consistent processing across all supported file types.
