"""
Data models for entity deduplication.
"""

from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import datetime


class EntityMatch(BaseModel):
    """
    Represents a match between two entities.
    """
    source_uuid: str = Field(..., description="UUID of the source entity")
    target_uuid: str = Field(..., description="UUID of the target entity (potential duplicate)")
    similarity_score: float = Field(..., description="Similarity score between the entities (0-1)")
    match_type: str = Field(..., description="Type of match (exact, fuzzy, semantic)")
    source_name: str = Field(..., description="Name of the source entity")
    target_name: str = Field(..., description="Name of the target entity")
    source_type: str = Field(..., description="Type of the source entity")
    target_type: str = Field(..., description="Type of the target entity")
    merged: bool = Field(False, description="Whether the entities were merged")


class DeduplicationResult(BaseModel):
    """
    Results of a deduplication operation.
    """
    total_entities: int = Field(..., description="Total number of entities processed")
    duplicate_groups: int = Field(..., description="Number of duplicate groups found")
    total_duplicates: int = Field(..., description="Total number of duplicate entities found")
    merged_entities: int = Field(..., description="Number of entities that were merged")
    entity_matches: List[EntityMatch] = Field(default_factory=list, description="List of entity matches")
    processing_time: float = Field(..., description="Time taken to process in seconds")
    timestamp: datetime = Field(default_factory=datetime.now, description="Timestamp of the deduplication operation")


class EntityForDeduplication(BaseModel):
    """
    Entity model used for deduplication processing.
    """
    uuid: str = Field(..., description="UUID of the entity")
    name: str = Field(..., description="Name of the entity")
    type: str = Field(..., description="Type of the entity")
    description: Optional[str] = Field(None, description="Description of the entity")
    source_document_id: Optional[str] = Field(None, description="ID of the source document")
    source_fact_id: Optional[str] = Field(None, description="ID of the source fact")
    confidence: Optional[float] = Field(None, description="Confidence score for the entity")
    attributes: Optional[Dict[str, Any]] = Field(None, description="Additional attributes")
    created_at: Optional[Union[str, int]] = Field(None, description="Creation timestamp")
    embedding: Optional[List[float]] = Field(None, description="Vector embedding of the entity name")

    class Config:
        arbitrary_types_allowed = True
