#!/usr/bin/env python3
"""
Test script to check upload progress tracking.
"""

import requests
import time

def test_upload_progress():
    """Test upload progress tracking"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Upload Progress Tracking")
    print("=" * 40)
    
    # Test 1: Check active operations
    print("1. Checking active operations...")
    try:
        response = requests.get(f"{base_url}/api/enhanced-progress", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Active operations: {len(data.get('active_operations', {}))}")
            
            for op_id, progress in data.get('active_operations', {}).items():
                print(f"  Operation {op_id}:")
                print(f"    Document: {progress.get('document_name', 'N/A')}")
                print(f"    Step: {progress.get('current_step', 0)}/{progress.get('total_steps', 0)}")
                print(f"    Status: {progress.get('status', 'N/A')}")
                print(f"    Progress: {progress.get('progress_percentage', 0)}%")
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Check document processing status
    print("\n2. Checking document processing status...")
    try:
        response = requests.get(f"{base_url}/api/processing-status", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Queue size: {data.get('queue_size', 0)}")
            print(f"Is processing: {data.get('is_processing', False)}")
            print(f"Active documents: {len(data.get('document_progress', {}))}")
            
            for doc_id, progress in data.get('document_progress', {}).items():
                print(f"  Document {doc_id}:")
                print(f"    Filename: {progress.get('filename', 'N/A')}")
                print(f"    Step: {progress.get('current_step', 0)}/{progress.get('total_steps', 0)}")
                print(f"    Status: {progress.get('status', 'N/A')}")
                print(f"    Progress: {progress.get('progress_percentage', 0)}%")
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Check if there are any completed documents
    print("\n3. Checking recent documents...")
    try:
        response = requests.get(f"{base_url}/api/fast/documents?limit=5", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"Recent documents: {len(documents)}")
            
            for doc in documents:
                name = doc.get('name', 'N/A')
                created = doc.get('created_at', 'N/A')
                print(f"  - {name} (created: {created})")
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_upload_progress()
