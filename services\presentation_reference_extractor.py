"""
Specialized Reference Extractor for PowerPoint Presentations
Designed to extract references from presentation slides with non-standard formats
"""

import re
import uuid
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class PresentationReferenceExtractor:
    """
    Specialized extractor for PowerPoint presentations converted to PDF.
    Handles non-numbered, embedded references in presentation format.
    """
    
    def __init__(self, mistral_ocr_processor):
        self.mistral_ocr = mistral_ocr_processor
        
    async def extract_references(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references from a presentation document using Mistral OCR.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary with extraction results
        """
        try:
            logger.info(f"🎯 Starting presentation reference extraction for: {Path(file_path).name}")
            
            # Extract text using Mistral OCR
            text = await self.mistral_ocr.extract_text_from_pdf(file_path)
            if not text:
                logger.error("❌ No text extracted from document")
                return self._empty_result("No text extracted")
            
            logger.info(f"📝 Extracted {len(text):,} characters from presentation")
            
            # Extract references using presentation-specific patterns
            references = self._extract_presentation_references(text)
            
            logger.info(f"📚 Found {len(references)} references in presentation")
            
            # Save references to CSV
            csv_path = await self._save_references_to_csv(references, file_path)
            
            return {
                "success": True,
                "filename": Path(file_path).name,
                "file_path": file_path,
                "extraction_method": "presentation_mistral_ocr",
                "total_reference_count": len(references),
                "references": references,
                "csv_path": csv_path,
                "extracted_text_length": len(text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error extracting presentation references: {e}", exc_info=True)
            return self._empty_result(str(e))
    
    def _extract_presentation_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references from presentation text using specialized patterns."""
        
        references = []
        
        # Strategy 1: Author-year format (most common in presentations)
        author_year_refs = self._extract_author_year_references(text)
        references.extend(author_year_refs)
        
        # Strategy 2: Journal citation format
        journal_refs = self._extract_journal_references(text)
        references.extend(journal_refs)
        
        # Strategy 3: DOI and URL references
        doi_url_refs = self._extract_doi_url_references(text)
        references.extend(doi_url_refs)
        
        # Strategy 4: Book and chapter references
        book_refs = self._extract_book_references(text)
        references.extend(book_refs)
        
        # Strategy 5: Conference and proceeding references
        conference_refs = self._extract_conference_references(text)
        references.extend(conference_refs)
        
        # Deduplicate and clean
        cleaned_refs = self._clean_and_deduplicate(references)
        
        logger.info(f"📋 Extraction breakdown:")
        logger.info(f"   Author-year format: {len(author_year_refs)}")
        logger.info(f"   Journal format: {len(journal_refs)}")
        logger.info(f"   DOI/URL format: {len(doi_url_refs)}")
        logger.info(f"   Book format: {len(book_refs)}")
        logger.info(f"   Conference format: {len(conference_refs)}")
        logger.info(f"   After deduplication: {len(cleaned_refs)}")
        
        return cleaned_refs
    
    def _extract_author_year_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract author-year format references like 'Smith AB, Jones CD et al. Title. Journal. 2020'"""
        references = []
        
        # Pattern for author-year references
        patterns = [
            # Pattern 1: Last name, initials format with et al
            r'[A-Z][a-z]+\s+[A-Z]{1,3}(?:,\s*[A-Z][a-z]+\s+[A-Z]{1,3})*(?:\s+et\s+al)?\.?\s+[^.]{10,200}\.?\s*[A-Z][^.]{5,100}\.?\s*\d{4}[^.]*\.?',
            
            # Pattern 2: Simplified author format
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*(?:,\s*[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*)*\s+et\s+al[^.]*\.[^.]{10,200}\.\s*\d{4}',
            
            # Pattern 3: Your specific example format
            r'[A-Z][a-z]+\s+[A-Z]{1,3},?\s+[A-Z][a-z]+\s+[A-Z]{1,3},?\s+[A-Z][a-z]+\s+[A-Z]{1,3}\s+et\s+al[^.]*\.[^.]{10,200}\.\s*[A-Z][^.]{5,50}\.\s*\d{4}',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                if self._is_valid_presentation_reference(match):
                    references.append(self._create_reference_dict(match, 'author_year'))
        
        return references
    
    def _extract_journal_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract journal-style references."""
        references = []
        
        patterns = [
            # Standard journal format
            r'[A-Z][^.]{10,200}\.\s*[A-Z][^.]{5,100}\.\s*\d{4}[;\s]*\d*[:\s]*\d*[-\d]*',
            
            # Journal with volume/issue
            r'[A-Z][^.]{10,200}\.\s*[A-Z][^.]{5,100}\s+\d{4}[;\s]*\d+[(\s]*\d*[)\s]*[:\s]*\d*[-\d]*',
            
            # Pain journal specific (from your example)
            r'[^.]{10,200}\.\s*Pain\.\s*\d{4}[^.]*',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if self._is_valid_presentation_reference(match):
                    references.append(self._create_reference_dict(match, 'journal'))
        
        return references
    
    def _extract_doi_url_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract DOI and URL references."""
        references = []
        
        patterns = [
            # DOI references
            r'[^.\n]{10,200}\s*doi:\s*10\.\d+/[^\s\n]+[^.\n]*',
            
            # URL references
            r'[^.\n]{10,200}\s*https?://[^\s\n]+[^.\n]*',
            
            # PubMed references
            r'[^.\n]{10,200}\s*PMID:\s*\d+[^.\n]*',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if self._is_valid_presentation_reference(match):
                    references.append(self._create_reference_dict(match, 'doi_url'))
        
        return references
    
    def _extract_book_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract book and chapter references."""
        references = []
        
        patterns = [
            # Book format
            r'[A-Z][^.]{10,200}\.\s*[A-Z][^,]{10,100},\s*\d{4}',
            
            # Chapter in book
            r'[A-Z][^.]{10,200}\.\s*In:\s*[^.]{10,200}\.\s*[A-Z][^,]{10,100},\s*\d{4}',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if self._is_valid_presentation_reference(match):
                    references.append(self._create_reference_dict(match, 'book'))
        
        return references
    
    def _extract_conference_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract conference and proceeding references."""
        references = []
        
        patterns = [
            # Conference proceedings
            r'[A-Z][^.]{10,200}\.\s*(?:Proceedings|Proc|Conference|Conf)[^.]{10,200}\.\s*\d{4}',
            
            # Abstract presentations
            r'[A-Z][^.]{10,200}\.\s*(?:Abstract|Poster|Presentation)[^.]{10,200}\.\s*\d{4}',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if self._is_valid_presentation_reference(match):
                    references.append(self._create_reference_dict(match, 'conference'))
        
        return references
    
    def _is_valid_presentation_reference(self, text: str) -> bool:
        """Check if text looks like a valid presentation reference."""
        if not text or len(text) < 30:  # Minimum length for a reference
            return False
        
        # Must contain some reference indicators
        indicators = [
            r'\d{4}',  # Year (required)
            r'[A-Z][a-z]+',  # Proper name
            r'et\s+al',  # Et al
            r'[Jj]ournal|[Pp]ain|[Nn]ature|[Ss]cience|[Cc]ell|[Ll]ancet',  # Journal names
            r'[Pp]roc|[Cc]onf|[Aa]bstract',  # Conference indicators
            r'doi:|PMID:|http',  # Digital identifiers
            r'[Vv]ol|[Pp]p\.|[Nn]o\.',  # Volume/page indicators
        ]
        
        indicator_count = sum(1 for pattern in indicators if re.search(pattern, text))
        
        # Must have year + at least one other indicator
        has_year = bool(re.search(r'\d{4}', text))
        
        return has_year and indicator_count >= 2
    
    def _create_reference_dict(self, text: str, extraction_method: str) -> Dict[str, Any]:
        """Create a standardized reference dictionary."""
        return {
            "text": text.strip(),
            "extraction_method": extraction_method,
            "source": "presentation_mistral_ocr",
            "uuid": str(uuid.uuid4()),
            "metadata": self._extract_presentation_metadata(text)
        }
    
    def _extract_presentation_metadata(self, text: str) -> Dict[str, Any]:
        """Extract metadata from presentation reference text."""
        metadata = {}
        
        # Extract year
        year_match = re.search(r'\b(19|20)\d{2}\b', text)
        if year_match:
            metadata['year'] = year_match.group()
        
        # Extract DOI
        doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', text, re.IGNORECASE)
        if doi_match:
            metadata['doi'] = doi_match.group(1)
        
        # Extract PMID
        pmid_match = re.search(r'PMID:\s*(\d+)', text, re.IGNORECASE)
        if pmid_match:
            metadata['pmid'] = pmid_match.group(1)
        
        # Extract journal name (common ones)
        journal_patterns = [
            r'\b(Pain)\b',
            r'\b(Nature)\b',
            r'\b(Science)\b',
            r'\b(Cell)\b',
            r'\b(Lancet)\b',
            r'\b(NEJM|New England Journal of Medicine)\b',
            r'\b(JAMA)\b',
        ]
        
        for pattern in journal_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                metadata['journal'] = match.group(1)
                break
        
        # Extract authors (first author)
        author_match = re.search(r'^([A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*)', text)
        if author_match:
            metadata['first_author'] = author_match.group(1)
        
        return metadata
    
    def _clean_and_deduplicate(self, references: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Clean and remove duplicate references."""
        seen_texts = set()
        unique_refs = []
        
        for ref in references:
            # Normalize text for comparison
            normalized = re.sub(r'\s+', ' ', ref['text'].lower().strip())
            
            # Remove very short or very long references
            if len(normalized) < 30 or len(normalized) > 1000:
                continue
            
            # Check for duplicates
            if normalized not in seen_texts:
                seen_texts.add(normalized)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def _save_references_to_csv(self, references: List[Dict[str, Any]], file_path: str) -> Optional[str]:
        """Save references to CSV file."""
        try:
            import csv
            from datetime import datetime
            
            # Create CSV filename
            base_name = Path(file_path).stem
            csv_filename = f"presentation_refs_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            csv_path = Path("data/references") / csv_filename
            csv_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['uuid', 'text', 'extraction_method', 'year', 'doi', 'pmid', 'journal', 'first_author', 'source_document']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for ref in references:
                    metadata = ref.get('metadata', {})
                    writer.writerow({
                        'uuid': ref['uuid'],
                        'text': ref['text'],
                        'extraction_method': ref['extraction_method'],
                        'year': metadata.get('year', ''),
                        'doi': metadata.get('doi', ''),
                        'pmid': metadata.get('pmid', ''),
                        'journal': metadata.get('journal', ''),
                        'first_author': metadata.get('first_author', ''),
                        'source_document': Path(file_path).name
                    })
            
            logger.info(f"💾 Saved {len(references)} presentation references to {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"❌ Error saving presentation references to CSV: {e}")
            return None
    
    def _empty_result(self, error_msg: str) -> Dict[str, Any]:
        """Return empty result with error."""
        return {
            "success": False,
            "error": error_msg,
            "total_reference_count": 0,
            "references": []
        }
