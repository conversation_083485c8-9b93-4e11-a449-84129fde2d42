"""
Integration tests for the settings API endpoints.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

def test_settings_endpoint(test_client, mock_settings):
    """
    Test the settings endpoint.
    
    Args:
        test_client: FastAPI test client
        mock_settings: Mocked settings
    """
    # Make a request to the settings endpoint
    response = test_client.get("/api/settings")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "llm" in data
    assert "database" in data
    assert "embedding" in data
    assert "ocr" in data
    
    # Check the LLM settings
    assert data["llm"]["provider"] == mock_settings["llm"]["provider"]
    assert data["llm"]["model"] == mock_settings["llm"]["model"]
    
    # Check the database settings
    assert data["database"]["type"] == "FalkorDB"
    assert data["database"]["host"] == mock_settings["falkordb"]["host"]
    assert data["database"]["port"] == mock_settings["falkordb"]["port"]
    
    # Check the embedding settings
    assert data["embedding"]["provider"] == mock_settings["embedding"]["provider"]
    assert data["embedding"]["model"] == mock_settings["embedding"]["model"]
    
    # Check the OCR settings
    assert data["ocr"]["provider"] == mock_settings["ocr"]["provider"]
    assert data["ocr"]["model"] == mock_settings["ocr"]["model"]

def test_settings_endpoint_error(test_client):
    """
    Test the settings endpoint with an error.
    
    Args:
        test_client: FastAPI test client
    """
    # Patch the get_settings dependency to raise an exception
    with patch("routes.settings_routes.get_app_settings", side_effect=Exception("Test error")):
        # Make a request to the settings endpoint
        response = test_client.get("/api/settings")
        
        # Check the response
        assert response.status_code == 500
        
        # Check the error message
        assert "Error getting settings" in response.json()["detail"]
