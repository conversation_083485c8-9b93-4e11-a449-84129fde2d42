"""
Check documents in FalkorDB
"""

import os
import logging
import redis
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def check_documents():
    """Check documents in FalkorDB"""
    # Get FalkorDB connection details from environment variables
    host = os.environ.get('FALKORDB_HOST', 'localhost')
    port = int(os.environ.get('FALKORDB_PORT', '6379'))
    password = os.environ.get('FALKORDB_PASSWORD', None)
    
    # Connect to FalkorDB
    redis_client = redis.Redis(
        host=host,
        port=port,
        password=password,
        decode_responses=True
    )
    
    try:
        # Check if FalkorDB is running
        info = redis_client.info()
        logger.info(f"Connected to FalkorDB: {info.get('redis_version', 'unknown')}")
        
        # Check if graph exists
        result = redis_client.execute_command("GRAPH.LIST")
        logger.info(f"Graphs: {result}")
        
        if "graphiti" in result:
            logger.info("graphiti graph exists")
            
            # Execute a query to get all documents (Episodes)
            query = """
            MATCH (e:Episode)
            RETURN e.name AS name, e.uuid AS uuid, e.description AS description
            """
            result = redis_client.execute_command("GRAPH.QUERY", "graphiti", query)
            
            logger.info(f"Query result: {result}")
            
            # Process the result
            if result and len(result) > 1:
                headers = result[0]
                data_rows = result[1]
                
                logger.info(f"Found {len(data_rows)} documents")
                
                for row in data_rows:
                    document = {}
                    for i, header in enumerate(headers):
                        if i < len(row):
                            document[header] = row[i]
                    logger.info(f"Document: {document}")
                    
                    # Get facts for this document
                    if 'uuid' in document:
                        facts_query = f"""
                        MATCH (e:Episode {{uuid: '{document['uuid']}'}}) -[:CONTAINS]-> (f:Fact)
                        RETURN f.body AS body, f.chunk_num AS chunk_num
                        LIMIT 5
                        """
                        facts_result = redis_client.execute_command("GRAPH.QUERY", "graphiti", facts_query)
                        
                        if facts_result and len(facts_result) > 1:
                            facts_headers = facts_result[0]
                            facts_rows = facts_result[1]
                            
                            logger.info(f"Found {len(facts_rows)} facts for document {document['name']}")
                            
                            for facts_row in facts_rows:
                                fact = {}
                                for i, header in enumerate(facts_headers):
                                    if i < len(facts_row):
                                        fact[header] = facts_row[i]
                                logger.info(f"Fact: {fact}")
            else:
                logger.info("No documents found")
            
            # Check for entities related to documents
            entity_query = """
            MATCH (e:Episode) -[:CONTAINS]-> (f:Fact) -[:MENTIONS]-> (entity:Entity)
            RETURN DISTINCT entity.name AS name, entity.type AS type, COUNT(f) AS mentions
            ORDER BY mentions DESC
            LIMIT 10
            """
            entity_result = redis_client.execute_command("GRAPH.QUERY", "graphiti", entity_query)
            
            logger.info(f"Entity query result: {entity_result}")
            
            # Process the result
            if entity_result and len(entity_result) > 1:
                headers = entity_result[0]
                data_rows = entity_result[1]
                
                logger.info(f"Found {len(data_rows)} entities mentioned in documents")
                
                for row in data_rows:
                    entity = {}
                    for i, header in enumerate(headers):
                        if i < len(row):
                            entity[header] = row[i]
                    logger.info(f"Entity: {entity}")
            else:
                logger.info("No entities found mentioned in documents")
        else:
            logger.info("graphiti graph does not exist")
    
    except Exception as e:
        logger.error(f"Error checking documents: {e}")
    
    finally:
        redis_client.close()
        logger.info("Closed FalkorDB connection")

if __name__ == "__main__":
    check_documents()
