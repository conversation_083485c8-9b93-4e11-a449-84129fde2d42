#!/usr/bin/env python3
"""
Debug script to check document storage and retrieval.
"""

import asyncio

async def debug_document_storage():
    """Debug document storage and retrieval"""
    print("🔍 Debugging Document Storage & Retrieval")
    print("=" * 50)
    
    # Test 1: Check what documents are in the database
    print("1. Checking documents in database...")
    try:
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        # Query for episodes (documents)
        query = """
        MATCH (d:Episode)
        RETURN d.uuid as uuid, d.name as name, d.created_at as created_at, 
               d.file_size as file_size, d.chunk_count as chunk_count
        ORDER BY d.created_at DESC
        LIMIT 10
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found {len(result[1])} documents in database:")
            for i, row in enumerate(result[1]):
                uuid = row[0] if row[0] else "N/A"
                name = row[1] if row[1] else "N/A"
                created_at = row[2] if row[2] else "N/A"
                file_size = row[3] if row[3] else 0
                chunk_count = row[4] if row[4] else 0
                
                print(f"  {i+1}. {name}")
                print(f"     UUID: {uuid}")
                print(f"     Created: {created_at}")
                print(f"     Size: {file_size} bytes")
                print(f"     Chunks: {chunk_count}")
                print()
        else:
            print("❌ No documents found in database")
            
    except Exception as e:
        print(f"❌ Database query error: {e}")
    
    # Test 2: Check facts (chunks) in database
    print("2. Checking facts (chunks) in database...")
    try:
        query = """
        MATCH (f:Fact)
        RETURN count(f) as total_facts
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            total_facts = result[1][0][0]
            print(f"✅ Total facts in database: {total_facts}")
        else:
            print("❌ No facts found")
            
        # Get sample facts
        query = """
        MATCH (f:Fact)
        RETURN f.uuid as uuid, f.body as body, f.episode_uuid as episode_uuid
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"Sample facts:")
            for i, row in enumerate(result[1]):
                uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                episode_uuid = row[2] if row[2] else "N/A"
                
                print(f"  {i+1}. Fact {uuid[:8]}...")
                print(f"     Episode: {episode_uuid[:8]}...")
                print(f"     Body: {body[:100]}...")
                print()
                
    except Exception as e:
        print(f"❌ Facts query error: {e}")
    
    # Test 3: Check entities in database
    print("3. Checking entities in database...")
    try:
        query = """
        MATCH (e:Entity)
        WHERE NOT e.name STARTS WITH 'Test'
        RETURN count(e) as total_entities
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            total_entities = result[1][0][0]
            print(f"✅ Total real entities in database: {total_entities}")
        else:
            print("❌ No entities found")
            
    except Exception as e:
        print(f"❌ Entities query error: {e}")
    
    # Test 4: Check document API response
    print("4. Testing document API...")
    try:
        import requests
        
        response = requests.get("http://127.0.0.1:9753/api/fast/documents?limit=5", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            
            print(f"✅ API returned {len(documents)} documents:")
            for doc in documents:
                name = doc.get('name', 'N/A')
                uuid = doc.get('uuid', 'N/A')
                created_at = doc.get('created_at', 'N/A')
                
                print(f"  - {name} ({uuid[:8]}...) - {created_at}")
        else:
            print(f"❌ API error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")
    
    # Test 5: Check if there's a mismatch between database and API
    print("5. Checking for database/API mismatch...")
    try:
        # Get document count from database
        query = "MATCH (d:Episode) RETURN count(d) as total"
        result = adapter.execute_cypher(query)
        db_count = result[1][0][0] if result and len(result) > 1 else 0
        
        # Get document count from API
        response = requests.get("http://127.0.0.1:9753/api/fast/documents?limit=1000", timeout=5)
        api_count = len(response.json().get('documents', [])) if response.status_code == 200 else 0
        
        print(f"Database documents: {db_count}")
        print(f"API documents: {api_count}")
        
        if db_count != api_count:
            print("❌ Mismatch between database and API!")
        else:
            print("✅ Database and API counts match")
            
    except Exception as e:
        print(f"❌ Mismatch check error: {e}")
    
    # Test 6: Check recent processing results
    print("6. Checking recent processing results...")
    try:
        from utils.processing_results_store import get_results_store
        results_store = get_results_store()
        
        # This might not work if the store doesn't have a list method
        print("✅ Processing results store accessible")
        
    except Exception as e:
        print(f"❌ Processing results store error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_document_storage())
