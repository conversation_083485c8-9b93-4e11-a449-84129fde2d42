#!/usr/bin/env python3
"""
Check entities extracted from OneNote pages.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database.falkordb_adapter import <PERSON>alkor<PERSON><PERSON><PERSON>pter

def check_entities():
    """Check what entities were extracted from OneNote pages."""
    
    print("🏷️ CHECKING ENTITIES EXTRACTED FROM ONENOTE PAGES")
    print("=" * 60)
    
    try:
        db = FalkorDBAdapter()
        
        # Get all entities
        query = 'MATCH (n) RETURN n.name as name, n.type as type, n.description as description LIMIT 100'
        result = db.execute_cypher(query)

        if not result or len(result) < 2:
            print("❌ No entities found in the knowledge graph")
            return

        # FalkorDB returns: [headers, [data_rows], stats]
        headers = result[0]  # ['name', 'type', 'description']
        data_rows = result[1] if len(result) > 1 else []

        if not data_rows:
            print("❌ No entity data found")
            return

        print(f"✅ Found {len(data_rows)} entities in the knowledge graph")
        print()

        # Group by type
        entity_types = {}
        for row in data_rows:
            if isinstance(row, list) and len(row) >= 3:
                name = row[0] if row[0] else 'Unknown'
                entity_type = row[1] if row[1] else 'Unknown'
                description = row[2] if row[2] else 'No description'
            else:
                print(f"Unexpected row format: {row}")
                continue
            
            if entity_type not in entity_types:
                entity_types[entity_type] = []
            entity_types[entity_type].append({
                'name': name,
                'description': description
            })
        
        # Show entities by type
        print("📊 ENTITIES BY TYPE:")
        print("-" * 60)
        
        for entity_type, entities in sorted(entity_types.items()):
            print(f"\n🔹 {entity_type.upper()} ({len(entities)} entities):")
            
            for entity in sorted(entities, key=lambda x: x['name'])[:10]:  # Show first 10
                name = entity['name']
                description = entity['description']
                
                # Truncate long descriptions
                if len(description) > 80:
                    description = description[:80] + "..."
                
                print(f"  • {name}")
                print(f"    {description}")
            
            if len(entities) > 10:
                print(f"    ... and {len(entities)-10} more {entity_type} entities")
        
        print(f"\n📈 SUMMARY:")
        print(f"Total entities: {len(data_rows)}")
        print(f"Entity types: {len(entity_types)}")
        
        # Show the most common entity types
        type_counts = [(t, len(e)) for t, e in entity_types.items()]
        type_counts.sort(key=lambda x: x[1], reverse=True)
        
        print(f"\nTop entity types:")
        for entity_type, count in type_counts[:5]:
            print(f"  {entity_type}: {count} entities")
        
        # Check for OneNote-specific entities
        onenote_entities = []
        for row in data_rows:
            if isinstance(row, list) and len(row) >= 1:
                name = (row[0] if row[0] else '').lower()
                actual_name = row[0] if row[0] else 'Unknown'

                if any(keyword in name for keyword in ['ginger', 'baicalein', 'neurotransmitter', 'dopamine', 'serotonin', 'tnf', 'nfkb']):
                    onenote_entities.append(actual_name)
        
        if onenote_entities:
            print(f"\n🎯 ONENOTE-RELATED ENTITIES FOUND:")
            for entity in sorted(onenote_entities)[:20]:
                print(f"  ✅ {entity}")
            if len(onenote_entities) > 20:
                print(f"  ... and {len(onenote_entities)-20} more")
        else:
            print(f"\n⚠️ No obvious OneNote-related entities found")
            print("This might indicate the entities were processed but with different names")
        
    except Exception as e:
        print(f"❌ Error checking entities: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_entities()
