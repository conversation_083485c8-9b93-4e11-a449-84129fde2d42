"""
Embedding service for the Graphiti application.
"""

from typing import Dict, Any, List, Optional
import os
import json
import httpx
import asyncio

from utils.logging_utils import get_logger
from utils.config import get_settings

# Set up logger
logger = get_logger(__name__)

async def test_embedding_connection() -> bool:
    """
    Test embedding API connection.
    
    Returns:
        True if connected, False otherwise
    """
    try:
        settings = get_settings()
        
        # Get embedding model
        model = settings.get("embedding_settings", {}).get("model", "snowflake-arctic-embed2")
        
        # Test connection based on model
        if model == "snowflake-arctic-embed2":
            return await test_ollama_embedding_connection("snowflake-arctic-embed2")
        elif model.startswith("openai"):
            api_key = settings.get("llm_settings", {}).get("api_key", "")
            return await test_openai_embedding_connection(api_key)
        else:
            # Assume it's an Ollama model
            return await test_ollama_embedding_connection(model)
    
    except Exception as e:
        logger.error(f"Error testing embedding connection: {str(e)}")
        return False

async def test_openai_embedding_connection(api_key: str) -> bool:
    """
    Test OpenAI embedding API connection.
    
    Args:
        api_key: OpenAI API key
        
    Returns:
        True if connected, False otherwise
    """
    try:
        if not api_key:
            logger.warning("No OpenAI API key provided")
            return False
        
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "https://api.openai.com/v1/embeddings",
                headers={"Authorization": f"Bearer {api_key}"},
                json={"input": "test", "model": "text-embedding-ada-002"}
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"OpenAI embedding API returned status code {response.status_code}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing OpenAI embedding connection: {str(e)}")
        return False

async def test_ollama_embedding_connection(model: str) -> bool:
    """
    Test Ollama embedding API connection.
    
    Args:
        model: Ollama model name
        
    Returns:
        True if connected, False otherwise
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.post(
                "http://localhost:11434/api/embeddings",
                json={"model": model, "prompt": "test"}
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"Ollama embedding API returned status code {response.status_code}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing Ollama embedding connection: {str(e)}")
        return False
