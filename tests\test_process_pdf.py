"""
Test script to process a PDF file with Mistral OCR
"""

import asyncio
import logging
import os
import sys
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    # Load environment variables
    load_dotenv()
    
    # Check if a PDF file path was provided
    if len(sys.argv) < 2:
        logger.error("Please provide a PDF file path as an argument")
        print("Usage: python test_process_pdf.py <pdf_file_path>")
        return False
    
    pdf_path = sys.argv[1]
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    # Import MistralOCRProcessor
    try:
        from mistral_ocr import MistralOCRProcessor
        logger.info("Successfully imported MistralOCRProcessor")
    except ImportError as e:
        logger.error(f"Failed to import MistralOCRProcessor: {e}")
        return False
    
    # Initialize MistralOCRProcessor
    mistral_api_key = os.environ.get('MISTRAL_API_KEY')
    processor = MistralOCRProcessor(api_key=mistral_api_key)
    
    # Process the PDF
    try:
        logger.info(f"Processing PDF: {pdf_path}")
        result = await processor.process_pdf(pdf_path)
        
        if result.get("success", False):
            text_length = len(result["text"])
            logger.info(f"Successfully extracted {text_length} characters of text")
            
            # Print a sample of the extracted text
            sample_length = min(500, text_length)
            print(f"\nSample of extracted text ({sample_length} characters):")
            print("-" * 80)
            print(result["text"][:sample_length])
            print("-" * 80)
            
            return True
        else:
            logger.error(f"Error processing PDF: {result.get('error', 'Unknown error')}")
            return False
    except Exception as e:
        logger.error(f"Exception while processing PDF: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        print("\nPDF processing with Mistral OCR completed successfully!")
    else:
        print("\nPDF processing with Mistral OCR failed. Check the logs above for details.")
