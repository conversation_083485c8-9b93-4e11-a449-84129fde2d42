# Reference Enhancements for Graphiti

This module enhances Graphiti's reference management capabilities with advanced features for deduplication, citation network analysis, and integration with external bibliographic databases.

## Features

### 1. Reference Deduplication

- Identifies duplicate references in the knowledge graph
- Uses fuzzy matching for title, author, and publication details
- Creates `DUPLICATE_OF` relationships between duplicate references
- Maintains a canonical reference for each group of duplicates
- Provides a user interface for browsing duplicate groups

### 2. Citation Network Analysis

- Builds a citation network from references in the knowledge graph
- Creates `CITES` relationships between documents
- Calculates network statistics (density, connected components, etc.)
- Identifies highly cited documents using PageRank
- Generates visualizations of the citation network
- Finds citation paths between documents

### 3. Bibliographic Enrichment

- Enriches references with data from external bibliographic databases
- Connects to Crossref, Semantic Scholar, and PubMed
- Adds DOIs, full metadata, and citation counts
- Respects rate limits for external APIs
- Provides a user interface for browsing enriched references

## Architecture

The implementation consists of several components:

1. **ReferenceDeduplcator**: Identifies and links duplicate references
2. **CitationNetworkAnalyzer**: Builds and analyzes citation networks
3. **BibliographicEnricher**: Enriches references with external data
4. **ReferenceEnhancementSuite**: Combines all enhancements in a single interface
5. **Web Interface**: Provides a user interface for running and viewing enhancements

## Knowledge Graph Schema

The implementation extends the existing knowledge graph schema with new relationships:

- **DUPLICATE_OF**: Links a duplicate reference to its canonical reference
- **CITES**: Links a citing document to a cited document

## Usage

### Command Line

#### Run All Enhancements

```bash
python reference_enhancement_suite.py
```

#### Run Specific Enhancements

```bash
# Run only deduplication
python reference_enhancement_suite.py --no-network --no-enrich

# Run only citation network analysis
python reference_enhancement_suite.py --no-deduplicate --no-enrich

# Run only bibliographic enrichment
python reference_enhancement_suite.py --no-deduplicate --no-network
```

#### Configure Enrichment

```bash
# Set batch size and maximum references
python reference_enhancement_suite.py --batch-size 20 --max-references 100
```

### Web Interface

1. Navigate to the **Enhancements** tab
2. Choose an enhancement to run:
   - **Deduplication**: Find and link duplicate references
   - **Citation Network**: Build a citation network
   - **Bibliographic Enrichment**: Enrich references with external data
   - **Run All**: Run all enhancements in sequence
3. View the results:
   - Duplicate groups
   - Citation network visualization
   - Enriched references

## API Endpoints

The implementation adds several API endpoints to the web interface:

- `/api/enhancements/status`: Get the status of all enhancements
- `/api/enhancements/deduplicate`: Run reference deduplication
- `/api/enhancements/build-network`: Build citation network
- `/api/enhancements/enrich`: Enrich references
- `/api/enhancements/run-all`: Run all enhancements
- `/api/enhancements/task/{task_id}`: Get the status of a background task
- `/api/enhancements/visualization`: Get the citation network visualization
- `/api/enhancements/duplicate-groups`: Get duplicate groups
- `/api/enhancements/enriched-references`: Get enriched references
- `/api/enhancements/enriched-references-csv`: Download enriched references as CSV
- `/api/enhancements/most-cited`: Get most cited documents
- `/api/enhancements/citation-path`: Get citation path between documents
- `/api/enhancements/export-network`: Export citation network data

## Dependencies

- **NetworkX**: For citation network analysis and visualization
- **Matplotlib**: For generating network visualizations
- **aiohttp**: For making asynchronous HTTP requests to external APIs
- **difflib**: For fuzzy string matching in deduplication

## Configuration

Configuration options are available in the `.env` file:

```
# Deduplication settings
TITLE_SIMILARITY_THRESHOLD=0.85
AUTHOR_SIMILARITY_THRESHOLD=0.75
YEAR_MATCH_REQUIRED=true
MIN_OVERALL_SIMILARITY=0.8

# External API keys
CROSSREF_EMAIL=<EMAIL>
SEMANTIC_SCHOLAR_API_KEY=your_api_key
PUBMED_API_KEY=your_api_key
```

## Limitations

- Deduplication accuracy depends on the quality of reference metadata
- Citation network analysis requires references to be matched to documents in the knowledge graph
- Bibliographic enrichment requires external API access and may be rate-limited
- Processing large numbers of references may take significant time

## Future Enhancements

- Implement more sophisticated deduplication algorithms
- Add support for more external bibliographic databases
- Improve citation network visualization with interactive features
- Add citation metrics and impact analysis
- Implement reference clustering by topic
