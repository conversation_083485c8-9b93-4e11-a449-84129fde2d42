#!/usr/bin/env python3
"""
Test script for dynamic threshold functionality.

This script demonstrates the new dynamic threshold system for entity deduplication.
"""

import asyncio
import logging
import sys
import os
from typing import List

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.dynamic_thresholds import DynamicThresholdCalculator
from entity_deduplication.utils import calculate_entity_similarity

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_entities() -> List[EntityForDeduplication]:
    """Create test entities for different scenarios."""
    entities = [
        # Person entities - should have higher thresholds
        EntityForDeduplication(
            uuid="person-1",
            name="<PERSON>",
            type="Person",
            description="A medical researcher",
            confidence=0.9
        ),
        EntityForDeduplication(
            uuid="person-2", 
            name="<PERSON>",
            type="Person",
            description="Medical researcher and author",
            confidence=0.8
        ),
        EntityForDeduplication(
            uuid="person-3",
            name="Dr. <PERSON>",
            type="Person", 
            description="Physician and researcher",
            confidence=0.85
        ),
        
        # Organization entities
        EntityForDeduplication(
            uuid="org-1",
            name="Harvard Medical School",
            type="Organization",
            description="Medical school in Boston",
            confidence=0.95
        ),
        EntityForDeduplication(
            uuid="org-2",
            name="Harvard Med School", 
            type="Organization",
            description="Medical education institution",
            confidence=0.7
        ),
        
        # Location entities - should have highest thresholds
        EntityForDeduplication(
            uuid="loc-1",
            name="Boston",
            type="Location",
            description="City in Massachusetts",
            confidence=0.9
        ),
        EntityForDeduplication(
            uuid="loc-2",
            name="Boston, MA",
            type="Location", 
            description="Major city in Massachusetts",
            confidence=0.85
        ),
        
        # Generic entities
        EntityForDeduplication(
            uuid="generic-1",
            name="Vitamin D",
            type="Nutrient",
            description="Essential vitamin for bone health",
            confidence=0.8
        ),
        EntityForDeduplication(
            uuid="generic-2",
            name="Vitamin D3",
            type="Nutrient",
            description="Active form of vitamin D",
            confidence=0.75
        )
    ]
    
    return entities


def test_static_vs_dynamic_thresholds():
    """Test comparison between static and dynamic thresholds."""
    logger.info("=== Testing Static vs Dynamic Thresholds ===")
    
    entities = create_test_entities()
    threshold_calc = DynamicThresholdCalculator()
    
    # Test pairs that should demonstrate different threshold behavior
    test_pairs = [
        (entities[0], entities[1]),  # John Smith vs John A. Smith (Person)
        (entities[3], entities[4]),  # Harvard Medical School vs Harvard Med School (Org)
        (entities[5], entities[6]),  # Boston vs Boston, MA (Location)
        (entities[7], entities[8])   # Vitamin D vs Vitamin D3 (Nutrient)
    ]
    
    for entity1, entity2 in test_pairs:
        logger.info(f"\nComparing: '{entity1.name}' vs '{entity2.name}' (Type: {entity1.type})")
        
        # Get dynamic thresholds
        name_threshold = threshold_calc.get_threshold(entity1, entity2, "name")
        semantic_threshold = threshold_calc.get_threshold(entity1, entity2, "semantic")
        
        logger.info(f"  Dynamic name threshold: {name_threshold:.3f}")
        logger.info(f"  Dynamic semantic threshold: {semantic_threshold:.3f}")
        
        # Calculate similarity
        similarity_score, match_type = calculate_entity_similarity(entity1, entity2)
        logger.info(f"  Similarity score: {similarity_score:.3f} (type: {match_type})")
        
        # Show confidence adjustment impact
        conf1 = entity1.confidence or 0.5
        conf2 = entity2.confidence or 0.5
        avg_confidence = (conf1 + conf2) / 2
        logger.info(f"  Average confidence: {avg_confidence:.3f}")


def test_threshold_adaptation():
    """Test how thresholds adapt based on feedback."""
    logger.info("\n=== Testing Threshold Adaptation ===")
    
    threshold_calc = DynamicThresholdCalculator()
    entities = create_test_entities()
    
    # Simulate some feedback scenarios
    entity1, entity2 = entities[0], entities[1]  # John Smith variants
    
    logger.info(f"Testing adaptation for: '{entity1.name}' vs '{entity2.name}'")
    
    # Get initial threshold
    initial_threshold = threshold_calc.get_threshold(entity1, entity2, "name")
    logger.info(f"Initial threshold: {initial_threshold:.3f}")
    
    # Simulate some false positives (predicted match but actually different entities)
    for i in range(5):
        threshold_calc.update_metrics(
            entity1, entity2, 
            similarity_score=0.87, 
            predicted_match=True, 
            actual_match=False  # False positive
        )
    
    # Get updated threshold (should be higher to reduce false positives)
    updated_threshold = threshold_calc.get_threshold(entity1, entity2, "name")
    logger.info(f"Threshold after false positives: {updated_threshold:.3f}")
    logger.info(f"Threshold change: {updated_threshold - initial_threshold:+.3f}")
    
    # Now simulate some false negatives (predicted no match but actually same entity)
    for i in range(3):
        threshold_calc.update_metrics(
            entity1, entity2,
            similarity_score=0.82,
            predicted_match=False,
            actual_match=True  # False negative
        )
    
    # Get final threshold (should be lower to reduce false negatives)
    final_threshold = threshold_calc.get_threshold(entity1, entity2, "name")
    logger.info(f"Threshold after false negatives: {final_threshold:.3f}")
    logger.info(f"Total threshold change: {final_threshold - initial_threshold:+.3f}")


def test_entity_type_differences():
    """Test how different entity types get different thresholds."""
    logger.info("\n=== Testing Entity Type Differences ===")
    
    threshold_calc = DynamicThresholdCalculator()
    entities = create_test_entities()
    
    # Create similar entities of different types
    test_entities = [
        EntityForDeduplication(uuid="test-person", name="Test Entity", type="Person", confidence=0.8),
        EntityForDeduplication(uuid="test-org", name="Test Entity", type="Organization", confidence=0.8),
        EntityForDeduplication(uuid="test-loc", name="Test Entity", type="Location", confidence=0.8),
        EntityForDeduplication(uuid="test-generic", name="Test Entity", type="Nutrient", confidence=0.8)
    ]
    
    reference_entity = EntityForDeduplication(
        uuid="ref", name="Test Entity Similar", type="Person", confidence=0.8
    )
    
    for entity in test_entities:
        name_threshold = threshold_calc.get_threshold(entity, reference_entity, "name")
        semantic_threshold = threshold_calc.get_threshold(entity, reference_entity, "semantic")
        
        logger.info(f"{entity.type:12} - Name: {name_threshold:.3f}, Semantic: {semantic_threshold:.3f}")


def test_performance_report():
    """Test the performance reporting functionality."""
    logger.info("\n=== Testing Performance Report ===")
    
    threshold_calc = DynamicThresholdCalculator()
    
    # Add some sample metrics
    entities = create_test_entities()
    entity1, entity2 = entities[0], entities[1]
    
    # Simulate various outcomes
    test_cases = [
        (0.92, True, True),   # True positive
        (0.88, True, True),   # True positive  
        (0.87, True, False),  # False positive
        (0.83, False, True),  # False negative
        (0.75, False, False), # True negative
        (0.79, False, False), # True negative
    ]
    
    for similarity, predicted, actual in test_cases:
        threshold_calc.update_metrics(entity1, entity2, similarity, predicted, actual)
    
    # Get performance report
    report = threshold_calc.get_performance_report()
    
    logger.info("Performance Report:")
    logger.info(f"Global Precision: {report['global_metrics']['precision']:.3f}")
    logger.info(f"Global Recall: {report['global_metrics']['recall']:.3f}")
    logger.info(f"Global F1 Score: {report['global_metrics']['f1_score']:.3f}")
    logger.info(f"Total Comparisons: {report['global_metrics']['total_comparisons']}")
    
    for entity_type, metrics in report['entity_types'].items():
        if metrics['total_comparisons'] > 0:
            logger.info(f"\n{entity_type}:")
            logger.info(f"  Precision: {metrics['precision']:.3f}")
            logger.info(f"  Recall: {metrics['recall']:.3f}")
            logger.info(f"  F1 Score: {metrics['f1_score']:.3f}")
            logger.info(f"  Comparisons: {metrics['total_comparisons']}")


def main():
    """Run all tests."""
    logger.info("Starting Dynamic Threshold Tests")
    logger.info("=" * 50)
    
    try:
        test_static_vs_dynamic_thresholds()
        test_threshold_adaptation()
        test_entity_type_differences()
        test_performance_report()
        
        logger.info("\n" + "=" * 50)
        logger.info("All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
