"""
Processing Results Store

This module provides persistent storage for document processing results
that can be retrieved even after the operation tracking is cleaned up.
"""

import json
import os
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional
from utils.logging_utils import get_logger

logger = get_logger(__name__)

class ProcessingResultsStore:
    """Store for persistent processing results."""
    
    def __init__(self, storage_dir: str = "processing_results"):
        """
        Initialize the processing results store.
        
        Args:
            storage_dir: Directory to store processing results
        """
        self.storage_dir = Path(storage_dir)
        self.storage_dir.mkdir(exist_ok=True)
        
    def save_results(self, operation_id: str, results: Dict[str, Any]) -> bool:
        """
        Save processing results for an operation.
        
        Args:
            operation_id: The operation ID
            results: Dictionary containing processing results
            
        Returns:
            True if saved successfully, False otherwise
        """
        try:
            # Add timestamp
            results["saved_at"] = datetime.now(timezone.utc).isoformat()
            results["operation_id"] = operation_id
            
            # Save to file
            file_path = self.storage_dir / f"{operation_id}.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Saved processing results for operation {operation_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving processing results for operation {operation_id}: {e}")
            return False
    
    def get_results(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get processing results for an operation.
        
        Args:
            operation_id: The operation ID
            
        Returns:
            Processing results dictionary or None if not found
        """
        try:
            file_path = self.storage_dir / f"{operation_id}.json"
            if not file_path.exists():
                return None
                
            with open(file_path, 'r', encoding='utf-8') as f:
                results = json.load(f)
            
            return results
            
        except Exception as e:
            logger.error(f"Error loading processing results for operation {operation_id}: {e}")
            return None
    
    def list_recent_results(self, limit: int = 10) -> list:
        """
        List recent processing results.
        
        Args:
            limit: Maximum number of results to return
            
        Returns:
            List of recent processing results
        """
        try:
            results = []
            
            # Get all result files
            for file_path in self.storage_dir.glob("*.json"):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        result = json.load(f)
                    results.append(result)
                except Exception as e:
                    logger.warning(f"Error reading result file {file_path}: {e}")
            
            # Sort by saved_at timestamp (newest first)
            results.sort(key=lambda x: x.get("saved_at", ""), reverse=True)
            
            return results[:limit]
            
        except Exception as e:
            logger.error(f"Error listing recent results: {e}")
            return []
    
    def cleanup_old_results(self, days: int = 30) -> int:
        """
        Clean up old processing results.
        
        Args:
            days: Number of days to keep results
            
        Returns:
            Number of files cleaned up
        """
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now(timezone.utc) - timedelta(days=days)
            cleaned_count = 0
            
            for file_path in self.storage_dir.glob("*.json"):
                try:
                    # Check file modification time
                    file_time = datetime.fromtimestamp(file_path.stat().st_mtime, timezone.utc)
                    
                    if file_time < cutoff_time:
                        file_path.unlink()
                        cleaned_count += 1
                        
                except Exception as e:
                    logger.warning(f"Error checking/deleting file {file_path}: {e}")
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old processing result files")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return 0

# Global instance
_results_store = None

def get_results_store() -> ProcessingResultsStore:
    """Get the global processing results store instance."""
    global _results_store
    if _results_store is None:
        _results_store = ProcessingResultsStore()
    return _results_store
