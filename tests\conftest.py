"""
Pytest configuration for the Graphiti application.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app
from utils.dependencies import get_db, get_settings

@pytest.fixture
def test_client():
    """
    Create a test client for the FastAPI application.
    
    Returns:
        TestClient: FastAPI test client
    """
    # Create a test client
    client = TestClient(app)
    return client

@pytest.fixture
def mock_db():
    """
    Mock the database dependency.
    
    Returns:
        MagicMock: Mocked database adapter
    """
    # Create a mock database adapter
    mock_adapter = MagicMock()
    mock_adapter.is_connected.return_value = True
    mock_adapter.execute_cypher.return_value = [["header1", "header2"], [["value1", "value2"]]]
    
    # Patch the get_db dependency
    with patch("app.get_db", return_value=mock_adapter):
        yield mock_adapter

@pytest.fixture
def mock_settings():
    """
    Mock the settings dependency.
    
    Returns:
        dict: Mocked settings
    """
    # Create mock settings
    mock_config = {
        'falkordb': {
            'host': 'test-host',
            'port': 1234,
            'password': 'test-password',
            'graph': 'test-graph'
        },
        'llm': {
            'provider': 'test-provider',
            'model': 'test-model',
            'available_models': ['model1', 'model2']
        },
        'embedding': {
            'provider': 'test-embedding-provider',
            'model': 'test-embedding-model'
        },
        'ocr': {
            'provider': 'test-ocr-provider',
            'model': 'test-ocr-model'
        },
        'server': {
            'host': 'test-host',
            'port': 5678
        },
        'paths': {
            'base_dir': '/test/path',
            'uploads_dir': '/test/path/uploads',
            'processed_dir': '/test/path/processed',
            'documents_dir': '/test/path/documents',
            'references_dir': '/test/path/references',
            'static_dir': '/test/path/static',
            'templates_dir': '/test/path/templates'
        }
    }
    
    # Patch the get_settings dependency
    with patch("app.get_settings", return_value=mock_config):
        yield mock_config
