#!/usr/bin/env python3
"""
Simple test for entities API.
"""

import requests

def test_entities_simple():
    """Test entities API"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entities API")
    print("=" * 30)
    
    try:
        response = requests.get(f"{base_url}/api/entities?limit=3", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Total count: {data.get('total_count', 0)}")
            print(f"Total relationships: {data.get('total_relationships', 0)}")
            
            if 'type_counts' in data:
                type_counts = data['type_counts']
                print(f"Type counts available: {len(type_counts)}")
                
                # Show top 5 types
                sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:5]
                print("Top 5 types:")
                for type_name, count in sorted_types:
                    if type_name and type_name != '':
                        print(f"  {type_name}: {count}")
            else:
                print("No type_counts in response")
        else:
            print(f"Error: {response.status_code}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entities_simple()
