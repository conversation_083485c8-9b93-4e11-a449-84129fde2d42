/**
 * Test script for Q&A interface
 */

console.log("QA test script loaded");

// Add event listener for DOMContentLoaded
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded");
    
    // Check if the question button exists
    const questionButton = document.getElementById('question-button');
    console.log("Question button:", questionButton);
    
    // Check if the question input exists
    const questionInput = document.getElementById('question-input');
    console.log("Question input:", questionInput);
    
    // Add a test click handler
    if (questionButton) {
        console.log("Adding click handler to question button");
        questionButton.addEventListener('click', function() {
            console.log("Question button clicked");
            const question = questionInput ? questionInput.value : "No input found";
            console.log("Question:", question);
            
            // Show an alert
            alert("Question button clicked: " + question);
        });
    }
    
    // Add a test keypress handler
    if (questionInput) {
        console.log("Adding keypress handler to question input");
        questionInput.addEventListener('keypress', function(e) {
            console.log("Key pressed:", e.key);
            if (e.key === 'Enter') {
                console.log("Enter key pressed");
                const question = questionInput.value;
                console.log("Question:", question);
                
                // Show an alert
                alert("Enter key pressed: " + question);
            }
        });
    }
});
