"""
<PERSON><PERSON><PERSON> to add PDF content to Neo4j with recursive chunking at 1200 characters
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone
import uuid
import PyPDF2
import textwrap

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path, max_pages=None):
    """Extract text from a PDF file using PyPDF2."""
    logger.info(f"Extracting text from {pdf_path}")
    
    full_text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            
            # Determine how many pages to process
            if max_pages is None:
                num_pages = len(reader.pages)
            else:
                num_pages = min(len(reader.pages), max_pages)
            
            logger.info(f"Processing {num_pages} pages out of {len(reader.pages)} total pages")
            
            for page_num in range(num_pages):
                page = reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    full_text += page_text + "\n\n"
        
        logger.info(f"Extracted {len(full_text)} characters from {pdf_path}")
        return full_text
    except Exception as e:
        logger.error(f"Error extracting text from {pdf_path}: {e}")
        return ""

def chunk_text_recursive(text, chunk_size=1200, overlap=50):
    """
    Split text into chunks recursively with a specified size and overlap.
    Returns a list of chunks.
    """
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    
    # Use textwrap for initial chunking
    wrapper = textwrap.TextWrapper(width=chunk_size, replace_whitespace=False)
    
    # Split text into paragraphs
    paragraphs = text.split('\n\n')
    current_chunk = ""
    
    for paragraph in paragraphs:
        # If paragraph is longer than chunk_size, split it
        if len(paragraph) > chunk_size:
            # Process any accumulated text first
            if current_chunk:
                chunks.append(current_chunk)
                current_chunk = ""
            
            # Split long paragraph into smaller chunks
            para_chunks = []
            for i in range(0, len(paragraph), chunk_size - overlap):
                end = min(i + chunk_size, len(paragraph))
                # Find a good breaking point
                if end < len(paragraph):
                    # Try to break at sentence or word boundary
                    sentence_break = paragraph.rfind('. ', i, end)
                    space_break = paragraph.rfind(' ', i, end)
                    
                    if sentence_break > i + (chunk_size // 2):
                        end = sentence_break + 2  # Include the period and space
                    elif space_break > i + (chunk_size // 2):
                        end = space_break + 1  # Include the space
                
                para_chunks.append(paragraph[i:end])
            
            # Add these paragraph chunks to our main chunks list
            chunks.extend(para_chunks)
        else:
            # Check if adding this paragraph would exceed chunk_size
            if len(current_chunk) + len(paragraph) + 2 > chunk_size:
                # Save current chunk and start a new one
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
    
    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(current_chunk)
    
    logger.info(f"Split text into {len(chunks)} chunks recursively")
    return chunks

async def add_pdf_chunks_to_neo4j(driver, file_path, max_pages=None, chunk_size=1200, overlap=50):
    """Add PDF content in recursive chunks to Neo4j."""
    logger.info(f"Processing PDF: {file_path}")
    
    # Extract text from PDF
    full_text = extract_text_from_pdf(file_path, max_pages)
    if not full_text:
        logger.warning(f"No text extracted from {file_path}, skipping")
        return
    
    # Get document title from filename
    document_title = os.path.basename(file_path)
    
    # Generate UUID for the Episode
    episode_id = str(uuid.uuid4())
    
    # Current timestamp
    timestamp = datetime.now(timezone.utc).isoformat()
    
    try:
        # Create chunks recursively
        chunks = chunk_text_recursive(full_text, chunk_size, overlap)
        logger.info(f"Created {len(chunks)} chunks of approximately {chunk_size} characters each")
        
        # Sample the first few characters of each chunk for logging
        for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
            logger.info(f"Chunk {i+1} sample: {chunk[:50]}...")
        
        async with driver.session() as session:
            # Create Episode node
            logger.info(f"Creating Episode node for {document_title}")
            episode_query = """
            CREATE (e:Episode {
                uuid: $uuid,
                name: $name,
                body: $body,
                created_at: datetime($timestamp),
                source: $source,
                source_description: $source_description,
                total_chunks: $total_chunks
            })
            RETURN e.uuid as uuid
            """
            
            episode_params = {
                "uuid": episode_id,
                "name": document_title,
                "body": f"Document: {document_title}\nSource: {file_path}",
                "timestamp": timestamp,
                "source": "pdf",
                "source_description": f"PDF Document: {file_path}",
                "total_chunks": len(chunks)
            }
            
            result = await session.run(episode_query, episode_params)
            record = await result.single()
            logger.info(f"Created Episode node with UUID: {record['uuid']}")
            
            # Create a Fact node for each chunk
            for i, chunk in enumerate(chunks):
                # Generate UUID for the Fact
                fact_id = str(uuid.uuid4())
                
                # Create Fact node
                fact_query = """
                CREATE (f:Fact {
                    uuid: $uuid,
                    body: $body,
                    created_at: datetime($timestamp),
                    chunk_num: $chunk_num,
                    chunk_total: $chunk_total
                })
                RETURN f.uuid as uuid
                """
                
                fact_params = {
                    "uuid": fact_id,
                    "body": chunk,
                    "timestamp": timestamp,
                    "chunk_num": i + 1,
                    "chunk_total": len(chunks)
                }
                
                result = await session.run(fact_query, fact_params)
                record = await result.single()
                
                # Create CONTAINS relationship
                relationship_query = """
                MATCH (e:Episode {uuid: $episode_uuid})
                MATCH (f:Fact {uuid: $fact_uuid})
                CREATE (e)-[r:CONTAINS]->(f)
                RETURN type(r) as type
                """
                
                relationship_params = {
                    "episode_uuid": episode_id,
                    "fact_uuid": fact_id
                }
                
                await session.run(relationship_query, relationship_params)
            
            logger.info(f"Created {len(chunks)} Fact nodes with recursive chunking")
            
            return episode_id
            
    except Exception as e:
        logger.error(f"Error adding PDF chunks to Neo4j: {e}", exc_info=True)
        return None

async def main():
    """Main function to add a PDF with recursive chunking to Neo4j."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Check if a PDF file was specified
    if len(sys.argv) < 2:
        logger.error("Please specify a PDF file to process")
        logger.info("Usage: python add_pdf_recursive_chunks.py <pdf_file> [max_pages] [chunk_size] [overlap]")
        return
    
    pdf_file = sys.argv[1]
    if not os.path.exists(pdf_file):
        logger.error(f"PDF file not found: {pdf_file}")
        return
    
    # Get optional parameters from command line arguments
    max_pages = None
    chunk_size = 1200  # Default chunk size as required
    overlap = 50
    
    if len(sys.argv) >= 3:
        try:
            max_pages = int(sys.argv[2])
            if max_pages <= 0:
                max_pages = None
        except ValueError:
            logger.warning(f"Invalid max_pages value: {sys.argv[2]}. Using default: all pages")
    
    if len(sys.argv) >= 4:
        try:
            chunk_size = int(sys.argv[3])
        except ValueError:
            logger.warning(f"Invalid chunk_size value: {sys.argv[3]}. Using default: 1200")
    
    if len(sys.argv) >= 5:
        try:
            overlap = int(sys.argv[4])
        except ValueError:
            logger.warning(f"Invalid overlap value: {sys.argv[4]}. Using default: 50")
    
    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        # Add PDF chunks to Neo4j
        episode_id = await add_pdf_chunks_to_neo4j(driver, pdf_file, max_pages, chunk_size, overlap)
        
        if episode_id:
            logger.info(f"PDF processing completed successfully! Episode ID: {episode_id}")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
