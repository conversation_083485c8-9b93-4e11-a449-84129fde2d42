"""
Enhanced citation service for Q&A responses.

This service provides detailed, clickable, numbered references with:
- Proper academic citation formatting (APA, MLA, Chicago)
- Clickable DOI and PMID links
- Document source links
- Reference metadata and abstracts
- Citation quality scoring
- Duplicate detection and merging
"""

import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
import json

logger = logging.getLogger(__name__)


@dataclass
class EnhancedReference:
    """Enhanced reference with full metadata."""
    id: str
    title: Optional[str] = None
    authors: Optional[str] = None
    year: Optional[str] = None
    journal: Optional[str] = None
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    doi: Optional[str] = None
    pmid: Optional[str] = None
    url: Optional[str] = None
    abstract: Optional[str] = None
    source_document: Optional[str] = None
    document_id: Optional[str] = None
    confidence: float = 0.0
    citation_apa: Optional[str] = None
    citation_mla: Optional[str] = None
    citation_chicago: Optional[str] = None
    raw_text: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'title': self.title,
            'authors': self.authors,
            'year': self.year,
            'journal': self.journal,
            'volume': self.volume,
            'issue': self.issue,
            'pages': self.pages,
            'doi': self.doi,
            'pmid': self.pmid,
            'url': self.url,
            'abstract': self.abstract,
            'source_document': self.source_document,
            'document_id': self.document_id,
            'confidence': self.confidence,
            'citation_apa': self.citation_apa,
            'citation_mla': self.citation_mla,
            'citation_chicago': self.citation_chicago,
            'raw_text': self.raw_text
        }


class EnhancedCitationService:
    """Service for creating enhanced citations in Q&A responses."""
    
    def __init__(self):
        self.reference_cache: Dict[str, EnhancedReference] = {}
    
    def enhance_qa_response_with_citations(
        self,
        answer_text: str,
        source_facts: List[Dict[str, Any]],
        citation_style: str = "apa"
    ) -> Dict[str, Any]:
        """
        Enhance Q&A response with detailed citations.
        
        Args:
            answer_text: The answer text with [n] reference markers
            source_facts: List of source facts/documents
            citation_style: Citation style (apa, mla, chicago)
            
        Returns:
            Enhanced response with detailed citations
        """
        # Extract reference numbers from answer
        reference_numbers = self._extract_reference_numbers(answer_text)
        
        # Create enhanced references
        enhanced_references = []
        reference_map = {}
        
        for i, ref_num in enumerate(reference_numbers):
            if i < len(source_facts):
                fact = source_facts[i]
                enhanced_ref = self._create_enhanced_reference(fact, ref_num)
                enhanced_references.append(enhanced_ref)
                reference_map[ref_num] = enhanced_ref
        
        # Generate citation section
        citation_section = self._generate_citation_section(
            enhanced_references, citation_style
        )
        
        # Create enhanced answer with improved citations
        enhanced_answer = self._enhance_answer_text(answer_text, reference_map)
        
        return {
            "answer": enhanced_answer,
            "citations": citation_section,
            "references": [ref.to_dict() for ref in enhanced_references],
            "citation_style": citation_style,
            "total_references": len(enhanced_references)
        }
    
    def _extract_reference_numbers(self, text: str) -> List[int]:
        """Extract reference numbers from text."""
        matches = re.findall(r'\[(\d+)\]', text)
        return [int(match) for match in matches]
    
    def _create_enhanced_reference(
        self,
        fact: Dict[str, Any],
        ref_number: int
    ) -> EnhancedReference:
        """Create enhanced reference from fact data."""
        # Extract metadata from fact
        fact_data = fact.get('fact', {})
        
        # Try to get reference data from various sources
        ref_data = self._extract_reference_metadata(fact)
        
        enhanced_ref = EnhancedReference(
            id=str(ref_number),
            title=ref_data.get('title') or fact_data.get('name') or f"Reference {ref_number}",
            authors=ref_data.get('authors'),
            year=ref_data.get('year'),
            journal=ref_data.get('journal'),
            volume=ref_data.get('volume'),
            issue=ref_data.get('issue'),
            pages=ref_data.get('pages'),
            doi=ref_data.get('doi'),
            pmid=ref_data.get('pmid'),
            url=ref_data.get('url'),
            abstract=ref_data.get('abstract') or fact_data.get('summary'),
            source_document=fact.get('document_name') or fact.get('source_document'),
            document_id=fact.get('document_id'),
            confidence=fact.get('confidence', 0.0),
            raw_text=ref_data.get('raw_text') or fact_data.get('body')
        )
        
        # Generate formatted citations
        enhanced_ref.citation_apa = self._format_citation_apa(enhanced_ref)
        enhanced_ref.citation_mla = self._format_citation_mla(enhanced_ref)
        enhanced_ref.citation_chicago = self._format_citation_chicago(enhanced_ref)
        
        return enhanced_ref
    
    def _extract_reference_metadata(self, fact: Dict[str, Any]) -> Dict[str, Any]:
        """Extract reference metadata from fact."""
        metadata = {}
        
        # Check if fact has reference data
        if 'reference' in fact:
            ref_data = fact['reference']
            metadata.update(ref_data)
        
        # Check fact properties for reference info
        fact_data = fact.get('fact', {})
        properties = fact_data.get('properties', {})
        
        # Extract from properties
        for key in ['title', 'authors', 'year', 'journal', 'doi', 'pmid', 'url']:
            if key in properties:
                metadata[key] = properties[key]
        
        # Try to parse reference from text
        text = fact_data.get('body', '') or fact_data.get('summary', '')
        if text:
            parsed_ref = self._parse_reference_from_text(text)
            metadata.update(parsed_ref)
        
        return metadata
    
    def _parse_reference_from_text(self, text: str) -> Dict[str, Any]:
        """Parse reference information from text."""
        metadata = {}
        
        # DOI pattern
        doi_match = re.search(r'(?:doi:|DOI:)\s*([10]\.\d+/[^\s]+)', text, re.IGNORECASE)
        if doi_match:
            metadata['doi'] = doi_match.group(1)
        
        # PMID pattern
        pmid_match = re.search(r'(?:pmid:|PMID:)\s*(\d+)', text, re.IGNORECASE)
        if pmid_match:
            metadata['pmid'] = pmid_match.group(1)
        
        # Year pattern
        year_match = re.search(r'\b(19|20)\d{2}\b', text)
        if year_match:
            metadata['year'] = year_match.group(0)
        
        # Journal pattern (simplified)
        journal_patterns = [
            r'(?:in|from)\s+([A-Z][^.]+(?:Journal|Review|Medicine|Science|Research))',
            r'([A-Z][^.]+(?:Journal|Review|Medicine|Science|Research))',
        ]
        
        for pattern in journal_patterns:
            journal_match = re.search(pattern, text, re.IGNORECASE)
            if journal_match:
                metadata['journal'] = journal_match.group(1).strip()
                break
        
        return metadata
    
    def _format_citation_apa(self, ref: EnhancedReference) -> str:
        """Format citation in APA style."""
        parts = []
        
        # Authors
        if ref.authors:
            parts.append(ref.authors)
        
        # Year
        if ref.year:
            parts.append(f"({ref.year})")
        
        # Title
        if ref.title:
            parts.append(f"{ref.title}.")
        
        # Journal info
        if ref.journal:
            journal_part = f"*{ref.journal}*"
            if ref.volume:
                journal_part += f", {ref.volume}"
                if ref.issue:
                    journal_part += f"({ref.issue})"
            if ref.pages:
                journal_part += f", {ref.pages}"
            parts.append(journal_part + ".")
        
        # DOI
        if ref.doi:
            parts.append(f"https://doi.org/{ref.doi}")
        
        return " ".join(parts) if parts else f"Reference {ref.id}"
    
    def _format_citation_mla(self, ref: EnhancedReference) -> str:
        """Format citation in MLA style."""
        parts = []
        
        # Authors (Last, First)
        if ref.authors:
            parts.append(f"{ref.authors}.")
        
        # Title
        if ref.title:
            parts.append(f'"{ref.title}."')
        
        # Journal
        if ref.journal:
            journal_part = f"*{ref.journal}*"
            if ref.volume:
                journal_part += f", vol. {ref.volume}"
                if ref.issue:
                    journal_part += f", no. {ref.issue}"
            if ref.year:
                journal_part += f", {ref.year}"
            if ref.pages:
                journal_part += f", pp. {ref.pages}"
            parts.append(journal_part + ".")
        
        # DOI
        if ref.doi:
            parts.append(f"doi:{ref.doi}")
        
        return " ".join(parts) if parts else f"Reference {ref.id}"
    
    def _format_citation_chicago(self, ref: EnhancedReference) -> str:
        """Format citation in Chicago style."""
        parts = []
        
        # Authors
        if ref.authors:
            parts.append(f"{ref.authors}.")
        
        # Title
        if ref.title:
            parts.append(f'"{ref.title}."')
        
        # Journal
        if ref.journal:
            journal_part = f"*{ref.journal}*"
            if ref.volume:
                journal_part += f" {ref.volume}"
                if ref.issue:
                    journal_part += f", no. {ref.issue}"
            if ref.year:
                journal_part += f" ({ref.year})"
            if ref.pages:
                journal_part += f": {ref.pages}"
            parts.append(journal_part + ".")
        
        # DOI
        if ref.doi:
            parts.append(f"https://doi.org/{ref.doi}")
        
        return " ".join(parts) if parts else f"Reference {ref.id}"
    
    def _generate_citation_section(
        self,
        references: List[EnhancedReference],
        citation_style: str
    ) -> str:
        """Generate the citation section for the response."""
        if not references:
            return ""
        
        citation_lines = []
        citation_lines.append("## References\n")
        
        for ref in references:
            # Get formatted citation
            if citation_style == "apa":
                citation = ref.citation_apa
            elif citation_style == "mla":
                citation = ref.citation_mla
            elif citation_style == "chicago":
                citation = ref.citation_chicago
            else:
                citation = ref.citation_apa  # Default to APA
            
            # Create clickable links
            enhanced_citation = self._add_clickable_links(citation, ref)
            
            citation_lines.append(f"[{ref.id}] {enhanced_citation}")
            
            # Add abstract if available
            if ref.abstract and len(ref.abstract) > 50:
                abstract_preview = ref.abstract[:200] + "..." if len(ref.abstract) > 200 else ref.abstract
                citation_lines.append(f"    *Abstract: {abstract_preview}*")
            
            # Add source document link
            if ref.source_document:
                if ref.document_id:
                    citation_lines.append(f"    *Source: [{ref.source_document}](/documents/{ref.document_id})*")
                else:
                    citation_lines.append(f"    *Source: {ref.source_document}*")
            
            citation_lines.append("")  # Empty line between references
        
        return "\n".join(citation_lines)
    
    def _add_clickable_links(self, citation: str, ref: EnhancedReference) -> str:
        """Add clickable links to citation."""
        enhanced_citation = citation
        
        # Make DOI clickable
        if ref.doi:
            doi_pattern = rf"(?:https://doi\.org/)?{re.escape(ref.doi)}"
            enhanced_citation = re.sub(
                doi_pattern,
                f'<a href="https://doi.org/{ref.doi}" target="_blank" class="text-primary">{ref.doi}</a>',
                enhanced_citation
            )
        
        # Make PMID clickable
        if ref.pmid:
            pmid_pattern = rf"(?:pmid:)?{re.escape(ref.pmid)}"
            enhanced_citation = re.sub(
                pmid_pattern,
                f'<a href="https://pubmed.ncbi.nlm.nih.gov/{ref.pmid}" target="_blank" class="text-primary">PMID: {ref.pmid}</a>',
                enhanced_citation,
                flags=re.IGNORECASE
            )
        
        # Make URLs clickable
        if ref.url and ref.url not in enhanced_citation:
            enhanced_citation += f' <a href="{ref.url}" target="_blank" class="text-primary">[Link]</a>'
        
        return enhanced_citation
    
    def _enhance_answer_text(
        self,
        answer_text: str,
        reference_map: Dict[int, EnhancedReference]
    ) -> str:
        """Enhance answer text with better reference formatting."""
        enhanced_text = answer_text
        
        # Replace [n] with clickable reference links
        def replace_reference(match):
            ref_num = int(match.group(1))
            if ref_num in reference_map:
                ref = reference_map[ref_num]
                title = ref.title or f"Reference {ref_num}"
                # Create a tooltip with reference info
                tooltip_text = f"{ref.authors or 'Unknown authors'} ({ref.year or 'Unknown year'})"
                return f'<sup><a href="#ref-{ref_num}" class="text-primary" title="{tooltip_text}">[{ref_num}]</a></sup>'
            return match.group(0)
        
        enhanced_text = re.sub(r'\[(\d+)\]', replace_reference, enhanced_text)
        
        return enhanced_text
    
    def generate_reference_summary(self, references: List[EnhancedReference]) -> Dict[str, Any]:
        """Generate a summary of references."""
        if not references:
            return {"total": 0, "summary": "No references found."}
        
        # Count by type
        journal_count = sum(1 for ref in references if ref.journal)
        doi_count = sum(1 for ref in references if ref.doi)
        recent_count = sum(1 for ref in references if ref.year and int(ref.year) >= 2020)
        
        # Get journals
        journals = list(set(ref.journal for ref in references if ref.journal))
        
        summary_parts = [
            f"**{len(references)} references total**",
            f"• {journal_count} from peer-reviewed journals" if journal_count > 0 else None,
            f"• {doi_count} with DOI links" if doi_count > 0 else None,
            f"• {recent_count} from 2020 or later" if recent_count > 0 else None,
            f"• Key journals: {', '.join(journals[:3])}" + ("..." if len(journals) > 3 else "") if journals else None
        ]
        
        summary = "\n".join(part for part in summary_parts if part)
        
        return {
            "total": len(references),
            "journal_count": journal_count,
            "doi_count": doi_count,
            "recent_count": recent_count,
            "journals": journals,
            "summary": summary
        }


# Global instance
_citation_service = None


def get_citation_service() -> EnhancedCitationService:
    """Get the global citation service."""
    global _citation_service
    if _citation_service is None:
        _citation_service = EnhancedCitationService()
    return _citation_service
