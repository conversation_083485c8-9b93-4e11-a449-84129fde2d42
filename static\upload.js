/**
 * Enhanced upload functionality with metadata and reference extraction
 */

document.addEventListener('DOMContentLoaded', function() {
    // Elements
    const dropzone = document.getElementById('dropzone');
    const fileInput = document.getElementById('file-input');
    const fileList = document.getElementById('file-list');
    const uploadButton = document.getElementById('upload-button');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const alertContainer = document.getElementById('alert-container');
    const chunkSizeInput = document.getElementById('chunk-size');
    const overlapInput = document.getElementById('overlap');
    const extractMetadataCheckbox = document.getElementById('extract-metadata');
    const extractReferencesCheckbox = document.getElementById('extract-references');
    
    // Store files to upload
    let filesToUpload = [];
    
    // Processing status check intervals
    const processingIntervals = {};
    
    // Handle file drop
    dropzone.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('dragover');
    });
    
    dropzone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');
    });
    
    dropzone.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');
        handleDrop(e);
    });
    
    // Handle click on dropzone
    dropzone.addEventListener('click', function() {
        fileInput.click();
    });
    
    function handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        handleFiles(files);
    }
    
    // Handle file input change
    fileInput.addEventListener('change', function() {
        handleFiles(this.files);
    });
    
    // Handle files
    function handleFiles(files) {
        if (files.length > 0) {
            // Filter for PDF files only
            const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');
            
            if (pdfFiles.length === 0) {
                showAlert('Please select PDF files only.', 'danger');
                return;
            }
            
            // Add files to the list
            filesToUpload = [...filesToUpload, ...pdfFiles];
            updateFileList();
        }
    }
    
    // Update file list
    function updateFileList() {
        fileList.innerHTML = '';
        
        filesToUpload.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item d-flex justify-content-between align-items-center p-2 border-bottom';
            
            const fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            fileInfo.innerHTML = `
                <span class="file-name">${file.name}</span>
                <span class="file-size text-muted">(${formatFileSize(file.size)})</span>
            `;
            
            const removeButton = document.createElement('button');
            removeButton.className = 'btn btn-sm btn-outline-danger';
            removeButton.innerHTML = '<i class="bi bi-x"></i>';
            removeButton.addEventListener('click', () => {
                filesToUpload.splice(index, 1);
                updateFileList();
            });
            
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(removeButton);
            fileList.appendChild(fileItem);
        });
        
        // Show/hide upload button
        if (filesToUpload.length > 0) {
            uploadButton.classList.remove('d-none');
        } else {
            uploadButton.classList.add('d-none');
        }
    }
    
    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // Show alert
    function showAlert(message, type) {
        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;
        
        alertContainer.appendChild(alert);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => {
                alert.remove();
            }, 150);
        }, 5000);
    }
    
    // Handle upload
    uploadButton.addEventListener('click', async function() {
        // Validate inputs
        const chunkSize = parseInt(chunkSizeInput.value);
        const overlap = parseInt(overlapInput.value);
        const extractMetadata = extractMetadataCheckbox.checked;
        const extractReferences = extractReferencesCheckbox.checked;
        
        if (isNaN(chunkSize) || chunkSize < 100 || chunkSize > 10000) {
            showAlert('Chunk size must be between 100 and 10000 characters.', 'danger');
            return;
        }
        
        if (isNaN(overlap) || overlap < 0 || overlap > 500) {
            showAlert('Overlap must be between 0 and 500 characters.', 'danger');
            return;
        }
        
        // Show progress
        progressContainer.classList.remove('d-none');
        progressBar.style.width = '0%';
        progressBar.setAttribute('aria-valuenow', 0);
        
        // Disable upload button
        uploadButton.disabled = true;
        
        // Upload files one by one
        for (let i = 0; i < filesToUpload.length; i++) {
            const file = filesToUpload[i];
            
            // Create form data
            const formData = new FormData();
            formData.append('file', file);
            formData.append('chunk_size', chunkSize);
            formData.append('overlap', overlap);
            formData.append('extract_metadata', extractMetadata);
            formData.append('extract_references', extractReferences);
            
            try {
                // Upload file
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    // Update progress
                    const progress = Math.round(((i + 1) / filesToUpload.length) * 100);
                    progressBar.style.width = `${progress}%`;
                    progressBar.setAttribute('aria-valuenow', progress);
                    
                    // Show success for this file
                    let successMessage = `Successfully uploaded ${file.name}. Processing started in the background.`;
                    if (extractMetadata) {
                        successMessage += ' Metadata extraction enabled.';
                    }
                    if (extractReferences) {
                        successMessage += ' Reference extraction enabled.';
                    }
                    showAlert(successMessage, 'success');
                    
                    // Start checking processing status
                    startProcessingStatusCheck(result.id, file.name);
                } else {
                    // Show error for this file
                    showAlert(`Error processing ${file.name}: ${result.error}`, 'danger');
                }
            } catch (error) {
                console.error('Error uploading file:', error);
                showAlert(`Error uploading ${file.name}: ${error.message}`, 'danger');
            }
        }
        
        // Reset file list
        filesToUpload = [];
        updateFileList();
        
        // Enable upload button
        uploadButton.disabled = false;
    });
    
    // Start checking processing status
    function startProcessingStatusCheck(fileId, fileName) {
        // Check every 5 seconds
        const intervalId = setInterval(async () => {
            try {
                const response = await fetch(`/api/processing-status/${fileId}`);
                const result = await response.json();
                
                if (response.ok) {
                    if (result.status === 'completed') {
                        // Processing completed
                        clearInterval(processingIntervals[fileId]);
                        delete processingIntervals[fileId];
                        
                        if (result.success) {
                            let message = `Processing completed for ${fileName}: ${result.chunks} chunks created.`;
                            
                            if (result.metadata_extracted) {
                                message += ' Metadata extracted.';
                            }
                            
                            if (result.references_extracted) {
                                message += ` ${result.reference_count} references extracted.`;
                            }
                            
                            showAlert(message, 'success');
                            
                            // Refresh documents list if available
                            if (typeof refreshDocumentsList === 'function') {
                                refreshDocumentsList();
                            }
                        } else {
                            showAlert(`Error processing ${fileName}`, 'danger');
                        }
                    }
                    // If still processing, continue checking
                } else {
                    // Error checking status
                    clearInterval(processingIntervals[fileId]);
                    delete processingIntervals[fileId];
                    showAlert(`Error checking processing status for ${fileName}: ${result.error}`, 'warning');
                }
            } catch (error) {
                console.error('Error checking processing status:', error);
                // Don't clear the interval, try again later
            }
        }, 5000);
        
        // Store interval ID
        processingIntervals[fileId] = intervalId;
    }
});
