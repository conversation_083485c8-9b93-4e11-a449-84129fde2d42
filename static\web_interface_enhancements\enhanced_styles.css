
/* Enhanced styles for the knowledge graph visualization */

/* Taxonomy visualization */
.taxonomy-tree {
    font-family: 'Arial', sans-serif;
    margin: 20px 0;
}

.taxonomy-node {
    margin: 5px 0;
    padding: 8px;
    border-radius: 4px;
    background-color: #f5f5f5;
    border-left: 4px solid #4285f4;
    transition: all 0.3s ease;
}

.taxonomy-node:hover {
    background-color: #e0e0e0;
    transform: translateX(5px);
}

.taxonomy-node.level-1 {
    margin-left: 0;
    font-weight: bold;
    background-color: #e8f0fe;
    border-left-color: #4285f4;
}

.taxonomy-node.level-2 {
    margin-left: 20px;
    background-color: #e6f4ea;
    border-left-color: #34a853;
}

.taxonomy-node.level-3 {
    margin-left: 40px;
    background-color: #fef7e0;
    border-left-color: #fbbc04;
}

.taxonomy-node.level-4 {
    margin-left: 60px;
    background-color: #fce8e6;
    border-left-color: #ea4335;
}

.taxonomy-count {
    float: right;
    background-color: #4285f4;
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8em;
}

/* Relationship visualization */
.relationship-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.relationship-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transform: translateY(-2px);
}

.relationship-type {
    font-weight: bold;
    color: #4285f4;
    margin-bottom: 5px;
}

.relationship-entities {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

.entity-source, .entity-target {
    padding: 5px 10px;
    border-radius: 4px;
    background-color: #e8f0fe;
}

.relationship-arrow {
    margin: 0 10px;
    color: #5f6368;
    font-size: 1.2em;
}

.relationship-evidence {
    font-style: italic;
    color: #5f6368;
    margin-top: 10px;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 4px;
}

.confidence-meter {
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin: 5px 0;
    overflow: hidden;
}

.confidence-level {
    height: 100%;
    background-color: #4285f4;
}

.confidence-text {
    font-size: 0.8em;
    color: #5f6368;
    text-align: right;
}

/* Entity attributes visualization */
.entity-attributes {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    background-color: #f8f9fa;
}

.attribute-name {
    font-weight: bold;
    color: #4285f4;
}

.attribute-value {
    margin-bottom: 10px;
}

/* Tabs for different visualizations */
.visualization-tabs {
    display: flex;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 20px;
}

.visualization-tab {
    padding: 10px 20px;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.visualization-tab:hover {
    background-color: #f5f5f5;
}

.visualization-tab.active {
    border-bottom-color: #4285f4;
    font-weight: bold;
}

.visualization-content {
    display: none;
}

.visualization-content.active {
    display: block;
}

/* Search enhancements */
.advanced-search {
    margin: 20px 0;
    padding: 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background-color: #f8f9fa;
}

.search-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.search-filter {
    display: flex;
    align-items: center;
}

.search-filter label {
    margin-right: 5px;
    font-weight: bold;
}

.search-filter select, .search-filter input {
    padding: 5px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

.search-buttons {
    display: flex;
    gap: 10px;
}

.search-button {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    background-color: #4285f4;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.search-button:hover {
    background-color: #3367d6;
}

.search-button.secondary {
    background-color: #5f6368;
}

.search-button.secondary:hover {
    background-color: #494c50;
}
    