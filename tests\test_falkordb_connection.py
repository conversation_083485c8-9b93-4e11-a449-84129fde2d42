"""
Test FalkorDB connection with Neo4j driver
"""

import os
import sys
from dotenv import load_dotenv
from neo4j import GraphDatabase

# Load environment variables
load_dotenv('.env.falkordb')

# Get connection details
# When running outside Docker, use localhost with port 7688
falkordb_uri = 'bolt://localhost:7688'
falkordb_user = 'default'
falkordb_password = 'Triathlon16!'

print(f"Trying to connect to FalkorDB at {falkordb_uri} with user {falkordb_user}")

try:
    # Try to connect using Neo4j driver with the correct authentication scheme
    # FalkorDB may require a different authentication method
    # Let's try without authentication first
    driver = GraphDatabase.driver(falkordb_uri)

    # Test the connection
    with driver.session() as session:
        result = session.run("RETURN 'Connected to FalkorDB!' AS message")
        record = result.single()
        print(record["message"])

    # Test creating a node
    with driver.session() as session:
        result = session.run("""
        CREATE (n:TestNode {name: 'FalkorDB Test', created: timestamp()})
        RETURN n.name AS name
        """)
        record = result.single()
        print(f"Created test node: {record['name']}")

    # Test querying the node
    with driver.session() as session:
        result = session.run("""
        MATCH (n:TestNode {name: 'FalkorDB Test'})
        RETURN n.name AS name, n.created AS created
        """)
        record = result.single()
        if record:
            print(f"Found test node: {record['name']}, created at: {record['created']}")
        else:
            print("Test node not found")

    # Close the driver
    driver.close()
    print("Connection test successful!")
except Exception as e:
    print(f"Error connecting to FalkorDB: {e}")
    sys.exit(1)
