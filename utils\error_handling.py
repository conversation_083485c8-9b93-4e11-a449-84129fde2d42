"""
Error handling utilities for the Graphiti application.
"""

from fastapi import HTT<PERSON>Ex<PERSON>, status
from typing import Dict, Any, Optional, Type, Union

from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

class GraphitiError(Exception):
    """Base class for all Graphiti errors."""
    
    def __init__(self, message: str, status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            status_code: HTTP status code
            details: Additional error details
        """
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the error to a dictionary.
        
        Returns:
            Error dictionary
        """
        return {
            "message": self.message,
            "status_code": self.status_code,
            "details": self.details
        }
    
    def to_http_exception(self) -> HTTPException:
        """
        Convert the error to an HTTP exception.
        
        Returns:
            HTTP exception
        """
        return HTTPException(
            status_code=self.status_code,
            detail={
                "message": self.message,
                "details": self.details
            }
        )

class DatabaseError(GraphitiError):
    """Database-related errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_503_SERVICE_UNAVAILABLE, details)

class ValidationError(GraphitiError):
    """Validation errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_400_BAD_REQUEST, details)

class NotFoundError(GraphitiError):
    """Not found errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_404_NOT_FOUND, details)

class AuthenticationError(GraphitiError):
    """Authentication errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_401_UNAUTHORIZED, details)

class AuthorizationError(GraphitiError):
    """Authorization errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_403_FORBIDDEN, details)

class ProcessingError(GraphitiError):
    """Processing errors."""
    
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        """
        Initialize the error.
        
        Args:
            message: Error message
            details: Additional error details
        """
        super().__init__(message, status.HTTP_500_INTERNAL_SERVER_ERROR, details)

def handle_exception(exception: Exception) -> HTTPException:
    """
    Handle an exception and convert it to an HTTP exception.
    
    Args:
        exception: Exception to handle
        
    Returns:
        HTTP exception
    """
    if isinstance(exception, GraphitiError):
        logger.error(f"{exception.__class__.__name__}: {exception.message}")
        return exception.to_http_exception()
    elif isinstance(exception, HTTPException):
        logger.error(f"HTTPException: {exception.detail}")
        return exception
    else:
        logger.error(f"Unhandled exception: {str(exception)}")
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail={
                "message": "An unexpected error occurred",
                "details": {"error": str(exception)}
            }
        )
