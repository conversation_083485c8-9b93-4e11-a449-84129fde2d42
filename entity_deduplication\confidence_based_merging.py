"""
Confidence-based entity merging system.

This module implements probabilistic entity merging that:
- Uses confidence scores to make merging decisions
- Maintains uncertainty scores for borderline cases
- Provides different merging strategies based on confidence levels
- Tracks merge quality and provides rollback capabilities
"""

import logging
import time
import uuid
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from enum import Enum
import json

from entity_deduplication.models import EntityForDeduplication, EntityMatch

logger = logging.getLogger(__name__)


class MergeDecision(Enum):
    """Possible merge decisions."""
    MERGE = "merge"
    NO_MERGE = "no_merge"
    UNCERTAIN = "uncertain"
    DEFER = "defer"


class MergeStrategy(Enum):
    """Different merging strategies."""
    CONSERVATIVE = "conservative"  # High confidence required
    BALANCED = "balanced"         # Moderate confidence required
    AGGRESSIVE = "aggressive"     # Low confidence required
    ADAPTIVE = "adaptive"         # Adapts based on entity type and context


@dataclass
class MergeCandidate:
    """Represents a potential merge between two entities."""
    source_entity: EntityForDeduplication
    target_entity: EntityForDeduplication
    similarity_score: float
    confidence_score: float
    uncertainty_score: float
    decision: MergeDecision
    reasoning: str
    timestamp: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'source_uuid': self.source_entity.uuid,
            'target_uuid': self.target_entity.uuid,
            'similarity_score': self.similarity_score,
            'confidence_score': self.confidence_score,
            'uncertainty_score': self.uncertainty_score,
            'decision': self.decision.value,
            'reasoning': self.reasoning,
            'timestamp': self.timestamp
        }


@dataclass
class MergeResult:
    """Result of a merge operation."""
    merged_entity: Optional[EntityForDeduplication]
    source_entities: List[EntityForDeduplication]
    confidence_score: float
    uncertainty_score: float
    merge_quality: float
    metadata: Dict[str, Any]
    rollback_info: Dict[str, Any]


class ConfidenceBasedMerger:
    """
    Implements confidence-based entity merging with uncertainty handling.
    """
    
    def __init__(
        self,
        strategy: MergeStrategy = MergeStrategy.BALANCED,
        confidence_thresholds: Optional[Dict[str, float]] = None,
        uncertainty_thresholds: Optional[Dict[str, float]] = None
    ):
        """
        Initialize the confidence-based merger.
        
        Args:
            strategy: Merging strategy to use
            confidence_thresholds: Custom confidence thresholds for decisions
            uncertainty_thresholds: Custom uncertainty thresholds
        """
        self.strategy = strategy
        
        # Default confidence thresholds for different decisions
        self.confidence_thresholds = confidence_thresholds or {
            'conservative': {'merge': 0.9, 'uncertain': 0.7, 'no_merge': 0.5},
            'balanced': {'merge': 0.8, 'uncertain': 0.6, 'no_merge': 0.4},
            'aggressive': {'merge': 0.7, 'uncertain': 0.5, 'no_merge': 0.3},
            'adaptive': {'merge': 0.8, 'uncertain': 0.6, 'no_merge': 0.4}  # Base values, adjusted per entity type
        }
        
        # Uncertainty thresholds
        self.uncertainty_thresholds = uncertainty_thresholds or {
            'high_uncertainty': 0.3,
            'medium_uncertainty': 0.2,
            'low_uncertainty': 0.1
        }
        
        # Track merge history for learning
        self.merge_history: List[MergeCandidate] = []
        self.uncertain_cases: List[MergeCandidate] = []
        
    def evaluate_merge_candidate(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        similarity_score: float,
        similarity_components: Optional[Dict[str, float]] = None
    ) -> MergeCandidate:
        """
        Evaluate whether two entities should be merged.
        
        Args:
            entity1: First entity
            entity2: Second entity
            similarity_score: Overall similarity score
            similarity_components: Breakdown of similarity components
            
        Returns:
            MergeCandidate with decision and confidence scores
        """
        # Calculate confidence score for this merge
        confidence_score = self._calculate_merge_confidence(
            entity1, entity2, similarity_score, similarity_components
        )
        
        # Calculate uncertainty score
        uncertainty_score = self._calculate_uncertainty(
            entity1, entity2, similarity_score, similarity_components
        )
        
        # Make merge decision
        decision, reasoning = self._make_merge_decision(
            entity1, entity2, confidence_score, uncertainty_score, similarity_score
        )
        
        candidate = MergeCandidate(
            source_entity=entity1,
            target_entity=entity2,
            similarity_score=similarity_score,
            confidence_score=confidence_score,
            uncertainty_score=uncertainty_score,
            decision=decision,
            reasoning=reasoning,
            timestamp=time.time()
        )
        
        # Track the candidate
        self.merge_history.append(candidate)
        if decision == MergeDecision.UNCERTAIN:
            self.uncertain_cases.append(candidate)
        
        return candidate
    
    def _calculate_merge_confidence(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        similarity_score: float,
        similarity_components: Optional[Dict[str, float]] = None
    ) -> float:
        """Calculate confidence score for merging these entities."""
        confidence_factors = []
        
        # 1. Base similarity confidence
        confidence_factors.append(similarity_score)
        
        # 2. Entity confidence scores
        entity1_conf = entity1.confidence or 0.5
        entity2_conf = entity2.confidence or 0.5
        avg_entity_conf = (entity1_conf + entity2_conf) / 2
        confidence_factors.append(avg_entity_conf)
        
        # 3. Type consistency confidence
        if entity1.type == entity2.type:
            confidence_factors.append(0.9)
        else:
            confidence_factors.append(0.3)
        
        # 4. Source document confidence
        if entity1.source_document_id and entity2.source_document_id:
            if entity1.source_document_id == entity2.source_document_id:
                # Same document - lower confidence for merging (likely different entities)
                confidence_factors.append(0.4)
            else:
                # Different documents - higher confidence for merging
                confidence_factors.append(0.8)
        else:
            confidence_factors.append(0.6)  # Neutral
        
        # 5. Attribute consistency confidence
        if entity1.attributes and entity2.attributes:
            attr_consistency = self._calculate_attribute_consistency(
                entity1.attributes, entity2.attributes
            )
            confidence_factors.append(attr_consistency)
        
        # 6. Component-based confidence (if available)
        if similarity_components:
            # Higher confidence if multiple components agree
            high_components = sum(1 for score in similarity_components.values() if score > 0.7)
            component_confidence = min(1.0, high_components / len(similarity_components))
            confidence_factors.append(component_confidence)
        
        # Calculate weighted average with emphasis on similarity and entity confidence
        weights = [0.3, 0.25, 0.15, 0.1, 0.1, 0.1][:len(confidence_factors)]
        weighted_confidence = sum(w * f for w, f in zip(weights, confidence_factors))
        
        return max(0.0, min(1.0, weighted_confidence))
    
    def _calculate_uncertainty(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        similarity_score: float,
        similarity_components: Optional[Dict[str, float]] = None
    ) -> float:
        """Calculate uncertainty score for this merge decision."""
        uncertainty_factors = []
        
        # 1. Similarity score uncertainty (higher uncertainty near decision boundaries)
        thresholds = self.confidence_thresholds[self.strategy.value]
        merge_threshold = thresholds['merge']
        uncertain_threshold = thresholds['uncertain']
        
        if abs(similarity_score - merge_threshold) < 0.1:
            uncertainty_factors.append(0.8)
        elif abs(similarity_score - uncertain_threshold) < 0.1:
            uncertainty_factors.append(0.6)
        else:
            uncertainty_factors.append(0.2)
        
        # 2. Entity confidence uncertainty
        entity1_conf = entity1.confidence or 0.5
        entity2_conf = entity2.confidence or 0.5
        conf_diff = abs(entity1_conf - entity2_conf)
        uncertainty_factors.append(conf_diff)  # Higher difference = higher uncertainty
        
        # 3. Missing information uncertainty
        missing_info = 0
        if not entity1.description or not entity2.description:
            missing_info += 0.2
        if not entity1.attributes or not entity2.attributes:
            missing_info += 0.2
        if not entity1.embedding or not entity2.embedding:
            missing_info += 0.3
        uncertainty_factors.append(missing_info)
        
        # 4. Component disagreement uncertainty
        if similarity_components:
            component_values = list(similarity_components.values())
            if len(component_values) > 1:
                component_std = self._calculate_std(component_values)
                uncertainty_factors.append(component_std)
        
        # 5. Type mismatch uncertainty
        if entity1.type != entity2.type:
            uncertainty_factors.append(0.7)
        else:
            uncertainty_factors.append(0.1)
        
        # Calculate average uncertainty
        avg_uncertainty = sum(uncertainty_factors) / len(uncertainty_factors)
        return max(0.0, min(1.0, avg_uncertainty))
    
    def _calculate_std(self, values: List[float]) -> float:
        """Calculate standard deviation of values."""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5
    
    def _calculate_attribute_consistency(
        self,
        attrs1: Dict[str, Any],
        attrs2: Dict[str, Any]
    ) -> float:
        """Calculate consistency between entity attributes."""
        common_keys = set(attrs1.keys()).intersection(set(attrs2.keys()))
        if not common_keys:
            return 0.5  # Neutral when no common attributes
        
        consistent_count = 0
        for key in common_keys:
            val1, val2 = attrs1[key], attrs2[key]
            if val1 == val2:
                consistent_count += 1
            elif isinstance(val1, str) and isinstance(val2, str):
                # String similarity for text attributes
                from entity_deduplication.utils import calculate_string_similarity
                if calculate_string_similarity(val1, val2) > 0.8:
                    consistent_count += 0.8
        
        return consistent_count / len(common_keys)
    
    def _make_merge_decision(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        confidence_score: float,
        uncertainty_score: float,
        similarity_score: float
    ) -> Tuple[MergeDecision, str]:
        """Make the final merge decision based on confidence and uncertainty."""
        # Get thresholds for current strategy
        thresholds = self.confidence_thresholds[self.strategy.value]
        
        # Adjust thresholds for adaptive strategy
        if self.strategy == MergeStrategy.ADAPTIVE:
            thresholds = self._get_adaptive_thresholds(entity1, entity2)
        
        # Apply uncertainty penalty to confidence
        adjusted_confidence = confidence_score * (1 - uncertainty_score * 0.5)
        
        # Make decision
        if adjusted_confidence >= thresholds['merge'] and uncertainty_score < self.uncertainty_thresholds['medium_uncertainty']:
            return MergeDecision.MERGE, f"High confidence ({adjusted_confidence:.3f}) and low uncertainty ({uncertainty_score:.3f})"
        
        elif uncertainty_score > self.uncertainty_thresholds['high_uncertainty']:
            return MergeDecision.UNCERTAIN, f"High uncertainty ({uncertainty_score:.3f}) requires manual review"
        
        elif adjusted_confidence >= thresholds['uncertain']:
            return MergeDecision.UNCERTAIN, f"Moderate confidence ({adjusted_confidence:.3f}) with some uncertainty ({uncertainty_score:.3f})"
        
        elif adjusted_confidence >= thresholds['no_merge']:
            return MergeDecision.DEFER, f"Low confidence ({adjusted_confidence:.3f}) - defer decision"
        
        else:
            return MergeDecision.NO_MERGE, f"Very low confidence ({adjusted_confidence:.3f}) - do not merge"
    
    def _get_adaptive_thresholds(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> Dict[str, float]:
        """Get adaptive thresholds based on entity types and context."""
        base_thresholds = self.confidence_thresholds['adaptive'].copy()
        
        # Adjust based on entity type
        entity_type = entity1.type if entity1.type == entity2.type else 'mixed'
        
        type_adjustments = {
            'Person': {'merge': +0.05, 'uncertain': +0.05, 'no_merge': +0.05},  # More conservative for people
            'Location': {'merge': +0.03, 'uncertain': +0.03, 'no_merge': +0.03},  # Slightly more conservative
            'Organization': {'merge': 0.0, 'uncertain': 0.0, 'no_merge': 0.0},  # Neutral
            'mixed': {'merge': +0.1, 'uncertain': +0.1, 'no_merge': +0.1}  # Very conservative for mixed types
        }
        
        adjustments = type_adjustments.get(entity_type, {'merge': 0.0, 'uncertain': 0.0, 'no_merge': 0.0})
        
        for key, adjustment in adjustments.items():
            base_thresholds[key] = max(0.0, min(1.0, base_thresholds[key] + adjustment))
        
        return base_thresholds
    
    def perform_merge(
        self,
        merge_candidate: MergeCandidate,
        merge_strategy: str = "preserve_highest_confidence"
    ) -> MergeResult:
        """
        Perform the actual merge of entities.
        
        Args:
            merge_candidate: The merge candidate to execute
            merge_strategy: How to merge the entities ("preserve_highest_confidence", "combine_attributes", etc.)
            
        Returns:
            MergeResult with the merged entity and metadata
        """
        if merge_candidate.decision != MergeDecision.MERGE:
            raise ValueError(f"Cannot merge entities with decision: {merge_candidate.decision}")
        
        source = merge_candidate.source_entity
        target = merge_candidate.target_entity
        
        # Create merged entity
        merged_entity = self._create_merged_entity(source, target, merge_strategy)
        
        # Calculate merge quality
        merge_quality = self._calculate_merge_quality(source, target, merged_entity)
        
        # Create rollback information
        rollback_info = {
            'source_entity': source,
            'target_entity': target,
            'merge_timestamp': time.time(),
            'merge_strategy': merge_strategy
        }
        
        return MergeResult(
            merged_entity=merged_entity,
            source_entities=[source, target],
            confidence_score=merge_candidate.confidence_score,
            uncertainty_score=merge_candidate.uncertainty_score,
            merge_quality=merge_quality,
            metadata={
                'similarity_score': merge_candidate.similarity_score,
                'reasoning': merge_candidate.reasoning,
                'strategy': merge_strategy
            },
            rollback_info=rollback_info
        )
    
    def _create_merged_entity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        strategy: str
    ) -> EntityForDeduplication:
        """Create a merged entity from two entities."""
        if strategy == "preserve_highest_confidence":
            # Use the entity with higher confidence as base
            base_entity = entity1 if (entity1.confidence or 0) >= (entity2.confidence or 0) else entity2
            other_entity = entity2 if base_entity == entity1 else entity1
        else:
            # Default to first entity as base
            base_entity = entity1
            other_entity = entity2
        
        # Create merged entity
        merged = EntityForDeduplication(
            uuid=str(uuid.uuid4()),  # New UUID for merged entity
            name=self._merge_names(base_entity.name, other_entity.name),
            type=base_entity.type,
            description=self._merge_descriptions(base_entity.description, other_entity.description),
            confidence=max(entity1.confidence or 0, entity2.confidence or 0),
            source_document_id=base_entity.source_document_id,  # Preserve primary source
            source_fact_id=base_entity.source_fact_id,
            attributes=self._merge_attributes(base_entity.attributes, other_entity.attributes),
            created_at=base_entity.created_at,
            embedding=base_entity.embedding  # Use primary entity's embedding
        )
        
        return merged
    
    def _merge_names(self, name1: str, name2: str) -> str:
        """Merge entity names, preferring the more complete one."""
        if not name1:
            return name2
        if not name2:
            return name1
        
        # Prefer the longer, more descriptive name
        return name1 if len(name1) >= len(name2) else name2
    
    def _merge_descriptions(self, desc1: Optional[str], desc2: Optional[str]) -> Optional[str]:
        """Merge entity descriptions."""
        if not desc1:
            return desc2
        if not desc2:
            return desc1
        
        # Combine descriptions if they're different
        if desc1.lower() != desc2.lower():
            return f"{desc1}; {desc2}"
        return desc1
    
    def _merge_attributes(
        self,
        attrs1: Optional[Dict[str, Any]],
        attrs2: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Merge entity attributes."""
        if not attrs1:
            return attrs2
        if not attrs2:
            return attrs1
        
        # Combine attributes, preferring non-null values
        merged = attrs1.copy()
        for key, value in attrs2.items():
            if key not in merged or merged[key] is None:
                merged[key] = value
        
        return merged
    
    def _calculate_merge_quality(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        merged_entity: EntityForDeduplication
    ) -> float:
        """Calculate the quality of the merge operation."""
        quality_factors = []
        
        # Information preservation
        info_preserved = 0
        if merged_entity.name:
            info_preserved += 0.3
        if merged_entity.description:
            info_preserved += 0.3
        if merged_entity.attributes:
            info_preserved += 0.2
        if merged_entity.embedding:
            info_preserved += 0.2
        quality_factors.append(info_preserved)
        
        # Confidence improvement
        original_max_conf = max(entity1.confidence or 0, entity2.confidence or 0)
        merged_conf = merged_entity.confidence or 0
        conf_improvement = merged_conf / original_max_conf if original_max_conf > 0 else 1.0
        quality_factors.append(min(1.0, conf_improvement))
        
        return sum(quality_factors) / len(quality_factors)
    
    def get_uncertain_cases(self) -> List[MergeCandidate]:
        """Get all cases marked as uncertain for manual review."""
        return self.uncertain_cases.copy()
    
    def get_merge_statistics(self) -> Dict[str, Any]:
        """Get statistics about merge decisions."""
        if not self.merge_history:
            return {}
        
        decisions = [candidate.decision for candidate in self.merge_history]
        decision_counts = {decision.value: decisions.count(decision) for decision in MergeDecision}
        
        avg_confidence = sum(c.confidence_score for c in self.merge_history) / len(self.merge_history)
        avg_uncertainty = sum(c.uncertainty_score for c in self.merge_history) / len(self.merge_history)
        
        return {
            'total_evaluations': len(self.merge_history),
            'decision_counts': decision_counts,
            'average_confidence': avg_confidence,
            'average_uncertainty': avg_uncertainty,
            'uncertain_cases_count': len(self.uncertain_cases)
        }


# Global instance
_confidence_merger = None


def get_confidence_merger(
    strategy: MergeStrategy = MergeStrategy.BALANCED
) -> ConfidenceBasedMerger:
    """Get the global confidence-based merger instance."""
    global _confidence_merger
    if _confidence_merger is None or _confidence_merger.strategy != strategy:
        _confidence_merger = ConfidenceBasedMerger(strategy)
    return _confidence_merger
