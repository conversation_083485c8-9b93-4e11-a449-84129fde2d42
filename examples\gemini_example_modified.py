"""
Copyright 2024, Zep Software, Inc.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
"""

import asyncio
import logging
import os
from datetime import datetime, timezone

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.nodes import EpisodeType
from graphiti_core.utils.maintenance.graph_data_operations import clear_data

# Import the OpenAI client as a fallback since Gemini is having import issues
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig


def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )


async def main():
    # Load environment variables
    load_dotenv()
    setup_logging()

    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    # Get OpenAI API key from environment variable
    openai_api_key = os.environ.get('OPENAI_API_KEY')

    print("Initializing Graphiti with OpenAI client (fallback for Gemini)")
    print("Using Neo4j connection:", neo4j_uri)

    # Initialize Graphiti with OpenAI client as a fallback
    graphiti = Graphiti(
        neo4j_uri,
        neo4j_user,
        neo4j_password,
        llm_client=OpenAIClient(
            config=LLMConfig(
                api_key=openai_api_key,
                model="gpt-4o"
            )
        ),
        embedder=OpenAIEmbedder(
            config=OpenAIEmbedderConfig(
                api_key=openai_api_key,
                embedding_model="text-embedding-3-small"
            )
        )
    )

    print("Clearing existing data...")
    # Clear existing data (optional - remove this in production)
    await clear_data(graphiti.driver)

    print("Setting up indices and constraints...")
    # Set up indices and constraints
    await graphiti.build_indices_and_constraints()

    print("Adding example episode...")
    # Example: Add a simple episode
    await graphiti.add_episode(
        name="Example Episode",
        episode_body="This is a test episode created using OpenAI for LLM and embeddings.",
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        source_description="Example",
    )

    print("Performing search...")
    # Example: Perform a search
    search_results = await graphiti.search(
        "test episode",
        limit=5,
    )

    # Print search results
    print("\nSearch Results:")
    for i, result in enumerate(search_results.edges):
        print(f"{i+1}. {result.fact} (Score: {result.score})")


if __name__ == "__main__":
    asyncio.run(main())
