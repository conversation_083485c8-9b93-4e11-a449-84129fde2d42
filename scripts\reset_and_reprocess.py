"""
Master script to clear the Neo4j database and reprocess all PDFs.
"""

import os
import asyncio
import logging
import time
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

async def run_script(script_name):
    """Run a Python script and wait for it to complete."""
    logger.info(f"Running script: {script_name}")
    
    try:
        process = await asyncio.create_subprocess_exec(
            'python', script_name,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            logger.info(f"Successfully ran script: {script_name}")
            return True
        else:
            logger.error(f"Error running script {script_name}: {stderr.decode()}")
            return False
            
    except Exception as e:
        logger.error(f"Error running script {script_name}: {e}")
        return False

async def main():
    """Main function to clear the database and reprocess all PDFs."""
    try:
        # Record start time
        start_time = time.time()
        
        # Step 1: Clear the database
        logger.info("Step 1: Clearing the database")
        success = await run_script('clear_database.py')
        if not success:
            logger.error("Failed to clear the database. Aborting.")
            return
            
        # Step 2: Reprocess all PDFs
        logger.info("Step 2: Reprocessing all PDFs")
        success = await run_script('reprocess_pdfs.py')
        if not success:
            logger.error("Failed to reprocess PDFs.")
            return
            
        # Record end time and calculate duration
        end_time = time.time()
        duration = end_time - start_time
        hours, remainder = divmod(duration, 3600)
        minutes, seconds = divmod(remainder, 60)
        
        logger.info(f"Process completed in {int(hours)}h {int(minutes)}m {int(seconds)}s")
        logger.info("Database has been reset and all PDFs have been reprocessed successfully")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}")

if __name__ == "__main__":
    asyncio.run(main())
