#!/usr/bin/env python3
"""
Test token limits for entity extraction
"""

import asyncio
from entity_extraction.extractors.llm_extractor import LLMEntityExtractor

async def test_token_limits():
    extractor = LLMEntityExtractor()
    
    # Get the system prompt
    system_prompt = extractor.get_system_prompt()
    
    print(f"System prompt length: {len(system_prompt)} characters")
    print(f"Estimated system prompt tokens: ~{len(system_prompt) // 4} tokens")
    
    # Test with different text sizes
    test_texts = [
        "Short text for testing",
        "A" * 500,   # 500 chars
        "A" * 1000,  # 1000 chars
        "A" * 2000,  # 2000 chars
        "A" * 4000,  # 4000 chars
    ]
    
    for i, text in enumerate(test_texts):
        estimated_user_tokens = len(text) // 4
        total_estimated_tokens = (len(system_prompt) // 4) + estimated_user_tokens
        
        print(f"\nTest {i+1}:")
        print(f"  Text length: {len(text)} chars")
        print(f"  Estimated user tokens: ~{estimated_user_tokens}")
        print(f"  Total estimated input tokens: ~{total_estimated_tokens}")
        print(f"  Remaining for completion (max 2000): {2000 - (total_estimated_tokens % 8192)}")
        
        if total_estimated_tokens > 6000:
            print(f"  ⚠️  WARNING: High token usage!")
        if total_estimated_tokens > 8000:
            print(f"  🚨 CRITICAL: Likely hitting context limits!")

if __name__ == "__main__":
    asyncio.run(test_token_limits())
