<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Explorer - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Vis.js for Network Visualization -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <link href="https://unpkg.com/vis-network/styles/vis-network.min.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">

    <style>
        #graph-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
            background-color: #f8f9fa;
        }

        .graph-controls {
            margin-bottom: 20px;
        }

        .node-info-panel {
            height: 600px;
            overflow-y: auto;
            border: 1px solid #ccc;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }

        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Knowledge Graph Explorer</h1>

        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item active" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item">Knowledge Graph</li>
                <li class="breadcrumb-item active" aria-current="page">Explorer</li>
            </ol>
        </nav>

        <!-- Graph Controls -->
        <div class="row graph-controls">
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" id="node-search" class="form-control" placeholder="Search for nodes...">
                    <button class="btn btn-primary" id="search-button">Search</button>
                </div>
            </div>
            <div class="col-md-6">
                <div class="d-flex justify-content-end">
                    <div class="btn-group me-2">
                        <button class="btn btn-outline-secondary" id="zoom-in-button"><i class="bi bi-zoom-in"></i></button>
                        <button class="btn btn-outline-secondary" id="zoom-out-button"><i class="bi bi-zoom-out"></i></button>
                        <button class="btn btn-outline-secondary" id="fit-button"><i class="bi bi-arrows-fullscreen"></i></button>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary" id="export-button"><i class="bi bi-download"></i> Export</button>
                        <button class="btn btn-outline-primary" id="settings-button"><i class="bi bi-gear"></i> Settings</button>
                        <button class="btn btn-outline-danger" id="debug-button"><i class="bi bi-bug"></i> Debug</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graph Visualization -->
        <div class="row">
            <div class="col-md-9">
                <div id="graph-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading knowledge graph...</p>
                </div>
                <div id="graph-container"></div>
            </div>
            <div class="col-md-3">
                <div class="node-info-panel">
                    <h4>Node Information</h4>
                    <div id="node-info">
                        <p class="text-muted">Click on a node to see its details</p>
                    </div>

                    <hr>

                    <h5>Graph Statistics</h5>
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Nodes
                            <span class="badge bg-primary rounded-pill" id="node-count">0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Edges
                            <span class="badge bg-primary rounded-pill" id="edge-count">0</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            Entity Types
                            <span class="badge bg-primary rounded-pill" id="entity-type-count">0</span>
                        </li>
                    </ul>

                    <hr>

                    <h5>Legend</h5>
                    <div id="graph-legend">
                        <!-- Legend items will be added dynamically -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let network = null;
        let graphData = null;

        // Initialize when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM content loaded, initializing knowledge graph...");
            loadKnowledgeGraph();

            // Set up event listeners
            document.getElementById('search-button').addEventListener('click', searchNodes);

            // Add event listener for Enter key in search input
            document.getElementById('node-search').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchNodes();
                }
            });

            document.getElementById('zoom-in-button').addEventListener('click', zoomIn);
            document.getElementById('zoom-out-button').addEventListener('click', zoomOut);
            document.getElementById('fit-button').addEventListener('click', fitGraph);
            document.getElementById('export-button').addEventListener('click', exportGraph);
            document.getElementById('settings-button').addEventListener('click', showSettings);
            document.getElementById('debug-button').addEventListener('click', debugGraph);
        });

        // Load knowledge graph data
        function loadKnowledgeGraph() {
            console.log("Loading knowledge graph data...");

            // Show loading spinner
            const loadingSpinner = document.getElementById('graph-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'flex';
            }

            // Fetch knowledge graph data
            fetch('/api/knowledge-graph?limit=100')
                .then(response => {
                    console.log("API response received:", response.status);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log("Knowledge graph data received:", data);

                    // Process and display the graph
                    processGraphData(data);

                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }
                })
                .catch(error => {
                    console.error('Error loading knowledge graph:', error);

                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                    }

                    // Show error message
                    const graphContainer = document.getElementById('graph-container');
                    if (graphContainer) {
                        graphContainer.innerHTML = `<div class="alert alert-danger">Error loading knowledge graph: ${error.message}</div>`;
                    }
                });
        }

        // Process graph data and initialize visualization
        function processGraphData(data) {
            // If no nodes, show message
            if (!data.nodes || data.nodes.length === 0) {
                const graphContainer = document.getElementById('graph-container');
                if (graphContainer) {
                    graphContainer.innerHTML = '<div class="alert alert-info">No nodes found in the knowledge graph.</div>';
                }

                // Update statistics
                document.getElementById('node-count').textContent = '0';
                document.getElementById('edge-count').textContent = '0';
                document.getElementById('entity-type-count').textContent = '0';

                return;
            }

            // Prepare data for visualization with unique IDs
            const nodeIds = new Set();
            const nodes = [];

            // Process nodes with unique IDs
            data.nodes.forEach((node, index) => {
                console.log("Processing node:", node);

                // Extract node data from properties if needed
                let nodeUuid = node.uuid;
                let nodeType = node.type;
                let nodeName = node.name;
                let nodeProperties = node.properties || {};

                // Handle different data formats
                if (!nodeUuid && nodeProperties) {
                    // Try to extract data from properties
                    console.log("Node has no UUID, trying to extract from properties:", nodeProperties);

                    // Check if properties contains ID information
                    for (const key in nodeProperties) {
                        if (key.includes('id')) {
                            const propValue = nodeProperties[key];
                            console.log("Found potential ID property:", key, propValue);

                            // Try to extract labels/type
                            if (Array.isArray(propValue) && propValue.length > 1 && propValue[0] === 'labels' && Array.isArray(propValue[1])) {
                                nodeType = propValue[1][0] || 'Unknown';
                                console.log("Extracted type from labels:", nodeType);
                            }
                        }
                    }

                    // If we still don't have a UUID, generate one
                    if (!nodeUuid) {
                        nodeUuid = `node_${index}`;
                        console.log("Generated UUID:", nodeUuid);
                    }

                    // If we don't have a name, use the type or UUID
                    if (!nodeName) {
                        nodeName = nodeType || nodeUuid;
                        console.log("Generated name:", nodeName);
                    }
                }

                // Skip nodes that still don't have enough information
                if (!nodeUuid) {
                    console.warn("Skipping node without UUID:", node);
                    return;
                }

                // Ensure unique IDs
                let nodeId = nodeUuid;
                let counter = 1;
                while (nodeIds.has(nodeId)) {
                    nodeId = `${nodeUuid}_${counter}`;
                    counter++;
                }

                nodeIds.add(nodeId);

                // Create node object
                const nodeObject = {
                    id: nodeId,
                    label: nodeName || nodeId,
                    group: nodeType || 'Unknown',
                    originalId: nodeUuid,
                    properties: nodeProperties
                };

                console.log("Created node object:", nodeObject);
                nodes.push(nodeObject);
            });

            // Process edges with unique IDs
            const edgeIds = new Set();
            const edges = [];

            (data.relationships || []).forEach((rel, index) => {
                console.log("Processing relationship:", rel);

                // Extract relationship data
                let relSource = rel.source;
                let relTarget = rel.target;
                let relType = rel.type || 'RELATED_TO';
                let relProperties = rel.properties || {};

                // Handle different data formats
                if ((!relSource || !relTarget) && Array.isArray(rel) && rel.length >= 3) {
                    console.log("Relationship in array format:", rel);
                    relSource = rel[0];
                    relTarget = rel[1];
                    relType = rel[2];
                }

                // Skip edges with missing source or target
                if (!relSource || !relTarget) {
                    console.warn("Skipping edge with missing source or target:", rel);
                    return;
                }

                // Handle source/target being objects or arrays
                if (typeof relSource === 'object' && relSource !== null) {
                    if (Array.isArray(relSource)) {
                        // Try to extract UUID from array
                        for (let i = 0; i < relSource.length; i += 2) {
                            if (i + 1 < relSource.length && relSource[i] === 'uuid') {
                                relSource = relSource[i + 1];
                                break;
                            }
                        }
                        // If still an array, use the first element or generate an ID
                        if (Array.isArray(relSource)) {
                            relSource = `source_${index}`;
                        }
                    } else if (relSource.uuid) {
                        relSource = relSource.uuid;
                    } else {
                        relSource = `source_${index}`;
                    }
                }

                if (typeof relTarget === 'object' && relTarget !== null) {
                    if (Array.isArray(relTarget)) {
                        // Try to extract UUID from array
                        for (let i = 0; i < relTarget.length; i += 2) {
                            if (i + 1 < relTarget.length && relTarget[i] === 'uuid') {
                                relTarget = relTarget[i + 1];
                                break;
                            }
                        }
                        // If still an array, use the first element or generate an ID
                        if (Array.isArray(relTarget)) {
                            relTarget = `target_${index}`;
                        }
                    } else if (relTarget.uuid) {
                        relTarget = relTarget.uuid;
                    } else {
                        relTarget = `target_${index}`;
                    }
                }

                console.log("Processed relationship source/target:", relSource, relTarget);

                // Find corresponding node IDs (they might have been modified for uniqueness)
                const sourceNode = nodes.find(n => n.originalId === relSource || n.id === relSource);
                const targetNode = nodes.find(n => n.originalId === relTarget || n.id === relTarget);

                if (!sourceNode || !targetNode) {
                    console.warn("Skipping edge with unknown source or target:", relSource, relTarget);
                    return;
                }

                // Create unique edge ID
                let edgeId = `${relSource}-${relTarget}-${relType}`;
                let counter = 1;
                while (edgeIds.has(edgeId)) {
                    edgeId = `${relSource}-${relTarget}-${relType}_${counter}`;
                    counter++;
                }

                edgeIds.add(edgeId);

                // Create edge object
                const edgeObject = {
                    id: edgeId,
                    from: sourceNode.id,
                    to: targetNode.id,
                    label: relType,
                    arrows: 'to',
                    properties: relProperties
                };

                console.log("Created edge object:", edgeObject);
                edges.push(edgeObject);
            });

            // Store processed data for later use
            graphData = {
                nodes: nodes,
                edges: edges
            };

            // Initialize network visualization
            initializeNetwork(nodes, edges);

            // Update statistics
            document.getElementById('node-count').textContent = nodes.length;
            document.getElementById('edge-count').textContent = edges.length;

            // Count unique entity types
            const entityTypes = [...new Set(nodes.map(node => node.group))];
            document.getElementById('entity-type-count').textContent = entityTypes.length;

            // Create legend
            createLegend(entityTypes);
        }

        // Initialize network visualization
        function initializeNetwork(nodes, edges) {
            console.log("Initializing network with nodes:", nodes.length, "and edges:", edges.length);

            try {
                const container = document.getElementById('graph-container');
                if (!container) {
                    console.error("Graph container not found");
                    return;
                }

                // Clear any previous content
                container.innerHTML = '';

                // Check for empty data
                if (nodes.length === 0) {
                    container.innerHTML = '<div class="alert alert-info">No nodes found in the knowledge graph.</div>';
                    return;
                }

                console.log("Creating vis.js datasets");

                try {
                    // Create datasets
                    const nodesDataset = new vis.DataSet();
                    const edgesDataset = new vis.DataSet();

                    // Add nodes one by one to catch any errors
                    for (const node of nodes) {
                        try {
                            nodesDataset.add(node);
                        } catch (err) {
                            console.error("Error adding node:", node, err);
                        }
                    }

                    // Add edges one by one to catch any errors
                    for (const edge of edges) {
                        try {
                            edgesDataset.add(edge);
                        } catch (err) {
                            console.error("Error adding edge:", edge, err);
                        }
                    }

                    console.log("Successfully created datasets with nodes:", nodesDataset.length, "and edges:", edgesDataset.length);

                    // Network options
                    const options = {
                        nodes: {
                            shape: 'dot',
                            size: 16,
                            font: {
                                size: 12,
                                face: 'Arial'
                            },
                            borderWidth: 2
                        },
                        edges: {
                            width: 1,
                            smooth: {
                                type: 'continuous'
                            },
                            arrows: {
                                to: {
                                    enabled: true,
                                    scaleFactor: 0.5
                                }
                            }
                        },
                        physics: {
                            stabilization: true,
                            barnesHut: {
                                gravitationalConstant: -80000,
                                springConstant: 0.001,
                                springLength: 200
                            }
                        },
                        interaction: {
                            navigationButtons: true,
                            keyboard: true
                        }
                    };

                    console.log("Creating network visualization");

                    // Create network
                    network = new vis.Network(container, {
                        nodes: nodesDataset,
                        edges: edgesDataset
                    }, options);

                    console.log("Network visualization created successfully");

                    // Add click event listener
                    network.on('click', function(params) {
                        if (params.nodes.length > 0) {
                            const nodeId = params.nodes[0];
                            displayNodeInfo(nodeId);
                        }
                    });

                    // Add stabilization event listeners
                    network.on('stabilizationProgress', function(params) {
                        console.log("Stabilization progress:", Math.round(params.iterations / params.total * 100) + "%");
                    });

                    network.on('stabilizationIterationsDone', function() {
                        console.log("Stabilization complete");
                    });

                } catch (err) {
                    console.error("Error creating network visualization:", err);
                    container.innerHTML = `<div class="alert alert-danger">Error creating network visualization: ${err.message}</div>`;
                }

            } catch (err) {
                console.error("Error in initializeNetwork:", err);
                const container = document.getElementById('graph-container');
                if (container) {
                    container.innerHTML = `<div class="alert alert-danger">Error initializing network: ${err.message}</div>`;
                }
            }
        }

        // Display node information
        function displayNodeInfo(nodeId) {
            const node = graphData.nodes.find(n => n.id === nodeId);
            if (!node) {
                console.error("Node not found:", nodeId);
                return;
            }

            // Use the original UUID for entity detail link
            const originalId = node.originalId || node.id;

            const nodeInfo = document.getElementById('node-info');
            nodeInfo.innerHTML = `
                <h5>${node.label}</h5>
                <p><strong>Type:</strong> ${node.group}</p>
                <p><strong>ID:</strong> ${originalId}</p>
                <hr>
                <h6>Properties</h6>
                <ul class="list-group">
                    ${Object.entries(node.properties || {})
                        .filter(([key]) => key !== 'uuid' && key !== 'name' && key !== 'type')
                        .map(([key, value]) => `<li class="list-group-item"><strong>${key}:</strong> ${value}</li>`)
                        .join('')}
                </ul>
                <hr>
                <a href="/entity-detail?uuid=${originalId}" class="btn btn-sm btn-primary">View Details</a>
            `;
        }

        // Create legend
        function createLegend(entityTypes) {
            const legend = document.getElementById('graph-legend');
            legend.innerHTML = '';

            entityTypes.forEach(type => {
                const color = getNodeColor(type);

                const legendItem = document.createElement('div');
                legendItem.className = 'legend-item';

                const colorBox = document.createElement('div');
                colorBox.className = 'legend-color';
                colorBox.style.backgroundColor = color;

                const label = document.createElement('span');
                label.textContent = type;

                legendItem.appendChild(colorBox);
                legendItem.appendChild(label);
                legend.appendChild(legendItem);
            });
        }

        // Get color for node type
        function getNodeColor(type) {
            const colorMap = {
                'Person': '#4e79a7',
                'Organization': '#f28e2c',
                'Location': '#e15759',
                'Disease': '#76b7b2',
                'Symptom': '#59a14f',
                'Treatment': '#edc949',
                'Medication': '#af7aa1',
                'Food': '#ff9da7',
                'Herb': '#9c755f',
                'Nutrient': '#bab0ab'
            };

            return colorMap[type] || '#b3b3b3';
        }

        // Search nodes
        function searchNodes() {
            const searchInput = document.getElementById('node-search');
            const searchQuery = searchInput.value.trim().toLowerCase();

            if (!searchQuery || !graphData || !graphData.nodes) {
                return;
            }

            console.log("Searching for:", searchQuery);

            // Debug: Log all nodes to see what we're working with
            console.log("All nodes:", graphData.nodes);

            // Debug: Create a simple array of node labels for easier inspection
            const nodeLabels = graphData.nodes.map(node => node.label);
            console.log("All node labels:", nodeLabels);

            // Find matching nodes - case insensitive search with extensive debugging
            const matchingNodes = graphData.nodes.filter(node => {
                console.log("Checking node:", node);

                // Check label (name)
                if (node.label && typeof node.label === 'string') {
                    const labelLower = node.label.toLowerCase();
                    console.log(`  Label: "${node.label}" -> "${labelLower}"`);
                    if (labelLower.includes(searchQuery)) {
                        console.log(`  ✓ Label match: "${labelLower}" includes "${searchQuery}"`);
                        return true;
                    }
                }

                // Check ID (uuid)
                if (node.id && typeof node.id === 'string') {
                    const idLower = node.id.toLowerCase();
                    console.log(`  ID: "${node.id}" -> "${idLower}"`);
                    if (idLower.includes(searchQuery)) {
                        console.log(`  ✓ ID match: "${idLower}" includes "${searchQuery}"`);
                        return true;
                    }
                }

                // Check original ID if different
                if (node.originalId && typeof node.originalId === 'string' && node.originalId !== node.id) {
                    const originalIdLower = node.originalId.toLowerCase();
                    console.log(`  Original ID: "${node.originalId}" -> "${originalIdLower}"`);
                    if (originalIdLower.includes(searchQuery)) {
                        console.log(`  ✓ Original ID match: "${originalIdLower}" includes "${searchQuery}"`);
                        return true;
                    }
                }

                // Check group/type
                if (node.group && typeof node.group === 'string') {
                    const groupLower = node.group.toLowerCase();
                    console.log(`  Group: "${node.group}" -> "${groupLower}"`);
                    if (groupLower.includes(searchQuery)) {
                        console.log(`  ✓ Group match: "${groupLower}" includes "${searchQuery}"`);
                        return true;
                    }
                }

                // Check properties
                if (node.properties) {
                    console.log("  Checking properties:", node.properties);
                    // Check if any property value contains the search query
                    for (const [key, value] of Object.entries(node.properties)) {
                        if (value && typeof value === 'string') {
                            const valueLower = value.toLowerCase();
                            console.log(`    Property ${key}: "${value}" -> "${valueLower}"`);
                            if (valueLower.includes(searchQuery)) {
                                console.log(`    ✓ Property match: "${key}=${valueLower}" includes "${searchQuery}"`);
                                return true;
                            }
                        } else if (value !== null && value !== undefined) {
                            const valueStr = String(value).toLowerCase();
                            console.log(`    Property ${key}: ${value} -> "${valueStr}"`);
                            if (valueStr.includes(searchQuery)) {
                                console.log(`    ✓ Property match: "${key}=${valueStr}" includes "${searchQuery}"`);
                                return true;
                            }
                        }
                    }
                }

                console.log("  ✗ No match found for this node");
                return false;
            });

            console.log("Found matching nodes:", matchingNodes.length);

            if (matchingNodes.length > 0) {
                // Focus on matching nodes
                network.fit({
                    nodes: matchingNodes.map(node => node.id),
                    animation: true
                });

                // Select the first matching node
                network.selectNodes([matchingNodes[0].id]);
                displayNodeInfo(matchingNodes[0].id);

                // Show success message
                const graphContainer = document.getElementById('graph-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'alert alert-success position-absolute m-3';
                messageDiv.style.top = '0';
                messageDiv.style.right = '0';
                messageDiv.style.zIndex = '1000';
                messageDiv.innerHTML = `Found ${matchingNodes.length} matching node${matchingNodes.length > 1 ? 's' : ''}`;
                graphContainer.appendChild(messageDiv);

                // Remove message after 3 seconds
                setTimeout(() => {
                    messageDiv.remove();
                }, 3000);
            } else {
                // Show error message
                const graphContainer = document.getElementById('graph-container');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'alert alert-warning position-absolute m-3';
                messageDiv.style.top = '0';
                messageDiv.style.right = '0';
                messageDiv.style.zIndex = '1000';
                messageDiv.innerHTML = `No matching nodes found for "${searchInput.value}"`;
                graphContainer.appendChild(messageDiv);

                // Remove message after 3 seconds
                setTimeout(() => {
                    messageDiv.remove();
                }, 3000);
            }
        }

        // Zoom in
        function zoomIn() {
            if (network) {
                const scale = network.getScale() * 1.2;
                network.moveTo({ scale: scale });
            }
        }

        // Zoom out
        function zoomOut() {
            if (network) {
                const scale = network.getScale() / 1.2;
                network.moveTo({ scale: scale });
            }
        }

        // Fit graph
        function fitGraph() {
            if (network) {
                network.fit();
            }
        }

        // Export graph
        function exportGraph() {
            if (!graphData) return;

            const exportData = {
                nodes: graphData.nodes,
                edges: graphData.edges
            };

            const dataStr = "data:text/json;charset=utf-8," + encodeURIComponent(JSON.stringify(exportData, null, 2));
            const downloadAnchorNode = document.createElement('a');
            downloadAnchorNode.setAttribute("href", dataStr);
            downloadAnchorNode.setAttribute("download", "knowledge_graph.json");
            document.body.appendChild(downloadAnchorNode);
            downloadAnchorNode.click();
            downloadAnchorNode.remove();
        }

        // Show settings
        function showSettings() {
            alert('Graph settings functionality is not implemented yet');
        }

        // Debug graph
        function debugGraph() {
            console.log("Debug button clicked");

            // Create a debug panel
            const debugPanel = document.createElement('div');
            debugPanel.className = 'modal fade';
            debugPanel.id = 'debug-modal';
            debugPanel.tabIndex = '-1';
            debugPanel.setAttribute('aria-labelledby', 'debug-modal-label');
            debugPanel.setAttribute('aria-hidden', 'true');

            debugPanel.innerHTML = `
                <div class="modal-dialog modal-lg modal-dialog-scrollable">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="debug-modal-label">Graph Debug Information</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <h6>Search for a specific node:</h6>
                            <div class="input-group mb-3">
                                <input type="text" id="debug-search-input" class="form-control" placeholder="Enter node name">
                                <button class="btn btn-primary" id="debug-search-button">Search</button>
                            </div>
                            <div id="debug-search-results" class="mb-3"></div>

                            <h6>Graph Statistics:</h6>
                            <ul class="list-group mb-3">
                                <li class="list-group-item">Nodes: ${graphData ? graphData.nodes.length : 0}</li>
                                <li class="list-group-item">Edges: ${graphData ? graphData.edges.length : 0}</li>
                                <li class="list-group-item">Entity Types: ${graphData ? [...new Set(graphData.nodes.map(n => n.group))].length : 0}</li>
                            </ul>

                            <h6>Node Types:</h6>
                            <ul class="list-group mb-3">
                                ${graphData ? [...new Set(graphData.nodes.map(n => n.group))].map(type =>
                                    `<li class="list-group-item">${type}: ${graphData.nodes.filter(n => n.group === type).length} nodes</li>`
                                ).join('') : ''}
                            </ul>

                            <h6>First 10 Nodes:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Label</th>
                                            <th>Type</th>
                                            <th>Original ID</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        ${graphData ? graphData.nodes.slice(0, 10).map(node => `
                                            <tr>
                                                <td>${node.id}</td>
                                                <td>${node.label}</td>
                                                <td>${node.group}</td>
                                                <td>${node.originalId}</td>
                                            </tr>
                                        `).join('') : ''}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            `;

            // Add the debug panel to the document
            document.body.appendChild(debugPanel);

            // Show the debug panel
            const modal = new bootstrap.Modal(debugPanel);
            modal.show();

            // Add event listener for the debug search button
            debugPanel.querySelector('#debug-search-button').addEventListener('click', function() {
                const searchInput = debugPanel.querySelector('#debug-search-input');
                const searchQuery = searchInput.value.trim().toLowerCase();
                const searchResults = debugPanel.querySelector('#debug-search-results');

                if (!searchQuery || !graphData || !graphData.nodes) {
                    searchResults.innerHTML = '<div class="alert alert-warning">Please enter a search query</div>';
                    return;
                }

                // Find matching nodes
                const matchingNodes = graphData.nodes.filter(node => {
                    // Check label (name)
                    if (node.label && typeof node.label === 'string' && node.label.toLowerCase().includes(searchQuery)) {
                        return true;
                    }

                    // Check ID (uuid)
                    if (node.id && typeof node.id === 'string' && node.id.toLowerCase().includes(searchQuery)) {
                        return true;
                    }

                    // Check original ID
                    if (node.originalId && typeof node.originalId === 'string' && node.originalId.toLowerCase().includes(searchQuery)) {
                        return true;
                    }

                    // Check group/type
                    if (node.group && typeof node.group === 'string' && node.group.toLowerCase().includes(searchQuery)) {
                        return true;
                    }

                    return false;
                });

                if (matchingNodes.length > 0) {
                    searchResults.innerHTML = `
                        <div class="alert alert-success">Found ${matchingNodes.length} matching nodes</div>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Label</th>
                                        <th>Type</th>
                                        <th>Original ID</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${matchingNodes.map(node => `
                                        <tr>
                                            <td>${node.id}</td>
                                            <td>${node.label}</td>
                                            <td>${node.group}</td>
                                            <td>${node.originalId}</td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    `;
                } else {
                    searchResults.innerHTML = `<div class="alert alert-warning">No matching nodes found for "${searchInput.value}"</div>`;
                }
            });

            // Add event listener for Enter key in debug search input
            debugPanel.querySelector('#debug-search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    debugPanel.querySelector('#debug-search-button').click();
                }
            });
        }
    </script>
</body>
</html>
