"""
LLM service for the Graphiti application.
"""

from typing import Dict, Any, List, Optional
import os
import json
import httpx
import asyncio

from utils.logging_utils import get_logger
from utils.config import get_settings

# Set up logger
logger = get_logger(__name__)

async def test_llm_connection() -> bool:
    """
    Test LLM API connection.
    
    Returns:
        True if connected, False otherwise
    """
    try:
        settings = get_settings()
        
        # Get LLM provider and API key
        provider = settings.get("llm_settings", {}).get("provider", "openai")
        api_key = settings.get("llm_settings", {}).get("api_key", "")
        
        if not api_key:
            logger.warning("No LLM API key provided")
            return False
        
        # Test connection based on provider
        if provider == "openai":
            return await test_openai_connection(api_key)
        elif provider == "openrouter":
            return await test_openrouter_connection(api_key)
        elif provider == "ollama":
            return await test_ollama_connection()
        else:
            logger.warning(f"Unknown LLM provider: {provider}")
            return False
    
    except Exception as e:
        logger.error(f"Error testing LLM connection: {str(e)}")
        return False

async def test_openai_connection(api_key: str) -> bool:
    """
    Test OpenAI API connection.
    
    Args:
        api_key: OpenAI API key
        
    Returns:
        True if connected, False otherwise
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://api.openai.com/v1/models",
                headers={"Authorization": f"Bearer {api_key}"}
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"OpenAI API returned status code {response.status_code}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing OpenAI connection: {str(e)}")
        return False

async def test_openrouter_connection(api_key: str) -> bool:
    """
    Test OpenRouter API connection.
    
    Args:
        api_key: OpenRouter API key
        
    Returns:
        True if connected, False otherwise
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://openrouter.ai/api/v1/models",
                headers={"Authorization": f"Bearer {api_key}"}
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"OpenRouter API returned status code {response.status_code}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing OpenRouter connection: {str(e)}")
        return False

async def test_ollama_connection() -> bool:
    """
    Test Ollama API connection.
    
    Returns:
        True if connected, False otherwise
    """
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get("http://localhost:11434/api/tags")
            
            if response.status_code == 200:
                return True
            else:
                logger.warning(f"Ollama API returned status code {response.status_code}")
                return False
    
    except Exception as e:
        logger.error(f"Error testing Ollama connection: {str(e)}")
        return False
