<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Graphiti - Knowledge Graph Platform{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js for visualizations -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            --warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --sidebar-bg: #f8f9fa;
            --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --hover-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f6fa;
        }
        
        /* Sidebar Styles */
        .sidebar {
            background: var(--sidebar-bg);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        
        .sidebar-brand {
            padding: 1.5rem;
            border-bottom: 1px solid #dee2e6;
            background: var(--primary-gradient);
            color: white;
            margin-bottom: 0;
        }
        
        .sidebar-brand h4 {
            margin: 0;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 1rem 0;
        }
        
        .nav-link {
            color: #495057;
            padding: 0.75rem 1.5rem;
            border-radius: 0;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover {
            background-color: #e9ecef;
            color: #495057;
            border-left-color: #667eea;
        }
        
        .nav-link.active {
            background-color: #e7f1ff;
            color: #0d6efd;
            border-left-color: #0d6efd;
            font-weight: 500;
        }
        
        .nav-link i {
            width: 20px;
            margin-right: 10px;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        /* Cards */
        .card {
            border: none;
            box-shadow: var(--card-shadow);
            border-radius: 12px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: var(--hover-shadow);
            transform: translateY(-2px);
        }
        
        .card-header {
            background: transparent;
            border-bottom: 1px solid #f1f3f4;
            padding: 1.25rem;
            font-weight: 600;
        }
        
        /* Gradient Cards */
        .gradient-card {
            background: var(--primary-gradient);
            color: white;
        }
        
        .gradient-card-success {
            background: var(--success-gradient);
            color: white;
        }
        
        .gradient-card-warning {
            background: var(--warning-gradient);
            color: white;
        }
        
        .gradient-card-info {
            background: var(--info-gradient);
            color: white;
        }
        
        /* Buttons */
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--primary-gradient);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
        
        /* Progress Bars */
        .progress {
            height: 8px;
            border-radius: 10px;
            background-color: #f1f3f4;
        }
        
        .progress-bar {
            border-radius: 10px;
        }
        
        /* Tables */
        .table {
            border-radius: 12px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fa;
            border: none;
            font-weight: 600;
            color: #495057;
        }
        
        /* Alerts */
        .alert {
            border: none;
            border-radius: 12px;
            border-left: 4px solid;
        }
        
        .alert-primary {
            border-left-color: #0d6efd;
        }
        
        .alert-success {
            border-left-color: #198754;
        }
        
        .alert-warning {
            border-left-color: #ffc107;
        }
        
        .alert-danger {
            border-left-color: #dc3545;
        }
        
        /* Stats Cards */
        .stats-card {
            text-align: center;
            padding: 2rem 1rem;
        }
        
        .stats-card .stats-number {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .stats-card .stats-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }
        
        /* Loading Spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #0d6efd;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }
            
            .sidebar.show {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
                padding: 1rem;
            }
        }
        
        /* Dark Mode Support */
        [data-bs-theme="dark"] {
            --sidebar-bg: #212529;
        }
        
        [data-bs-theme="dark"] .sidebar {
            background: var(--sidebar-bg);
        }
        
        [data-bs-theme="dark"] .nav-link {
            color: #adb5bd;
        }
        
        [data-bs-theme="dark"] .nav-link:hover {
            background-color: #343a40;
            color: #fff;
        }
        
        [data-bs-theme="dark"] .nav-link.active {
            background-color: #0d6efd;
            color: #fff;
        }
        
        /* Custom Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
        
        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out;
        }
        
        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-brand">
            <h4>
                <i class="bi bi-graph-up"></i>
                Graphiti
            </h4>
            <small>Knowledge Graph Platform</small>
        </div>
        
        <div class="sidebar-nav">
            <nav class="nav flex-column">
                <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                    <i class="bi bi-house-door"></i> Dashboard
                </a>
                <a class="nav-link {% if '/enhanced-upload' in request.url.path %}active{% endif %}" href="/enhanced-upload">
                    <i class="bi bi-cloud-upload"></i> Enhanced Upload
                </a>
                <a class="nav-link {% if '/batch-upload' in request.url.path %}active{% endif %}" href="/batch-upload">
                    <i class="bi bi-files"></i> Batch Upload
                </a>
                <a class="nav-link {% if '/documents' in request.url.path %}active{% endif %}" href="/documents">
                    <i class="bi bi-file-earmark-text"></i> Documents
                </a>
                <a class="nav-link {% if '/entities' in request.url.path %}active{% endif %}" href="/entities">
                    <i class="bi bi-diagram-3"></i> Entities
                </a>
                <a class="nav-link {% if '/references' in request.url.path %}active{% endif %}" href="/references">
                    <i class="bi bi-bookmark"></i> References
                </a>
                <a class="nav-link {% if '/search' in request.url.path %}active{% endif %}" href="/search">
                    <i class="bi bi-search"></i> Search
                </a>
                <a class="nav-link {% if '/qa' in request.url.path %}active{% endif %}" href="/qa">
                    <i class="bi bi-chat-dots"></i> Q&A
                </a>
                <a class="nav-link {% if '/knowledge-graph' in request.url.path %}active{% endif %}" href="/knowledge-graph">
                    <i class="bi bi-share"></i> Knowledge Graph
                </a>
                <a class="nav-link {% if '/settings' in request.url.path %}active{% endif %}" href="/settings">
                    <i class="bi bi-gear"></i> Settings
                </a>
            </nav>
        </div>
        
        <!-- Theme Toggle -->
        <div class="p-3 border-top">
            <button class="btn btn-outline-secondary btn-sm w-100" onclick="toggleTheme()">
                <i class="bi bi-moon-stars" id="theme-icon"></i>
                <span id="theme-text">Dark Mode</span>
            </button>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="main-content">
        <!-- Mobile Menu Button -->
        <button class="btn btn-outline-secondary d-md-none mb-3" type="button" onclick="toggleSidebar()">
            <i class="bi bi-list"></i> Menu
        </button>
        
        <!-- Alert Container -->
        <div id="alert-container"></div>
        
        <!-- Page Content -->
        {% block content %}{% endblock %}
    </main>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Theme Toggle
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-bs-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-bs-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Update button text and icon
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');
            
            if (newTheme === 'dark') {
                themeIcon.className = 'bi bi-sun';
                themeText.textContent = 'Light Mode';
            } else {
                themeIcon.className = 'bi bi-moon-stars';
                themeText.textContent = 'Dark Mode';
            }
        }
        
        // Load saved theme
        document.addEventListener('DOMContentLoaded', function() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-bs-theme', savedTheme);
            
            const themeIcon = document.getElementById('theme-icon');
            const themeText = document.getElementById('theme-text');
            
            if (savedTheme === 'dark') {
                themeIcon.className = 'bi bi-sun';
                themeText.textContent = 'Light Mode';
            }
        });
        
        // Mobile Sidebar Toggle
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Show Alert Function
        function showAlert(message, type = 'info', duration = 5000) {
            const alertContainer = document.getElementById('alert-container');
            const alertId = 'alert-' + Date.now();
            
            const alert = document.createElement('div');
            alert.id = alertId;
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            alertContainer.appendChild(alert);
            
            // Auto-dismiss
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    const bsAlert = new bootstrap.Alert(alertElement);
                    bsAlert.close();
                }
            }, duration);
        }
        
        // Loading Spinner
        function showLoading(element) {
            const originalContent = element.innerHTML;
            element.innerHTML = '<span class="loading-spinner"></span> Loading...';
            element.disabled = true;
            
            return function hideLoading() {
                element.innerHTML = originalContent;
                element.disabled = false;
            };
        }
        
        // Format File Size
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // Format Date
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
        }
        
        // Add fade-in animation to cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.classList.add('fade-in');
                }, index * 100);
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
