"""
Document-related routes for the Graphiti application.
"""

from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Query, BackgroundTasks, Depends
from typing import List, Dict, Any, Optional
import os
from pathlib import Path

from services.document_service import process_document, get_document_list, get_document_details
from services.document_processing_service import get_document_processing_service
from services.reference_service import extract_references_from_document
from models.document_simplified import (
    DocumentProcessingOptions, DocumentUploadResponse, DocumentList,
    BatchDocumentUploadResponse, DocumentDetails, ProcessingQueueStatus,
    DocumentProgressStatus
)
from utils.logging_utils import get_logger
from utils.file_utils import save_uploaded_file
from utils.config import get_config

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["documents"])

@router.post("/batch-upload", response_model=BatchDocumentUploadResponse)
async def batch_upload_documents(
    files: List[UploadFile] = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    max_parallel_processes: int = Form(4),
    background_tasks: BackgroundTasks = None,
    document_processing_service = Depends(get_document_processing_service)
):
    """
    Upload and process multiple documents in parallel.

    Args:
        files: List of document files
        chunk_size: Size of text chunks
        overlap: Overlap between chunks
        extract_entities: Whether to extract entities
        extract_references: Whether to extract references
        extract_metadata: Whether to extract metadata
        max_parallel_processes: Maximum number of documents to process in parallel
        background_tasks: Background tasks
        document_processing_service: Document processing service

    Returns:
        Batch processing result
    """
    try:
        # Get config
        config = get_config()

        # Initialize response
        response = {
            "total_files": len(files),
            "successful_uploads": 0,
            "failed_uploads": 0,
            "documents": [],
            "success": True
        }

        # Process each file
        for file in files:
            try:
                # Save the uploaded file
                file_content = await file.read()
                file_path = save_uploaded_file(
                    file_content,
                    file.filename,
                    config['paths']['uploads_dir']
                )

                # Add to processing queue
                if background_tasks:
                    background_tasks.add_task(
                        document_processing_service.add_document_to_queue,
                        file_path
                    )

                    # Add to response
                    doc_response = {
                        "filename": file.filename,
                        "uuid": str(Path(file_path).stem),
                        "file_type": "document",
                        "chunks": 0,
                        "entities": 0,
                        "references": 0,
                        "success": True,
                        "entity_extraction_running": extract_entities,
                        "reference_extraction_running": extract_references,
                        "ocr_provider": "processing"
                    }

                    response["documents"].append(doc_response)
                    response["successful_uploads"] += 1

            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {str(e)}")

                # Add failed upload to response
                doc_response = {
                    "filename": file.filename,
                    "uuid": "",  # Empty UUID for failed uploads
                    "file_type": "document",
                    "chunks": 0,
                    "entities": 0,
                    "references": 0,
                    "success": False,
                    "entity_extraction_running": False,
                    "reference_extraction_running": False,
                    "ocr_provider": "none",
                    "error": str(e)
                }

                response["documents"].append(doc_response)
                response["failed_uploads"] += 1

        # Update overall success status
        if response["failed_uploads"] > 0 and response["successful_uploads"] == 0:
            response["success"] = False
            response["error"] = "All uploads failed"

        return response

    except Exception as e:
        logger.error(f"Error in batch upload: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error in batch upload: {str(e)}"
        )

@router.post("/upload", response_model=DocumentUploadResponse)
async def upload_document(
    file: UploadFile = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    background_tasks: BackgroundTasks = None,
    document_processing_service = Depends(get_document_processing_service)
):
    """
    Upload and process a single document.

    Uploads a document file and processes it to extract text, entities, and references.
    The document is chunked according to the specified parameters and stored in the knowledge graph.
    For detailed information about the request and response format, see the API documentation:
    docs/API_DOCUMENTATION.md

    Args:
        file: Document file to upload (PDF, text, Word, etc.)
        chunk_size: Size of text chunks in characters (default: 1200)
        overlap: Overlap between chunks in characters (default: 0)
        extract_entities: Whether to extract entities from the document (default: True)
        extract_references: Whether to extract references from the document (default: True)
        extract_metadata: Whether to extract metadata from the document (default: True)
        background_tasks: Background tasks for asynchronous processing
        document_processing_service: Document processing service dependency

    Returns:
        DocumentUploadResponse: Processing result with document ID, chunk count, entity count, etc.

    Raises:
        HTTPException: If there is an error processing the document
    """
    try:
        # Get config
        config = get_config()

        # Save the uploaded file
        file_content = await file.read()
        file_path = save_uploaded_file(
            file_content,
            file.filename,
            config['paths']['uploads_dir']
        )

        # Process the document in the background
        if background_tasks:
            # Add to processing queue
            background_tasks.add_task(
                document_processing_service.add_document_to_queue,
                file_path
            )

            # Return immediate response
            return {
                "filename": file.filename,
                "uuid": str(Path(file_path).stem),
                "file_type": "document",
                "chunks": 0,
                "entities": 0,
                "references": 0,
                "success": True,
                "entity_extraction_running": extract_entities,
                "reference_extraction_running": extract_references,
                "ocr_provider": "processing"
            }
        else:
            # Process the document synchronously
            result = await document_processing_service.process_document(
                file_path,
                chunk_size=chunk_size,
                overlap=overlap,
                extract_entities=extract_entities,
                extract_references=extract_references,
                extract_metadata=extract_metadata
            )

            if result.get("success", False):
                # Return response
                return {
                    "filename": file.filename,
                    "uuid": result.get("episode_id", ""),
                    "file_type": "document",
                    "chunks": result.get("chunks", 0),
                    "entities": result.get("entities_extracted", 0),
                    "references": result.get("references_extracted", 0),
                    "success": True,
                    "entity_extraction_running": False,
                    "reference_extraction_running": False,
                    "ocr_provider": result.get("ocr_provider", "pypdf2")
                }
            else:
                # Return error
                raise HTTPException(
                    status_code=500,
                    detail=result.get("error", "Unknown error during processing")
                )

    except Exception as e:
        logger.error(f"Error processing file: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error processing file: {str(e)}"
        )

@router.get("/documents", response_model=DocumentList)
async def get_documents(
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100)
):
    """
    Get a list of documents.

    Retrieves a paginated list of documents in the knowledge graph.
    For detailed information about the response format, see the API documentation:
    docs/API_DOCUMENTATION.md

    Args:
        page: Page number (starts at 1)
        page_size: Number of documents per page (max 100)

    Returns:
        DocumentList: A paginated list of documents with metadata

    Raises:
        HTTPException: If there is an error retrieving the documents
    """
    try:
        result = await get_document_list(page, page_size)
        return result

    except Exception as e:
        logger.error(f"Error getting documents: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting documents: {str(e)}"
        )

@router.get("/documents/{document_id}", response_model=DocumentDetails)
async def get_document(document_id: str):
    """
    Get details of a document.

    Retrieves detailed information about a specific document by its ID.
    For detailed information about the response format, see the API documentation:
    docs/API_DOCUMENTATION.md

    Args:
        document_id: Unique identifier of the document

    Returns:
        DocumentDetails: Document details including metadata, chunk count, entity count, and references

    Raises:
        HTTPException: If there is an error retrieving the document details or if the document doesn't exist
    """
    try:
        result = await get_document_details(document_id)
        return result

    except Exception as e:
        logger.error(f"Error getting document {document_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document: {str(e)}"
        )

@router.get("/processing-status", response_model=ProcessingQueueStatus)
async def get_processing_status(
    document_processing_service = Depends(get_document_processing_service)
):
    """
    Get the status of the document processing queue.

    Retrieves information about the current state of the document processing queue,
    including the number of documents waiting to be processed and the progress of
    the document currently being processed.

    Args:
        document_processing_service: Document processing service dependency

    Returns:
        ProcessingQueueStatus: Status of the document processing queue

    Raises:
        HTTPException: If there is an error retrieving the queue status
    """
    try:
        status = await document_processing_service.get_queue_status()
        return status

    except Exception as e:
        logger.error(f"Error getting processing status: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting processing status: {str(e)}"
        )

@router.get("/document-progress/{document_id}", response_model=DocumentProgressStatus)
async def get_document_progress(
    document_id: str,
    document_processing_service = Depends(get_document_processing_service)
):
    """
    Get the progress of a specific document being processed.

    Retrieves the current processing progress of a document, including the
    current step, progress percentage, and additional details.

    Args:
        document_id: Unique identifier of the document
        document_processing_service: Document processing service dependency

    Returns:
        DocumentProgressStatus: Processing progress of the document

    Raises:
        HTTPException: If there is an error retrieving the document progress
    """
    try:
        status = await document_processing_service.get_queue_status()

        # Check if this document is in the documents_progress dictionary
        documents_progress = status.get("documents_progress", {})
        if document_id in documents_progress:
            return documents_progress[document_id]

        # Check if this is the document currently being processed
        progress = status.get("current_progress", {})
        if progress and progress.get("document_id") == document_id:
            return progress
        else:
            # Check if the document is in the queue
            return DocumentProgressStatus(
                document_id=document_id,
                status="queued" if status.get("is_processing") else "not_found",
                progress_percentage=0,
                step_name="Waiting in queue" if status.get("is_processing") else "Not found",
                details={}
            )

    except Exception as e:
        logger.error(f"Error getting document progress: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document progress: {str(e)}"
        )
