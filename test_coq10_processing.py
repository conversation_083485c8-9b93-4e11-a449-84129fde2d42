#!/usr/bin/env python3
"""
Test the Co Q10 document processing
"""

import asyncio
from processors.enhanced_document_processor import EnhancedDocumentProcessor

async def test_complete_processing():
    processor = EnhancedDocumentProcessor()
    file_path = 'uploads/4e8bb988-26e9-4e75-a50d-53e7bcb4835d_Co Q10 - Athletes.pdf'
    
    print(f'Testing complete document processing for: {file_path}')
    
    result = await processor.process_document(
        file_path,
        chunk_size=1200,
        overlap=0,
        extract_entities=True,
        extract_references=True,
        extract_metadata=True,
        generate_embeddings=False  # Skip embeddings for this test
    )
    
    if result.get('success'):
        print('✅ Document processed successfully!')
        print(f'Episode ID: {result.get("episode_id")}')
        print(f'Chunks: {result.get("chunks")}')
        print(f'Text length: {result.get("text_length")}')
        print(f'Entities: {result.get("entities")}')
        print(f'References: {result.get("references")}')
        print(f'OCR Provider: {result.get("ocr_provider")}')
    else:
        error_msg = result.get('error', 'Unknown error')
        print(f'❌ Document processing failed: {error_msg}')

if __name__ == "__main__":
    asyncio.run(test_complete_processing())
