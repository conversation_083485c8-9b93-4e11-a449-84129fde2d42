#!/usr/bin/env python3
"""
Extract and save OneNote embedded images for manual inspection.

This script extracts the embedded images from OneNote files and saves them
so we can manually inspect what content they contain.
"""

import sys
import os
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def extract_and_save_onenote_images():
    """Extract and save OneNote embedded images for inspection."""
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Extracting images from: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from one_extract import OneNoteExtractor, OneNoteExtractorError
        
        # Read the OneNote file
        with open(onenote_file, 'rb') as f:
            file_data = f.read()
        
        # Extract content with one-extract
        try:
            extractor = OneNoteExtractor(data=file_data)
            print("✅ Initialized OneNote extractor")
        except OneNoteExtractorError as e:
            print(f"❌ OneNote extraction failed: {e}")
            return False
        
        # Create output directory
        output_dir = Path("extracted_onenote_images")
        output_dir.mkdir(exist_ok=True)
        print(f"📁 Created output directory: {output_dir}")
        
        # Extract embedded files (images)
        print("\n🖼️ EXTRACTING AND SAVING IMAGES:")
        print("=" * 60)
        
        embedded_files = list(extractor.extract_files())
        print(f"📎 Found {len(embedded_files)} embedded files")
        
        saved_images = 0
        
        for i, file_data in enumerate(embedded_files):
            print(f"\n📷 Processing embedded file {i+1}:")
            print(f"   Size: {len(file_data):,} bytes")
            
            # Check if it's an image and determine type
            if file_data.startswith(b'\x89PNG'):
                file_type = "PNG"
                file_ext = ".png"
            elif file_data.startswith(b'\xff\xd8\xff'):
                file_type = "JPEG"
                file_ext = ".jpg"
            elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
                file_type = "GIF"
                file_ext = ".gif"
            else:
                print(f"   ⚠️ Not an image file")
                # Save as binary file for inspection
                binary_file = output_dir / f"embedded_file_{i+1}.bin"
                with open(binary_file, 'wb') as f:
                    f.write(file_data)
                print(f"   💾 Saved binary file: {binary_file}")
                continue
            
            print(f"   🖼️ Image type: {file_type}")
            
            # Save the image
            image_file = output_dir / f"onenote_image_{i+1}_{file_type.lower()}{file_ext}"
            with open(image_file, 'wb') as f:
                f.write(file_data)
            
            print(f"   💾 Saved image: {image_file}")
            saved_images += 1
            
            # Try to get image dimensions if PIL is available
            try:
                from PIL import Image
                with Image.open(image_file) as img:
                    width, height = img.size
                    mode = img.mode
                    print(f"   📐 Dimensions: {width}x{height} pixels, Mode: {mode}")
            except ImportError:
                print(f"   📐 PIL not available for dimension check")
            except Exception as e:
                print(f"   ⚠️ Could not read image dimensions: {e}")
        
        # Summary
        print(f"\n📊 EXTRACTION SUMMARY:")
        print("=" * 60)
        print(f"Total embedded files: {len(embedded_files)}")
        print(f"Images saved: {saved_images}")
        print(f"Output directory: {output_dir.absolute()}")
        
        if saved_images > 0:
            print(f"\n🎯 NEXT STEPS:")
            print("1. Manually open the saved images to see what content they contain")
            print("2. Check if they contain text, diagrams, charts, or other visual content")
            print("3. Determine if OCR should be able to extract text from them")
            print("4. Consider alternative OCR approaches if needed")
            
            # List the saved files
            print(f"\n📁 SAVED FILES:")
            for file_path in sorted(output_dir.glob("*")):
                size = file_path.stat().st_size
                print(f"   {file_path.name} - {size:,} bytes")
            
            return True
        else:
            print("❌ No images were extracted")
            return False
            
    except ImportError:
        print("❌ one-extract library not available")
        return False
    except Exception as e:
        print(f"❌ Error during image extraction: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_extracted_images():
    """Analyze the extracted images if they exist."""
    
    output_dir = Path("extracted_onenote_images")
    if not output_dir.exists():
        print("❌ No extracted images directory found")
        return
    
    image_files = list(output_dir.glob("*.png")) + list(output_dir.glob("*.jpg")) + list(output_dir.glob("*.gif"))
    
    if not image_files:
        print("❌ No image files found in extracted directory")
        return
    
    print(f"\n🔍 ANALYZING {len(image_files)} EXTRACTED IMAGES:")
    print("=" * 60)
    
    try:
        from PIL import Image
        
        for image_file in sorted(image_files):
            print(f"\n📷 {image_file.name}:")
            
            try:
                with Image.open(image_file) as img:
                    width, height = img.size
                    mode = img.mode
                    format_name = img.format
                    
                    print(f"   📐 Size: {width}x{height} pixels")
                    print(f"   🎨 Mode: {mode}")
                    print(f"   📄 Format: {format_name}")
                    print(f"   💾 File size: {image_file.stat().st_size:,} bytes")
                    
                    # Check if image is mostly text-like (high contrast, lots of white space)
                    # This is a rough heuristic
                    if mode in ('RGB', 'RGBA'):
                        # Sample some pixels to get an idea of content
                        sample_size = min(100, width, height)
                        sample = img.resize((sample_size, sample_size))
                        pixels = list(sample.getdata())
                        
                        # Count white/light pixels (potential text background)
                        light_pixels = 0
                        for pixel in pixels:
                            if mode == 'RGB':
                                r, g, b = pixel
                                brightness = (r + g + b) / 3
                            else:  # RGBA
                                r, g, b, a = pixel
                                brightness = (r + g + b) / 3
                            
                            if brightness > 200:  # Light pixel
                                light_pixels += 1
                        
                        light_ratio = light_pixels / len(pixels)
                        print(f"   💡 Light pixels: {light_ratio:.1%} (high = likely text/diagrams)")
                        
                        if light_ratio > 0.7:
                            print(f"   📝 Likely contains text or diagrams (good for OCR)")
                        elif light_ratio > 0.4:
                            print(f"   🖼️ Mixed content (may contain some text)")
                        else:
                            print(f"   🎨 Likely photo/complex image (OCR may be difficult)")
                    
            except Exception as e:
                print(f"   ❌ Error analyzing image: {e}")
                
    except ImportError:
        print("⚠️ PIL not available for image analysis")


def main():
    """Run OneNote image extraction and analysis."""
    print("🖼️ ONENOTE IMAGE EXTRACTION AND ANALYSIS")
    print("=" * 60)
    
    # Extract images
    success = extract_and_save_onenote_images()
    
    if success:
        # Analyze the extracted images
        analyze_extracted_images()
    
    print("\n" + "=" * 60)
    print("🎯 EXTRACTION SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 OneNote image extraction successful!")
        print("✅ Images saved for manual inspection")
        print("✅ Analysis completed")
        print("\nYou can now:")
        print("1. Open the extracted images to see their content")
        print("2. Determine if they contain readable text")
        print("3. Test alternative OCR approaches if needed")
    else:
        print("❌ OneNote image extraction failed")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
