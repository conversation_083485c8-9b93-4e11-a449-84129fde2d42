# Unified Environment Configuration for Graphiti with MCP
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE PASSWORDS (Standardized)
# =============================================================================
FALKORDB_PASSWORD=Triathlon16!
REDIS_PASSWORD=Triathlon16!
NEO4J_PASSWORD=Triathlon16!

# =============================================================================
# API KEYS (Standardized)
# =============================================================================
# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API Key (preferred for LLM)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mistral API Key (for OCR and processing)
MISTRAL_API_KEY=your_mistral_api_key_here

# Google Gemini API Key
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# LLM CONFIGURATION (Standardized)
# =============================================================================
# Primary LLM provider (openrouter, openai, ollama)
LLM_PROVIDER=openrouter

# Primary LLM model for Q&A and entity extraction
LLM_MODEL=meta-llama/llama-4-maverick

# LLM temperature for main operations
LLM_TEMPERATURE=0.7

# LLM top-k parameter
LLM_TOP_K=40

# LLM top-p parameter
LLM_TOP_P=0.9

# =============================================================================
# MCP SERVER LLM CONFIGURATION
# =============================================================================
# MCP server specific model (can be different from main LLM)
MCP_MODEL_NAME=gpt-4o-mini

# MCP LLM temperature (usually lower for consistency)
MCP_LLM_TEMPERATURE=0.0

# =============================================================================
# EMBEDDING CONFIGURATION (Standardized)
# =============================================================================
# Embedding provider (ollama, openai, openrouter)
EMBEDDING_PROVIDER=ollama

# Embedding model (snowflake-arctic-embed2 preferred)
EMBEDDING_MODEL=snowflake-arctic-embed2

# Embedding dimensions (1024 for snowflake-arctic-embed2)
EMBEDDING_DIMENSIONS=1024

# Embedding distance metric (COSINE preferred)
EMBEDDING_DISTANCE=COSINE

# =============================================================================
# DOCUMENT PROCESSING CONFIGURATION
# =============================================================================
# Default chunk size for document processing
CHUNK_SIZE=1200

# Default chunk overlap
CHUNK_OVERLAP=0

# Document processing strategy (recursive preferred)
CHUNKING_STRATEGY=recursive

# Maximum document size (in MB)
MAX_DOCUMENT_SIZE=50

# Supported file types (comma-separated)
SUPPORTED_FILE_TYPES=pdf,txt,md,docx

# =============================================================================
# ENTITY EXTRACTION CONFIGURATION
# =============================================================================
# Entity extraction model (qwen3-4b preferred)
ENTITY_EXTRACTION_MODEL=qwen3-4b

# Entity extraction provider (ollama, openrouter, mistral)
ENTITY_EXTRACTION_PROVIDER=ollama

# Entity confidence threshold
ENTITY_CONFIDENCE_THRESHOLD=0.5

# Maximum entities per document
MAX_ENTITIES_PER_DOCUMENT=100

# =============================================================================
# REFERENCE EXTRACTION CONFIGURATION
# =============================================================================
# Use Mistral OCR for reference extraction
USE_MISTRAL_OCR=true

# Reference extraction confidence threshold
REFERENCE_CONFIDENCE_THRESHOLD=0.6

# Maximum references per document
MAX_REFERENCES_PER_DOCUMENT=50

# =============================================================================
# REFERENCE DEDUPLICATION SETTINGS
# =============================================================================
TITLE_SIMILARITY_THRESHOLD=0.85
AUTHOR_SIMILARITY_THRESHOLD=0.75
YEAR_MATCH_REQUIRED=true
MIN_OVERALL_SIMILARITY=0.8

# =============================================================================
# ENTITY DEDUPLICATION SETTINGS
# =============================================================================
ENTITY_NAME_SIMILARITY_THRESHOLD=0.85
ENTITY_TYPE_MATCH_REQUIRED=true
ENTITY_MIN_OVERALL_SIMILARITY=0.8
ENTITY_DEDUP_BATCH_SIZE=100
ENTITY_DEDUP_FETCH_BATCH_SIZE=5000
ENTITY_DEDUP_MAX_PER_REQUEST=0
USE_LLM_FOR_ENTITY_DEDUPLICATION=true
ENTITY_DEDUP_LLM_PROVIDER=openrouter
ENTITY_DEDUP_LLM_MODEL=meta-llama/llama-4-maverick
ENTITY_DEDUP_VERBOSE_LOGGING=false

# =============================================================================
# MCP SERVER CONFIGURATION
# =============================================================================
# MCP transport protocol (sse, stdio)
MCP_TRANSPORT=sse

# MCP server host
MCP_HOST=0.0.0.0

# MCP server port
MCP_PORT=8000

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
# Debug mode
DEBUG=false

# Log level (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Default group ID for knowledge graph
DEFAULT_GROUP_ID=default

# Enable parallel processing
ENABLE_PARALLEL_PROCESSING=true

# Number of worker processes
WORKER_PROCESSES=4

# =============================================================================
# VECTOR SEARCH CONFIGURATION
# =============================================================================
# Vector search provider (redis, falkordb)
VECTOR_SEARCH_PROVIDER=redis

# Vector search index name
VECTOR_SEARCH_INDEX=graphiti_embeddings

# Vector search top-k results
VECTOR_SEARCH_TOP_K=20

# =============================================================================
# UI CONFIGURATION
# =============================================================================
# Show test mode message
SHOW_TEST_MODE=false

# Enable progress bars in UI
ENABLE_PROGRESS_BARS=true

# Enable real-time logs in UI
ENABLE_REALTIME_LOGS=true

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================
# Connection pool size
CONNECTION_POOL_SIZE=10

# Query timeout (seconds)
QUERY_TIMEOUT=30

# Batch processing size
BATCH_SIZE=100

# Cache TTL (seconds)
CACHE_TTL=3600

# =============================================================================
# EXTERNAL API CONFIGURATION
# =============================================================================
# CrossRef email for API access
CROSSREF_EMAIL=<EMAIL>

# PubMed API key
PUBMED_API_KEY=your_pubmed_api_key_here

# Semantic Scholar API key
SEMANTIC_SCHOLAR_API_KEY=your_semantic_scholar_api_key_here

# =============================================================================
# OLLAMA CONFIGURATION
# =============================================================================
# Ollama base URL
OLLAMA_BASE_URL=http://localhost:11434

# Available Ollama models (comma-separated)
OLLAMA_MODELS=snowflake-arctic-embed2:latest,mistral-nemo:latest,qwen3-4b:latest

# =============================================================================
# OCR CONFIGURATION
# =============================================================================
# Use Mistral OCR for document processing
USE_MISTRAL_OCR=true

# Mistral OCR model
MISTRAL_OCR_MODEL=mistral-ocr-latest
