"""
Test FalkorDB natural language Q&A
"""

import asyncio
import os
from dotenv import load_dotenv
from falkordb_adapter import FalkorDBAdapter
from openai import AsyncOpenAI

# Load environment variables
load_dotenv()

# OpenAI API key
api_key = os.environ.get('OPENAI_API_KEY')
if not api_key:
    raise ValueError("OPENAI_API_KEY environment variable not set")

# Initialize OpenAI client
client = AsyncOpenAI(api_key=api_key)

async def generate_cypher_query(question):
    """Generate a Cypher query from a natural language question"""
    system_prompt = """
    You are a helpful assistant that translates natural language questions into Cypher queries for a graph database.
    The database contains nodes with labels like Nutrient, Disease, Process, etc.
    Relationships between nodes include SUPPORTS, PREVENTS, TREATS, etc.
    
    Generate only the Cypher query without any explanation or additional text.
    """
    
    user_prompt = f"Translate this question into a Cypher query: {question}"
    
    response = await client.chat.completions.create(
        model="gpt-4.1-mini",
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        temperature=0
    )
    
    return response.choices[0].message.content.strip()

async def answer_question(question):
    """Answer a natural language question using FalkorDB"""
    adapter = FalkorDBAdapter(graph_name="graphiti")
    
    try:
        # Generate Cypher query
        print(f"Question: {question}")
        cypher_query = await generate_cypher_query(question)
        print(f"Generated Cypher query: {cypher_query}")
        
        # Execute query
        result = adapter.execute_cypher(cypher_query)
        print(f"Query result: {result}")
        
        # Generate answer
        system_prompt = """
        You are a helpful assistant that answers questions based on graph database query results.
        Provide a concise and accurate answer based on the data provided.
        If the data doesn't contain the answer, say so.
        """
        
        user_prompt = f"""
        Question: {question}
        
        Database query result: {result}
        
        Please provide a concise answer based on this data.
        """
        
        response = await client.chat.completions.create(
            model="gpt-4.1-mini",
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3
        )
        
        answer = response.choices[0].message.content.strip()
        print(f"\nAnswer: {answer}")
        
    finally:
        adapter.close()

async def main():
    """Main function"""
    questions = [
        "What does Vitamin C support?",
        "What can Vitamin C prevent?",
        "What are the properties of Vitamin C?"
    ]
    
    for question in questions:
        await answer_question(question)
        print("\n" + "-" * 50 + "\n")

if __name__ == "__main__":
    asyncio.run(main())
