#!/usr/bin/env python3
"""
Test script to check graph API with larger dataset.
"""

import requests

def test_graph_large():
    """Test graph API with larger dataset"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Graph API with Larger Dataset")
    print("=" * 50)
    
    # Test with different limits
    for limit in [50, 100]:
        print(f"\n📊 Testing with limit={limit}")
        try:
            response = requests.get(f"{base_url}/api/knowledge-graph/graph?limit={limit}", timeout=15)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                nodes = data.get('nodes', [])
                relationships = data.get('relationships', [])
                
                print(f"✅ Nodes: {len(nodes)}")
                print(f"✅ Relationships: {len(relationships)}")
                
                # Check entity types
                if nodes:
                    entity_types = {}
                    for node in nodes:
                        node_type = node.get('type', 'Unknown')
                        entity_types[node_type] = entity_types.get(node_type, 0) + 1
                    
                    print(f"Entity types found:")
                    for entity_type, count in sorted(entity_types.items(), key=lambda x: x[1], reverse=True)[:10]:
                        print(f"  - {entity_type}: {count}")
                
                # Check relationship types
                if relationships:
                    rel_types = {}
                    for rel in relationships:
                        rel_type = rel.get('type', 'Unknown')
                        rel_types[rel_type] = rel_types.get(rel_type, 0) + 1
                    
                    print(f"Relationship types found:")
                    for rel_type, count in sorted(rel_types.items(), key=lambda x: x[1], reverse=True)[:5]:
                        print(f"  - {rel_type}: {count}")
                
                # Sample some interesting nodes
                print(f"Sample interesting nodes:")
                herb_nodes = [n for n in nodes if n.get('type') == 'Herb'][:3]
                for i, node in enumerate(herb_nodes):
                    name = node.get('name', 'N/A')
                    mentions = node.get('properties', {}).get('mention_count', 0)
                    print(f"  🌿 {i+1}. {name} - {mentions} mentions")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_graph_large()
