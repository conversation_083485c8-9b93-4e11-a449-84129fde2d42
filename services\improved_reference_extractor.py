"""
Improved Reference Extractor using Mistral OCR
Clean, focused implementation for better reference extraction
"""

import re
import uuid
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class ImprovedReferenceExtractor:
    """
    Clean, focused reference extractor using Mistral OCR.
    Designed to be much more effective at finding references.
    """
    
    def __init__(self, mistral_ocr_processor):
        self.mistral_ocr = mistral_ocr_processor
        
    async def extract_references(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references from a document using Mistral OCR.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary with extraction results
        """
        try:
            logger.info(f"🔍 Starting reference extraction for: {Path(file_path).name}")
            
            # Extract text using Mistral OCR
            text = await self.mistral_ocr.extract_text_from_pdf(file_path)
            if not text:
                logger.error("❌ No text extracted from document")
                return self._empty_result("No text extracted")
            
            logger.info(f"📝 Extracted {len(text):,} characters from document")
            
            # Find and extract references
            references = self._extract_all_references(text)
            
            logger.info(f"📚 Found {len(references)} references")
            
            # Save references to CSV
            csv_path = await self._save_references_to_csv(references, file_path)
            
            return {
                "success": True,
                "filename": Path(file_path).name,
                "file_path": file_path,
                "extraction_method": "improved_mistral_ocr",
                "total_reference_count": len(references),
                "references": references,
                "csv_path": csv_path,
                "extracted_text_length": len(text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error extracting references: {e}", exc_info=True)
            return self._empty_result(str(e))
    
    def _extract_all_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract all references from text using multiple strategies."""
        
        # Strategy 1: Find dedicated reference sections
        section_refs = self._extract_from_reference_sections(text)
        if len(section_refs) >= 5:  # If we found a good number in sections
            logger.info(f"✅ Found {len(section_refs)} references in dedicated sections")
            return section_refs
        
        # Strategy 2: Look for numbered references throughout document
        numbered_refs = self._extract_numbered_references(text)
        if len(numbered_refs) >= 5:
            logger.info(f"✅ Found {len(numbered_refs)} numbered references")
            return numbered_refs
        
        # Strategy 3: Pattern-based extraction (fallback)
        pattern_refs = self._extract_pattern_references(text)
        logger.info(f"✅ Found {len(pattern_refs)} references using pattern matching")
        
        # Combine all methods and deduplicate
        all_refs = section_refs + numbered_refs + pattern_refs
        return self._deduplicate_references(all_refs)
    
    def _extract_from_reference_sections(self, text: str) -> List[Dict[str, Any]]:
        """Extract references from dedicated reference sections."""
        references = []
        
        # Find reference sections with simpler, more flexible patterns
        ref_section_patterns = [
            r'(?i)(?:^|\n)\s*(?:references?|bibliography|citations?|literature\s+cited)\s*(?:\n|$)',
            r'(?i)(?:^|\n)\s*\d+\.?\s*(?:references?|bibliography)\s*(?:\n|$)',
            r'(?i)(?:^|\n)\s*#{1,6}\s*(?:references?|bibliography)\s*(?:\n|$)',  # Markdown headers
        ]
        
        for pattern in ref_section_patterns:
            matches = list(re.finditer(pattern, text, re.MULTILINE))
            for match in matches:
                # Extract section from match to end or next major section
                start = match.end()
                
                # Look for end of references (next major section)
                end_patterns = [
                    r'(?i)(?:\n|^)\s*(?:appendix|acknowledgments?|author\s+information|supplementary|figure\s+\d+|table\s+\d+)\s*(?:\n|$)',
                    r'(?:\n|^)\s*\d+\.?\s*(?:appendix|acknowledgments?|conclusion)\s*(?:\n|$)'
                ]
                
                end = len(text)
                for end_pattern in end_patterns:
                    end_match = re.search(end_pattern, text[start:], re.MULTILINE)
                    if end_match:
                        end = start + end_match.start()
                        break
                
                section_text = text[start:end].strip()
                if len(section_text) > 100:  # Must be substantial
                    section_refs = self._parse_reference_section(section_text)
                    references.extend(section_refs)
        
        return references
    
    def _parse_reference_section(self, section_text: str) -> List[Dict[str, Any]]:
        """Parse individual references from a reference section."""
        references = []
        
        # Try multiple splitting strategies
        strategies = [
            # Strategy 1: Split by numbered references [1], [2], etc.
            (r'\[(\d+)\]', 'bracketed_numbers'),
            # Strategy 2: Split by numbered references 1., 2., etc.
            (r'(?:^|\n)\s*(\d+)\.\s+', 'numbered_dots'),
            # Strategy 3: Split by author patterns (Last, F.)
            (r'(?:^|\n)([A-Z][a-z]+,\s*[A-Z]\.)', 'author_patterns'),
            # Strategy 4: Split by double newlines
            (r'\n\s*\n', 'paragraphs'),
        ]
        
        best_refs = []
        max_count = 0
        
        for pattern, strategy_name in strategies:
            try:
                if strategy_name == 'paragraphs':
                    # Simple split for paragraphs
                    parts = re.split(pattern, section_text)
                else:
                    # Split by pattern
                    parts = re.split(pattern, section_text)
                
                strategy_refs = []
                for part in parts:
                    part = part.strip()
                    if self._looks_like_reference(part):
                        strategy_refs.append(self._create_reference_dict(part, strategy_name))
                
                if len(strategy_refs) > max_count:
                    max_count = len(strategy_refs)
                    best_refs = strategy_refs
                    logger.info(f"📋 Strategy '{strategy_name}' found {len(strategy_refs)} references")
                    
            except re.error:
                continue
        
        return best_refs
    
    def _extract_numbered_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract numbered references from throughout the document."""
        references = []
        
        # Look for in-text citations and their corresponding references
        citation_patterns = [
            r'\[(\d+)\]',  # [1], [2], etc.
            r'\((\d+)\)',  # (1), (2), etc.
        ]
        
        # Find all citation numbers
        citation_numbers = set()
        for pattern in citation_patterns:
            matches = re.findall(pattern, text)
            citation_numbers.update(int(num) for num in matches if num.isdigit())
        
        if citation_numbers:
            logger.info(f"🔢 Found citations for numbers: {sorted(citation_numbers)}")
            
            # Look for corresponding reference text for each number
            for num in sorted(citation_numbers):
                ref_patterns = [
                    rf'(?:^|\n)\s*\[{num}\]\s+([^\[\n]+(?:\n(?!\s*\[\d+\])[^\[\n]+)*)',
                    rf'(?:^|\n)\s*{num}\.\s+([^\n]+(?:\n(?!\s*\d+\.)[^\n]+)*)',
                ]
                
                for pattern in ref_patterns:
                    matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
                    for match in matches:
                        ref_text = match.strip()
                        if self._looks_like_reference(ref_text):
                            references.append(self._create_reference_dict(ref_text, f'numbered_{num}'))
                            break
        
        return references
    
    def _extract_pattern_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references using pattern matching as fallback."""
        references = []
        
        # Comprehensive patterns for different reference formats
        patterns = [
            # Journal articles: Author(s). Title. Journal. Year;Volume:Pages
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*(?:,\s*[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*)*\.\s*[^.]{10,100}\.\s*[A-Z][^.]{5,50}\.\s*\d{4}[;\s]*\d*[:\s]*\d*[-\d]*',
            
            # Books: Author(s). Title. Publisher, Year
            r'[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*(?:,\s*[A-Z][a-z]+)*\.\s*[^.]{10,100}\.\s*[A-Z][^,]{5,50},\s*\d{4}',
            
            # DOI references
            r'[^.\n]*doi:\s*10\.\d+/[^\s\n]+[^.\n]*',
            
            # URLs
            r'[^.\n]*https?://[^\s\n]+[^.\n]*',
            
            # PubMed references
            r'[^.\n]*PMID:\s*\d+[^.\n]*',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            for match in matches:
                ref_text = match.strip()
                if self._looks_like_reference(ref_text):
                    references.append(self._create_reference_dict(ref_text, 'pattern_match'))
        
        return references
    
    def _looks_like_reference(self, text: str) -> bool:
        """Check if text looks like a valid reference."""
        if not text or len(text) < 20:
            return False
        
        # Must contain some reference indicators
        indicators = [
            r'\d{4}',  # Year
            r'[Jj]ournal',  # Journal
            r'[Pp]roc',  # Proceedings
            r'[Vv]ol',  # Volume
            r'pp?\.',  # Pages
            r'doi:',  # DOI
            r'PMID:',  # PubMed
            r'http',  # URL
            r'[A-Z][a-z]+,\s*[A-Z]\.',  # Author pattern
        ]
        
        indicator_count = sum(1 for pattern in indicators if re.search(pattern, text))
        return indicator_count >= 2  # Must have at least 2 indicators
    
    def _create_reference_dict(self, text: str, extraction_method: str) -> Dict[str, Any]:
        """Create a standardized reference dictionary."""
        return {
            "text": text.strip(),
            "extraction_method": extraction_method,
            "source": "improved_mistral_ocr",
            "uuid": str(uuid.uuid4()),
            "metadata": self._extract_basic_metadata(text)
        }
    
    def _extract_basic_metadata(self, text: str) -> Dict[str, Any]:
        """Extract basic metadata from reference text."""
        metadata = {}
        
        # Extract year
        year_match = re.search(r'\b(19|20)\d{2}\b', text)
        if year_match:
            metadata['year'] = year_match.group()
        
        # Extract DOI
        doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', text, re.IGNORECASE)
        if doi_match:
            metadata['doi'] = doi_match.group(1)
        
        # Extract PMID
        pmid_match = re.search(r'PMID:\s*(\d+)', text, re.IGNORECASE)
        if pmid_match:
            metadata['pmid'] = pmid_match.group(1)
        
        return metadata
    
    def _deduplicate_references(self, references: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate references."""
        seen_texts = set()
        unique_refs = []
        
        for ref in references:
            # Normalize text for comparison
            normalized = re.sub(r'\s+', ' ', ref['text'].lower().strip())
            if normalized not in seen_texts and len(normalized) > 20:
                seen_texts.add(normalized)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def _save_references_to_csv(self, references: List[Dict[str, Any]], file_path: str) -> Optional[str]:
        """Save references to CSV file."""
        try:
            import csv
            from datetime import datetime
            
            # Create CSV filename
            base_name = Path(file_path).stem
            csv_filename = f"references_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            csv_path = Path("data/references") / csv_filename
            csv_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['uuid', 'text', 'extraction_method', 'year', 'doi', 'pmid', 'source_document']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for ref in references:
                    metadata = ref.get('metadata', {})
                    writer.writerow({
                        'uuid': ref['uuid'],
                        'text': ref['text'],
                        'extraction_method': ref['extraction_method'],
                        'year': metadata.get('year', ''),
                        'doi': metadata.get('doi', ''),
                        'pmid': metadata.get('pmid', ''),
                        'source_document': Path(file_path).name
                    })
            
            logger.info(f"💾 Saved {len(references)} references to {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"❌ Error saving references to CSV: {e}")
            return None
    
    def _empty_result(self, error_msg: str) -> Dict[str, Any]:
        """Return empty result with error."""
        return {
            "success": False,
            "error": error_msg,
            "total_reference_count": 0,
            "references": []
        }
