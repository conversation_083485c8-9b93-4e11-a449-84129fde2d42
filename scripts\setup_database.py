#!/usr/bin/env python3
"""
Database setup script for Graphiti.
"""

import asyncio
import sys
import argparse
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from database.database_service import (
    get_database_adapter,
    create_episode_node,
    create_fact_node,
    create_entity_node
)
from utils.config import get_config

async def setup_database(reset=False):
    """Set up the database with proper schema and indices."""
    print("🔧 Setting up Graphiti database...")
    
    try:
        # Get database adapter
        db = get_database_adapter()
        
        if reset:
            print("⚠️ Resetting database (this will delete all data)...")
            response = input("Are you sure? Type 'yes' to continue: ")
            if response.lower() != 'yes':
                print("❌ Database reset cancelled.")
                return False
            
            # Clear all data
            await db.execute_query("MATCH (n) DETACH DELETE n")
            print("✅ Database cleared.")
        
        # Create indices and constraints
        print("📊 Creating database indices and constraints...")
        
        # Episode indices
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (e:Episode) ON (e.uuid)")
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (e:Episode) ON (e.name)")
        
        # Fact indices
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (f:Fact) ON (f.uuid)")
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (f:Fact) ON (f.episode_id)")
        
        # Entity indices
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.uuid)")
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.name)")
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR (e:Entity) ON (e.type)")
        
        # Relationship indices
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR ()-[r:RELATES_TO]-() ON (r.type)")
        await db.execute_query("CREATE INDEX IF NOT EXISTS FOR ()-[r:CONTAINS]-() ON (r.type)")
        
        print("✅ Database indices created.")
        
        # Create sample data if database is empty
        result = await db.execute_query("MATCH (n) RETURN count(n) as count")
        if result and result[0]['count'] == 0:
            print("📝 Creating sample data...")
            
            # Create sample episode
            episode_id = "sample-episode-001"
            await create_episode_node(
                "Sample Episode",
                {
                    'uuid': episode_id,
                    'description': 'Sample episode for testing',
                    'created_at': '2025-06-02T00:00:00Z'
                }
            )
            
            # Create sample fact
            await create_fact_node(
                "This is a sample fact for testing the database setup.",
                episode_id,
                {
                    'uuid': 'sample-fact-001',
                    'type': 'sample'
                }
            )
            
            # Create sample entity
            await create_entity_node(
                "Sample Entity",
                "Test",
                {
                    'uuid': 'sample-entity-001',
                    'description': 'A sample entity for testing'
                }
            )
            
            print("✅ Sample data created.")
        
        print("🎉 Database setup completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

async def check_database_status():
    """Check the current status of the database."""
    print("📊 Checking database status...")
    
    try:
        db = get_database_adapter()
        
        # Count nodes by type
        queries = [
            ("Episodes", "MATCH (e:Episode) RETURN count(e) as count"),
            ("Facts", "MATCH (f:Fact) RETURN count(f) as count"),
            ("Entities", "MATCH (e:Entity) RETURN count(e) as count"),
            ("Relationships", "MATCH ()-[r]->() RETURN count(r) as count")
        ]
        
        for name, query in queries:
            result = await db.execute_query(query)
            count = result[0]['count'] if result else 0
            print(f"  {name}: {count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database status check failed: {e}")
        return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Set up Graphiti database")
    parser.add_argument("--reset", action="store_true", help="Reset database (delete all data)")
    parser.add_argument("--status", action="store_true", help="Check database status only")
    
    args = parser.parse_args()
    
    if args.status:
        asyncio.run(check_database_status())
    else:
        success = asyncio.run(setup_database(reset=args.reset))
        if not success:
            sys.exit(1)

if __name__ == "__main__":
    main()
