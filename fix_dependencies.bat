@echo off
echo 🚀 FIXING GRAPHITI DEPENDENCY CONFLICTS
echo ============================================================

echo 📦 Upgrading core dependencies...
pip install --upgrade typing-extensions>=4.12.0
pip install --upgrade pydantic>=2.8.2
pip install --upgrade fastapi>=0.115.9
pip install --upgrade httpx>=0.28.1
pip install --upgrade uvicorn>=0.34.0
pip install --upgrade anyio>=4.8.0
pip install --upgrade openai>=1.68.2
pip install --upgrade python-dotenv>=1.0.1
pip install --upgrade jinja2>=3.1.3

echo.
echo 🔄 Reinstalling problematic packages...
pip install --upgrade --force-reinstall chromadb
pip install --upgrade --force-reinstall google-genai
pip install --upgrade --force-reinstall graphiti-core
pip install --upgrade --force-reinstall langchain
pip install --upgrade --force-reinstall mistralai
pip install --upgrade --force-reinstall ollama
pip install --upgrade --force-reinstall mcp

echo.
echo 📋 Installing requirements...
if exist requirements.txt (
    pip install -r requirements.txt
)

echo.
echo 🔍 Checking for conflicts...
pip check

echo.
echo ✅ DEPENDENCY FIX COMPLETE!
echo ============================================================
echo 🚀 Try starting the frontend now with:
echo    python app.py
echo    or
echo    python start.py ui
echo.
echo 📍 Frontend should be available at: http://localhost:9753
pause
