"""
HTML document processor for web pages and HTML files.
"""

from typing import Dict, Any
import logging
import re

from .base_processor import BaseDocumentProcessor

logger = logging.getLogger(__name__)

class HTMLProcessor(BaseDocumentProcessor):
    """
    HTML document processor for web pages and HTML files.
    """
    
    def __init__(self):
        """Initialize the HTML processor."""
        super().__init__()
        self.supported_extensions = {'.html', '.htm', '.xhtml'}
    
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports HTML files."""
        return self.get_file_extension(file_path) in self.supported_extensions
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process an HTML document and extract text.
        
        Args:
            file_path: Path to the HTML file
            
        Returns:
            Processing result dictionary
        """
        self.log_processing_start(file_path)
        
        # Validate file
        validation = self.validate_file(file_path)
        if not validation["valid"]:
            return {
                "success": False,
                "error": validation["error"]
            }
        
        try:
            # Read HTML content
            html_content = await self._read_html_file(file_path)
            
            if not html_content:
                return {
                    "success": False,
                    "error": "No HTML content found in file"
                }
            
            # Extract text and metadata
            text, html_metadata = await self._extract_from_html(html_content)
            
            if not text:
                return {
                    "success": False,
                    "error": "No text content found in HTML"
                }
            
            # Extract metadata
            metadata = await self.extract_metadata(file_path)
            metadata.update(html_metadata)
            
            self.log_processing_success(file_path, len(text))
            
            return {
                "success": True,
                "text": text,
                "metadata": metadata
            }
            
        except Exception as e:
            error_msg = f"Error processing HTML file: {str(e)}"
            self.log_processing_error(file_path, error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def _read_html_file(self, file_path: str) -> str:
        """Read HTML file with encoding detection."""
        try:
            # Try to detect encoding from file
            import chardet
            
            with open(file_path, 'rb') as file:
                raw_data = file.read()
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
            
            # Read with detected encoding
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
                
        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}, using utf-8")
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    return file.read()
            except UnicodeDecodeError:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                    return file.read()
    
    async def _extract_from_html(self, html_content: str) -> tuple[str, Dict[str, Any]]:
        """Extract text and metadata from HTML content."""
        try:
            from bs4 import BeautifulSoup
            
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract metadata from HTML head
            metadata = await self._extract_html_metadata(soup)
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text from body or entire document
            body = soup.find('body')
            if body:
                text = body.get_text()
            else:
                text = soup.get_text()
            
            # Clean up text
            text = await self._clean_extracted_text(text)
            
            # Add text statistics to metadata
            metadata.update({
                "word_count": len(text.split()),
                "character_count": len(text),
                "line_count": text.count('\n') + 1
            })
            
            return text, metadata
            
        except ImportError:
            logger.warning("BeautifulSoup not available, using basic HTML processing")
            return await self._basic_html_processing(html_content)
        except Exception as e:
            logger.warning(f"BeautifulSoup processing failed: {e}, using basic processing")
            return await self._basic_html_processing(html_content)
    
    async def _extract_html_metadata(self, soup) -> Dict[str, Any]:
        """Extract metadata from HTML head section."""
        metadata = {}
        
        try:
            # Title
            title_tag = soup.find('title')
            if title_tag:
                metadata['title'] = title_tag.get_text().strip()
            
            # Meta tags
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                name = meta.get('name', '').lower()
                content = meta.get('content', '')
                
                if name == 'description':
                    metadata['description'] = content
                elif name == 'keywords':
                    metadata['keywords'] = content
                elif name == 'author':
                    metadata['author'] = content
                elif name == 'generator':
                    metadata['generator'] = content
                elif name == 'viewport':
                    metadata['viewport'] = content
            
            # Language
            html_tag = soup.find('html')
            if html_tag and html_tag.get('lang'):
                metadata['language'] = html_tag.get('lang')
            
            # Count elements
            metadata.update({
                'heading_count': len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])),
                'paragraph_count': len(soup.find_all('p')),
                'link_count': len(soup.find_all('a')),
                'image_count': len(soup.find_all('img')),
                'table_count': len(soup.find_all('table'))
            })
            
        except Exception as e:
            logger.warning(f"Error extracting HTML metadata: {e}")
        
        return metadata
    
    async def _basic_html_processing(self, html_content: str) -> tuple[str, Dict[str, Any]]:
        """Basic HTML processing without BeautifulSoup."""
        # Remove HTML tags using regex
        text = re.sub(r'<script[^>]*>.*?</script>', '', html_content, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<style[^>]*>.*?</style>', '', text, flags=re.DOTALL | re.IGNORECASE)
        text = re.sub(r'<[^>]+>', '', text)
        
        # Clean up text
        text = await self._clean_extracted_text(text)
        
        # Basic metadata
        metadata = {
            "word_count": len(text.split()),
            "character_count": len(text),
            "extraction_method": "regex"
        }
        
        # Try to extract title
        title_match = re.search(r'<title[^>]*>(.*?)</title>', html_content, re.IGNORECASE | re.DOTALL)
        if title_match:
            metadata['title'] = title_match.group(1).strip()
        
        return text, metadata
    
    async def _clean_extracted_text(self, text: str) -> str:
        """Clean up extracted text."""
        # Decode HTML entities
        import html
        text = html.unescape(text)
        
        # Normalize whitespace
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        return text.strip()
