# PowerShell script to start Ollama with monitoring and auto-restart
# Can be run at startup or manually

param(
    [int]$MaxRetries = 3,
    [int]$RetryDelay = 10,
    [switch]$Monitor,
    [switch]$Silent
)

function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    if (-not $Silent) {
        switch ($Level) {
            "ERROR" { Write-Host $logMessage -ForegroundColor Red }
            "WARN"  { Write-Host $logMessage -ForegroundColor Yellow }
            "SUCCESS" { Write-Host $logMessage -ForegroundColor Green }
            default { Write-Host $logMessage -ForegroundColor Cyan }
        }
    }
    
    # Also log to file
    $logFile = Join-Path $env:TEMP "ollama_startup.log"
    Add-Content -Path $logFile -Value $logMessage
}

function Test-OllamaRunning {
    try {
        $process = Get-Process -Name "ollama" -ErrorAction SilentlyContinue
        if ($process) {
            # Check if it's actually responding
            $response = Invoke-RestMethod -Uri "http://localhost:11434/api/version" -TimeoutSec 5 -ErrorAction SilentlyContinue
            return $true
        }
    } catch {
        return $false
    }
    return $false
}

function Start-OllamaService {
    Write-Log "🚀 Starting Ollama AI Service..."
    
    # Check if already running
    if (Test-OllamaRunning) {
        Write-Log "✅ Ollama is already running" "SUCCESS"
        return $true
    }
    
    # Find Ollama executable
    $ollamaExe = Get-Command ollama -ErrorAction SilentlyContinue
    if (-not $ollamaExe) {
        Write-Log "❌ Ollama not found in PATH. Please install Ollama first." "ERROR"
        return $false
    }
    
    Write-Log "🔧 Found Ollama at: $($ollamaExe.Source)"
    
    # Start Ollama
    try {
        $process = Start-Process -FilePath $ollamaExe.Source -ArgumentList "serve" -PassThru -WindowStyle Hidden
        Write-Log "🔄 Started Ollama process (PID: $($process.Id))"
        
        # Wait for it to be ready
        $attempts = 0
        $maxAttempts = 30  # 30 seconds
        
        while ($attempts -lt $maxAttempts) {
            Start-Sleep -Seconds 1
            if (Test-OllamaRunning) {
                Write-Log "✅ Ollama is now running and responding!" "SUCCESS"
                return $true
            }
            $attempts++
        }
        
        Write-Log "⚠️ Ollama started but not responding after 30 seconds" "WARN"
        return $false
        
    } catch {
        Write-Log "❌ Failed to start Ollama: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Monitor-Ollama {
    Write-Log "👁️ Starting Ollama monitoring..."
    
    while ($true) {
        if (-not (Test-OllamaRunning)) {
            Write-Log "⚠️ Ollama is not running. Attempting restart..." "WARN"
            
            $retries = 0
            while ($retries -lt $MaxRetries) {
                if (Start-OllamaService) {
                    Write-Log "✅ Ollama restarted successfully" "SUCCESS"
                    break
                }
                
                $retries++
                Write-Log "❌ Restart attempt $retries failed. Retrying in $RetryDelay seconds..." "ERROR"
                Start-Sleep -Seconds $RetryDelay
            }
            
            if ($retries -eq $MaxRetries) {
                Write-Log "❌ Failed to restart Ollama after $MaxRetries attempts" "ERROR"
                break
            }
        }
        
        # Check every 30 seconds
        Start-Sleep -Seconds 30
    }
}

# Main execution
Write-Log "🎯 Ollama Startup Script Started"

# Start Ollama
$success = Start-OllamaService

if ($success) {
    Write-Log "✅ Ollama startup completed successfully" "SUCCESS"
    
    # Test with a simple model list
    try {
        Start-Sleep -Seconds 2
        $models = ollama list 2>$null
        if ($models) {
            Write-Log "📋 Available models: $(($models | Measure-Object).Count) models found"
        }
    } catch {
        Write-Log "⚠️ Could not list models, but service is running" "WARN"
    }
    
    if ($Monitor) {
        Monitor-Ollama
    }
} else {
    Write-Log "❌ Failed to start Ollama" "ERROR"
    exit 1
}

Write-Log "🏁 Script completed"
