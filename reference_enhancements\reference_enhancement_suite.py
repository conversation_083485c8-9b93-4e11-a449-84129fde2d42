"""
Reference Enhancement Suite

Combines all reference enhancement modules in a single interface.
"""

import asyncio
import logging
import json
import argparse
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from reference_enhancements.reference_deduplication import ReferenceDeduplcator
from reference_enhancements.citation_network import CitationNetwork<PERSON>nalyzer
from reference_enhancements.bibliographic_enrichment import BibliographicEnricher

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('reference_enhancements.log')
    ]
)
logger = logging.getLogger(__name__)

class ReferenceEnhancementSuite:
    """
    Combines all reference enhancement modules in a single interface.
    """
    
    def __init__(self):
        """Initialize the reference enhancement suite."""
        self.deduplicator = ReferenceDeduplcator()
        self.network_analyzer = CitationNetworkAnalyzer()
        self.enricher = BibliographicEnricher()
    
    async def run_all_enhancements(self,
                                 deduplicate: bool = True,
                                 build_network: bool = True,
                                 enrich: bool = True,
                                 batch_size: int = 10,
                                 max_references: int = 50,
                                 output_dir: str = "enhancement_output") -> Dict[str, Any]:
        """
        Run all reference enhancements.
        
        Args:
            deduplicate: Whether to run deduplication
            build_network: Whether to build citation network
            enrich: Whether to run bibliographic enrichment
            batch_size: Batch size for enrichment
            max_references: Maximum references for enrichment
            output_dir: Output directory for results
            
        Returns:
            Combined results from all enhancements
        """
        logger.info("🚀 Starting comprehensive reference enhancement suite")
        logger.info(f"📊 Configuration: deduplicate={deduplicate}, network={build_network}, enrich={enrich}")
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        results = {
            "suite_started": datetime.now().isoformat(),
            "configuration": {
                "deduplicate": deduplicate,
                "build_network": build_network,
                "enrich": enrich,
                "batch_size": batch_size,
                "max_references": max_references,
                "output_dir": output_dir
            },
            "results": {}
        }
        
        try:
            # Step 1: Reference Deduplication
            if deduplicate:
                logger.info("\n" + "="*60)
                logger.info("🔍 STEP 1: REFERENCE DEDUPLICATION")
                logger.info("="*60)
                
                dedup_result = await self.deduplicator.find_duplicate_references()
                results["results"]["deduplication"] = dedup_result
                
                if dedup_result.get("success", False):
                    duplicate_groups = dedup_result.get("duplicate_groups", [])
                    
                    # Export duplicate groups
                    if duplicate_groups:
                        export_path = output_path / "duplicate_groups.csv"
                        await self.deduplicator.export_duplicate_groups(duplicate_groups, str(export_path))
                        
                        # Create duplicate relationships
                        relationship_result = await self.deduplicator.create_duplicate_relationships(duplicate_groups)
                        results["results"]["deduplication"]["relationship_creation"] = relationship_result
                    
                    logger.info(f"✅ Deduplication complete: {dedup_result.get('duplicate_count', 0)} duplicates found")
                else:
                    logger.error(f"❌ Deduplication failed: {dedup_result.get('error', 'Unknown error')}")
            
            # Step 2: Citation Network Analysis
            if build_network:
                logger.info("\n" + "="*60)
                logger.info("🕸️ STEP 2: CITATION NETWORK ANALYSIS")
                logger.info("="*60)
                
                network_result = await self.network_analyzer.build_citation_network()
                results["results"]["citation_network"] = network_result
                
                if network_result.get("success", False):
                    # Save network visualization
                    viz_path = output_path / "citation_network.png"
                    self.network_analyzer.save_network_visualization(str(viz_path))
                    
                    # Export network data
                    network_data_path = output_path / "citation_network.json"
                    await self.network_analyzer.export_network_data(str(network_data_path))
                    
                    # Get most cited documents
                    most_cited = await self.network_analyzer.get_most_cited_documents(limit=10)
                    results["results"]["citation_network"]["most_cited"] = most_cited
                    
                    logger.info(f"✅ Citation network complete: {network_result.get('nodes', 0)} nodes, {network_result.get('edges', 0)} edges")
                else:
                    logger.error(f"❌ Citation network failed: {network_result.get('error', 'Unknown error')}")
            
            # Step 3: Bibliographic Enrichment
            if enrich:
                logger.info("\n" + "="*60)
                logger.info("🔍 STEP 3: BIBLIOGRAPHIC ENRICHMENT")
                logger.info("="*60)
                
                enrichment_result = await self.enricher.enrich_references(
                    batch_size=batch_size,
                    max_references=max_references
                )
                results["results"]["bibliographic_enrichment"] = enrichment_result
                
                if enrichment_result.get("success", False):
                    # Export enriched references
                    enriched_path = output_path / "enriched_references.csv"
                    await self.enricher.export_enriched_references(str(enriched_path))
                    
                    logger.info(f"✅ Enrichment complete: {enrichment_result.get('enriched_count', 0)} references enriched")
                else:
                    logger.error(f"❌ Enrichment failed: {enrichment_result.get('error', 'Unknown error')}")
            
            # Final summary
            results["suite_completed"] = datetime.now().isoformat()
            results["success"] = True
            
            # Save combined results
            results_path = output_path / "enhancement_suite_results.json"
            with open(results_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            logger.info("\n" + "="*60)
            logger.info("🎉 REFERENCE ENHANCEMENT SUITE COMPLETE")
            logger.info("="*60)
            logger.info(f"📊 Results saved to: {output_dir}")
            
            # Print summary
            await self._print_summary(results)
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Error in reference enhancement suite: {e}", exc_info=True)
            results["success"] = False
            results["error"] = str(e)
            results["suite_completed"] = datetime.now().isoformat()
            return results
    
    async def run_deduplication_only(self, output_dir: str = "enhancement_output") -> Dict[str, Any]:
        """Run only reference deduplication."""
        return await self.run_all_enhancements(
            deduplicate=True,
            build_network=False,
            enrich=False,
            output_dir=output_dir
        )
    
    async def run_network_analysis_only(self, output_dir: str = "enhancement_output") -> Dict[str, Any]:
        """Run only citation network analysis."""
        return await self.run_all_enhancements(
            deduplicate=False,
            build_network=True,
            enrich=False,
            output_dir=output_dir
        )
    
    async def run_enrichment_only(self, batch_size: int = 10, max_references: int = 50, output_dir: str = "enhancement_output") -> Dict[str, Any]:
        """Run only bibliographic enrichment."""
        return await self.run_all_enhancements(
            deduplicate=False,
            build_network=False,
            enrich=True,
            batch_size=batch_size,
            max_references=max_references,
            output_dir=output_dir
        )
    
    async def get_enhancement_status(self) -> Dict[str, Any]:
        """Get the current status of all enhancements."""
        try:
            # Get basic statistics
            dedup_stats = await self.deduplicator.find_duplicate_references()
            enriched_refs = await self.enricher.get_enriched_references(limit=10)
            
            status = {
                "deduplication": {
                    "total_references": dedup_stats.get("total_references", 0),
                    "duplicate_count": dedup_stats.get("duplicate_count", 0),
                    "unique_count": dedup_stats.get("unique_count", 0),
                    "duplicate_groups": dedup_stats.get("groups_found", 0)
                },
                "enrichment": {
                    "enriched_references": len(enriched_refs),
                    "sample_enriched": enriched_refs[:5] if enriched_refs else []
                },
                "citation_network": {
                    "network_built": self.network_analyzer.network is not None,
                    "nodes": self.network_analyzer.network.number_of_nodes() if self.network_analyzer.network else 0,
                    "edges": self.network_analyzer.network.number_of_edges() if self.network_analyzer.network else 0
                }
            }
            
            return {
                "success": True,
                "status": status,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error getting enhancement status: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
    
    async def _print_summary(self, results: Dict[str, Any]):
        """Print a summary of enhancement results."""
        print("\n" + "="*60)
        print("📊 REFERENCE ENHANCEMENT SUMMARY")
        print("="*60)
        
        # Deduplication summary
        if "deduplication" in results["results"]:
            dedup = results["results"]["deduplication"]
            if dedup.get("success", False):
                print(f"🔍 DEDUPLICATION:")
                print(f"   • Total references: {dedup.get('total_references', 0)}")
                print(f"   • Duplicates found: {dedup.get('duplicate_count', 0)}")
                print(f"   • Unique references: {dedup.get('unique_count', 0)}")
                print(f"   • Duplicate groups: {dedup.get('groups_found', 0)}")
            else:
                print(f"🔍 DEDUPLICATION: ❌ Failed - {dedup.get('error', 'Unknown error')}")
        
        # Citation network summary
        if "citation_network" in results["results"]:
            network = results["results"]["citation_network"]
            if network.get("success", False):
                print(f"🕸️ CITATION NETWORK:")
                print(f"   • Nodes: {network.get('nodes', 0)}")
                print(f"   • Edges: {network.get('edges', 0)}")
                print(f"   • Citations created: {network.get('citations_created', 0)}")
                
                most_cited = network.get("most_cited", [])
                if most_cited:
                    print(f"   • Top cited document: {most_cited[0].get('document', 'Unknown')}")
            else:
                print(f"🕸️ CITATION NETWORK: ❌ Failed - {network.get('error', 'Unknown error')}")
        
        # Enrichment summary
        if "bibliographic_enrichment" in results["results"]:
            enrichment = results["results"]["bibliographic_enrichment"]
            if enrichment.get("success", False):
                print(f"🔍 BIBLIOGRAPHIC ENRICHMENT:")
                print(f"   • References processed: {enrichment.get('total_processed', 0)}")
                print(f"   • References enriched: {enrichment.get('enriched_count', 0)}")
                completion_rate = enrichment.get('completion_rate', 0) * 100
                print(f"   • Success rate: {completion_rate:.1f}%")
            else:
                print(f"🔍 BIBLIOGRAPHIC ENRICHMENT: ❌ Failed - {enrichment.get('error', 'Unknown error')}")
        
        print("="*60)


async def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Reference Enhancement Suite")
    parser.add_argument("--no-deduplicate", action="store_true", help="Skip deduplication")
    parser.add_argument("--no-network", action="store_true", help="Skip citation network analysis")
    parser.add_argument("--no-enrich", action="store_true", help="Skip bibliographic enrichment")
    parser.add_argument("--batch-size", type=int, default=10, help="Batch size for enrichment")
    parser.add_argument("--max-references", type=int, default=50, help="Maximum references for enrichment")
    parser.add_argument("--output-dir", default="enhancement_output", help="Output directory")
    parser.add_argument("--status", action="store_true", help="Show enhancement status only")
    
    args = parser.parse_args()
    
    # Initialize suite
    suite = ReferenceEnhancementSuite()
    
    if args.status:
        # Show status only
        status = await suite.get_enhancement_status()
        print(json.dumps(status, indent=2))
        return
    
    # Run enhancements
    result = await suite.run_all_enhancements(
        deduplicate=not args.no_deduplicate,
        build_network=not args.no_network,
        enrich=not args.no_enrich,
        batch_size=args.batch_size,
        max_references=args.max_references,
        output_dir=args.output_dir
    )
    
    if result.get("success", False):
        print("\n🎉 Reference enhancement suite completed successfully!")
    else:
        print(f"\n❌ Reference enhancement suite failed: {result.get('error', 'Unknown error')}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
