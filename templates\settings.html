<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Graphiti Knowledge Graph</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    
    <style>
        .settings-section {
            margin-bottom: 30px;
        }
        
        .settings-section-header {
            padding-bottom: 10px;
            margin-bottom: 20px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .settings-card {
            height: 100%;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-connected {
            background-color: #28a745;
        }
        
        .status-disconnected {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Settings</h1>
        
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Settings</li>
            </ol>
        </nav>
        
        <!-- Settings Loading -->
        <div id="settings-loading" class="loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p>Loading settings...</p>
        </div>
        
        <!-- Settings Form -->
        <div id="settings-form">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Configuration Settings</h3>
                <div>
                    <button class="btn btn-primary" id="save-settings-button">
                        <i class="bi bi-save"></i> Save Settings
                    </button>
                    <button class="btn btn-outline-secondary" id="reset-settings-button">
                        <i class="bi bi-arrow-counterclockwise"></i> Reset to Defaults
                    </button>
                </div>
            </div>
            
            <div id="save-status" class="alert" style="display: none;"></div>
            
            <!-- LLM Settings -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h4><i class="bi bi-cpu"></i> LLM Settings</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="llm-provider" class="form-label">LLM Provider</label>
                            <select class="form-select" id="llm-provider">
                                <option value="openai">OpenAI</option>
                                <option value="openrouter">OpenRouter</option>
                                <option value="ollama">Ollama (Local)</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="llm-model" class="form-label">LLM Model</label>
                            <select class="form-select" id="llm-model">
                                <option value="meta-llama/llama-4-maverick">Llama 4 Maverick</option>
                                <option value="gpt-4o">GPT-4o</option>
                                <option value="mistral-large">Mistral Large</option>
                                <option value="qwen3-4b">Qwen3 4B</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="llm-api-key" class="form-label">API Key</label>
                            <input type="password" class="form-control" id="llm-api-key" placeholder="Enter API key">
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Embedding Settings -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h4><i class="bi bi-diagram-3"></i> Embedding Settings</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="embedding-model" class="form-label">Embedding Model</label>
                            <select class="form-select" id="embedding-model">
                                <option value="snowflake-arctic-embed2">Snowflake Arctic Embed2</option>
                                <option value="openai-ada-002">OpenAI Ada 002</option>
                                <option value="bge-large">BGE Large</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="chunk-size" class="form-label">Chunk Size</label>
                            <input type="number" class="form-control" id="chunk-size" min="100" max="5000" value="1200">
                            <div class="form-text">Characters per chunk (100-5000)</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="chunk-overlap" class="form-label">Chunk Overlap</label>
                            <input type="number" class="form-control" id="chunk-overlap" min="0" max="500" value="0">
                            <div class="form-text">Overlap between chunks (0-500)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Database Settings -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h4><i class="bi bi-database"></i> Database Settings</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5>FalkorDB</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="falkordb-host" class="form-label">Host</label>
                                    <input type="text" class="form-control" id="falkordb-host" value="localhost">
                                </div>
                                <div class="mb-3">
                                    <label for="falkordb-port" class="form-label">Port</label>
                                    <input type="number" class="form-control" id="falkordb-port" value="6379">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card settings-card">
                            <div class="card-header">
                                <h5>Redis Vector Search</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="redis-host" class="form-label">Host</label>
                                    <input type="text" class="form-control" id="redis-host" value="localhost">
                                </div>
                                <div class="mb-3">
                                    <label for="redis-port" class="form-label">Port</label>
                                    <input type="number" class="form-control" id="redis-port" value="6380">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-outline-primary" id="test-connection-button">
                        <i class="bi bi-check-circle"></i> Test Connection
                    </button>
                    <span id="connection-status" class="ms-3" style="display: none;"></span>
                </div>
            </div>
            
            <!-- System Settings -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h4><i class="bi bi-gear"></i> System Settings</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="max-file-size" class="form-label">Max File Size (MB)</label>
                            <input type="number" class="form-control" id="max-file-size" min="1" max="100" value="50">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="max-parallel-processes" class="form-label">Max Parallel Processes</label>
                            <input type="number" class="form-control" id="max-parallel-processes" min="1" max="16" value="4">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="log-level" class="form-label">Log Level</label>
                            <select class="form-select" id="log-level">
                                <option value="DEBUG">DEBUG</option>
                                <option value="INFO" selected>INFO</option>
                                <option value="WARNING">WARNING</option>
                                <option value="ERROR">ERROR</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- System Status -->
            <div class="settings-section">
                <div class="settings-section-header">
                    <h4><i class="bi bi-info-circle"></i> System Status</h4>
                </div>
                
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center mb-3">
                            <div class="card-body">
                                <h5 class="card-title">FalkorDB</h5>
                                <p class="card-text">
                                    <span class="status-indicator status-connected" id="falkordb-status-indicator"></span>
                                    <span id="falkordb-status">Connected</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Redis</h5>
                                <p class="card-text">
                                    <span class="status-indicator status-connected" id="redis-status-indicator"></span>
                                    <span id="redis-status">Connected</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center mb-3">
                            <div class="card-body">
                                <h5 class="card-title">LLM API</h5>
                                <p class="card-text">
                                    <span class="status-indicator status-connected" id="llm-status-indicator"></span>
                                    <span id="llm-status">Connected</span>
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center mb-3">
                            <div class="card-body">
                                <h5 class="card-title">Embedding API</h5>
                                <p class="card-text">
                                    <span class="status-indicator status-connected" id="embedding-status-indicator"></span>
                                    <span id="embedding-status">Connected</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5>System Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><strong>Version:</strong> <span id="system-version">1.0.0</span></p>
                                        <p><strong>Uptime:</strong> <span id="system-uptime">0 days, 0 hours, 0 minutes</span></p>
                                        <p><strong>Documents Processed:</strong> <span id="documents-processed">0</span></p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><strong>Entities Extracted:</strong> <span id="entities-extracted">0</span></p>
                                        <p><strong>References Extracted:</strong> <span id="references-extracted">0</span></p>
                                        <p><strong>Last Processing:</strong> <span id="last-processing">Never</span></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="static/js/graphiti_ui.js"></script>
    <script src="static/js/graphiti_ui_part7.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the settings page
            initializeSettingsTab();
            
            // Load system status
            loadSystemStatus();
        });
        
        function loadSystemStatus() {
            // Fetch system status from the API
            fetch('/api/system-status')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Update system information
                    document.getElementById('system-version').textContent = data.version;
                    document.getElementById('system-uptime').textContent = data.uptime;
                    document.getElementById('documents-processed').textContent = data.documents_processed;
                    document.getElementById('entities-extracted').textContent = data.entities_extracted;
                    document.getElementById('references-extracted').textContent = data.references_extracted;
                    document.getElementById('last-processing').textContent = data.last_processing;
                    
                    // Update status indicators
                    updateStatusIndicator('falkordb', data.falkordb_connected);
                    updateStatusIndicator('redis', data.redis_connected);
                    updateStatusIndicator('llm', data.llm_connected);
                    updateStatusIndicator('embedding', data.embedding_connected);
                })
                .catch(error => {
                    console.error('Error loading system status:', error);
                });
        }
        
        function updateStatusIndicator(service, connected) {
            const indicator = document.getElementById(`${service}-status-indicator`);
            const status = document.getElementById(`${service}-status`);
            
            if (connected) {
                indicator.className = 'status-indicator status-connected';
                status.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-disconnected';
                status.textContent = 'Disconnected';
            }
        }
    </script>
</body>
</html>
