#!/usr/bin/env python3
"""
Test script for the improved reference extraction system.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_reference_extraction():
    """Test the improved reference extraction on a sample document."""
    
    # Check for Mistral API key
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        print("Please set your Mistral API key to test reference extraction")
        return
    
    # Find a test document
    uploads_dir = Path("uploads")
    if not uploads_dir.exists():
        print("❌ No uploads directory found")
        return

    # Look for the specific pain relief document first
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if pain_relief_files:
        test_file = pain_relief_files[0]
        print(f"🎯 Found pain relief document: {test_file.name}")
    else:
        # Look for PDF files
        pdf_files = list(uploads_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found in uploads directory")
            return
        test_file = pdf_files[0]  # Use the first PDF found
    print(f"🧪 Testing reference extraction on: {test_file.name}")
    
    try:
        # Import required modules
        from utils.mistral_ocr import MistralOCRProcessor
        from services.improved_reference_extractor import ImprovedReferenceExtractor
        
        # Initialize processors
        print("🔧 Initializing Mistral OCR processor...")
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        
        print("🔧 Initializing improved reference extractor...")
        extractor = ImprovedReferenceExtractor(mistral_ocr)
        
        # Extract references
        print("🚀 Starting reference extraction...")
        result = await extractor.extract_references(str(test_file))
        
        # Display results
        print("\n" + "="*60)
        print("📊 REFERENCE EXTRACTION RESULTS")
        print("="*60)
        
        if result.get('success', False):
            print(f"✅ Success: {result['success']}")
            print(f"📄 Document: {result['filename']}")
            print(f"🔍 Method: {result['extraction_method']}")
            print(f"📚 Total References Found: {result['total_reference_count']}")
            print(f"📝 Text Length: {result.get('extracted_text_length', 0):,} characters")
            
            if result.get('csv_path'):
                print(f"💾 CSV Saved: {result['csv_path']}")
            
            # Show first few references
            references = result.get('references', [])
            if references:
                print(f"\n📋 First {min(5, len(references))} References:")
                print("-" * 60)
                for i, ref in enumerate(references[:5], 1):
                    print(f"\n{i}. Method: {ref.get('extraction_method', 'unknown')}")
                    print(f"   Text: {ref['text'][:200]}{'...' if len(ref['text']) > 200 else ''}")
                    if ref.get('metadata'):
                        metadata = ref['metadata']
                        if metadata.get('year'):
                            print(f"   Year: {metadata['year']}")
                        if metadata.get('doi'):
                            print(f"   DOI: {metadata['doi']}")
            
            print(f"\n🎯 TOTAL REFERENCES FOUND: {result['total_reference_count']}")
            
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")
        
        print("="*60)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def compare_extraction_methods():
    """Compare old vs new reference extraction methods."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")

    # Look for the specific pain relief document first
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if pain_relief_files:
        test_file = pain_relief_files[0]
        print(f"🎯 Found pain relief document: {test_file.name}")
    else:
        pdf_files = list(uploads_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found")
            return
        test_file = pdf_files[0]
    print(f"🔄 Comparing extraction methods on: {test_file.name}")
    
    try:
        # Test improved method
        from utils.mistral_ocr import MistralOCRProcessor
        from services.improved_reference_extractor import ImprovedReferenceExtractor
        
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        improved_extractor = ImprovedReferenceExtractor(mistral_ocr)
        
        print("🚀 Testing improved method...")
        improved_result = await improved_extractor.extract_references(str(test_file))
        
        # Test old method
        from services.reference_processor import ReferenceProcessor
        
        print("🔄 Testing old method...")
        old_processor = ReferenceProcessor()
        old_result = await old_processor.extract_references_from_document(str(test_file))
        
        # Compare results
        print("\n" + "="*60)
        print("📊 COMPARISON RESULTS")
        print("="*60)
        
        improved_count = improved_result.get('total_reference_count', 0)
        old_count = old_result.get('total_reference_count', 0)
        
        print(f"🆕 Improved Method: {improved_count} references")
        print(f"🔄 Old Method: {old_count} references")
        print(f"📈 Improvement: {improved_count - old_count:+d} references")
        
        if improved_count > old_count:
            print("✅ Improved method found MORE references!")
        elif improved_count == old_count:
            print("➡️ Both methods found the same number")
        else:
            print("⚠️ Old method found more references")
        
        print("="*60)
        
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 Reference Extraction Test Suite")
    print("="*40)
    
    # Run basic test
    print("\n1️⃣ Testing improved reference extraction...")
    asyncio.run(test_reference_extraction())
    
    # Run comparison
    print("\n2️⃣ Comparing extraction methods...")
    asyncio.run(compare_extraction_methods())
    
    print("\n✅ Testing complete!")
