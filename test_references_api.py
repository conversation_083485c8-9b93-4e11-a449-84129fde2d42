#!/usr/bin/env python3
"""
Test script to check references API.
"""

import requests
import time

def test_references_api():
    """Test the references API endpoint"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing References API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/fast/references", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Count: {data.get('count', 0)}")
            print(f"Files processed: {data.get('files_processed', 0)}")
            print(f"Status: {data.get('status', 'unknown')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Also test graph stats
    print("\n🔍 Testing Graph Stats API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/fast/graph-stats", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Episodes: {data.get('total_episodes', 0)}")
            print(f"Entities: {data.get('total_entities', 0)}")
            print(f"Relationships: {data.get('total_relationships', 0)}")
            print(f"Status: {data.get('status', 'unknown')}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_references_api()
