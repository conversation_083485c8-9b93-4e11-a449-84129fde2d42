#!/usr/bin/env python3
"""
Debug script to check Episode-Fact relationships.
"""

import asyncio

async def debug_episode_fact_relationships():
    """Debug Episode-Fact relationships"""
    print("🔍 Debugging Episode-Fact Relationships")
    print("=" * 40)
    
    try:
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        # Test 1: Check what relationships exist between Episodes and Facts
        print("1. Checking Episode-Fact relationships...")
        query = """
        MATCH (e:Episode)-[r]-(f:Fact)
        RETURN type(r) as relationship_type, count(*) as count
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found Episode-Fact relationships:")
            for row in result[1]:
                rel_type = row[0] if row[0] else "N/A"
                count = row[1] if row[1] else 0
                print(f"  - {rel_type}: {count} relationships")
        else:
            print("❌ No Episode-Fact relationships found")
        
        # Test 2: Check if Facts have episode_uuid property instead
        print("\n2. Checking if Facts have episode_uuid property...")
        query = """
        MATCH (f:Fact)
        WHERE f.episode_uuid IS NOT NULL
        RETURN count(f) as facts_with_episode_uuid
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            count = result[1][0][0]
            print(f"✅ Found {count} facts with episode_uuid property")
        else:
            print("❌ No facts with episode_uuid property")
        
        # Test 3: Check sample fact structure
        print("\n3. Checking sample fact structure...")
        query = """
        MATCH (f:Fact)
        RETURN f.uuid as uuid, f.episode_uuid as episode_uuid, f.body as body
        LIMIT 3
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"Sample facts:")
            for i, row in enumerate(result[1]):
                uuid = row[0] if row[0] else "N/A"
                episode_uuid = row[1] if row[1] else "N/A"
                body = row[2] if row[2] else "N/A"
                print(f"  {i+1}. Fact {uuid[:8]}...")
                print(f"     Episode UUID: {episode_uuid}")
                print(f"     Body: {body[:100]}...")
                print()
        
        # Test 4: Try to find Episodes that match Facts
        print("4. Checking if Episodes exist for Facts...")
        query = """
        MATCH (f:Fact)
        WHERE f.episode_uuid IS NOT NULL
        WITH f.episode_uuid as ep_uuid
        MATCH (e:Episode)
        WHERE e.uuid = ep_uuid
        RETURN count(e) as matching_episodes
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            count = result[1][0][0]
            print(f"✅ Found {count} Episodes that match Fact episode_uuids")
        else:
            print("❌ No Episodes match Fact episode_uuids")
        
        # Test 5: Check if we can find Andrographis facts with episode info
        print("\n5. Checking Andrographis facts with episode info...")
        query = """
        MATCH (f:Fact)
        WHERE toLower(f.body) CONTAINS 'andrographis'
        RETURN f.uuid as fact_uuid, f.episode_uuid as episode_uuid, f.body as body
        LIMIT 3
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"Andrographis facts with episode info:")
            for i, row in enumerate(result[1]):
                fact_uuid = row[0] if row[0] else "N/A"
                episode_uuid = row[1] if row[1] else "N/A"
                body = row[2] if row[2] else "N/A"
                print(f"  {i+1}. Fact {fact_uuid[:8]}...")
                print(f"     Episode: {episode_uuid}")
                print(f"     Body: {body[:150]}...")
                print()
        
        # Test 6: Try the exact Q&A service query with Andrographis
        print("6. Testing exact Q&A service query...")
        query = """
        MATCH (f:Fact)
        WHERE f.body CONTAINS 'herb' AND f.body CONTAINS 'andrographis'
        WITH f
        MATCH (e:Episode)-[:CONTAINS]->(f)
        RETURN f.uuid as uuid, f.body as body, e.name as document_name
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Q&A service query found {len(result[1])} facts")
            for i, row in enumerate(result[1]):
                fact_uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                doc_name = row[2] if row[2] else "N/A"
                print(f"  {i+1}. {doc_name}: {body[:100]}...")
        else:
            print("❌ Q&A service query found no facts")
            
        # Test 7: Try alternative query without Episode relationship
        print("\n7. Testing alternative query without Episode relationship...")
        query = """
        MATCH (f:Fact)
        WHERE f.body CONTAINS 'herb' AND f.body CONTAINS 'andrographis'
        RETURN f.uuid as uuid, f.body as body, f.episode_uuid as episode_uuid
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Alternative query found {len(result[1])} facts")
            for i, row in enumerate(result[1]):
                fact_uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                episode_uuid = row[2] if row[2] else "N/A"
                print(f"  {i+1}. Episode {episode_uuid}: {body[:100]}...")
        else:
            print("❌ Alternative query found no facts")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_episode_fact_relationships())
