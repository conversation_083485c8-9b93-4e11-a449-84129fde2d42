#!/usr/bin/env python3
"""
Test script to check entities display.
"""

import requests

def test_entities_display():
    """Test entities API for display"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entities Display")
    print("=" * 40)
    
    try:
        # Test with small limit for debugging
        response = requests.get(f"{base_url}/api/entities?limit=5&page=1", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"Response keys: {list(data.keys())}")
            print(f"Entities count: {len(data.get('entities', []))}")
            print(f"Total count: {data.get('total_count', 0)}")
            print(f"Count: {data.get('count', 0)}")
            
            entities = data.get('entities', [])
            if entities:
                print(f"\nFirst 3 entities:")
                for i, entity in enumerate(entities[:3]):
                    print(f"  {i+1}. UUID: {entity.get('uuid', 'N/A')}")
                    print(f"     Name: '{entity.get('name', 'N/A')}'")
                    print(f"     Type: '{entity.get('type', 'N/A')}'")
                    print(f"     Mentions: {entity.get('mention_count', 0)}")
                    print()
            else:
                print("No entities in response")
                
            # Check pagination
            pagination = data.get('pagination', {})
            if pagination:
                print(f"Pagination: {pagination}")
            
        else:
            print(f"Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entities_display()
