# Graphiti Tests

This directory contains unit tests for the Graphiti application.

## Running Tests

You can run all tests using the `run_tests.py` script:

```bash
python run_tests.py
```

To run a specific test, use the `--pattern` option:

```bash
python run_tests.py --pattern=settings
```

Alternatively, you can use pytest directly:

```bash
pytest tests/
```

## Test Coverage

To generate a test coverage report, run:

```bash
pytest tests/ --cov=. --cov-report=html
```

This will generate an HTML coverage report in the `htmlcov` directory.

## Test Structure

The tests are organized by module:

- `test_settings.py`: Tests for the settings module
- `test_file_utils.py`: Tests for the file utilities module
- `test_text_utils.py`: Tests for the text utilities module

## Writing Tests

When writing tests, follow these guidelines:

1. Create a new test file for each module you want to test
2. Use the `unittest` framework
3. Use descriptive test method names
4. Add docstrings to test methods
5. Use assertions to verify expected behavior
6. Mock external dependencies

Example:

```python
import unittest
from unittest.mock import patch, MagicMock

class TestMyModule(unittest.TestCase):
    def setUp(self):
        # Set up test environment
        pass
    
    def tearDown(self):
        # Clean up test environment
        pass
    
    def test_my_function(self):
        """Test that my_function works correctly."""
        # Arrange
        input_data = "test"
        expected_output = "TEST"
        
        # Act
        actual_output = my_function(input_data)
        
        # Assert
        self.assertEqual(actual_output, expected_output)
    
    @patch('my_module.external_dependency')
    def test_function_with_dependency(self, mock_dependency):
        """Test that function_with_dependency works correctly."""
        # Arrange
        mock_dependency.return_value = "mocked_value"
        
        # Act
        result = function_with_dependency()
        
        # Assert
        self.assertEqual(result, "mocked_value")
        mock_dependency.assert_called_once()
```

## Continuous Integration

Tests are automatically run on GitHub Actions when you push to the main branch or create a pull request. See the `.github/workflows/ci.yml` file for details.
