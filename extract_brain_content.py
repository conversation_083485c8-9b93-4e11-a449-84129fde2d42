#!/usr/bin/env python3
"""
Extract raw content from Brain.one file to see what references are actually there.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(os.getcwd())

async def extract_brain_content():
    """Extract and display the raw content from Brain.one file."""
    
    print("🧠 EXTRACTING RAW CONTENT FROM BRAIN.ONE FILE")
    print("=" * 80)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    print(f"📁 Full path: {brain_file}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    try:
        # Try to use the OneNote processor
        from processors.onenote_processor import OneNoteProcessor
        
        processor = OneNoteProcessor()
        print("\n🔄 Extracting content using OneNote processor...")
        
        # Process the file
        result = await processor.process_document(str(brain_file))
        
        if result:
            print("✅ OneNote processor returned result")
            print(f"📊 Result keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
            
            # Check for content
            if isinstance(result, dict) and 'content' in result:
                content = result['content']
                print(f"📝 Content length: {len(content)} characters")
                
                # Save content to file
                with open("brain_extracted_content.txt", "w", encoding="utf-8") as f:
                    f.write(content)
                print("💾 Saved content to brain_extracted_content.txt")
                
                # Show first 2000 characters
                print("\n📖 FIRST 2000 CHARACTERS OF CONTENT:")
                print("-" * 60)
                print(content[:2000])
                print("-" * 60)
                
                # Look for reference patterns
                import re
                
                # Count potential references
                numbered_refs = re.findall(r'\d+\.\s+[A-Z]', content)
                author_year_refs = re.findall(r'[A-Z][a-z]+,?\s+[A-Z]\..*?\(\d{4}\)', content)
                doi_refs = re.findall(r'doi:\s*10\.\d+', content, re.IGNORECASE)
                
                print(f"\n🔍 REFERENCE PATTERN ANALYSIS:")
                print(f"   Numbered references (X. Author): {len(numbered_refs)}")
                print(f"   Author-year format: {len(author_year_refs)}")
                print(f"   DOI references: {len(doi_refs)}")
                
                if numbered_refs:
                    print(f"\n📚 SAMPLE NUMBERED REFERENCES:")
                    for i, ref in enumerate(numbered_refs[:5]):
                        print(f"   {i+1}. {ref}...")
                
            else:
                print("❌ No 'content' key in result")
                if isinstance(result, dict):
                    for key, value in result.items():
                        print(f"   {key}: {str(value)[:100]}...")
        else:
            print("❌ OneNote processor returned None")
            
    except Exception as e:
        print(f"❌ Error with OneNote processor: {e}")
        
        # Try alternative method - check if it's already converted to PDF
        try:
            print("\n🔄 Trying alternative PDF extraction...")
            
            # Look for temporary PDF files
            import tempfile
            temp_dir = Path(tempfile.gettempdir())
            pdf_files = list(temp_dir.glob("*.pdf"))
            
            print(f"📁 Found {len(pdf_files)} PDF files in temp directory")
            
            # Try to find recent PDF that might be from OneNote conversion
            recent_pdfs = [f for f in pdf_files if (f.stat().st_mtime > (brain_file.stat().st_mtime - 3600))]
            
            if recent_pdfs:
                print(f"📄 Found {len(recent_pdfs)} recent PDF files")
                
                # Try to extract from the most recent PDF
                recent_pdf = max(recent_pdfs, key=lambda f: f.stat().st_mtime)
                print(f"📖 Trying to extract from: {recent_pdf}")
                
                try:
                    import PyPDF2
                    
                    with open(recent_pdf, 'rb') as pdf_file:
                        pdf_reader = PyPDF2.PdfReader(pdf_file)
                        text = ""
                        for page in pdf_reader.pages:
                            text += page.extract_text()
                    
                    print(f"✅ Extracted {len(text)} characters from PDF")
                    
                    # Save PDF content
                    with open("brain_pdf_content.txt", "w", encoding="utf-8") as f:
                        f.write(text)
                    print("💾 Saved PDF content to brain_pdf_content.txt")
                    
                    # Show first 2000 characters
                    print("\n📖 FIRST 2000 CHARACTERS FROM PDF:")
                    print("-" * 60)
                    print(text[:2000])
                    print("-" * 60)
                    
                except Exception as pdf_error:
                    print(f"❌ PDF extraction error: {pdf_error}")
            
        except Exception as alt_error:
            print(f"❌ Alternative extraction error: {alt_error}")

if __name__ == "__main__":
    asyncio.run(extract_brain_content())
