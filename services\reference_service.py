"""
Reference extraction and management service for the Graphiti application.
"""

import os
import csv
import json
import io
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union, Set
from datetime import datetime
from collections import Counter

from utils.config import REFERENCES_DIR, PROCESSED_DIR
from utils.logging_utils import get_logger
from reference_extraction import ReferenceExtractor
from models.reference import Reference, ReferenceList, ReferenceFilter, CitationLink, CitationNetwork

# Set up logger
logger = get_logger(__name__)

async def extract_references_from_document(file_path: Union[str, Path], llm_provider: str = 'openai') -> Dict[str, Any]:
    """
    Extract references from a document.

    Args:
        file_path: Path to the document
        llm_provider: LLM provider to use

    Returns:
        Extracted references
    """
    logger.info(f"Extracting references from {file_path}")

    try:
        # Initialize reference extractor
        reference_extractor = ReferenceExtractor(llm_provider=llm_provider)

        # Process the document
        references = await reference_extractor.process_document(file_path)

        # Create a dictionary with the right format for the JSON/CSV writer
        references_dict = {
            "filename": Path(file_path).name,
            "file_path": str(file_path),
            "extraction_date": datetime.now().isoformat(),
            "success": True
        }

        # Add references based on the type
        if isinstance(references, dict):
            references_dict["llm_references"] = references.get("llm_references", [])
            references_dict["regex_references"] = references.get("regex_references", [])
        elif isinstance(references, list):
            # Determine if it's a list of LLM references or regex references
            if len(references) > 0 and isinstance(references[0], dict):
                references_dict["llm_references"] = references
                references_dict["regex_references"] = []
            else:
                references_dict["llm_references"] = []
                references_dict["regex_references"] = references

        # Generate a file ID
        import uuid
        file_id = str(uuid.uuid4())

        # Save references to files
        json_path = PROCESSED_DIR / f"{file_id}_references.json"
        csv_path = PROCESSED_DIR / f"{file_id}_references.csv"

        reference_extractor.save_references_to_json(references_dict, str(json_path))

        # Save to CSV even if no references found (creates empty CSV with headers)
        llm_refs = len(references_dict.get("llm_references", []))
        regex_refs = len(references_dict.get("regex_references", []))

        if llm_refs > 0 or regex_refs > 0:
            reference_extractor.save_references_to_csv(references_dict, str(csv_path))
        else:
            # Create empty CSV with headers
            fields = [
                "source_document", "extraction_method", "reference_text",
                "authors", "title", "year", "journal", "volume", "issue", "pages",
                "doi", "url"
            ]
            with open(csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()

        # Copy to references directory
        os.makedirs(REFERENCES_DIR, exist_ok=True)
        references_csv_path = REFERENCES_DIR / f"{file_id}_references.csv"

        import shutil
        shutil.copy(str(csv_path), str(references_csv_path))

        # Update master CSV
        await update_master_csv(references_dict)

        return references_dict

    except Exception as e:
        logger.error(f"Error extracting references: {e}")
        return {
            "filename": Path(file_path).name,
            "file_path": str(file_path),
            "extraction_date": datetime.now().isoformat(),
            "success": False,
            "error": str(e),
            "llm_references": [],
            "regex_references": []
        }

async def update_master_csv(references_dict: Dict[str, Any]) -> bool:
    """
    Update the master references CSV file.

    Args:
        references_dict: References dictionary

    Returns:
        True if successful, False otherwise
    """
    # Path to the master CSV file
    master_csv_path = REFERENCES_DIR / "all_references.csv"

    # Get references
    llm_references = references_dict.get("llm_references", [])
    regex_references = references_dict.get("regex_references", [])

    # If no references, return early
    if not llm_references and not regex_references:
        logger.info("No references to add to master CSV")
        return True

    # Prepare new references to add
    new_references = []

    # Add regex references
    for ref in regex_references:
        new_references.append({
            "source_document": references_dict.get("filename", ""),
            "extraction_method": "regex",
            "reference_text": ref,
            "authors": "",
            "title": "",
            "year": "",
            "journal": "",
            "volume": "",
            "issue": "",
            "pages": "",
            "doi": "",
            "url": ""
        })

    # Add LLM references
    for ref in llm_references:
        # Convert authors list to string if needed
        authors = ref.get("authors", "")
        if isinstance(authors, list):
            authors = "; ".join(authors)

        new_references.append({
            "source_document": references_dict.get("filename", ""),
            "extraction_method": "llm",
            "reference_text": "",
            "authors": authors,
            "title": ref.get("title", ""),
            "year": ref.get("year", ""),
            "journal": ref.get("journal", ref.get("conference", ref.get("book", ""))),
            "volume": ref.get("volume", ""),
            "issue": ref.get("issue", ""),
            "pages": ref.get("pages", ""),
            "doi": ref.get("doi", ""),
            "url": ref.get("url", "")
        })

    # Define the fields for the CSV
    fields = [
        "source_document", "extraction_method", "reference_text",
        "authors", "title", "year", "journal", "volume", "issue", "pages",
        "doi", "url"
    ]

    try:
        # Check if the master CSV exists
        if os.path.exists(master_csv_path):
            # Read existing references
            existing_references = []
            try:
                with open(master_csv_path, 'r', encoding='utf-8', newline='') as f:
                    # Handle BOM if present
                    content = f.read()
                    if content.startswith('\ufeff'):
                        content = content[1:]

                    # Read CSV from string
                    reader = csv.DictReader(io.StringIO(content))
                    for row in reader:
                        # Clean up BOM in keys if present
                        clean_row = {}
                        for key, value in row.items():
                            clean_key = key.replace('\ufeff', '')
                            clean_row[clean_key] = value
                        existing_references.append(clean_row)
            except Exception as e:
                logger.error(f"Error reading existing references: {e}")
                existing_references = []

            # Combine existing and new references
            all_references = existing_references + new_references

            # Write back to the master CSV
            with open(master_csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()
                writer.writerows(all_references)
        else:
            # Create a new master CSV
            with open(master_csv_path, 'w', encoding='utf-8', newline='') as f:
                writer = csv.DictWriter(f, fieldnames=fields)
                writer.writeheader()
                writer.writerows(new_references)

        logger.info(f"Updated master references CSV: {master_csv_path}")
        return True

    except Exception as e:
        logger.error(f"Error updating master references CSV: {e}")
        return False

async def get_reference_count() -> int:
    """
    Get the total number of references.

    Returns:
        Total number of references
    """
    try:
        # Path to the master CSV file
        master_csv_path = REFERENCES_DIR / "all_references.csv"

        # Check if the file exists
        if not os.path.exists(master_csv_path):
            return 0

        # Read the CSV file and count rows
        count = 0
        try:
            with open(master_csv_path, 'r', encoding='utf-8', newline='') as f:
                # Handle BOM if present
                content = f.read()
                if content.startswith('\ufeff'):
                    content = content[1:]

                # Read CSV from string
                reader = csv.DictReader(io.StringIO(content))
                count = sum(1 for _ in reader)
        except Exception as e:
            logger.error(f"Error counting references: {e}")
            return 0

        return count

    except Exception as e:
        logger.error(f"Error getting reference count: {str(e)}")
        return 0

async def get_all_references(filter: Optional[ReferenceFilter] = None) -> ReferenceList:
    """
    Get all references.

    Args:
        filter: Optional filter criteria

    Returns:
        List of references
    """
    # Path to the master CSV file
    master_csv_path = REFERENCES_DIR / "all_references.csv"

    # Check if the file exists
    if not os.path.exists(master_csv_path):
        return ReferenceList(references=[])

    # Read the CSV file
    references = []
    try:
        with open(master_csv_path, 'r', encoding='utf-8', newline='') as f:
            # Handle BOM if present
            content = f.read()
            if content.startswith('\ufeff'):
                content = content[1:]

            # Read CSV from string
            reader = csv.DictReader(io.StringIO(content))
            for row in reader:
                # Clean up BOM in keys if present
                clean_row = {}
                for key, value in row.items():
                    clean_key = key.replace('\ufeff', '')
                    clean_row[clean_key] = value

                # Apply filters if provided
                if filter:
                    # Filter by source document
                    if filter.source_document and filter.source_document != clean_row.get("source_document", ""):
                        continue

                    # Filter by authors
                    if filter.authors and filter.authors.lower() not in clean_row.get("authors", "").lower():
                        continue

                    # Filter by year
                    if filter.year and filter.year != clean_row.get("year", ""):
                        continue

                    # Filter by journal
                    if filter.journal and filter.journal.lower() not in clean_row.get("journal", "").lower():
                        continue

                    # Filter by extraction method
                    if filter.extraction_method and filter.extraction_method != clean_row.get("extraction_method", ""):
                        continue

                    # Filter by search query
                    if filter.search_query:
                        query = filter.search_query.lower()
                        found = False
                        for field in ["authors", "title", "journal", "reference_text"]:
                            if query in clean_row.get(field, "").lower():
                                found = True
                                break
                        if not found:
                            continue

                references.append(Reference(**clean_row))

    except Exception as e:
        logger.error(f"Error getting references: {e}")

    return ReferenceList(references=references)

async def get_reference_statistics() -> Dict[str, Any]:
    """
    Get statistics about references.

    Returns:
        Dictionary with reference statistics
    """
    # Get all references
    references = await get_all_references()

    # Initialize statistics
    stats = {
        "total_references": len(references.references),
        "references_by_document": {},
        "references_by_year": {},
        "references_by_journal": {},
        "references_by_extraction_method": {
            "llm": 0,
            "regex": 0,
            "manual": 0
        },
        "top_authors": {},
        "documents_with_references": 0
    }

    # Process references
    documents = set()
    journals = set()
    authors_set = set()

    for ref in references.references:
        # Count by document
        doc = ref.source_document
        if doc:
            documents.add(doc)
            stats["references_by_document"][doc] = stats["references_by_document"].get(doc, 0) + 1

        # Count by year
        year = ref.year
        if year:
            stats["references_by_year"][year] = stats["references_by_year"].get(year, 0) + 1

        # Count by journal
        journal = ref.journal
        if journal:
            journals.add(journal)
            stats["references_by_journal"][journal] = stats["references_by_journal"].get(journal, 0) + 1

        # Count by extraction method
        method = ref.extraction_method
        if method:
            stats["references_by_extraction_method"][method] = stats["references_by_extraction_method"].get(method, 0) + 1

        # Count authors
        authors = ref.authors
        if authors:
            # Split authors by common separators
            author_list = [a.strip() for a in re.split(r';|,|and', authors) if a.strip()]
            for author in author_list:
                authors_set.add(author)
                stats["top_authors"][author] = stats["top_authors"].get(author, 0) + 1

    # Count documents with references
    stats["documents_with_references"] = len(documents)
    stats["total_journals"] = len(journals)
    stats["total_authors"] = len(authors_set)

    # Sort and limit top authors
    stats["top_authors"] = dict(sorted(stats["top_authors"].items(), key=lambda x: x[1], reverse=True)[:20])

    return stats

async def build_citation_network() -> CitationNetwork:
    """
    Build a citation network from references.

    Returns:
        Citation network
    """
    # Get all references
    references = await get_all_references()

    # Initialize network
    network = {
        "references": references.references,
        "citations": [],
        "documents": [],
        "last_updated": datetime.now()
    }

    # Extract unique documents
    documents = set()
    for ref in references.references:
        if ref.source_document:
            documents.add(ref.source_document)

    network["documents"] = sorted(list(documents))

    # Create citation links based on title similarity
    citations = []
    ref_dict = {i: ref for i, ref in enumerate(references.references)}

    for i, ref1 in ref_dict.items():
        if not ref1.title:
            continue

        title1 = ref1.title.lower()

        for j, ref2 in ref_dict.items():
            if i == j or not ref2.title:
                continue

            title2 = ref2.title.lower()

            # Simple similarity check - could be improved with fuzzy matching
            if title1 == title2 or (len(title1) > 20 and title1 in title2) or (len(title2) > 20 and title2 in title1):
                citations.append(CitationLink(
                    source_reference_id=str(i),
                    target_reference_id=str(j),
                    confidence=1.0
                ))

    network["citations"] = citations

    return CitationNetwork(**network)

async def get_document_references(document_name: str) -> Dict[str, Any]:
    """
    Get references for a specific document.

    Args:
        document_name: Document name

    Returns:
        References for the document
    """
    # Create filter
    filter = ReferenceFilter(source_document=document_name)

    # Get references
    references = await get_all_references(filter)

    return {
        "document": document_name,
        "references": references.references,
        "reference_count": len(references.references)
    }
