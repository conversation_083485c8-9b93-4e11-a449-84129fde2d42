"""
Error Recovery Service for handling and recovering from various system failures.
Provides automatic recovery mechanisms for common error scenarios.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path

from utils.operation_state_manager import get_operation_state_manager
from utils.websocket_manager import get_websocket_manager

logger = logging.getLogger(__name__)

class ErrorRecoveryService:
    """
    Service for handling error recovery and system resilience.
    """
    
    def __init__(self):
        self.recovery_strategies = {
            'network_error': self._recover_network_error,
            'timeout_error': self._recover_timeout_error,
            'memory_error': self._recover_memory_error,
            'file_not_found': self._recover_file_error,
            'permission_error': self._recover_permission_error,
            'processing_error': self._recover_processing_error
        }
        self.recovery_attempts = {}  # Track recovery attempts per operation
        self.max_recovery_attempts = 3
        
    async def handle_operation_error(self, operation_id: str, error_type: str, 
                                   error_details: Dict[str, Any]) -> bool:
        """
        Handle an operation error with appropriate recovery strategy.
        
        Args:
            operation_id: ID of the failed operation
            error_type: Type of error that occurred
            error_details: Additional error information
            
        Returns:
            True if recovery was attempted, False otherwise
        """
        try:
            # Check if we've exceeded max recovery attempts
            if operation_id not in self.recovery_attempts:
                self.recovery_attempts[operation_id] = 0
                
            if self.recovery_attempts[operation_id] >= self.max_recovery_attempts:
                logger.warning(f"Max recovery attempts reached for operation {operation_id}")
                await self._mark_operation_permanently_failed(operation_id, error_details)
                return False
            
            # Increment recovery attempt counter
            self.recovery_attempts[operation_id] += 1
            
            # Get recovery strategy
            recovery_func = self.recovery_strategies.get(error_type)
            if not recovery_func:
                logger.warning(f"No recovery strategy for error type: {error_type}")
                return False
            
            logger.info(f"Attempting recovery for operation {operation_id}, "
                       f"error type: {error_type}, attempt: {self.recovery_attempts[operation_id]}")
            
            # Execute recovery strategy
            success = await recovery_func(operation_id, error_details)
            
            if success:
                logger.info(f"Recovery successful for operation {operation_id}")
                # Reset recovery attempts on success
                self.recovery_attempts[operation_id] = 0
            else:
                logger.warning(f"Recovery failed for operation {operation_id}")
                
            return success
            
        except Exception as e:
            logger.error(f"Error during recovery for operation {operation_id}: {e}")
            return False
    
    async def _recover_network_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from network-related errors."""
        try:
            # Wait before retry with exponential backoff
            attempt = self.recovery_attempts[operation_id]
            delay = min(2 ** attempt, 60)  # Max 60 seconds
            
            logger.info(f"Network error recovery: waiting {delay} seconds before retry")
            await asyncio.sleep(delay)
            
            # Check if network is available
            import aiohttp
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.get('http://localhost:8000/ws/health', timeout=5) as response:
                        if response.status == 200:
                            # Network is back, attempt to restart operation
                            return await self._restart_operation(operation_id, error_details)
                        else:
                            return False
                except:
                    return False
                    
        except Exception as e:
            logger.error(f"Network recovery error: {e}")
            return False
    
    async def _recover_timeout_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from timeout errors."""
        try:
            # For timeout errors, try to restart with increased timeout
            logger.info(f"Timeout error recovery for operation {operation_id}")
            
            # Wait a bit before retry
            await asyncio.sleep(10)
            
            # Attempt to restart the operation
            return await self._restart_operation(operation_id, error_details)
            
        except Exception as e:
            logger.error(f"Timeout recovery error: {e}")
            return False
    
    async def _recover_memory_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from memory errors."""
        try:
            logger.info(f"Memory error recovery for operation {operation_id}")
            
            # Force garbage collection
            import gc
            gc.collect()
            
            # Wait for memory to be freed
            await asyncio.sleep(30)
            
            # Check available memory
            import psutil
            memory = psutil.virtual_memory()
            if memory.percent < 80:  # If memory usage is below 80%
                return await self._restart_operation(operation_id, error_details)
            else:
                logger.warning("Insufficient memory for recovery")
                return False
                
        except Exception as e:
            logger.error(f"Memory recovery error: {e}")
            return False
    
    async def _recover_file_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from file-related errors."""
        try:
            filename = error_details.get('filename', '')
            logger.info(f"File error recovery for operation {operation_id}, file: {filename}")
            
            # Check if file exists now
            if filename:
                file_path = Path(filename)
                if file_path.exists():
                    return await self._restart_operation(operation_id, error_details)
                else:
                    logger.warning(f"File still not found: {filename}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"File recovery error: {e}")
            return False
    
    async def _recover_permission_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from permission errors."""
        try:
            logger.info(f"Permission error recovery for operation {operation_id}")
            
            # For permission errors, there's not much we can do automatically
            # Log the issue and mark as permanently failed
            await self._mark_operation_permanently_failed(operation_id, error_details)
            return False
            
        except Exception as e:
            logger.error(f"Permission recovery error: {e}")
            return False
    
    async def _recover_processing_error(self, operation_id: str, error_details: Dict) -> bool:
        """Recover from general processing errors."""
        try:
            logger.info(f"Processing error recovery for operation {operation_id}")
            
            # Wait before retry
            await asyncio.sleep(5)
            
            # Attempt to restart the operation
            return await self._restart_operation(operation_id, error_details)
            
        except Exception as e:
            logger.error(f"Processing recovery error: {e}")
            return False
    
    async def _restart_operation(self, operation_id: str, error_details: Dict) -> bool:
        """Attempt to restart a failed operation."""
        try:
            state_manager = get_operation_state_manager()
            websocket_manager = get_websocket_manager()
            
            # Get operation details
            operation = state_manager.get_operation(operation_id)
            if not operation:
                logger.warning(f"Operation {operation_id} not found for restart")
                return False
            
            # Update operation status to processing
            state_manager.update_operation(
                operation_id=operation_id,
                status='processing',
                current_step_name='Restarting after error...',
                error_message=None
            )
            
            # Notify via WebSocket
            await websocket_manager.send_progress_update(operation_id, {
                'status': 'processing',
                'current_step_name': 'Restarting after error...',
                'progress_percentage': operation.get('progress_percentage', 0),
                'recovery_attempt': self.recovery_attempts[operation_id]
            })
            
            # Here you would trigger the actual restart of the processing
            # This depends on your specific processing architecture
            logger.info(f"Operation {operation_id} marked for restart")
            return True
            
        except Exception as e:
            logger.error(f"Error restarting operation {operation_id}: {e}")
            return False
    
    async def _mark_operation_permanently_failed(self, operation_id: str, error_details: Dict):
        """Mark an operation as permanently failed after max recovery attempts."""
        try:
            state_manager = get_operation_state_manager()
            websocket_manager = get_websocket_manager()
            
            # Update persistent state
            state_manager.update_operation(
                operation_id=operation_id,
                status='failed',
                current_step_name='Recovery failed',
                error_message=f"Failed after {self.max_recovery_attempts} recovery attempts"
            )
            
            # Notify via WebSocket
            await websocket_manager.send_error(
                operation_id, 
                f"Operation failed permanently after {self.max_recovery_attempts} recovery attempts",
                error_details
            )
            
            # Clean up recovery tracking
            if operation_id in self.recovery_attempts:
                del self.recovery_attempts[operation_id]
                
            logger.info(f"Operation {operation_id} marked as permanently failed")
            
        except Exception as e:
            logger.error(f"Error marking operation as permanently failed: {e}")
    
    async def cleanup_old_recovery_attempts(self, hours_old: int = 24):
        """Clean up old recovery attempt tracking."""
        try:
            # This is a simple cleanup - in a real system you'd want to track timestamps
            # For now, just clear all recovery attempts periodically
            old_count = len(self.recovery_attempts)
            self.recovery_attempts.clear()
            logger.info(f"Cleaned up {old_count} old recovery attempt records")
            
        except Exception as e:
            logger.error(f"Error cleaning up recovery attempts: {e}")

# Global instance
_error_recovery_service = None

def get_error_recovery_service() -> ErrorRecoveryService:
    """Get the global error recovery service instance."""
    global _error_recovery_service
    if _error_recovery_service is None:
        _error_recovery_service = ErrorRecoveryService()
    return _error_recovery_service
