"""
Search routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional

from services.search_service import search_knowledge_graph, search_documents, search_facts
from models.knowledge_graph import SearchQuery, SearchResult
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["search"])

@router.post("/search", response_model=SearchResult)
async def search(query: SearchQuery):
    """
    Search the knowledge graph.
    
    Args:
        query: Search query
        
    Returns:
        Search results
    """
    try:
        results = await search_knowledge_graph(query)
        return results
    
    except Exception as e:
        logger.error(f"Error searching knowledge graph: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching knowledge graph: {str(e)}"
        )

@router.get("/search/documents")
async def search_document_api(
    query: str = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """
    Search documents.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    try:
        results = await search_documents(query, limit)
        return {"documents": results, "count": len(results)}
    
    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching documents: {str(e)}"
        )

@router.get("/search/facts")
async def search_facts_api(
    query: str = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """
    Search facts.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    try:
        results = await search_facts(query, limit)
        return {"facts": results, "count": len(results)}

    except Exception as e:
        logger.error(f"Error searching facts: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching facts: {str(e)}"
        )


@router.get("/search/entities")
async def search_entities_api(
    query: str = Query(...),
    limit: int = Query(10, ge=1, le=100)
):
    """
    Search entities by name.

    Args:
        query: Search query
        limit: Maximum number of results to return

    Returns:
        Search results
    """
    try:
        from database.database_service import get_falkordb_adapter

        adapter = await get_falkordb_adapter()

        # Search entities by name (case-insensitive)
        search_query = f"""
        MATCH (e:Entity)
        WHERE toLower(e.name) CONTAINS toLower('{query}')
        OPTIONAL MATCH (f:Fact)-[:MENTIONS]->(e)
        WITH e, count(f) as mentions
        RETURN e.uuid as uuid, e.name as name, e.type as type, mentions
        ORDER BY mentions DESC, e.name
        LIMIT {limit}
        """

        result = adapter.execute_cypher(search_query)
        entities = []

        if result and len(result) > 1:
            for row in result[1]:
                entities.append({
                    "uuid": row[0],
                    "name": row[1],
                    "type": row[2],
                    "mention_count": row[3]
                })

        return {"entities": entities, "count": len(entities)}

    except Exception as e:
        logger.error(f"Error searching entities: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error searching entities: {str(e)}"
        )
