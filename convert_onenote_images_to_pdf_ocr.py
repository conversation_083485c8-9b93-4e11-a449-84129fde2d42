#!/usr/bin/env python3
"""
Convert OneNote embedded images to PDF and process with Mistral OCR.

Since Mistral OCR doesn't support direct image processing, this script:
1. Extracts images from OneNote files
2. Converts each image to a PDF document
3. Processes the PDFs with Mistral OCR to extract text content
"""

import sys
import os
import logging
import asyncio
import tempfile
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def convert_onenote_images_to_pdf_and_ocr():
    """Convert OneNote images to PDF and process with OCR."""
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Processing images from: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from one_extract import OneNoteExtractor, OneNoteExtractorError
        from utils.mistral_ocr import MistralOCRProcessor
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Image as ReportLabImage
        from PIL import Image
        
        # Initialize Mistral OCR
        mistral_ocr = MistralOCRProcessor()
        print("✅ Initialized Mistral OCR processor")
        
        # Read the OneNote file
        with open(onenote_file, 'rb') as f:
            file_data = f.read()
        
        # Extract content with one-extract
        try:
            extractor = OneNoteExtractor(data=file_data)
            print("✅ Initialized OneNote extractor")
        except OneNoteExtractorError as e:
            print(f"❌ OneNote extraction failed: {e}")
            return False
        
        # Extract embedded files (images)
        print("\n🖼️ CONVERTING IMAGES TO PDF AND PROCESSING:")
        print("=" * 60)
        
        embedded_files = list(extractor.extract_files())
        print(f"📎 Found {len(embedded_files)} embedded files")
        
        total_extracted_text = ""
        successful_ocr_count = 0
        
        for i, file_data in enumerate(embedded_files):
            print(f"\n📷 Processing embedded file {i+1}:")
            print(f"   Size: {len(file_data):,} bytes")
            
            # Check if it's an image
            if file_data.startswith(b'\x89PNG'):
                file_type = "PNG"
                file_ext = ".png"
            elif file_data.startswith(b'\xff\xd8\xff'):
                file_type = "JPEG"
                file_ext = ".jpg"
            elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
                file_type = "GIF"
                file_ext = ".gif"
            else:
                print(f"   ⚠️ Not an image file (skipping)")
                continue
            
            print(f"   🖼️ Image type: {file_type}")
            
            # Save image to temporary file
            with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_image_file:
                temp_image_file.write(file_data)
                temp_image_path = temp_image_file.name
            
            try:
                # Convert image to PDF
                print(f"   📄 Converting {file_type} to PDF...")
                pdf_path = await convert_image_to_pdf(temp_image_path, file_type, i+1)
                
                if pdf_path and pdf_path.exists():
                    print(f"   ✅ PDF created: {pdf_path.stat().st_size:,} bytes")
                    
                    # Process PDF with Mistral OCR
                    print(f"   🔍 Processing PDF with Mistral OCR...")
                    extracted_text = await mistral_ocr.extract_text_from_document(str(pdf_path))
                    
                    if extracted_text and len(extracted_text.strip()) > 10:
                        print(f"   🎉 OCR SUCCESS: Extracted {len(extracted_text)} characters")
                        print(f"   📝 Preview: {extracted_text[:200]}...")
                        
                        total_extracted_text += f"\n\n=== IMAGE {i+1} CONTENT ({file_type} → PDF → OCR) ===\n"
                        total_extracted_text += extracted_text
                        successful_ocr_count += 1
                    else:
                        print(f"   ❌ OCR returned no text from PDF")
                    
                    # Clean up PDF
                    try:
                        pdf_path.unlink()
                    except Exception:
                        pass
                else:
                    print(f"   ❌ Failed to create PDF from {file_type}")
                
            except Exception as e:
                print(f"   ❌ Error processing {file_type}: {e}")
            
            finally:
                # Clean up image file
                try:
                    Path(temp_image_path).unlink()
                except Exception:
                    pass
        
        # Summary
        print(f"\n📊 IMAGE → PDF → OCR PROCESSING SUMMARY:")
        print("=" * 60)
        print(f"Total embedded files: {len(embedded_files)}")
        print(f"Successful OCR extractions: {successful_ocr_count}")
        print(f"Total text extracted: {len(total_extracted_text):,} characters")
        
        if total_extracted_text:
            # Save the extracted content
            output_file = Path("onenote_image_pdf_ocr_content.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== ONENOTE IMAGE → PDF → OCR EXTRACTED CONTENT ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Successful OCR extractions: {successful_ocr_count}/{len(embedded_files)}\n")
                f.write(f"Total text extracted: {len(total_extracted_text):,} characters\n\n")
                f.write("=== EXTRACTED TEXT FROM IMAGES VIA PDF ===\n")
                f.write(total_extracted_text)
            
            print(f"💾 Saved OCR results to: {output_file}")
            
            # Show preview of extracted content
            print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
            print("=" * 60)
            print(total_extracted_text[:1000])
            if len(total_extracted_text) > 1000:
                print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
            print("=" * 60)
            
            # Compare with previous extraction
            print(f"\n🎉 COMPARISON WITH PREVIOUS EXTRACTION:")
            print(f"Previous OneNote extraction: ~1,880 characters (mostly metadata)")
            print(f"Image → PDF → OCR: {len(total_extracted_text):,} characters (actual content)")
            
            if len(total_extracted_text) > 1880:
                improvement = len(total_extracted_text) / 1880
                print(f"Improvement factor: {improvement:.1f}x more actual content!")
                print("🎉 THIS IS THE REAL ONENOTE CONTENT!")
            
            return True
        else:
            print("❌ No text was extracted from any images")
            return False
            
    except ImportError as e:
        print(f"❌ Required libraries not available: {e}")
        print("Make sure you have: one-extract, reportlab, PIL")
        return False
    except Exception as e:
        print(f"❌ Error during image → PDF → OCR processing: {e}")
        import traceback
        traceback.print_exc()
        return False


async def convert_image_to_pdf(image_path: str, file_type: str, index: int) -> Path:
    """
    Convert an image file to PDF format.
    
    Args:
        image_path: Path to the image file
        file_type: Type of image (PNG, JPEG, GIF)
        index: Image index for naming
        
    Returns:
        Path to the created PDF file
    """
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate
        from reportlab.platypus.flowables import Image as ReportLabImage
        from PIL import Image
        
        # Create temporary PDF file
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf_file:
            pdf_path = Path(temp_pdf_file.name)
        
        # Open and process the image
        with Image.open(image_path) as img:
            # Convert to RGB if necessary (for JPEG compatibility)
            if img.mode in ('RGBA', 'LA', 'P'):
                img = img.convert('RGB')
            
            # Get image dimensions
            img_width, img_height = img.size
            
            # Calculate scaling to fit on letter-sized page with margins
            page_width, page_height = letter
            margin = 50
            max_width = page_width - 2 * margin
            max_height = page_height - 2 * margin
            
            # Calculate scale factor
            scale_x = max_width / img_width
            scale_y = max_height / img_height
            scale = min(scale_x, scale_y, 1.0)  # Don't upscale
            
            # Calculate final dimensions
            final_width = img_width * scale
            final_height = img_height * scale
            
            # Create PDF document
            doc = SimpleDocTemplate(str(pdf_path), pagesize=letter)
            
            # Create ReportLab Image object
            rl_image = ReportLabImage(image_path, width=final_width, height=final_height)
            
            # Build PDF
            story = [rl_image]
            doc.build(story)
            
            logger.debug(f"Created PDF from {file_type} image {index}: {pdf_path}")
            return pdf_path
            
    except Exception as e:
        logger.error(f"Error converting image {index} to PDF: {e}")
        return None


async def test_image_to_pdf_conversion():
    """Test the image to PDF conversion process."""
    print("\n🧪 TESTING IMAGE → PDF CONVERSION:")
    print("=" * 60)
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Create a test image with text
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # Add some text that simulates OneNote content
        text_lines = [
            "OneNote Page Content",
            "",
            "Ginger Neuroprotective Effects:",
            "• Gingerol compounds provide neuroprotection",
            "• Anti-inflammatory properties",
            "• Supports cognitive function",
            "",
            "Baicalein Research:",
            "• Inhibits TNF-α pathway",
            "• Reduces NF-κB activation",
            "• Flavonoid with anti-inflammatory effects",
            "",
            "Neurotransmitter Systems:",
            "• Dopamine regulation",
            "• Serotonin pathways",
            "• GABA modulation"
        ]
        
        y_position = 50
        for line in text_lines:
            if line:  # Skip empty lines
                draw.text((50, y_position), line, fill='black')
            y_position += 30
        
        # Save test image
        with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
            img.save(temp_file.name)
            test_image_path = temp_file.name
        
        try:
            # Convert to PDF
            pdf_path = await convert_image_to_pdf(test_image_path, "PNG", 0)
            
            if pdf_path and pdf_path.exists():
                print(f"✅ Test image converted to PDF: {pdf_path.stat().st_size:,} bytes")
                
                # Test OCR on the PDF
                from utils.mistral_ocr import MistralOCRProcessor
                mistral_ocr = MistralOCRProcessor()
                
                extracted_text = await mistral_ocr.extract_text_from_document(str(pdf_path))
                
                if extracted_text:
                    print(f"✅ OCR extracted {len(extracted_text)} characters from test PDF")
                    print(f"📝 Extracted text preview: {extracted_text[:300]}...")
                    return True
                else:
                    print("❌ OCR returned no text from test PDF")
                    return False
                    
            else:
                print("❌ Failed to convert test image to PDF")
                return False
                
        finally:
            # Clean up
            try:
                Path(test_image_path).unlink()
                if pdf_path:
                    pdf_path.unlink()
            except Exception:
                pass
                
    except ImportError:
        print("⚠️ PIL not available for test image creation")
        return True  # Skip test but don't fail
    except Exception as e:
        print(f"❌ Test conversion error: {e}")
        return False


def main():
    """Run OneNote image → PDF → OCR processing."""
    print("🖼️ ONENOTE IMAGE → PDF → OCR PROCESSING")
    print("=" * 60)
    
    async def run_processing():
        # Test conversion first
        test_success = await test_image_to_pdf_conversion()
        
        if test_success:
            # Process actual OneNote images
            success = await convert_onenote_images_to_pdf_and_ocr()
            return success
        else:
            print("❌ Test conversion failed, skipping actual processing")
            return False
    
    success = asyncio.run(run_processing())
    
    print("\n" + "=" * 60)
    print("🎯 IMAGE → PDF → OCR PROCESSING SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 OneNote image → PDF → OCR processing successful!")
        print("✅ Actual content extracted from OneNote images")
        print("✅ This should be the REAL OneNote content")
        print("✅ Much better than just metadata extraction")
        print("\nNext steps:")
        print("1. Integrate this approach into OneNote processor")
        print("2. Use extracted content for entity extraction")
        print("3. Process references from actual content")
    else:
        print("❌ OneNote image → PDF → OCR processing failed")
        print("Check the logs above for specific issues")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
