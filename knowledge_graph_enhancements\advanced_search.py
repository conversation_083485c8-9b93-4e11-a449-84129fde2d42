"""
<PERSON><PERSON>t to implement advanced search functionality for the knowledge graph.
"""

import os
import asyncio
import logging
import json
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

async def get_neo4j_driver():
    """Get a Neo4j driver."""
    driver = AsyncGraphDatabase.driver(
        neo4j_uri,
        auth=(neo4j_user, neo4j_password)
    )
    return driver

async def search_by_entity_type(entity_type, limit=20):
    """Search for entities by type."""
    logger.info(f"Searching for entities of type: {entity_type}")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (e:Entity)
                WHERE e.type = $type
                RETURN e.name AS name, e.type AS type, e.description AS description,
                       e.attributes AS attributes
                LIMIT $limit
                """,
                {"type": entity_type, "limit": limit}
            )
            
            entities = []
            async for record in result:
                entities.append({
                    "name": record["name"],
                    "type": record["type"],
                    "description": record["description"],
                    "attributes": json.loads(record["attributes"]) if record["attributes"] else {}
                })
                
            logger.info(f"Found {len(entities)} entities of type {entity_type}")
            return entities
    except Exception as e:
        logger.error(f"Error searching for entities by type: {e}")
        return []
    finally:
        await driver.close()

async def search_by_relationship_type(relationship_type, min_confidence=0.5, limit=20):
    """Search for relationships by type."""
    logger.info(f"Searching for relationships of type: {relationship_type}")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            result = await session.run(
                f"""
                MATCH (e1:Entity)-[r:{relationship_type}]->(e2:Entity)
                WHERE r.confidence >= $min_confidence
                RETURN e1.name AS source, e1.type AS source_type,
                       e2.name AS target, e2.type AS target_type,
                       type(r) AS type, r.confidence AS confidence,
                       r.evidence AS evidence
                LIMIT $limit
                """,
                {"min_confidence": min_confidence, "limit": limit}
            )
            
            relationships = []
            async for record in result:
                relationships.append({
                    "source": record["source"],
                    "source_type": record["source_type"],
                    "target": record["target"],
                    "target_type": record["target_type"],
                    "type": record["type"],
                    "confidence": record["confidence"],
                    "evidence": record["evidence"]
                })
                
            logger.info(f"Found {len(relationships)} relationships of type {relationship_type}")
            return relationships
    except Exception as e:
        logger.error(f"Error searching for relationships by type: {e}")
        return []
    finally:
        await driver.close()

async def search_by_keyword(keyword, limit=20):
    """Search for entities and relationships by keyword."""
    logger.info(f"Searching for keyword: {keyword}")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            # Search for entities
            entity_result = await session.run(
                """
                MATCH (e:Entity)
                WHERE e.name CONTAINS $keyword OR e.description CONTAINS $keyword
                RETURN e.name AS name, e.type AS type, e.description AS description,
                       e.attributes AS attributes
                LIMIT $limit
                """,
                {"keyword": keyword, "limit": limit}
            )
            
            entities = []
            async for record in entity_result:
                entities.append({
                    "name": record["name"],
                    "type": record["type"],
                    "description": record["description"],
                    "attributes": json.loads(record["attributes"]) if record["attributes"] else {}
                })
                
            # Search for relationships
            rel_result = await session.run(
                """
                MATCH (e1:Entity)-[r]->(e2:Entity)
                WHERE type(r) <> 'MENTIONS' AND type(r) <> 'RELATED_TO'
                AND (e1.name CONTAINS $keyword OR e2.name CONTAINS $keyword OR r.evidence CONTAINS $keyword)
                RETURN e1.name AS source, e1.type AS source_type,
                       e2.name AS target, e2.type AS target_type,
                       type(r) AS type, r.confidence AS confidence,
                       r.evidence AS evidence
                LIMIT $limit
                """,
                {"keyword": keyword, "limit": limit}
            )
            
            relationships = []
            async for record in rel_result:
                relationships.append({
                    "source": record["source"],
                    "source_type": record["source_type"],
                    "target": record["target"],
                    "target_type": record["target_type"],
                    "type": record["type"],
                    "confidence": record["confidence"],
                    "evidence": record["evidence"]
                })
                
            logger.info(f"Found {len(entities)} entities and {len(relationships)} relationships for keyword '{keyword}'")
            
            return {
                "entities": entities,
                "relationships": relationships
            }
    except Exception as e:
        logger.error(f"Error searching by keyword: {e}")
        return {"entities": [], "relationships": []}
    finally:
        await driver.close()

async def advanced_search(query=None, entity_type=None, relationship_type=None, min_confidence=0.5, limit=20):
    """Perform advanced search with multiple criteria."""
    logger.info(f"Performing advanced search: query='{query}', entity_type='{entity_type}', relationship_type='{relationship_type}', min_confidence={min_confidence}")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            # Build the entity search query
            entity_query = """
            MATCH (e:Entity)
            WHERE 1=1
            """
            
            entity_params = {"limit": limit}
            
            if query:
                entity_query += """
                AND (e.name CONTAINS $query OR e.description CONTAINS $query)
                """
                entity_params["query"] = query
            
            if entity_type:
                entity_query += """
                AND e.type = $entity_type
                """
                entity_params["entity_type"] = entity_type
            
            entity_query += """
            RETURN e.name AS name, e.type AS type, e.description AS description,
                   e.attributes AS attributes
            LIMIT $limit
            """
            
            # Execute the entity search query
            entity_result = await session.run(entity_query, entity_params)
            
            entities = []
            async for record in entity_result:
                entities.append({
                    "name": record["name"],
                    "type": record["type"],
                    "description": record["description"],
                    "attributes": json.loads(record["attributes"]) if record["attributes"] else {}
                })
            
            # Build the relationship search query if relationship_type is specified
            relationships = []
            if relationship_type:
                rel_query = f"""
                MATCH (e1:Entity)-[r:{relationship_type}]->(e2:Entity)
                WHERE r.confidence >= $min_confidence
                """
                
                rel_params = {"min_confidence": min_confidence, "limit": limit}
                
                if query:
                    rel_query += """
                    AND (e1.name CONTAINS $query OR e2.name CONTAINS $query OR r.evidence CONTAINS $query)
                    """
                    rel_params["query"] = query
                
                if entity_type:
                    rel_query += """
                    AND (e1.type = $entity_type OR e2.type = $entity_type)
                    """
                    rel_params["entity_type"] = entity_type
                
                rel_query += """
                RETURN e1.name AS source, e1.type AS source_type,
                       e2.name AS target, e2.type AS target_type,
                       type(r) AS type, r.confidence AS confidence,
                       r.evidence AS evidence
                LIMIT $limit
                """
                
                # Execute the relationship search query
                rel_result = await session.run(rel_query, rel_params)
                
                async for record in rel_result:
                    relationships.append({
                        "source": record["source"],
                        "source_type": record["source_type"],
                        "target": record["target"],
                        "target_type": record["target_type"],
                        "type": record["type"],
                        "confidence": record["confidence"],
                        "evidence": record["evidence"]
                    })
            
            logger.info(f"Found {len(entities)} entities and {len(relationships)} relationships in advanced search")
            
            return {
                "entities": entities,
                "relationships": relationships
            }
    except Exception as e:
        logger.error(f"Error performing advanced search: {e}")
        return {"entities": [], "relationships": []}
    finally:
        await driver.close()

async def get_entity_types():
    """Get all entity types in the knowledge graph."""
    logger.info("Getting all entity types")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (e:Entity)
                RETURN DISTINCT e.type AS type, count(e) AS count
                ORDER BY count DESC
                """
            )
            
            entity_types = []
            async for record in result:
                entity_types.append({
                    "type": record["type"],
                    "count": record["count"]
                })
                
            logger.info(f"Found {len(entity_types)} entity types")
            return entity_types
    except Exception as e:
        logger.error(f"Error getting entity types: {e}")
        return []
    finally:
        await driver.close()

async def get_relationship_types():
    """Get all relationship types in the knowledge graph."""
    logger.info("Getting all relationship types")
    
    driver = await get_neo4j_driver()
    
    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH ()-[r]->()
                WHERE type(r) <> 'MENTIONS' AND type(r) <> 'RELATED_TO'
                RETURN DISTINCT type(r) AS type, count(r) AS count
                ORDER BY count DESC
                """
            )
            
            relationship_types = []
            async for record in result:
                relationship_types.append({
                    "type": record["type"],
                    "count": record["count"]
                })
                
            logger.info(f"Found {len(relationship_types)} relationship types")
            return relationship_types
    except Exception as e:
        logger.error(f"Error getting relationship types: {e}")
        return []
    finally:
        await driver.close()

async def main():
    """Main function to test advanced search functionality."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Perform advanced search on the knowledge graph.')
    parser.add_argument('command', choices=['entity-types', 'relationship-types', 'entity-search', 'relationship-search', 'keyword-search', 'advanced-search'],
                        help='Command to execute')
    parser.add_argument('--query', type=str, help='Search query')
    parser.add_argument('--entity-type', type=str, help='Entity type to search for')
    parser.add_argument('--relationship-type', type=str, help='Relationship type to search for')
    parser.add_argument('--min-confidence', type=float, default=0.5, help='Minimum confidence score')
    parser.add_argument('--limit', type=int, default=20, help='Maximum number of results')
    
    args = parser.parse_args()
    
    if args.command == 'entity-types':
        entity_types = await get_entity_types()
        print(f"Found {len(entity_types)} entity types:")
        for entity_type in entity_types:
            print(f"  {entity_type['type']}: {entity_type['count']} entities")
    
    elif args.command == 'relationship-types':
        relationship_types = await get_relationship_types()
        print(f"Found {len(relationship_types)} relationship types:")
        for relationship_type in relationship_types:
            print(f"  {relationship_type['type']}: {relationship_type['count']} relationships")
    
    elif args.command == 'entity-search':
        if not args.entity_type:
            print("Error: --entity-type is required for entity-search command")
            return
        
        entities = await search_by_entity_type(args.entity_type, args.limit)
        print(f"Found {len(entities)} entities of type {args.entity_type}:")
        for entity in entities:
            print(f"  {entity['name']} - {entity['description'] or 'No description'}")
    
    elif args.command == 'relationship-search':
        if not args.relationship_type:
            print("Error: --relationship-type is required for relationship-search command")
            return
        
        relationships = await search_by_relationship_type(args.relationship_type, args.min_confidence, args.limit)
        print(f"Found {len(relationships)} relationships of type {args.relationship_type}:")
        for rel in relationships:
            print(f"  {rel['source']} -> {rel['target']} (confidence: {rel['confidence']})")
            if rel['evidence']:
                print(f"    Evidence: {rel['evidence']}")
    
    elif args.command == 'keyword-search':
        if not args.query:
            print("Error: --query is required for keyword-search command")
            return
        
        results = await search_by_keyword(args.query, args.limit)
        print(f"Found {len(results['entities'])} entities and {len(results['relationships'])} relationships for keyword '{args.query}'")
        
        if results['entities']:
            print("\nEntities:")
            for entity in results['entities']:
                print(f"  {entity['name']} ({entity['type']}) - {entity['description'] or 'No description'}")
        
        if results['relationships']:
            print("\nRelationships:")
            for rel in results['relationships']:
                print(f"  {rel['source']} -> {rel['target']} ({rel['type']}, confidence: {rel['confidence']})")
                if rel['evidence']:
                    print(f"    Evidence: {rel['evidence']}")
    
    elif args.command == 'advanced-search':
        results = await advanced_search(args.query, args.entity_type, args.relationship_type, args.min_confidence, args.limit)
        print(f"Found {len(results['entities'])} entities and {len(results['relationships'])} relationships")
        
        if results['entities']:
            print("\nEntities:")
            for entity in results['entities']:
                print(f"  {entity['name']} ({entity['type']}) - {entity['description'] or 'No description'}")
        
        if results['relationships']:
            print("\nRelationships:")
            for rel in results['relationships']:
                print(f"  {rel['source']} -> {rel['target']} ({rel['type']}, confidence: {rel['confidence']})")
                if rel['evidence']:
                    print(f"    Evidence: {rel['evidence']}")

if __name__ == "__main__":
    asyncio.run(main())
