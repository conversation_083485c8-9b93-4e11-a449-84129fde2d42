#!/usr/bin/env python3
"""
Debug script to test Andrographis search and retrieval.
"""

import asyncio

async def debug_andrographis_search():
    """Debug Andrographis search and retrieval"""
    print("🔍 Debugging Andrographis Search & Retrieval")
    print("=" * 50)
    
    # Test 1: Check if Andrographis entity exists
    print("1. Checking if Andrographis entity exists...")
    try:
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        query = """
        MATCH (e:Entity)
        WHERE toLower(e.name) CONTAINS 'andrographis'
        RETURN e.uuid as uuid, e.name as name, e.type as type, e.confidence as confidence
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found {len(result[1])} Andrographis entities:")
            for i, row in enumerate(result[1]):
                uuid = row[0] if row[0] else "N/A"
                name = row[1] if row[1] else "N/A"
                entity_type = row[2] if row[2] else "N/A"
                confidence = row[3] if row[3] else "N/A"
                print(f"  {i+1}. {name} ({entity_type}) - UUID: {uuid[:8]}... - Confidence: {confidence}")
        else:
            print("❌ No Andrographis entities found")
            
    except Exception as e:
        print(f"❌ Entity search error: {e}")
    
    # Test 2: Check facts that mention Andrographis
    print("\n2. Checking facts that mention Andrographis...")
    try:
        query = """
        MATCH (e:Entity)-[:MENTIONED_IN]-(f:Fact)
        WHERE toLower(e.name) CONTAINS 'andrographis'
        RETURN f.uuid as fact_uuid, f.body as body, e.name as entity_name
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found {len(result[1])} facts mentioning Andrographis:")
            for i, row in enumerate(result[1]):
                fact_uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                entity_name = row[2] if row[2] else "N/A"
                print(f"  {i+1}. Fact {fact_uuid[:8]}... mentions {entity_name}")
                print(f"     Body: {body[:200]}...")
                print()
        else:
            print("❌ No facts found with MENTIONED_IN relationship")
            
        # Try alternative relationship
        query = """
        MATCH (f:Fact)-[:MENTIONS]->(e:Entity)
        WHERE toLower(e.name) CONTAINS 'andrographis'
        RETURN f.uuid as fact_uuid, f.body as body, e.name as entity_name
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found {len(result[1])} facts with MENTIONS relationship:")
            for i, row in enumerate(result[1]):
                fact_uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                entity_name = row[2] if row[2] else "N/A"
                print(f"  {i+1}. Fact {fact_uuid[:8]}... mentions {entity_name}")
                print(f"     Body: {body[:200]}...")
                print()
        else:
            print("❌ No facts found with MENTIONS relationship")
            
    except Exception as e:
        print(f"❌ Facts search error: {e}")
    
    # Test 3: Test the Q&A service keyword extraction
    print("3. Testing Q&A service keyword extraction...")
    try:
        from services.qa_service import extract_keywords
        
        question = "what can you tell me about the herb andrographis"
        keywords = extract_keywords(question)
        print(f"✅ Keywords extracted: {keywords}")
        
    except Exception as e:
        print(f"❌ Keyword extraction error: {e}")
    
    # Test 4: Test the Q&A service fact retrieval
    print("\n4. Testing Q&A service fact retrieval...")
    try:
        from services.qa_service import get_relevant_facts
        
        question = "what can you tell me about the herb andrographis"
        facts = await get_relevant_facts(question, limit=10)
        
        print(f"✅ Q&A service returned {len(facts)} facts")
        
        if facts:
            for i, fact in enumerate(facts):
                print(f"  {i+1}. {fact.get('body', 'N/A')[:200]}...")
        else:
            print("❌ No facts returned by Q&A service")
            
    except Exception as e:
        print(f"❌ Q&A service error: {e}")
    
    # Test 5: Test direct fact search with keyword
    print("\n5. Testing direct fact search with 'andrographis' keyword...")
    try:
        query = """
        MATCH (f:Fact)
        WHERE toLower(f.body) CONTAINS 'andrographis'
        RETURN f.uuid as uuid, f.body as body
        LIMIT 5
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1:
            print(f"✅ Found {len(result[1])} facts containing 'andrographis':")
            for i, row in enumerate(result[1]):
                uuid = row[0] if row[0] else "N/A"
                body = row[1] if row[1] else "N/A"
                print(f"  {i+1}. Fact {uuid[:8]}...")
                print(f"     Body: {body[:300]}...")
                print()
        else:
            print("❌ No facts found containing 'andrographis'")
            
    except Exception as e:
        print(f"❌ Direct fact search error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_andrographis_search())
