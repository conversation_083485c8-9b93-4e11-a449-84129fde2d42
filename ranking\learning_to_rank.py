"""
Learning-to-Rank (LTR) system for knowledge graph search results.

This module implements machine learning-based ranking that learns from:
- User interaction patterns (clicks, dwell time, feedback)
- Entity importance scores (centrality, frequency, confidence)
- Document recency and quality metrics
- Query-result relevance patterns
- Domain-specific ranking preferences
"""

import logging
import time
import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum
import pickle
import os
from collections import defaultdict, deque

logger = logging.getLogger(__name__)


class InteractionType(Enum):
    """Types of user interactions for learning."""
    CLICK = "click"
    DWELL = "dwell"
    BOOKMARK = "bookmark"
    SHARE = "share"
    FEEDBACK_POSITIVE = "feedback_positive"
    FEEDBACK_NEGATIVE = "feedback_negative"
    QUERY_REFINEMENT = "query_refinement"


@dataclass
class UserInteraction:
    """Represents a user interaction with search results."""
    query: str
    result_uuid: str
    result_type: str  # 'node', 'edge', 'community'
    interaction_type: InteractionType
    timestamp: float
    position: int  # Position in search results (0-based)
    dwell_time: Optional[float] = None
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


@dataclass
class RankingFeatures:
    """Features used for learning-to-rank."""
    # Relevance features
    bm25_score: float = 0.0
    cosine_similarity: float = 0.0
    cross_encoder_score: float = 0.0
    
    # Entity importance features
    entity_centrality: float = 0.0
    entity_frequency: float = 0.0
    entity_confidence: float = 0.0
    entity_recency: float = 0.0
    
    # Graph features
    node_degree: int = 0
    community_size: int = 0
    path_length_to_query_entities: float = 0.0
    
    # Document features
    document_recency: float = 0.0
    document_quality: float = 0.0
    source_reliability: float = 0.0
    
    # User interaction features
    historical_click_rate: float = 0.0
    avg_dwell_time: float = 0.0
    positive_feedback_rate: float = 0.0
    
    # Query features
    query_entity_match: float = 0.0
    query_length: int = 0
    query_complexity: float = 0.0
    
    # Temporal features
    time_since_creation: float = 0.0
    time_since_last_update: float = 0.0
    seasonal_relevance: float = 0.0
    
    def to_vector(self) -> np.ndarray:
        """Convert features to numpy vector for ML models."""
        return np.array([
            self.bm25_score,
            self.cosine_similarity,
            self.cross_encoder_score,
            self.entity_centrality,
            self.entity_frequency,
            self.entity_confidence,
            self.entity_recency,
            float(self.node_degree),
            float(self.community_size),
            self.path_length_to_query_entities,
            self.document_recency,
            self.document_quality,
            self.source_reliability,
            self.historical_click_rate,
            self.avg_dwell_time,
            self.positive_feedback_rate,
            self.query_entity_match,
            float(self.query_length),
            self.query_complexity,
            self.time_since_creation,
            self.time_since_last_update,
            self.seasonal_relevance
        ])
    
    @classmethod
    def get_feature_names(cls) -> List[str]:
        """Get list of feature names."""
        return [
            'bm25_score', 'cosine_similarity', 'cross_encoder_score',
            'entity_centrality', 'entity_frequency', 'entity_confidence', 'entity_recency',
            'node_degree', 'community_size', 'path_length_to_query_entities',
            'document_recency', 'document_quality', 'source_reliability',
            'historical_click_rate', 'avg_dwell_time', 'positive_feedback_rate',
            'query_entity_match', 'query_length', 'query_complexity',
            'time_since_creation', 'time_since_last_update', 'seasonal_relevance'
        ]


@dataclass
class RankingExample:
    """Training example for learning-to-rank."""
    query: str
    result_uuid: str
    result_type: str
    features: RankingFeatures
    relevance_score: float  # 0-1 based on user interactions
    position: int
    timestamp: float


class InteractionTracker:
    """Tracks user interactions for learning."""
    
    def __init__(self, storage_path: str = "ranking_interactions.json"):
        self.storage_path = storage_path
        self.interactions: List[UserInteraction] = []
        self.interaction_stats: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))
        self._load_interactions()
    
    def record_interaction(self, interaction: UserInteraction):
        """Record a user interaction."""
        self.interactions.append(interaction)
        
        # Update statistics
        key = f"{interaction.result_uuid}:{interaction.result_type}"
        stats = self.interaction_stats[key]
        
        stats['total_interactions'] += 1
        stats['total_clicks'] += 1 if interaction.interaction_type == InteractionType.CLICK else 0
        stats['total_positive_feedback'] += 1 if interaction.interaction_type == InteractionType.FEEDBACK_POSITIVE else 0
        stats['total_negative_feedback'] += 1 if interaction.interaction_type == InteractionType.FEEDBACK_NEGATIVE else 0
        
        if interaction.dwell_time:
            stats['total_dwell_time'] += interaction.dwell_time
            stats['dwell_count'] += 1
        
        # Periodically save
        if len(self.interactions) % 100 == 0:
            self._save_interactions()
    
    def get_interaction_features(self, result_uuid: str, result_type: str) -> Dict[str, float]:
        """Get interaction-based features for a result."""
        key = f"{result_uuid}:{result_type}"
        stats = self.interaction_stats[key]
        
        total_interactions = stats.get('total_interactions', 0)
        if total_interactions == 0:
            return {
                'historical_click_rate': 0.0,
                'avg_dwell_time': 0.0,
                'positive_feedback_rate': 0.0
            }
        
        click_rate = stats.get('total_clicks', 0) / total_interactions
        avg_dwell = (stats.get('total_dwell_time', 0) / stats.get('dwell_count', 1)) if stats.get('dwell_count', 0) > 0 else 0.0
        positive_rate = stats.get('total_positive_feedback', 0) / total_interactions
        
        return {
            'historical_click_rate': click_rate,
            'avg_dwell_time': avg_dwell,
            'positive_feedback_rate': positive_rate
        }
    
    def calculate_relevance_score(self, result_uuid: str, result_type: str, query: str) -> float:
        """Calculate relevance score based on interactions."""
        # Get interactions for this result
        result_interactions = [
            i for i in self.interactions 
            if i.result_uuid == result_uuid and i.result_type == result_type
        ]
        
        if not result_interactions:
            return 0.5  # Neutral score for new results
        
        # Calculate weighted relevance score
        score = 0.0
        total_weight = 0.0
        
        for interaction in result_interactions:
            weight = 1.0
            interaction_score = 0.0
            
            # Score based on interaction type
            if interaction.interaction_type == InteractionType.CLICK:
                interaction_score = 0.6
                weight = 1.0
            elif interaction.interaction_type == InteractionType.DWELL:
                # Score based on dwell time (longer = more relevant)
                if interaction.dwell_time:
                    interaction_score = min(1.0, interaction.dwell_time / 30.0)  # 30 seconds = max score
                    weight = 1.5
            elif interaction.interaction_type == InteractionType.FEEDBACK_POSITIVE:
                interaction_score = 1.0
                weight = 3.0
            elif interaction.interaction_type == InteractionType.FEEDBACK_NEGATIVE:
                interaction_score = 0.0
                weight = 3.0
            elif interaction.interaction_type == InteractionType.BOOKMARK:
                interaction_score = 0.9
                weight = 2.0
            elif interaction.interaction_type == InteractionType.SHARE:
                interaction_score = 0.8
                weight = 2.0
            
            # Apply position bias (lower positions get penalty)
            position_factor = 1.0 / (1.0 + interaction.position * 0.1)
            
            # Apply time decay (recent interactions matter more)
            time_factor = np.exp(-(time.time() - interaction.timestamp) / (7 * 24 * 3600))  # 7 day half-life
            
            score += interaction_score * weight * position_factor * time_factor
            total_weight += weight * position_factor * time_factor
        
        return score / total_weight if total_weight > 0 else 0.5
    
    def _save_interactions(self):
        """Save interactions to file."""
        try:
            data = {
                'interactions': [asdict(i) for i in self.interactions],
                'stats': dict(self.interaction_stats)
            }
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, default=str)
        except Exception as e:
            logger.error(f"Failed to save interactions: {e}")
    
    def _load_interactions(self):
        """Load interactions from file."""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                
                # Load interactions
                for i_data in data.get('interactions', []):
                    i_data['interaction_type'] = InteractionType(i_data['interaction_type'])
                    self.interactions.append(UserInteraction(**i_data))
                
                # Load stats
                for key, stats in data.get('stats', {}).items():
                    self.interaction_stats[key] = defaultdict(float, stats)
        except Exception as e:
            logger.error(f"Failed to load interactions: {e}")


class FeatureExtractor:
    """Extracts features for learning-to-rank."""
    
    def __init__(self, interaction_tracker: InteractionTracker):
        self.interaction_tracker = interaction_tracker
    
    def extract_features(
        self,
        query: str,
        result: Dict[str, Any],
        result_type: str,
        search_scores: Dict[str, float],
        graph_stats: Optional[Dict[str, Any]] = None
    ) -> RankingFeatures:
        """Extract features for a search result."""
        features = RankingFeatures()
        
        # Basic search scores
        features.bm25_score = search_scores.get('bm25_score', 0.0)
        features.cosine_similarity = search_scores.get('cosine_similarity', 0.0)
        features.cross_encoder_score = search_scores.get('cross_encoder_score', 0.0)
        
        # Entity features
        features.entity_confidence = result.get('confidence', 0.5)
        features.entity_recency = self._calculate_recency(result.get('created_at'))
        
        # Graph features
        if graph_stats:
            features.entity_centrality = graph_stats.get('centrality', 0.0)
            features.entity_frequency = graph_stats.get('frequency', 0.0)
            features.node_degree = graph_stats.get('degree', 0)
            features.community_size = graph_stats.get('community_size', 0)
        
        # Document features
        features.document_recency = self._calculate_recency(result.get('document_created_at'))
        features.document_quality = result.get('document_quality', 0.5)
        features.source_reliability = result.get('source_reliability', 0.5)
        
        # User interaction features
        interaction_features = self.interaction_tracker.get_interaction_features(
            result.get('uuid', ''), result_type
        )
        features.historical_click_rate = interaction_features['historical_click_rate']
        features.avg_dwell_time = interaction_features['avg_dwell_time']
        features.positive_feedback_rate = interaction_features['positive_feedback_rate']
        
        # Query features
        features.query_entity_match = self._calculate_query_entity_match(query, result)
        features.query_length = len(query.split())
        features.query_complexity = self._calculate_query_complexity(query)
        
        # Temporal features
        features.time_since_creation = self._calculate_recency(result.get('created_at'))
        features.time_since_last_update = self._calculate_recency(result.get('updated_at'))
        features.seasonal_relevance = self._calculate_seasonal_relevance(query, result)
        
        return features
    
    def _calculate_recency(self, timestamp: Optional[Union[str, int, float]]) -> float:
        """Calculate recency score (0-1, higher = more recent)."""
        if not timestamp:
            return 0.5
        
        try:
            if isinstance(timestamp, str):
                # Parse ISO timestamp
                from datetime import datetime
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                timestamp = dt.timestamp()
            elif isinstance(timestamp, (int, float)):
                timestamp = float(timestamp)
            else:
                return 0.5
            
            # Calculate days since creation
            days_old = (time.time() - timestamp) / (24 * 3600)
            
            # Exponential decay with 30-day half-life
            return np.exp(-days_old / 30.0)
            
        except Exception:
            return 0.5
    
    def _calculate_query_entity_match(self, query: str, result: Dict[str, Any]) -> float:
        """Calculate how well the result matches query entities."""
        query_words = set(query.lower().split())
        result_name = result.get('name', '').lower()
        result_summary = result.get('summary', '').lower()
        
        # Check word overlap
        result_words = set(result_name.split()) | set(result_summary.split())
        overlap = len(query_words & result_words)
        
        return overlap / len(query_words) if query_words else 0.0
    
    def _calculate_query_complexity(self, query: str) -> float:
        """Calculate query complexity score."""
        # Simple heuristic based on length and special characters
        word_count = len(query.split())
        char_count = len(query)
        special_chars = len([c for c in query if not c.isalnum() and not c.isspace()])
        
        # Normalize to 0-1 range
        complexity = (word_count / 10.0 + char_count / 100.0 + special_chars / 10.0) / 3.0
        return min(1.0, complexity)
    
    def _calculate_seasonal_relevance(self, query: str, result: Dict[str, Any]) -> float:
        """Calculate seasonal relevance (placeholder for domain-specific logic)."""
        # This could be enhanced with domain-specific seasonal patterns
        # For now, return neutral score
        return 0.5


class LearningToRankModel:
    """Machine learning model for learning-to-rank."""
    
    def __init__(self, model_path: str = "ltr_model.pkl"):
        self.model_path = model_path
        self.model = None
        self.feature_scaler = None
        self.training_data: List[RankingExample] = []
        self._load_model()
    
    def add_training_example(self, example: RankingExample):
        """Add a training example."""
        self.training_data.append(example)
        
        # Retrain periodically
        if len(self.training_data) % 100 == 0:
            self.train()
    
    def train(self):
        """Train the ranking model."""
        if len(self.training_data) < 10:
            logger.info("Not enough training data for LTR model")
            return
        
        try:
            from sklearn.ensemble import RandomForestRegressor
            from sklearn.preprocessing import StandardScaler
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import mean_squared_error, r2_score
            
            # Prepare training data
            X = np.array([example.features.to_vector() for example in self.training_data])
            y = np.array([example.relevance_score for example in self.training_data])
            
            # Scale features
            self.feature_scaler = StandardScaler()
            X_scaled = self.feature_scaler.fit_transform(X)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=0.2, random_state=42
            )
            
            # Train model
            self.model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
            self.model.fit(X_train, y_train)
            
            # Evaluate
            y_pred = self.model.predict(X_test)
            mse = mean_squared_error(y_test, y_pred)
            r2 = r2_score(y_test, y_pred)
            
            logger.info(f"LTR model trained: MSE={mse:.4f}, R2={r2:.4f}")
            
            # Save model
            self._save_model()
            
        except ImportError:
            logger.warning("scikit-learn not available, using simple linear model")
            self._train_simple_model()
        except Exception as e:
            logger.error(f"Failed to train LTR model: {e}")
    
    def _train_simple_model(self):
        """Train a simple linear model as fallback."""
        if len(self.training_data) < 5:
            return
        
        # Simple weighted average of features
        X = np.array([example.features.to_vector() for example in self.training_data])
        y = np.array([example.relevance_score for example in self.training_data])
        
        # Calculate feature weights based on correlation with relevance
        weights = []
        for i in range(X.shape[1]):
            corr = np.corrcoef(X[:, i], y)[0, 1]
            weights.append(corr if not np.isnan(corr) else 0.0)
        
        self.model = {'type': 'simple', 'weights': weights}
        logger.info("Trained simple LTR model")
    
    def predict_relevance(self, features: RankingFeatures) -> float:
        """Predict relevance score for given features."""
        if self.model is None:
            return 0.5  # Default score
        
        try:
            feature_vector = features.to_vector().reshape(1, -1)
            
            if isinstance(self.model, dict) and self.model.get('type') == 'simple':
                # Simple weighted sum
                weights = np.array(self.model['weights'])
                score = np.dot(feature_vector[0], weights)
                return max(0.0, min(1.0, score))
            else:
                # Scikit-learn model
                if self.feature_scaler:
                    feature_vector = self.feature_scaler.transform(feature_vector)
                score = self.model.predict(feature_vector)[0]
                return max(0.0, min(1.0, score))
                
        except Exception as e:
            logger.error(f"Error predicting relevance: {e}")
            return 0.5
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance scores."""
        if self.model is None:
            return {}
        
        try:
            feature_names = RankingFeatures.get_feature_names()
            
            if isinstance(self.model, dict) and self.model.get('type') == 'simple':
                weights = self.model['weights']
                return dict(zip(feature_names, weights))
            else:
                # Scikit-learn model
                if hasattr(self.model, 'feature_importances_'):
                    importances = self.model.feature_importances_
                    return dict(zip(feature_names, importances))
                
        except Exception as e:
            logger.error(f"Error getting feature importance: {e}")
        
        return {}
    
    def _save_model(self):
        """Save the trained model."""
        try:
            model_data = {
                'model': self.model,
                'scaler': self.feature_scaler,
                'training_examples': len(self.training_data)
            }
            with open(self.model_path, 'wb') as f:
                pickle.dump(model_data, f)
        except Exception as e:
            logger.error(f"Failed to save LTR model: {e}")
    
    def _load_model(self):
        """Load a previously trained model."""
        try:
            if os.path.exists(self.model_path):
                with open(self.model_path, 'rb') as f:
                    model_data = pickle.load(f)
                self.model = model_data.get('model')
                self.feature_scaler = model_data.get('scaler')
                logger.info(f"Loaded LTR model with {model_data.get('training_examples', 0)} training examples")
        except Exception as e:
            logger.error(f"Failed to load LTR model: {e}")


class LearningToRankReranker:
    """Main LTR reranker that integrates with the search system."""
    
    def __init__(self):
        self.interaction_tracker = InteractionTracker()
        self.feature_extractor = FeatureExtractor(self.interaction_tracker)
        self.model = LearningToRankModel()
    
    def rerank_results(
        self,
        query: str,
        results: List[Dict[str, Any]],
        result_type: str,
        search_scores: List[Dict[str, float]],
        graph_stats: Optional[List[Dict[str, Any]]] = None
    ) -> List[Dict[str, Any]]:
        """Rerank search results using learning-to-rank."""
        if not results:
            return results
        
        # Extract features and predict relevance for each result
        scored_results = []
        
        for i, result in enumerate(results):
            # Get search scores for this result
            scores = search_scores[i] if i < len(search_scores) else {}
            
            # Get graph stats for this result
            stats = graph_stats[i] if graph_stats and i < len(graph_stats) else None
            
            # Extract features
            features = self.feature_extractor.extract_features(
                query, result, result_type, scores, stats
            )
            
            # Predict relevance
            relevance_score = self.model.predict_relevance(features)
            
            scored_results.append((result, relevance_score, features))
        
        # Sort by predicted relevance
        scored_results.sort(key=lambda x: x[1], reverse=True)
        
        # Return reranked results
        return [result for result, _, _ in scored_results]
    
    def record_interaction(self, interaction: UserInteraction):
        """Record user interaction for learning."""
        self.interaction_tracker.record_interaction(interaction)
        
        # Create training example
        # This would typically be done in batch, but for simplicity we do it here
        relevance_score = self.interaction_tracker.calculate_relevance_score(
            interaction.result_uuid, interaction.result_type, interaction.query
        )
        
        # Note: In a real system, you'd need to store the features that were used
        # when this result was originally ranked. For now, we skip creating training examples
        # from individual interactions.
    
    def get_model_stats(self) -> Dict[str, Any]:
        """Get statistics about the LTR model."""
        return {
            'total_interactions': len(self.interaction_tracker.interactions),
            'training_examples': len(self.model.training_data),
            'feature_importance': self.model.get_feature_importance(),
            'model_loaded': self.model.model is not None
        }


# Global instance
_ltr_reranker = None


def get_ltr_reranker() -> LearningToRankReranker:
    """Get the global learning-to-rank reranker."""
    global _ltr_reranker
    if _ltr_reranker is None:
        _ltr_reranker = LearningToRankReranker()
    return _ltr_reranker
