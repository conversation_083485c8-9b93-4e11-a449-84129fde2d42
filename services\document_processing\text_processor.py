"""
Text document processor for plain text, markdown, and RTF files.
"""

from typing import Dict, Any
import logging
import chardet

from .base_processor import BaseDocumentProcessor

logger = logging.getLogger(__name__)

class TextProcessor(BaseDocumentProcessor):
    """
    Text document processor for various text formats.
    """
    
    def __init__(self):
        """Initialize the text processor."""
        super().__init__()
        self.supported_extensions = {'.txt', '.md', '.markdown', '.rtf', '.text'}
    
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports text files."""
        return self.get_file_extension(file_path) in self.supported_extensions
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process a text document and extract content.
        
        Args:
            file_path: Path to the text file
            
        Returns:
            Processing result dictionary
        """
        self.log_processing_start(file_path)
        
        # Validate file
        validation = self.validate_file(file_path)
        if not validation["valid"]:
            return {
                "success": False,
                "error": validation["error"]
            }
        
        try:
            # Detect encoding
            encoding = await self._detect_encoding(file_path)
            
            # Read file content
            text = await self._read_text_file(file_path, encoding)
            
            if not text:
                return {
                    "success": False,
                    "error": "No text content found in file"
                }
            
            # Process based on file type
            extension = self.get_file_extension(file_path)
            if extension == '.rtf':
                text = await self._process_rtf(text)
            elif extension in ['.md', '.markdown']:
                text = await self._process_markdown(text)
            
            # Extract metadata
            metadata = await self.extract_metadata(file_path)
            metadata.update({
                "encoding": encoding,
                "line_count": text.count('\n') + 1,
                "word_count": len(text.split()),
                "character_count": len(text)
            })
            
            self.log_processing_success(file_path, len(text))
            
            return {
                "success": True,
                "text": text,
                "metadata": metadata
            }
            
        except Exception as e:
            error_msg = f"Error processing text file: {str(e)}"
            self.log_processing_error(file_path, error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def _detect_encoding(self, file_path: str) -> str:
        """Detect the encoding of a text file."""
        try:
            with open(file_path, 'rb') as file:
                raw_data = file.read(10000)  # Read first 10KB for detection
                result = chardet.detect(raw_data)
                encoding = result.get('encoding', 'utf-8')
                confidence = result.get('confidence', 0)
                
                logger.debug(f"Detected encoding: {encoding} (confidence: {confidence:.2f})")
                
                # Fallback to utf-8 if confidence is too low
                if confidence < 0.7:
                    logger.warning(f"Low confidence encoding detection, using utf-8")
                    encoding = 'utf-8'
                
                return encoding
        except Exception as e:
            logger.warning(f"Encoding detection failed: {e}, using utf-8")
            return 'utf-8'
    
    async def _read_text_file(self, file_path: str, encoding: str) -> str:
        """Read text file with specified encoding."""
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                return file.read()
        except UnicodeDecodeError:
            # Try with utf-8 if specified encoding fails
            logger.warning(f"Failed to read with {encoding}, trying utf-8")
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    return file.read()
            except UnicodeDecodeError:
                # Last resort: read with errors='ignore'
                logger.warning("UTF-8 failed, reading with error handling")
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                    return file.read()
    
    async def _process_rtf(self, text: str) -> str:
        """Process RTF content to extract plain text."""
        try:
            # Try to use striprtf if available
            from striprtf.striprtf import rtf_to_text
            return rtf_to_text(text)
        except ImportError:
            logger.warning("striprtf not available, using basic RTF processing")
            return await self._basic_rtf_processing(text)
        except Exception as e:
            logger.warning(f"RTF processing failed: {e}, using basic processing")
            return await self._basic_rtf_processing(text)
    
    async def _basic_rtf_processing(self, text: str) -> str:
        """Basic RTF processing to remove common RTF tags."""
        import re
        
        # Remove RTF control words and groups
        text = re.sub(r'\\[a-z]+\d*\s?', '', text)  # Control words
        text = re.sub(r'[{}]', '', text)  # Braces
        text = re.sub(r'\\[^a-z]', '', text)  # Control symbols
        
        # Clean up whitespace
        text = re.sub(r'\s+', ' ', text)
        
        return text.strip()
    
    async def _process_markdown(self, text: str) -> str:
        """Process Markdown content."""
        try:
            # Try to use markdown library if available
            import markdown
            from markdown.extensions import codehilite, tables, toc
            
            md = markdown.Markdown(extensions=['codehilite', 'tables', 'toc'])
            html = md.convert(text)
            
            # Convert HTML back to plain text
            from html2text import html2text
            return html2text(html)
            
        except ImportError:
            logger.info("Markdown processing libraries not available, returning raw text")
            return text
        except Exception as e:
            logger.warning(f"Markdown processing failed: {e}, returning raw text")
            return text
