"""
Base classes and constants for entity extraction.

This module defines the base classes and constants used throughout the entity extraction
package. It includes the abstract EntityExtractor class that all extractors must implement,
as well as the ENTITY_TYPES constant that defines the entity types supported by the system.

The EntityExtractor class provides a common interface for all entity extractors, regardless
of their implementation details. This allows for easy swapping of different extraction
strategies (e.g., LLM-based, rule-based) without changing the rest of the code.

Example:
    ```python
    from entity_extraction.base import EntityExtractor, ENTITY_TYPES

    # Print all supported entity types
    print(ENTITY_TYPES)

    # Create a custom entity extractor
    class MyExtractor(EntityExtractor):
        def extract_entities(self, text):
            # Custom implementation
            return []
    ```
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Entity types supported by the system
ENTITY_TYPES = [
    "Person",              # Individuals mentioned in the text
    "Organization",        # Companies, institutions, and other organizations
    "Location",            # Geographic locations and places
    "Treatment",           # Medical treatments and interventions
    "Herb",                # Medicinal herbs and botanical remedies
    "Medication",          # Pharmaceutical drugs and medications
    "Disease",             # Medical conditions and diseases
    "Symptom",             # Signs and symptoms of medical conditions
    "Nutrient",            # Vitamins, minerals, and other nutrients
    "Concept",             # Abstract concepts and ideas
    "Process",             # Biological and physiological processes
    "Research",            # Research studies and publications
    "Study",               # Clinical trials and scientific studies
    "Bioactive_Compound",  # Active compounds in herbs and foods
    "Botanical_Source",    # Plant sources of bioactive compounds
    "Biochemical_Pathway", # Metabolic and signaling pathways
    "Health_Condition",    # General health states and conditions
    "Genetic_Element",     # Genes, alleles, and genetic factors
    "Therapeutic_Intervention", # Therapeutic approaches and interventions
    "Food",                # Food items and dietary components
    "Application",         # Specific uses or applications of substances
    "Part_Used",           # Plant parts used medicinally (root, leaf, etc.)
    "Preparation",         # Methods of preparation (tincture, extract, etc.)
    "Dosage",              # Specific dosage information and protocols
    "Mechanism",           # Mechanisms of action and biological effects
    "Clinical_Trial",      # Specific clinical trials and studies
    "Biomarker",           # Biological markers and indicators
    "Enzyme",              # Specific enzymes and proteins
    "Receptor",            # Cellular receptors and binding sites
    "Metabolite"           # Metabolic products and intermediates
]

class EntityExtractor(ABC):
    """
    Base class for entity extractors.

    This abstract class defines the interface that all entity extractors must implement.
    It provides a common way to extract entities from text, regardless of the underlying
    implementation details (e.g., which LLM or rule-based system is used).

    Attributes:
        api_key (Optional[str]): API key for the LLM service
        logger (Logger): Logger instance for this class
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the entity extractor.

        Args:
            api_key (Optional[str]): API key for the LLM service. If None, the extractor
                will attempt to use environment variables or default credentials.
        """
        self.api_key = api_key
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    @abstractmethod
    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from text.

        This method must be implemented by all subclasses. It should analyze the given
        text and return a list of entities found within it.

        Args:
            text (str): Text to extract entities from. This can be a sentence, paragraph,
                or longer document.

        Returns:
            List[Dict[str, Any]]: A list of dictionaries, where each dictionary represents
                an entity with the following keys:
                - name (str): The name of the entity
                - type (str): The type of the entity (one of ENTITY_TYPES)
                - description (Optional[str]): A description of the entity
                - attributes (Optional[Dict[str, Any]]): Additional attributes of the entity

        Raises:
            NotImplementedError: If the subclass does not implement this method
        """
        pass

    def get_system_prompt(self) -> str:
        """
        Get the system prompt for entity extraction.

        This method returns a detailed system prompt that instructs the LLM on how to
        extract entities from text. The prompt includes information about the entity types
        to extract, specific attributes to look for in each entity type, and formatting
        instructions for the output.

        The prompt is designed to work with modern LLMs like GPT-4, Claude, and Llama 2/3,
        instructing them to return structured JSON that can be easily parsed by the system.

        The prompt now includes instructions to add confidence scores for each entity
        to indicate the model's certainty about the extraction. This helps with filtering
        and prioritizing entities in the knowledge graph.

        Returns:
            str: A detailed system prompt for entity extraction
        """
        return f"""Extract health/medical entities from scientific text. Classify into these types:

**TYPES:** {', '.join(ENTITY_TYPES)}

**KEY RULES:**
- Medication: Drugs, pharmaceuticals | Nutrient: Vitamins, natural compounds
- Disease: Diagnosed conditions | Symptom: Observable signs
- Process: Biological mechanisms | Treatment: Therapeutic interventions
- Food: Common foods | Herb: Medicinal plants
- Research: Study methods | Application: Clinical uses

**EXTRACT:** Bioactive compounds, dosages, mechanisms, study data, plant parts, preparation methods

**OUTPUT:** JSON with "entities" array. Each entity needs:
- name: Exact text from source
- type: One of the types above
- description: Brief scientific explanation
- confidence: 0.60-1.0

**CONFIDENCE:**
- 0.95-1.0: Explicitly stated
- 0.85-0.94: Clearly implied
- 0.75-0.84: Reasonably inferred
- 0.60-0.74: Some ambiguity
- <0.60: Don't extract

**EXAMPLE:**
```json
{{
    "entities": [
        {{
            "name": "vitamin D",
            "type": "Nutrient",
            "description": "Fat-soluble vitamin for bone health",
            "confidence": 0.95
        }}
    ]
}}
```

Extract 5-15 relevant entities with confidence ≥ 0.60. Focus on health/nutrition/medical terms.

"""
