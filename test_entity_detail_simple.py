#!/usr/bin/env python3
"""
Simple test for entity detail API.
"""

import requests

def test_entity_detail_simple():
    """Test entity detail API with a known entity"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entity Detail API")
    print("=" * 30)
    
    # First get any entity UUID from the entities list
    try:
        print("1. Getting entity list...")
        response = requests.get(f"{base_url}/api/entities?limit=1&entity_type=Herb", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            
            if entities:
                entity = entities[0]
                uuid = entity.get('uuid')
                name = entity.get('name')
                entity_type = entity.get('type')
                mentions = entity.get('mention_count', 0)
                
                print(f"✅ Found entity: {name} ({entity_type}) - {mentions} mentions")
                print(f"   UUID: {uuid}")
                
                # Test entity detail API
                print(f"\n2. Testing entity detail API...")
                detail_response = requests.get(f"{base_url}/api/entity/{uuid}", timeout=5)
                print(f"Status: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    print(f"Response keys: {list(detail_data.keys())}")
                    
                    if 'entity' in detail_data:
                        entity_detail = detail_data['entity']
                        print(f"✅ Entity detail:")
                        print(f"   Name: {entity_detail.get('name', 'N/A')}")
                        print(f"   Type: {entity_detail.get('type', 'N/A')}")
                        print(f"   UUID: {entity_detail.get('uuid', 'N/A')}")
                        print(f"   Mentions: {entity_detail.get('mention_count', 'N/A')}")
                        print(f"   Confidence: {entity_detail.get('confidence', 'N/A')}")
                        print(f"   Created: {entity_detail.get('created_at', 'N/A')}")
                        
                        # Check if all fields are populated
                        if (entity_detail.get('name') and entity_detail.get('name') != 'Unknown' and
                            entity_detail.get('type') and entity_detail.get('type') != 'Unknown'):
                            print(f"✅ Entity detail API working correctly!")
                        else:
                            print(f"❌ Some fields still showing as Unknown")
                    else:
                        print(f"❌ No 'entity' key in response")
                        
                    if 'relationships' in detail_data:
                        relationships = detail_data['relationships']
                        print(f"✅ Relationships: {len(relationships)} found")
                        
                else:
                    print(f"❌ Entity detail error: {detail_response.status_code}")
                    print(f"Response: {detail_response.text}")
            else:
                print("❌ No entities found")
        else:
            print(f"❌ Entities list error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_entity_detail_simple()
