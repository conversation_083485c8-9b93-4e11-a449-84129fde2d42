# Graphiti with Google Gemini Examples

This directory contains examples demonstrating how to use Graphiti with Google's Gemini models for various knowledge graph operations.

## Prerequisites

- Neo4j database (running locally or remotely)
- Google API key with access to Gemini models
- Python 3.9+
- Graphiti installed (`pip install graphiti-core`)
- Google Generative AI package (`pip install google-generativeai`)

## Setup Instructions

1. Make sure your Neo4j database is running and accessible.

2. Set up your environment variables:
   - Copy the `.env.example` file to `.env` in the root directory (if not already done)
   - Add your Google API key to the `.env` file:
     ```
     GOOGLE_API_KEY=your-google-api-key-here
     ```
   - Verify your Neo4j connection details are correct in the `.env` file:
     ```
     NEO4J_URI=bolt://localhost:7687
     NEO4J_USER=neo4j
     NEO4J_PASSWORD=your-password
     ```

3. Install the required packages:
   ```bash
   pip install graphiti-core google-generativeai
   ```

## Available Examples

### 1. Basic Gemini Example

**File:** `gemini_example.py`

This example demonstrates the basic setup of Graphiti with Google Gemini for both LLM inference and embeddings. It:
- Connects to your Neo4j database
- Initializes Graphiti with Google Gemini
- Adds a simple episode to the knowledge graph
- Performs a basic search query

Run the example:
```bash
python examples/gemini_example.py
```

### 2. Advanced Gemini Example

**File:** `gemini_advanced_example.py`

This example demonstrates more advanced usage of Graphiti with Google Gemini, including:
- Creating a knowledge graph with multiple episodes
- Performing various search queries
- Asking questions to the knowledge graph
- Using Gemini to generate answers based on retrieved facts

Run the example:
```bash
python examples/gemini_advanced_example.py
```

### 3. Knowledge Extraction Example

**File:** `gemini_knowledge_extraction.py`

This example demonstrates how to use Gemini for knowledge extraction from text:
- Extracting entities and relationships from unstructured text
- Adding the extracted knowledge to the knowledge graph
- Connecting entities with relationships
- Searching the knowledge graph to verify the extracted knowledge

Run the example:
```bash
python examples/gemini_knowledge_extraction.py
```

### 4. Conversational Memory Example

**File:** `gemini_conversational_memory.py`

This example demonstrates how to use Graphiti for conversational memory with Gemini:
- Managing conversation history in the knowledge graph
- Adding user and assistant messages as facts
- Retrieving conversation history
- Generating contextual responses based on conversation history

Run the example:
```bash
python examples/gemini_conversational_memory.py
```

## Available Gemini Models

### LLM Models

- `models/gemini-1.5-flash` - Fast, efficient model for most tasks
- `models/gemini-1.5-pro` - More powerful model for complex tasks
- `models/gemini-2.0-flash` - Latest generation fast model
- `models/gemini-2.0-pro` - Latest generation powerful model

### Embedding Models

- `models/embedding-001` - General purpose embedding model
- `models/text-embedding-004` - Text-specific embedding model
- `models/gemini-embedding-exp-03-07` - Experimental Gemini embedding model

**Important**: When using the Google Generative AI API, model names must be prefixed with `models/` as shown above.

## Troubleshooting

- If you encounter authentication errors, verify your Google API key is correct and has access to Gemini models
- For Neo4j connection issues, check that your database is running and the connection details in `.env` are correct
- Make sure you have the latest version of the `google-generativeai` Python package installed
- If you get an error about invalid model names, ensure you're using the full model name with the `models/` prefix
- For embedding errors, try using a different embedding model like `models/text-embedding-004` or `models/embedding-001`
- If you encounter import errors with `from google import genai`, use `import google.generativeai as genai` instead

## Additional Resources

- [Google AI Gemini API Documentation](https://ai.google.dev/docs)
- [Gemini Embeddings Documentation](https://ai.google.dev/gemini-api/docs/embeddings)
- [Graphiti Documentation](https://help.getzep.com/graphiti)
- [Google AI Studio](https://aistudio.google.com/) - For testing Gemini models and getting an API key
- [Neo4j Documentation](https://neo4j.com/docs/)
