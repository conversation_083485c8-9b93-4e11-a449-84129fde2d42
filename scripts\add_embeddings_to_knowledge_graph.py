"""
<PERSON><PERSON><PERSON> to add vector embeddings to Fact nodes in the knowledge graph
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timezone

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase
import openai

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_vector_index(driver):
    """Create a vector index in Neo4j for Fact nodes."""
    logger.info("Creating vector index for Fact nodes")
    
    try:
        async with driver.session() as session:
            # Check if the index already exists
            result = await session.run(
                """
                SHOW INDEXES
                YIELD name, type
                WHERE name = 'fact_embedding' AND type = 'VECTOR'
                RETURN count(*) > 0 AS exists
                """
            )
            record = await result.single()
            
            if record and record.get("exists", False):
                logger.info("Vector index 'fact_embedding' already exists")
                return
            
            # Create the vector index
            result = await session.run(
                """
                CREATE VECTOR INDEX fact_embedding IF NOT EXISTS
                FOR (f:Fact) ON (f.embedding)
                OPTIONS {
                  indexConfig: {
                    `vector.dimensions`: 1536,
                    `vector.similarity_function`: 'cosine'
                  }
                }
                """
            )
            await result.consume()
            logger.info("Created vector index 'fact_embedding'")
    except Exception as e:
        logger.error(f"Error creating vector index: {e}")
        raise

async def get_facts_without_embeddings(driver, batch_size=20):
    """Get Fact nodes that don't have embeddings."""
    logger.info(f"Getting Fact nodes without embeddings (batch size: {batch_size})")
    
    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)
                WHERE f.embedding IS NULL
                RETURN f.uuid AS uuid, f.body AS body
                LIMIT $batch_size
                """,
                {"batch_size": batch_size}
            )
            
            facts = []
            async for record in result:
                facts.append({
                    "uuid": record["uuid"],
                    "body": record["body"]
                })
            
            logger.info(f"Found {len(facts)} Fact nodes without embeddings")
            return facts
    except Exception as e:
        logger.error(f"Error getting Facts without embeddings: {e}")
        return []

async def generate_embeddings(api_key, texts, model="text-embedding-3-small"):
    """Generate embeddings for a list of texts using OpenAI."""
    logger.info(f"Generating embeddings for {len(texts)} texts using model {model}")
    
    try:
        client = openai.OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=texts,
            model=model
        )
        
        embeddings = [data.embedding for data in response.data]
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise

async def update_facts_with_embeddings(driver, facts, embeddings):
    """Update Fact nodes with their embeddings."""
    logger.info(f"Updating {len(facts)} Fact nodes with embeddings")
    
    try:
        async with driver.session() as session:
            for i, fact in enumerate(facts):
                await session.run(
                    """
                    MATCH (f:Fact {uuid: $uuid})
                    SET f.embedding = $embedding,
                        f.embedding_model = $model,
                        f.embedding_updated_at = datetime($timestamp)
                    """,
                    {
                        "uuid": fact["uuid"],
                        "embedding": embeddings[i],
                        "model": "text-embedding-3-small",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
            
            logger.info(f"Updated {len(facts)} Fact nodes with embeddings")
    except Exception as e:
        logger.error(f"Error updating Facts with embeddings: {e}")
        raise

async def add_embeddings_to_facts(driver, api_key, batch_size=20, max_batches=None):
    """Add embeddings to Fact nodes in batches."""
    logger.info("Adding embeddings to Fact nodes")
    
    # Create vector index if it doesn't exist
    await create_vector_index(driver)
    
    batch_count = 0
    total_processed = 0
    
    while True:
        # Check if we've reached the maximum number of batches
        if max_batches is not None and batch_count >= max_batches:
            logger.info(f"Reached maximum number of batches ({max_batches})")
            break
        
        # Get a batch of Facts without embeddings
        facts = await get_facts_without_embeddings(driver, batch_size)
        
        # If there are no more Facts without embeddings, we're done
        if not facts:
            logger.info("No more Facts without embeddings")
            break
        
        # Generate embeddings for the batch
        texts = [fact["body"] for fact in facts]
        embeddings = await generate_embeddings(api_key, texts)
        
        # Update the Facts with their embeddings
        await update_facts_with_embeddings(driver, facts, embeddings)
        
        batch_count += 1
        total_processed += len(facts)
        
        logger.info(f"Processed batch {batch_count} ({total_processed} Facts total)")
        
        # Sleep briefly to avoid rate limits
        await asyncio.sleep(1)
    
    logger.info(f"Finished adding embeddings to {total_processed} Fact nodes")
    return total_processed

async def count_facts_with_embeddings(driver):
    """Count how many Fact nodes have embeddings."""
    logger.info("Counting Facts with embeddings")
    
    try:
        async with driver.session() as session:
            # Count Facts with embeddings
            result = await session.run(
                """
                MATCH (f:Fact)
                RETURN count(f) AS total,
                       count(f.embedding) AS with_embeddings
                """
            )
            record = await result.single()
            
            total = record["total"]
            with_embeddings = record["with_embeddings"]
            percentage = (with_embeddings / total * 100) if total > 0 else 0
            
            logger.info(f"{with_embeddings} out of {total} Facts have embeddings ({percentage:.2f}%)")
            return {"total": total, "with_embeddings": with_embeddings, "percentage": percentage}
    except Exception as e:
        logger.error(f"Error counting Facts with embeddings: {e}")
        return {"total": 0, "with_embeddings": 0, "percentage": 0}

async def test_semantic_search(driver, query, limit=5):
    """Test semantic search using vector embeddings."""
    logger.info(f"Testing semantic search for query: '{query}'")
    
    try:
        # Generate embedding for the query
        client = openai.OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))
        response = client.embeddings.create(
            input=query,
            model="text-embedding-3-small"
        )
        query_embedding = response.data[0].embedding
        
        # Search using vector similarity
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)
                WHERE f.embedding IS NOT NULL
                WITH f, vector.similarity(f.embedding, $query_embedding) AS score
                ORDER BY score DESC
                LIMIT $limit
                MATCH (e:Episode)-[:CONTAINS]->(f)
                RETURN f.uuid AS uuid, 
                       substring(f.body, 0, 200) + '...' AS preview,
                       score,
                       e.name AS document
                """,
                {"query_embedding": query_embedding, "limit": limit}
            )
            
            results = []
            async for record in result:
                results.append({
                    "uuid": record["uuid"],
                    "preview": record["preview"],
                    "score": record["score"],
                    "document": record["document"]
                })
            
            logger.info(f"Found {len(results)} results for query: '{query}'")
            
            # Display the results
            for i, result in enumerate(results):
                logger.info(f"\nResult {i+1} (Score: {result['score']:.4f}):")
                logger.info(f"Document: {result['document']}")
                logger.info(f"Preview: {result['preview']}")
            
            return results
    except Exception as e:
        logger.error(f"Error performing semantic search: {e}")
        return []

async def main():
    """Main function to add embeddings to the knowledge graph."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return
    
    # Check command line arguments
    if len(sys.argv) < 2:
        logger.error("Please specify a command: add-embeddings, count, or search")
        logger.info("Usage:")
        logger.info("  python add_embeddings_to_knowledge_graph.py add-embeddings [batch_size] [max_batches]")
        logger.info("  python add_embeddings_to_knowledge_graph.py count")
        logger.info("  python add_embeddings_to_knowledge_graph.py search <query>")
        return
    
    command = sys.argv[1].lower()
    
    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        if command == "add-embeddings":
            # Get optional parameters
            batch_size = 20
            max_batches = None
            
            if len(sys.argv) >= 3:
                try:
                    batch_size = int(sys.argv[2])
                except ValueError:
                    logger.warning(f"Invalid batch_size value: {sys.argv[2]}. Using default: 20")
            
            if len(sys.argv) >= 4:
                try:
                    max_batches = int(sys.argv[3])
                except ValueError:
                    logger.warning(f"Invalid max_batches value: {sys.argv[3]}. Using default: None (unlimited)")
            
            # Add embeddings to Facts
            total_processed = await add_embeddings_to_facts(driver, openai_api_key, batch_size, max_batches)
            logger.info(f"Added embeddings to {total_processed} Fact nodes")
        
        elif command == "count":
            # Count Facts with embeddings
            await count_facts_with_embeddings(driver)
        
        elif command == "search":
            # Test semantic search
            if len(sys.argv) < 3:
                logger.error("Please specify a search query")
                logger.info("Usage: python add_embeddings_to_knowledge_graph.py search <query>")
                return
            
            query = " ".join(sys.argv[2:])
            await test_semantic_search(driver, query)
        
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: add-embeddings, count, search")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
