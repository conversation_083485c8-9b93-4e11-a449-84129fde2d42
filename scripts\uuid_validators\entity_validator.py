"""
Entity UUID validator.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class EntityValidator(BaseValidator):
    """Validator for Entity node UUIDs."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "entities_total": 0,
            "entities_missing_uuid": 0,
            "entities_fixed": 0
        }

    async def validate(self):
        """Check Entity nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Entity nodes
        count_query = """
        MATCH (e:Entity)
        RETURN count(e) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["entities_total"] = count_result[1][0][0]

        # Find Entity nodes without UUIDs
        missing_query = """
        MATCH (e:Entity)
        WHERE e.uuid IS NULL
        RETURN id(e) as id, e.name as name, e.type as type
        """

        missing_result = adapter.execute_cypher(missing_query)
        entities = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                entities.append({
                    "id": row[0],
                    "name": row[1],
                    "type": row[2]
                })

            self.stats["entities_missing_uuid"] = len(entities)
            self.log_info(f"Found {len(entities)} Entity nodes without UUIDs")

            if self.fix:
                fixed_count = await self._fix_entities(entities)
                self.stats["entities_fixed"] = fixed_count

    async def _fix_entities(self, entities: List[Dict[str, Any]]) -> int:
        """
        Fix Entity nodes missing UUIDs.

        Args:
            entities: List of Entity nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for entity in entities:
            entity_uuid = self.generate_new_uuid()
            timestamp = self.get_timestamp()

            update_query = f"""
            MATCH (e:Entity)
            WHERE id(e) = {entity['id']}
            SET e.uuid = '{entity_uuid}',
                e.updated_at = '{timestamp}'
            RETURN e.name as name, e.type as type, e.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                self.log_info(f"Fixed Entity node: {update_result[1][0][0]} ({update_result[1][0][1]}) with UUID {update_result[1][0][2]}")

        return fixed_count
