#!/usr/bin/env python3
"""
Process all PDF files in a directory through the complete Graphiti pipeline.
This script finds and processes all PDFs in specified directories.
"""

import asyncio
import argparse
import sys
from pathlib import Path
from typing import List, Set

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.document_processing_service import DocumentProcessingService
from database.database_service import DatabaseService
from utils.logging_utils import setup_logging

logger = setup_logging(__name__)

async def get_processed_documents() -> Set[str]:
    """Get list of already processed documents from database."""
    try:
        db_service = DatabaseService()
        documents = await db_service.get_all_documents()
        processed = set()
        
        for doc in documents:
            # Extract original filename from document data
            if "file_path" in doc:
                processed.add(Path(doc["file_path"]).name)
            elif "title" in doc:
                processed.add(doc["title"])
        
        return processed
    except Exception as e:
        logger.warning(f"Could not get processed documents: {e}")
        return set()

async def find_pdf_files(directories: List[Path], recursive: bool = True) -> List[Path]:
    """Find all PDF files in specified directories."""
    pdf_files = []
    
    for directory in directories:
        if not directory.exists():
            logger.warning(f"Directory not found: {directory}")
            continue
        
        if recursive:
            pattern = "**/*.pdf"
        else:
            pattern = "*.pdf"
        
        found_files = list(directory.glob(pattern))
        pdf_files.extend(found_files)
        logger.info(f"Found {len(found_files)} PDF files in {directory}")
    
    return pdf_files

async def process_all_pdfs(
    directories: List[Path],
    skip_processed: bool = True,
    max_workers: int = 3,
    recursive: bool = True
) -> None:
    """
    Process all PDF files in specified directories.
    
    Args:
        directories: List of directories to search
        skip_processed: Skip already processed documents
        max_workers: Maximum parallel workers
        recursive: Search directories recursively
    """
    # Find all PDF files
    pdf_files = await find_pdf_files(directories, recursive)
    
    if not pdf_files:
        logger.warning("No PDF files found")
        return
    
    # Get already processed documents
    processed_docs = set()
    if skip_processed:
        processed_docs = await get_processed_documents()
        logger.info(f"Found {len(processed_docs)} already processed documents")
    
    # Filter out already processed files
    files_to_process = []
    for pdf_file in pdf_files:
        if skip_processed and pdf_file.name in processed_docs:
            logger.info(f"Skipping already processed: {pdf_file.name}")
            continue
        files_to_process.append(pdf_file)
    
    if not files_to_process:
        logger.info("All PDF files have already been processed")
        return
    
    logger.info(f"Processing {len(files_to_process)} PDF files")
    
    # Process files
    service = DocumentProcessingService()
    semaphore = asyncio.Semaphore(max_workers)
    
    async def process_single_pdf(pdf_path: Path) -> None:
        async with semaphore:
            try:
                logger.info(f"Processing: {pdf_path}")
                result = await service.process_document(str(pdf_path))
                
                if result.get("success"):
                    logger.info(f"✅ Successfully processed: {pdf_path.name}")
                else:
                    logger.error(f"❌ Failed to process {pdf_path.name}: {result.get('error')}")
            except Exception as e:
                logger.error(f"❌ Error processing {pdf_path.name}: {e}")
    
    # Process all files concurrently
    tasks = [process_single_pdf(pdf_path) for pdf_path in files_to_process]
    await asyncio.gather(*tasks, return_exceptions=True)
    
    logger.info("✅ Batch processing completed")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Process all PDF files")
    parser.add_argument("directories", nargs="+", help="Directories to search for PDFs")
    parser.add_argument("--max-workers", type=int, default=3, help="Maximum parallel workers")
    parser.add_argument("--no-skip", action="store_true", help="Don't skip processed files")
    parser.add_argument("--no-recursive", action="store_true", help="Don't search recursively")
    
    args = parser.parse_args()
    
    # Validate directories
    directories = []
    for dir_path in args.directories:
        path = Path(dir_path)
        if path.exists() and path.is_dir():
            directories.append(path)
        else:
            logger.error(f"Directory not found: {dir_path}")
            sys.exit(1)
    
    skip_processed = not args.no_skip
    recursive = not args.no_recursive
    
    logger.info(f"Searching {len(directories)} directories")
    logger.info(f"Skip processed: {skip_processed}")
    logger.info(f"Recursive search: {recursive}")
    logger.info(f"Max workers: {args.max_workers}")
    
    # Run processing
    asyncio.run(process_all_pdfs(
        directories, 
        skip_processed, 
        args.max_workers, 
        recursive
    ))

if __name__ == "__main__":
    main()
