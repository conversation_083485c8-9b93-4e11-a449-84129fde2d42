<!-- Enhanced Progress Tracking UI -->
<div id="enhanced-progress-container" class="mt-4" style="display: none;">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5>Document Processing Progress</h5>
            <span id="processing-time" class="badge bg-secondary">00:00</span>
        </div>
        <div class="card-body">
            <!-- Overall Progress -->
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-1">
                    <span>Overall Progress</span>
                    <span id="overall-progress-percentage">0%</span>
                </div>
                <div class="progress" style="height: 20px;">
                    <div id="overall-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
                <p id="overall-status" class="mt-2 mb-0">Processing document...</p>
            </div>

            <!-- Visual Step Indicators -->
            <div class="step-indicators d-flex justify-content-between mb-4">
                <div class="text-center">
                    <div class="step-indicator" id="step-indicator-1">1</div>
                    <div class="step-label">Text Extraction</div>
                </div>
                <div class="text-center">
                    <div class="step-indicator" id="step-indicator-2">2</div>
                    <div class="step-label">Entity Extraction</div>
                </div>
                <div class="text-center">
                    <div class="step-indicator" id="step-indicator-3">3</div>
                    <div class="step-label">Reference Extraction</div>
                </div>
                <div class="text-center">
                    <div class="step-indicator" id="step-indicator-4">4</div>
                    <div class="step-label">Metadata Extraction</div>
                </div>
                <div class="text-center">
                    <div class="step-indicator" id="step-indicator-5">5</div>
                    <div class="step-label">Embedding Generation</div>
                </div>
            </div>

            <!-- Current Step Progress -->
            <div class="mb-4">
                <div class="d-flex justify-content-between mb-1">
                    <span id="current-step-name">Current Step</span>
                    <span id="current-step-percentage">0%</span>
                </div>
                <div class="progress" style="height: 15px;">
                    <div id="current-step-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                         role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>

            <!-- Detailed Processing Statistics Panel -->
            <div class="card mt-3">
                <div class="card-header">
                    <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#processingDetailsCollapse">
                        Processing Details
                    </button>
                </div>
                <div id="processingDetailsCollapse" class="collapse">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Document Information</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Document ID:</span>
                                        <span id="document-id">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Filename:</span>
                                        <span id="document-filename">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Processing Start:</span>
                                        <span id="processing-start">-</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Processing Statistics</h6>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Facts Extracted:</span>
                                        <span id="facts-count">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Entities Extracted:</span>
                                        <span id="entities-count">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>References Extracted:</span>
                                        <span id="references-count">-</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between">
                                        <span>Embeddings Generated:</span>
                                        <span id="embeddings-count">-</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <h6>Processing Steps</h6>
                            <div id="processing-steps">
                                <!-- Step progress bars will be added here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Real-time Processing Log -->
            <div class="card mt-3">
                <div class="card-header">
                    <button class="btn btn-link p-0" type="button" data-bs-toggle="collapse" data-bs-target="#processingLogCollapse">
                        Processing Log
                    </button>
                </div>
                <div id="processingLogCollapse" class="collapse">
                    <div class="card-body">
                        <div class="bg-dark text-light p-3 rounded" style="max-height: 200px; overflow-y: auto;">
                            <pre id="processing-log">Waiting for processing to start...</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
