"""
Reference adapter for the Graphiti application.

This module provides functions for storing references in the database.
"""

import time
from typing import Dict, Any, Optional, List
import uuid

from database.database_service import get_falkordb_adapter
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def create_reference_node(text: str, properties: Dict[str, Any] = None) -> Optional[str]:
    """
    Create a Reference node.

    Args:
        text: Text of the reference
        properties: Additional properties

    Returns:
        UUID of the created node, or None if creation failed
    """
    adapter = await get_falkordb_adapter()

    # Prepare properties
    props = {"text": text}
    if properties:
        props.update(properties)

    # Ensure UUID is generated
    if "uuid" not in props:
        props["uuid"] = str(uuid.uuid4())

    # Add timestamp instead of using datetime() function
    if "extraction_timestamp" not in props:
        props["extraction_timestamp"] = int(time.time())

    # Create node
    return adapter.create_node("Reference", props)

async def link_reference_to_document(reference_uuid: str, document_uuid: str) -> bool:
    """
    Link a Reference node to an Episode node.

    Args:
        reference_uuid: UUID of the reference
        document_uuid: UUID of the document

    Returns:
        True if the relationship was created, False otherwise
    """
    adapter = await get_falkordb_adapter()

    # Create relationship
    return adapter.create_relationship(document_uuid, reference_uuid, "HAS_REFERENCE")

async def store_references_in_database(document_uuid: str, references: List[Dict[str, Any]]) -> int:
    """
    Store references in the database.

    Args:
        document_uuid: UUID of the document
        references: List of references to store

    Returns:
        Number of references stored
    """
    adapter = await get_falkordb_adapter()
    references_stored = 0

    for ref in references:
        # Handle different reference formats
        if isinstance(ref, dict):
            # Extract text and method
            text = ref.get("text", "")
            if not text and "reference_text" in ref:
                text = ref["reference_text"]

            method = ref.get("extraction_method", "regex")

            # Clean up text
            text = text.replace("'", "\\'")

            # Use timestamp instead of datetime() function
            timestamp = int(time.time())

            # Create query with all available properties
            props = []
            props.append(f"r.extraction_method = '{method}'")
            props.append(f"r.extraction_timestamp = {timestamp}")

            # Add other properties if available
            for key, value in ref.items():
                if key not in ["text", "reference_text", "extraction_method"] and value:
                    if isinstance(value, str):
                        # Escape single quotes for Cypher
                        escaped_value = value.replace("'", "\\'")
                        props.append(f"r.{key} = '{escaped_value}'")
                    elif isinstance(value, (int, float, bool)):
                        props.append(f"r.{key} = {value}")

            # Create query
            query = f"""
            MATCH (e:Episode {{uuid: '{document_uuid}'}})
            MERGE (r:Reference {{text: '{text}'}})
            ON CREATE SET {', '.join(props)}
            MERGE (e)-[:HAS_REFERENCE]->(r)
            RETURN r.uuid
            """

            result = adapter.execute_cypher(query)

            if result and len(result) > 1 and len(result[1]) > 0:
                references_stored += 1

        elif isinstance(ref, str):
            # Simple string reference
            text = ref.replace("'", "\\'")
            timestamp = int(time.time())

            query = f"""
            MATCH (e:Episode {{uuid: '{document_uuid}'}})
            MERGE (r:Reference {{text: '{text}'}})
            ON CREATE SET r.extraction_method = 'regex', r.extraction_timestamp = {timestamp}
            MERGE (e)-[:HAS_REFERENCE]->(r)
            RETURN r.uuid
            """

            result = adapter.execute_cypher(query)

            if result and len(result) > 1 and len(result[1]) > 0:
                references_stored += 1

    return references_stored
