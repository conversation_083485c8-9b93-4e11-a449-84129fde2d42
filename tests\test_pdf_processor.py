"""
Test script to check if the PDF processor is working correctly
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    # Load environment variables
    load_dotenv()
    
    # Import the PDF processor
    try:
        from pdf_processor import extract_text_from_pdf, mistral_ocr_processor
        logger.info("Successfully imported PDF processor functions")
    except ImportError as e:
        logger.error(f"Failed to import PDF processor functions: {e}")
        return False
    
    # Check if Mistral OCR processor is initialized
    if mistral_ocr_processor:
        logger.info("Mistral OCR processor is initialized")
    else:
        logger.warning("Mistral OCR processor is NOT initialized")
    
    # Test with a sample PDF
    pdf_path = "Documents/3 Antioxidant Controversy Shauna <PERSON>.pdf"
    if not os.path.exists(pdf_path):
        logger.error(f"PDF file not found: {pdf_path}")
        return False
    
    # Extract text from the PDF
    try:
        logger.info(f"Extracting text from PDF: {pdf_path}")
        text = await extract_text_from_pdf(pdf_path)
        
        if text:
            text_length = len(text)
            logger.info(f"Successfully extracted {text_length} characters of text")
            
            # Print a sample of the extracted text
            sample_length = min(500, text_length)
            print(f"\nSample of extracted text ({sample_length} characters):")
            print("-" * 80)
            print(text[:sample_length])
            print("-" * 80)
            
            return True
        else:
            logger.error("No text extracted from the PDF")
            return False
    except Exception as e:
        logger.error(f"Exception while extracting text from PDF: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        print("\nPDF processing completed successfully!")
    else:
        print("\nPDF processing failed. Check the logs above for details.")
