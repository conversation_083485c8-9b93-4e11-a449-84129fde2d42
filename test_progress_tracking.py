#!/usr/bin/env python3
"""
Test script to verify progress tracking is working correctly.
"""

import requests
import json
import time
import sys

def test_operation_progress(operation_id):
    """Test progress tracking for a specific operation."""
    base_url = "http://localhost:8000"
    
    print(f"🔍 Testing Progress Tracking for Operation: {operation_id}")
    print("=" * 60)
    
    # Test basic progress endpoint
    print("\n1. Testing basic progress endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/progress/{operation_id}", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Basic progress data retrieved")
            print(f"   Document: {data.get('document_name', 'Unknown')}")
            print(f"   Status: {data.get('status', 'Unknown')}")
            print(f"   Progress: {data.get('progress_percentage', 0)}%")
            print(f"   Step: {data.get('current_step', 0)}/7")
        else:
            print(f"   ❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False
    
    # Test detailed progress endpoint
    print("\n2. Testing detailed progress endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/progress/{operation_id}/detailed", timeout=10)
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Detailed progress data retrieved")
            print(f"   Document: {data.get('document_name', 'Unknown')}")
            print(f"   Status: {data.get('status', 'Unknown')}")
            print(f"   Progress: {data.get('progress_percentage', 0)}%")
            
            # Show statistics
            print(f"\n   📊 Statistics:")
            print(f"   Facts: {data.get('facts_count', 0)}")
            print(f"   Entities: {data.get('entities_count', 0)}")
            print(f"   References: {data.get('references_count', 0)}")
            print(f"   Embeddings: {data.get('embeddings_count', 0)}")
            
            # Show timing info
            if data.get('start_time'):
                print(f"\n   ⏱️  Timing:")
                print(f"   Start: {data.get('start_time')}")
                if data.get('completion_time'):
                    print(f"   Completed: {data.get('completion_time')}")
                if data.get('processing_time'):
                    print(f"   Duration: {data.get('processing_time')}")
            
            return True
        else:
            print(f"   ❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return False


def simulate_upload_and_track():
    """Simulate an upload and track its progress."""
    print("\n🚀 Simulating Upload and Progress Tracking")
    print("=" * 60)
    
    # This would normally upload a file, but for testing we'll just
    # show how the progress tracking should work
    print("Note: This is a simulation of how progress tracking should work")
    print("In a real scenario, you would:")
    print("1. Upload a file to /api/enhanced/enhanced-upload")
    print("2. Get an operation_id in the response")
    print("3. Poll /api/enhanced/progress/{operation_id} for updates")
    print("4. Display real-time progress in the UI")


def main():
    """Main test function."""
    if len(sys.argv) > 1:
        operation_id = sys.argv[1]
        success = test_operation_progress(operation_id)
        
        if success:
            print(f"\n✅ Progress tracking test PASSED for operation {operation_id}")
            print("\nTo test in the UI:")
            print(f"1. Open: http://localhost:8000/enhanced-upload?operation_id={operation_id}")
            print("2. The progress should automatically load and display the results")
        else:
            print(f"\n❌ Progress tracking test FAILED for operation {operation_id}")
    else:
        print("Usage: python test_progress_tracking.py <operation_id>")
        print("\nExample:")
        print("python test_progress_tracking.py fec4f92c-8a58-4274-a6db-deb2885491e5")
        
        simulate_upload_and_track()


if __name__ == "__main__":
    main()
