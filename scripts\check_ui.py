"""
<PERSON><PERSON>t to check the UI tabs and features of the Graphiti web interface
"""

import requests
from bs4 import BeautifulSoup

def check_ui_tabs():
    """Check the UI tabs and features"""
    print("Checking UI tabs and features...")

    # Get the main page
    response = requests.get("http://localhost:9753")
    soup = BeautifulSoup(response.text, 'html.parser')

    # Find all tabs
    tabs = soup.select('.nav-item')
    print(f"Found {len(tabs)} tabs:")

    for i, tab in enumerate(tabs):
        tab_link = tab.select_one('.nav-link')
        if tab_link:
            tab_id = tab_link.get('id')
            tab_text = tab_link.text.strip()
            print(f"  {i+1}. {tab_text} (ID: {tab_id})")

    # Check API endpoints
    print("\nChecking API endpoints...")

    # Check entities endpoint
    entities_response = requests.get("http://localhost:9753/api/entities")
    entities_data = entities_response.json()
    print(f"  /api/entities: {len(entities_data.get('entities', []))} entities returned")

    # Check entity types
    entity_types = {}
    for entity in entities_data.get('entities', []):
        entity_type = entity.get('type')
        if entity_type:
            entity_types[entity_type] = entity_types.get(entity_type, 0) + 1

    print("  Entity types:")
    for entity_type, count in sorted(entity_types.items()):
        print(f"    - {entity_type}: {count} entities")

    # Check a specific entity
    if entities_data.get('entities'):
        entity_uuid = entities_data['entities'][0]['uuid']
        entity_response = requests.get(f"http://localhost:9753/api/entities/{entity_uuid}")
        entity_data = entity_response.json()
        print(f"\n  Entity details for {entity_data.get('name')}:")
        print(f"    - Type: {entity_data.get('type')}")
        print(f"    - Description: {entity_data.get('description', '')[:50]}...")
        print(f"    - Mentions: {len(entity_data.get('mentions', []))}")
        print(f"    - Relationships: {len(entity_data.get('relationships', []))}")

    # Check documents endpoint
    try:
        documents_response = requests.get("http://localhost:9753/api/documents")
        documents_data = documents_response.json()
        print(f"\n  /api/documents: {len(documents_data.get('documents', []))} documents returned")
    except Exception as e:
        print(f"\n  /api/documents: Error - {str(e)}")

    print("\nUI check complete!")

if __name__ == "__main__":
    check_ui_tabs()
