"""
Utility functions for entity deduplication.
"""

import difflib
import logging
import re
from typing import Dict, List, Tuple, Any, Optional

from entity_deduplication.models import EntityForDeduplication, EntityMatch
from entity_deduplication.config import (
    NAME_SIMILARITY_THRESHOLD,
    TYPE_MATCH_REQUIRED,
    MIN_OVERALL_SIMILARITY,
    USE_DYNAMIC_THRESHOLDS
)

# Set up logging
logger = logging.getLogger(__name__)


def normalize_text(text: str) -> str:
    """
    Normalize text for comparison by removing punctuation, extra spaces, and converting to lowercase.

    Args:
        text: Text to normalize

    Returns:
        Normalized text
    """
    if not text:
        return ""

    # Convert to lowercase
    text = text.lower()

    # Remove punctuation
    text = re.sub(r'[^\w\s]', ' ', text)

    # Remove extra whitespace
    text = re.sub(r'\s+', ' ', text).strip()

    return text


def calculate_string_similarity(str1: str, str2: str) -> float:
    """
    Calculate similarity between two strings using sequence matcher.

    Args:
        str1: First string
        str2: Second string

    Returns:
        Similarity score between 0 and 1
    """
    if not str1 or not str2:
        return 0.0

    # Normalize strings
    norm_str1 = normalize_text(str1)
    norm_str2 = normalize_text(str2)

    # Calculate similarity
    return difflib.SequenceMatcher(None, norm_str1, norm_str2).ratio()


def calculate_entity_similarity(
    entity1: EntityForDeduplication,
    entity2: EntityForDeduplication
) -> Tuple[float, str]:
    """
    Calculate similarity between two entities using dynamic thresholds.

    Args:
        entity1: First entity
        entity2: Second entity

    Returns:
        Tuple of (similarity score, match type)
    """
    # Check if types match (if required)
    if TYPE_MATCH_REQUIRED and entity1.type != entity2.type:
        return 0.0, "none"

    # Get dynamic threshold calculator if enabled
    if USE_DYNAMIC_THRESHOLDS:
        try:
            from entity_deduplication.dynamic_thresholds import get_threshold_calculator
            threshold_calc = get_threshold_calculator()
            name_threshold = threshold_calc.get_threshold(entity1, entity2, "name")
            semantic_threshold = threshold_calc.get_threshold(entity1, entity2, "semantic")
        except ImportError:
            # Fall back to static thresholds if dynamic module not available
            name_threshold = NAME_SIMILARITY_THRESHOLD
            semantic_threshold = MIN_OVERALL_SIMILARITY
    else:
        name_threshold = NAME_SIMILARITY_THRESHOLD
        semantic_threshold = MIN_OVERALL_SIMILARITY

    # Calculate name similarity
    name_similarity = calculate_string_similarity(entity1.name, entity2.name)

    # If names are exactly the same (case-insensitive)
    if normalize_text(entity1.name) == normalize_text(entity2.name):
        return 1.0, "exact"

    # If names are very similar (using dynamic threshold)
    if name_similarity >= name_threshold:
        return name_similarity, "fuzzy"

    # If embeddings are available, calculate cosine similarity
    if entity1.embedding and entity2.embedding:
        from numpy import dot
        from numpy.linalg import norm

        cosine_similarity = dot(entity1.embedding, entity2.embedding) / (
            norm(entity1.embedding) * norm(entity2.embedding)
        )

        if cosine_similarity >= semantic_threshold:
            return cosine_similarity, "semantic"

    return 0.0, "none"


def calculate_entity_similarity_multidimensional(
    entity1: EntityForDeduplication,
    entity2: EntityForDeduplication
) -> Tuple[float, str]:
    """
    Calculate multi-dimensional similarity between two entities.

    This function uses the new multi-dimensional scoring system that combines:
    - Semantic embeddings
    - Syntactic similarity
    - Contextual relevance
    - Domain-specific attributes
    - Confidence weighting

    Args:
        entity1: First entity
        entity2: Second entity

    Returns:
        Tuple of (similarity score, match type with details)
    """
    # Check if types match (if required)
    if TYPE_MATCH_REQUIRED and entity1.type != entity2.type:
        return 0.0, "none"

    try:
        from entity_deduplication.multi_dimensional_scoring import get_multi_dimensional_scorer

        scorer = get_multi_dimensional_scorer()
        components = scorer.calculate_similarity(entity1, entity2)

        # Determine match type based on component scores
        if components.overall_score >= 0.95:
            match_type = "exact_multi"
        elif components.overall_score >= 0.8:
            # Determine primary matching factor
            scores = components.to_dict()
            primary_factor = max(scores.items(), key=lambda x: x[1] if x[0] != 'overall' else 0)
            match_type = f"strong_{primary_factor[0]}"
        elif components.overall_score >= 0.6:
            scores = components.to_dict()
            primary_factor = max(scores.items(), key=lambda x: x[1] if x[0] != 'overall' else 0)
            match_type = f"moderate_{primary_factor[0]}"
        elif components.overall_score >= 0.4:
            match_type = "weak_multi"
        else:
            match_type = "none"

        return components.overall_score, match_type

    except ImportError:
        # Fall back to original method if multi-dimensional scoring not available
        return calculate_entity_similarity(entity1, entity2)


def create_entity_match(
    entity1: EntityForDeduplication,
    entity2: EntityForDeduplication,
    similarity_score: float,
    match_type: str
) -> EntityMatch:
    """
    Create an entity match object.

    Args:
        entity1: Source entity
        entity2: Target entity
        similarity_score: Similarity score
        match_type: Type of match

    Returns:
        EntityMatch object
    """
    return EntityMatch(
        source_uuid=entity1.uuid,
        target_uuid=entity2.uuid,
        similarity_score=similarity_score,
        match_type=match_type,
        source_name=entity1.name,
        target_name=entity2.name,
        source_type=entity1.type,
        target_type=entity2.type,
        merged=False
    )


def chunk_entities(entities: List[EntityForDeduplication], chunk_size: int) -> List[List[EntityForDeduplication]]:
    """
    Split entities into chunks for batch processing.

    Args:
        entities: List of entities
        chunk_size: Size of each chunk

    Returns:
        List of entity chunks
    """
    return [entities[i:i + chunk_size] for i in range(0, len(entities), chunk_size)]
