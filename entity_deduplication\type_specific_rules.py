"""
Entity type-specific deduplication rules.

This module implements specialized deduplication logic for different entity types:
- Person: Name variations, titles, aliases
- Organization: Abbreviations, legal forms, subsidiaries
- Location: Geographic variations, administrative levels
- Medical entities: Drug names, conditions, treatments
- Research entities: Papers, authors, institutions
"""

import logging
import re
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass
from abc import ABC, abstractmethod

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.utils import calculate_string_similarity, normalize_text

logger = logging.getLogger(__name__)


@dataclass
class TypeSpecificMatch:
    """Result of type-specific matching."""
    similarity_score: float
    match_type: str
    confidence: float
    reasoning: str
    type_specific_factors: Dict[str, Any]


class EntityTypeRule(ABC):
    """Abstract base class for entity type-specific rules."""
    
    @abstractmethod
    def calculate_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> TypeSpecificMatch:
        """Calculate type-specific similarity between entities."""
        pass
    
    @abstractmethod
    def get_entity_variants(self, entity: EntityForDeduplication) -> Set[str]:
        """Get possible variants/aliases for an entity."""
        pass
    
    @abstractmethod
    def normalize_entity_name(self, name: str) -> str:
        """Normalize entity name according to type-specific rules."""
        pass


class PersonEntityRule(EntityTypeRule):
    """Rules for Person entities."""
    
    def __init__(self):
        self.titles = {
            'dr', 'doctor', 'prof', 'professor', 'mr', 'mrs', 'ms', 'miss',
            'phd', 'md', 'dds', 'dvm', 'jr', 'sr', 'ii', 'iii', 'iv'
        }
        self.name_prefixes = {'van', 'von', 'de', 'del', 'la', 'le', 'mac', 'mc', 'o'}
    
    def calculate_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> TypeSpecificMatch:
        """Calculate person-specific similarity."""
        name1 = entity1.name
        name2 = entity2.name
        
        # Extract name components
        components1 = self._extract_name_components(name1)
        components2 = self._extract_name_components(name2)
        
        # Calculate component-wise similarity
        first_name_sim = self._calculate_name_similarity(
            components1.get('first', ''), components2.get('first', '')
        )
        last_name_sim = self._calculate_name_similarity(
            components1.get('last', ''), components2.get('last', '')
        )
        middle_sim = self._calculate_middle_name_similarity(
            components1.get('middle', []), components2.get('middle', [])
        )
        
        # Title consistency
        title_consistency = self._check_title_consistency(
            components1.get('titles', []), components2.get('titles', [])
        )
        
        # Calculate weighted similarity
        # Last name is most important, first name second, middle names and titles less so
        weights = {'last': 0.4, 'first': 0.35, 'middle': 0.15, 'title': 0.1}
        
        overall_similarity = (
            last_name_sim * weights['last'] +
            first_name_sim * weights['first'] +
            middle_sim * weights['middle'] +
            title_consistency * weights['title']
        )
        
        # Determine match type
        if last_name_sim > 0.9 and first_name_sim > 0.9:
            match_type = "exact_person"
            confidence = 0.95
        elif last_name_sim > 0.8 and first_name_sim > 0.7:
            match_type = "strong_person"
            confidence = 0.8
        elif last_name_sim > 0.7 and (first_name_sim > 0.8 or self._is_initial_match(components1, components2)):
            match_type = "probable_person"
            confidence = 0.7
        else:
            match_type = "weak_person"
            confidence = 0.4
        
        reasoning = f"Last name: {last_name_sim:.2f}, First name: {first_name_sim:.2f}, Middle: {middle_sim:.2f}"
        
        return TypeSpecificMatch(
            similarity_score=overall_similarity,
            match_type=match_type,
            confidence=confidence,
            reasoning=reasoning,
            type_specific_factors={
                'last_name_similarity': last_name_sim,
                'first_name_similarity': first_name_sim,
                'middle_name_similarity': middle_sim,
                'title_consistency': title_consistency,
                'components1': components1,
                'components2': components2
            }
        )
    
    def _extract_name_components(self, name: str) -> Dict[str, Any]:
        """Extract name components (first, middle, last, titles)."""
        if not name:
            return {}
        
        # Clean and split name
        clean_name = re.sub(r'[^\w\s\.]', ' ', name.lower())
        parts = [p.strip('.') for p in clean_name.split() if p.strip('.')]
        
        components = {'titles': [], 'middle': []}
        
        # Extract titles
        remaining_parts = []
        for part in parts:
            if part in self.titles:
                components['titles'].append(part)
            else:
                remaining_parts.append(part)
        
        # Extract first, middle, last names
        if len(remaining_parts) == 1:
            components['first'] = remaining_parts[0]
        elif len(remaining_parts) == 2:
            components['first'] = remaining_parts[0]
            components['last'] = remaining_parts[1]
        elif len(remaining_parts) >= 3:
            components['first'] = remaining_parts[0]
            components['last'] = remaining_parts[-1]
            components['middle'] = remaining_parts[1:-1]
        
        return components
    
    def _calculate_name_similarity(self, name1: str, name2: str) -> float:
        """Calculate similarity between name parts."""
        if not name1 or not name2:
            return 0.0
        
        # Exact match
        if name1 == name2:
            return 1.0
        
        # Initial match (e.g., "John" vs "J")
        if len(name1) == 1 and name2.startswith(name1):
            return 0.8
        if len(name2) == 1 and name1.startswith(name2):
            return 0.8
        
        # String similarity
        return calculate_string_similarity(name1, name2)
    
    def _calculate_middle_name_similarity(self, middle1: List[str], middle2: List[str]) -> float:
        """Calculate similarity between middle names."""
        if not middle1 and not middle2:
            return 1.0
        if not middle1 or not middle2:
            return 0.5  # One has middle names, other doesn't
        
        # Compare each middle name
        similarities = []
        for m1 in middle1:
            best_sim = max([self._calculate_name_similarity(m1, m2) for m2 in middle2], default=0)
            similarities.append(best_sim)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def _check_title_consistency(self, titles1: List[str], titles2: List[str]) -> float:
        """Check consistency between titles."""
        if not titles1 and not titles2:
            return 1.0
        
        # Convert to sets for comparison
        set1 = set(titles1)
        set2 = set(titles2)
        
        # Check for equivalent titles
        equivalent_titles = {
            ('dr', 'doctor'), ('prof', 'professor'), ('md', 'doctor')
        }
        
        for title1 in set1:
            for title2 in set2:
                if title1 == title2:
                    return 1.0
                for equiv in equivalent_titles:
                    if (title1 in equiv and title2 in equiv):
                        return 0.9
        
        return 0.5 if set1 or set2 else 1.0
    
    def _is_initial_match(self, components1: Dict, components2: Dict) -> bool:
        """Check if one name uses initials for the other."""
        first1 = components1.get('first', '')
        first2 = components2.get('first', '')
        
        if len(first1) == 1 and len(first2) > 1:
            return first2.startswith(first1)
        if len(first2) == 1 and len(first1) > 1:
            return first1.startswith(first2)
        
        return False
    
    def get_entity_variants(self, entity: EntityForDeduplication) -> Set[str]:
        """Get possible variants for a person name."""
        variants = {entity.name}
        components = self._extract_name_components(entity.name)
        
        first = components.get('first', '')
        last = components.get('last', '')
        middle = components.get('middle', [])
        titles = components.get('titles', [])
        
        if first and last:
            # Add variants with/without titles
            base_name = f"{first} {last}"
            variants.add(base_name)
            
            # Add with initials
            if len(first) > 1:
                variants.add(f"{first[0]}. {last}")
                variants.add(f"{first[0]} {last}")
            
            # Add with middle names/initials
            for m in middle:
                variants.add(f"{first} {m} {last}")
                if len(m) > 1:
                    variants.add(f"{first} {m[0]}. {last}")
            
            # Add with titles
            for title in titles:
                variants.add(f"{title.title()} {first} {last}")
                variants.add(f"{first} {last}, {title.upper()}")
        
        return variants
    
    def normalize_entity_name(self, name: str) -> str:
        """Normalize person name."""
        components = self._extract_name_components(name)
        
        # Build normalized name: First Middle Last
        parts = []
        if 'first' in components:
            parts.append(components['first'].title())
        if 'middle' in components:
            parts.extend([m.title() for m in components['middle']])
        if 'last' in components:
            parts.append(components['last'].title())
        
        return ' '.join(parts)


class OrganizationEntityRule(EntityTypeRule):
    """Rules for Organization entities."""
    
    def __init__(self):
        self.legal_forms = {
            'inc', 'incorporated', 'corp', 'corporation', 'llc', 'ltd', 'limited',
            'co', 'company', 'plc', 'sa', 'gmbh', 'ag', 'bv', 'nv'
        }
        self.common_abbreviations = {
            'university': ['univ', 'u'],
            'institute': ['inst', 'i'],
            'hospital': ['hosp', 'h'],
            'medical': ['med'],
            'school': ['sch'],
            'center': ['ctr', 'centre'],
            'international': ['intl', 'int'],
            'national': ['natl', 'nat']
        }
    
    def calculate_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> TypeSpecificMatch:
        """Calculate organization-specific similarity."""
        name1 = entity1.name
        name2 = entity2.name
        
        # Normalize names
        norm1 = self._normalize_org_name(name1)
        norm2 = self._normalize_org_name(name2)
        
        # Basic similarity
        basic_sim = calculate_string_similarity(norm1, norm2)
        
        # Check for abbreviation matches
        abbrev_sim = self._check_abbreviation_similarity(name1, name2)
        
        # Check legal form consistency
        legal_consistency = self._check_legal_form_consistency(name1, name2)
        
        # Check for parent-subsidiary relationships
        hierarchy_sim = self._check_organizational_hierarchy(name1, name2)
        
        # Combine scores
        overall_similarity = max(basic_sim, abbrev_sim) * 0.6 + legal_consistency * 0.2 + hierarchy_sim * 0.2
        
        # Determine match type and confidence
        if overall_similarity > 0.9:
            match_type = "exact_organization"
            confidence = 0.9
        elif overall_similarity > 0.8 or abbrev_sim > 0.8:
            match_type = "strong_organization"
            confidence = 0.8
        elif overall_similarity > 0.7:
            match_type = "probable_organization"
            confidence = 0.7
        else:
            match_type = "weak_organization"
            confidence = 0.4
        
        reasoning = f"Basic: {basic_sim:.2f}, Abbrev: {abbrev_sim:.2f}, Legal: {legal_consistency:.2f}"
        
        return TypeSpecificMatch(
            similarity_score=overall_similarity,
            match_type=match_type,
            confidence=confidence,
            reasoning=reasoning,
            type_specific_factors={
                'basic_similarity': basic_sim,
                'abbreviation_similarity': abbrev_sim,
                'legal_consistency': legal_consistency,
                'hierarchy_similarity': hierarchy_sim
            }
        )
    
    def _normalize_org_name(self, name: str) -> str:
        """Normalize organization name."""
        if not name:
            return ""
        
        # Convert to lowercase and remove punctuation
        normalized = re.sub(r'[^\w\s]', ' ', name.lower())
        
        # Remove legal forms
        words = normalized.split()
        filtered_words = [w for w in words if w not in self.legal_forms]
        
        return ' '.join(filtered_words)
    
    def _check_abbreviation_similarity(self, name1: str, name2: str) -> float:
        """Check for abbreviation matches."""
        # Check if one is an abbreviation of the other
        words1 = re.findall(r'\b\w+\b', name1.lower())
        words2 = re.findall(r'\b\w+\b', name2.lower())
        
        # Check for acronym match
        if len(words1) > 1 and len(words2) == 1:
            acronym = ''.join(w[0] for w in words1)
            if acronym == words2[0]:
                return 0.9
        
        if len(words2) > 1 and len(words1) == 1:
            acronym = ''.join(w[0] for w in words2)
            if acronym == words1[0]:
                return 0.9
        
        # Check for common abbreviations
        for word1 in words1:
            for word2 in words2:
                for full_word, abbrevs in self.common_abbreviations.items():
                    if (word1 == full_word and word2 in abbrevs) or (word2 == full_word and word1 in abbrevs):
                        return 0.8
        
        return 0.0
    
    def _check_legal_form_consistency(self, name1: str, name2: str) -> float:
        """Check legal form consistency."""
        legal1 = self._extract_legal_forms(name1)
        legal2 = self._extract_legal_forms(name2)
        
        if not legal1 and not legal2:
            return 1.0
        if legal1 == legal2:
            return 1.0
        if legal1 and legal2:
            return 0.5  # Different legal forms
        return 0.8  # One has legal form, other doesn't
    
    def _extract_legal_forms(self, name: str) -> Set[str]:
        """Extract legal forms from organization name."""
        words = re.findall(r'\b\w+\b', name.lower())
        return {w for w in words if w in self.legal_forms}
    
    def _check_organizational_hierarchy(self, name1: str, name2: str) -> float:
        """Check for parent-subsidiary or departmental relationships."""
        # Simple check: if one name contains the other
        norm1 = self._normalize_org_name(name1)
        norm2 = self._normalize_org_name(name2)
        
        if norm1 in norm2 or norm2 in norm1:
            return 0.6  # Possible hierarchy relationship
        
        return 0.0
    
    def get_entity_variants(self, entity: EntityForDeduplication) -> Set[str]:
        """Get possible variants for organization name."""
        variants = {entity.name}
        name = entity.name
        
        # Add normalized version
        normalized = self._normalize_org_name(name)
        if normalized:
            variants.add(normalized)
        
        # Add versions with/without legal forms
        legal_forms = self._extract_legal_forms(name)
        if legal_forms:
            # Version without legal forms
            words = name.split()
            filtered = [w for w in words if w.lower() not in self.legal_forms]
            variants.add(' '.join(filtered))
        
        # Add common abbreviations
        words = name.split()
        for i, word in enumerate(words):
            word_lower = word.lower()
            for full_word, abbrevs in self.common_abbreviations.items():
                if word_lower == full_word:
                    for abbrev in abbrevs:
                        new_words = words.copy()
                        new_words[i] = abbrev.title()
                        variants.add(' '.join(new_words))
        
        return variants
    
    def normalize_entity_name(self, name: str) -> str:
        """Normalize organization name."""
        if not name:
            return ""
        
        # Capitalize properly and clean up spacing
        words = re.findall(r'\b\w+\b', name)
        return ' '.join(word.title() for word in words)


class LocationEntityRule(EntityTypeRule):
    """Rules for Location entities."""
    
    def __init__(self):
        self.location_types = {
            'city', 'town', 'village', 'county', 'state', 'province', 'country',
            'district', 'region', 'area', 'zone', 'territory'
        }
        self.abbreviations = {
            'saint': ['st', 'st.'],
            'mount': ['mt', 'mt.'],
            'fort': ['ft', 'ft.'],
            'north': ['n', 'n.'],
            'south': ['s', 's.'],
            'east': ['e', 'e.'],
            'west': ['w', 'w.']
        }
    
    def calculate_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> TypeSpecificMatch:
        """Calculate location-specific similarity."""
        name1 = entity1.name
        name2 = entity2.name
        
        # Extract location components
        components1 = self._extract_location_components(name1)
        components2 = self._extract_location_components(name2)
        
        # Calculate component similarities
        primary_sim = calculate_string_similarity(
            components1.get('primary', ''), components2.get('primary', '')
        )
        
        # Check for administrative level consistency
        admin_consistency = self._check_admin_level_consistency(components1, components2)
        
        # Check for abbreviation matches
        abbrev_sim = self._check_location_abbreviations(name1, name2)
        
        # Geographic hierarchy check
        hierarchy_sim = self._check_geographic_hierarchy(components1, components2)
        
        # Combine scores
        overall_similarity = max(primary_sim, abbrev_sim) * 0.7 + admin_consistency * 0.2 + hierarchy_sim * 0.1
        
        # Determine match type and confidence
        if overall_similarity > 0.95:
            match_type = "exact_location"
            confidence = 0.95
        elif overall_similarity > 0.85:
            match_type = "strong_location"
            confidence = 0.85
        elif overall_similarity > 0.75:
            match_type = "probable_location"
            confidence = 0.75
        else:
            match_type = "weak_location"
            confidence = 0.4
        
        reasoning = f"Primary: {primary_sim:.2f}, Admin: {admin_consistency:.2f}, Abbrev: {abbrev_sim:.2f}"
        
        return TypeSpecificMatch(
            similarity_score=overall_similarity,
            match_type=match_type,
            confidence=confidence,
            reasoning=reasoning,
            type_specific_factors={
                'primary_similarity': primary_sim,
                'admin_consistency': admin_consistency,
                'abbreviation_similarity': abbrev_sim,
                'hierarchy_similarity': hierarchy_sim,
                'components1': components1,
                'components2': components2
            }
        )
    
    def _extract_location_components(self, name: str) -> Dict[str, str]:
        """Extract location components (primary name, admin level, etc.)."""
        if not name:
            return {}
        
        # Split by comma (common in location names)
        parts = [p.strip() for p in name.split(',')]
        
        components = {'primary': parts[0]}
        
        if len(parts) > 1:
            components['secondary'] = parts[1]
        if len(parts) > 2:
            components['tertiary'] = parts[2]
        
        return components
    
    def _check_admin_level_consistency(self, comp1: Dict, comp2: Dict) -> float:
        """Check administrative level consistency."""
        # If both have same number of components, likely same admin level
        len1 = len([v for v in comp1.values() if v])
        len2 = len([v for v in comp2.values() if v])
        
        if len1 == len2:
            return 1.0
        elif abs(len1 - len2) == 1:
            return 0.8
        else:
            return 0.5
    
    def _check_location_abbreviations(self, name1: str, name2: str) -> float:
        """Check for location-specific abbreviations."""
        words1 = name1.lower().split()
        words2 = name2.lower().split()
        
        for word1 in words1:
            for word2 in words2:
                for full_word, abbrevs in self.abbreviations.items():
                    if (word1 == full_word and word2 in abbrevs) or (word2 == full_word and word1 in abbrevs):
                        return 0.9
        
        return 0.0
    
    def _check_geographic_hierarchy(self, comp1: Dict, comp2: Dict) -> float:
        """Check for geographic hierarchy relationships."""
        # Simple check: if one location is contained in another
        all_parts1 = ' '.join(comp1.values()).lower()
        all_parts2 = ' '.join(comp2.values()).lower()
        
        if comp1.get('primary', '').lower() in all_parts2 or comp2.get('primary', '').lower() in all_parts1:
            return 0.6
        
        return 0.0
    
    def get_entity_variants(self, entity: EntityForDeduplication) -> Set[str]:
        """Get possible variants for location name."""
        variants = {entity.name}
        
        # Add versions with common abbreviations
        name = entity.name
        for full_word, abbrevs in self.abbreviations.items():
            if full_word in name.lower():
                for abbrev in abbrevs:
                    variants.add(name.replace(full_word, abbrev))
                    variants.add(name.replace(full_word.title(), abbrev.title()))
        
        return variants
    
    def normalize_entity_name(self, name: str) -> str:
        """Normalize location name."""
        if not name:
            return ""
        
        # Capitalize properly
        parts = name.split(',')
        normalized_parts = [part.strip().title() for part in parts]
        return ', '.join(normalized_parts)


class TypeSpecificRuleEngine:
    """Engine for applying type-specific deduplication rules."""
    
    def __init__(self):
        self.rules = {
            'Person': PersonEntityRule(),
            'Organization': OrganizationEntityRule(),
            'Location': LocationEntityRule(),
            # Add more rules as needed
        }
    
    def calculate_type_specific_similarity(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> Optional[TypeSpecificMatch]:
        """Calculate type-specific similarity if rules exist for the entity type."""
        entity_type = entity1.type if entity1.type == entity2.type else None
        
        if entity_type in self.rules:
            return self.rules[entity_type].calculate_similarity(entity1, entity2)
        
        return None
    
    def get_entity_variants(self, entity: EntityForDeduplication) -> Set[str]:
        """Get entity variants using type-specific rules."""
        if entity.type in self.rules:
            return self.rules[entity.type].get_entity_variants(entity)
        
        return {entity.name}
    
    def normalize_entity_name(self, entity: EntityForDeduplication) -> str:
        """Normalize entity name using type-specific rules."""
        if entity.type in self.rules:
            return self.rules[entity.type].normalize_entity_name(entity.name)
        
        return entity.name
    
    def get_supported_types(self) -> List[str]:
        """Get list of supported entity types."""
        return list(self.rules.keys())


# Global instance
_type_rule_engine = None


def get_type_rule_engine() -> TypeSpecificRuleEngine:
    """Get the global type-specific rule engine."""
    global _type_rule_engine
    if _type_rule_engine is None:
        _type_rule_engine = TypeSpecificRuleEngine()
    return _type_rule_engine
