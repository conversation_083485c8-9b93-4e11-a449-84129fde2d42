"""
Test script to check the API endpoints with filtering
"""

import requests
import json

def test_api_filtering():
    """Test the API endpoints with filtering"""
    # Test filtering by entity type
    type_url = "http://localhost:8024/api/entities?type=Herb"
    print(f"Testing filtering by type: {type_url}")
    try:
        response = requests.get(type_url)
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data['entities'])} entities of type Herb")
            print(json.dumps(data, indent=2))
    except Exception as e:
        print(f"Error: {e}")
    
    # Test filtering by search query
    query_url = "http://localhost:8024/api/entities?q=Nutrient"
    print(f"\nTesting filtering by search query: {query_url}")
    try:
        response = requests.get(query_url)
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data['entities'])} entities matching 'Nutrient'")
            print(json.dumps(data, indent=2))
    except Exception as e:
        print(f"Error: {e}")
    
    # Test filtering by minimum mentions
    mentions_url = "http://localhost:8024/api/entities?min_mentions=10"
    print(f"\nTesting filtering by minimum mentions: {mentions_url}")
    try:
        response = requests.get(mentions_url)
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data['entities'])} entities with at least 10 mentions")
            print(json.dumps(data, indent=2))
    except Exception as e:
        print(f"Error: {e}")
    
    # Test combined filtering
    combined_url = "http://localhost:8024/api/entities?type=Herb&min_mentions=10"
    print(f"\nTesting combined filtering: {combined_url}")
    try:
        response = requests.get(combined_url)
        if response.status_code == 200:
            data = response.json()
            print(f"Found {len(data['entities'])} Herb entities with at least 10 mentions")
            print(json.dumps(data, indent=2))
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api_filtering()
