"""
Metadata processing service for the Graphiti application.

This service handles metadata extraction from documents.
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

from database.database_service import get_falkordb_adapter
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)


class MetadataProcessor:
    """Service for processing metadata from documents."""

    def __init__(self):
        """Initialize the metadata processor."""
        pass

    async def extract_metadata_from_document(self, file_path: str, episode_id: str) -> Dict[str, Any]:
        """
        Extract metadata from a document and store it in the database.

        Args:
            file_path: Path to the document file
            episode_id: ID of the episode node

        Returns:
            Result dictionary with metadata extraction details
        """
        logger.info(f"Extracting metadata from document: {file_path}")

        try:
            file_path = Path(file_path)
            
            # Extract basic file metadata
            metadata = self._extract_file_metadata(file_path)
            
            # Extract PDF-specific metadata if it's a PDF
            if file_path.suffix.lower() == '.pdf':
                pdf_metadata = self._extract_pdf_metadata(file_path)
                metadata.update(pdf_metadata)

            # Store metadata in the database
            success = await self._store_metadata_in_database(metadata, episode_id)

            return {
                "success": success,
                "metadata_extracted": success,
                "metadata": metadata,
                "episode_id": episode_id
            }

        except Exception as e:
            logger.error(f"Error extracting metadata from document {file_path}: {e}", exc_info=True)
            return {
                "success": False,
                "metadata_extracted": False,
                "error": str(e),
                "episode_id": episode_id
            }

    def _extract_file_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract basic file metadata.

        Args:
            file_path: Path to the file

        Returns:
            Dictionary with file metadata
        """
        try:
            stat = file_path.stat()
            
            metadata = {
                "filename": file_path.name,
                "file_size": stat.st_size,
                "file_extension": file_path.suffix.lower(),
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "file_path": str(file_path)
            }

            logger.info(f"Extracted basic file metadata: {metadata}")
            return metadata

        except Exception as e:
            logger.error(f"Error extracting file metadata: {e}")
            return {}

    def _extract_pdf_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract PDF-specific metadata.

        Args:
            file_path: Path to the PDF file

        Returns:
            Dictionary with PDF metadata
        """
        metadata = {}

        try:
            # Try PyMuPDF first
            try:
                import fitz
                doc = fitz.open(str(file_path))
                
                pdf_metadata = doc.metadata
                metadata.update({
                    "pdf_title": pdf_metadata.get("title", ""),
                    "pdf_author": pdf_metadata.get("author", ""),
                    "pdf_subject": pdf_metadata.get("subject", ""),
                    "pdf_creator": pdf_metadata.get("creator", ""),
                    "pdf_producer": pdf_metadata.get("producer", ""),
                    "pdf_creation_date": pdf_metadata.get("creationDate", ""),
                    "pdf_modification_date": pdf_metadata.get("modDate", ""),
                    "page_count": doc.page_count
                })
                
                doc.close()
                logger.info(f"Extracted PDF metadata using PyMuPDF: {metadata}")
                return metadata

            except ImportError:
                logger.warning("PyMuPDF not available, trying PyPDF2")

            # Fall back to PyPDF2
            try:
                import PyPDF2
                with open(file_path, 'rb') as file:
                    reader = PyPDF2.PdfReader(file)
                    
                    if reader.metadata:
                        metadata.update({
                            "pdf_title": reader.metadata.get("/Title", ""),
                            "pdf_author": reader.metadata.get("/Author", ""),
                            "pdf_subject": reader.metadata.get("/Subject", ""),
                            "pdf_creator": reader.metadata.get("/Creator", ""),
                            "pdf_producer": reader.metadata.get("/Producer", ""),
                            "pdf_creation_date": str(reader.metadata.get("/CreationDate", "")),
                            "pdf_modification_date": str(reader.metadata.get("/ModDate", "")),
                        })
                    
                    metadata["page_count"] = len(reader.pages)
                    
                logger.info(f"Extracted PDF metadata using PyPDF2: {metadata}")
                return metadata

            except ImportError:
                logger.warning("PyPDF2 not available")

        except Exception as e:
            logger.error(f"Error extracting PDF metadata: {e}")

        return metadata

    async def _store_metadata_in_database(self, metadata: Dict[str, Any], episode_id: str) -> bool:
        """
        Store metadata in the database.

        Args:
            metadata: Metadata dictionary
            episode_id: ID of the episode node

        Returns:
            True if successful, False otherwise
        """
        try:
            adapter = await get_falkordb_adapter()

            # Escape special characters in metadata values
            escaped_metadata = {}
            for key, value in metadata.items():
                if isinstance(value, str):
                    escaped_metadata[key] = value.replace("'", "\\'").replace('"', '\\"')
                else:
                    escaped_metadata[key] = value

            # Create metadata properties for the episode
            set_clauses = []
            for key, value in escaped_metadata.items():
                if isinstance(value, str):
                    set_clauses.append(f"e.{key} = '{value}'")
                elif isinstance(value, (int, float)):
                    set_clauses.append(f"e.{key} = {value}")
                elif isinstance(value, bool):
                    set_clauses.append(f"e.{key} = {str(value).lower()}")

            if set_clauses:
                query = f"""
                MATCH (e:Episode {{uuid: '{episode_id}'}})
                SET {', '.join(set_clauses)}
                RETURN e.uuid
                """

                result = adapter.execute_cypher(query)
                
                if result and len(result) > 1 and len(result[1]) > 0:
                    logger.info(f"Successfully stored metadata for episode {episode_id}")
                    return True
                else:
                    logger.error(f"Failed to store metadata for episode {episode_id}")
                    return False
            else:
                logger.warning("No metadata to store")
                return False

        except Exception as e:
            logger.error(f"Error storing metadata in database: {e}", exc_info=True)
            return False

    def get_metadata_summary(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get a summary of the extracted metadata.

        Args:
            metadata: Metadata dictionary

        Returns:
            Summary dictionary
        """
        try:
            summary = {
                "filename": metadata.get("filename", "unknown"),
                "file_size_mb": round(metadata.get("file_size", 0) / (1024 * 1024), 2),
                "file_extension": metadata.get("file_extension", "unknown"),
                "page_count": metadata.get("page_count", 0),
                "has_pdf_metadata": bool(metadata.get("pdf_title") or metadata.get("pdf_author")),
                "pdf_title": metadata.get("pdf_title", ""),
                "pdf_author": metadata.get("pdf_author", "")
            }

            return summary

        except Exception as e:
            logger.error(f"Error creating metadata summary: {e}")
            return {
                "filename": "unknown",
                "file_size_mb": 0,
                "file_extension": "unknown",
                "page_count": 0,
                "has_pdf_metadata": False,
                "pdf_title": "",
                "pdf_author": ""
            }
