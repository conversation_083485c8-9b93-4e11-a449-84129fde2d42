
// Enhanced JavaScript for knowledge graph visualizations

// Function to initialize the enhanced visualizations
function initEnhancedVisualizations() {
    // Set up tabs
    setupVisualizationTabs();

    // Load taxonomy visualization
    loadTaxonomyVisualization();

    // Load relationship visualization
    loadRelationshipVisualization();

    // Set up advanced search
    setupAdvancedSearch();
}

// Function to set up visualization tabs
function setupVisualizationTabs() {
    const tabs = document.querySelectorAll('.visualization-tab');
    const contents = document.querySelectorAll('.visualization-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and contents
            tabs.forEach(t => t.classList.remove('active'));
            contents.forEach(c => c.classList.remove('active'));

            // Add active class to clicked tab and corresponding content
            tab.classList.add('active');
            const contentId = tab.getAttribute('data-content');
            document.getElementById(contentId).classList.add('active');
        });
    });
}

// Function to load taxonomy visualization
function loadTaxonomyVisualization() {
    fetch('/api/taxonomy')
        .then(response => response.json())
        .then(data => {
            renderTaxonomyTree(data);
        })
        .catch(error => {
            console.error('Error loading taxonomy:', error);
        });
}

// Function to render taxonomy tree
function renderTaxonomyTree(taxonomy) {
    const container = document.getElementById('taxonomy-visualization');

    // Clear container
    container.innerHTML = '';

    // Create taxonomy tree
    const tree = document.createElement('div');
    tree.className = 'taxonomy-tree';

    // Render each taxonomy node
    renderTaxonomyNodes(tree, taxonomy, 1);

    // Add tree to container
    container.appendChild(tree);
}

// Function to render taxonomy nodes recursively
function renderTaxonomyNodes(parent, nodes, level) {
    nodes.forEach(node => {
        // Create node element
        const nodeElement = document.createElement('div');
        nodeElement.className = `taxonomy-node level-${level}`;

        // Create node content
        nodeElement.innerHTML = `
            ${node.name}
            <span class="taxonomy-count">${node.entity_count || 0}</span>
        `;

        // Add node to parent
        parent.appendChild(nodeElement);

        // Render children recursively
        if (node.children && node.children.length > 0) {
            renderTaxonomyNodes(parent, node.children, level + 1);
        }
    });
}

// Function to load relationship visualization
function loadRelationshipVisualization() {
    fetch('/api/relationships')
        .then(response => response.json())
        .then(data => {
            renderRelationships(data);
        })
        .catch(error => {
            console.error('Error loading relationships:', error);
        });
}

// Function to render relationships
function renderRelationships(relationships) {
    const container = document.getElementById('relationship-visualization');

    // Clear container
    container.innerHTML = '';

    // Group relationships by type
    const groupedRelationships = {};
    relationships.forEach(rel => {
        if (!groupedRelationships[rel.type]) {
            groupedRelationships[rel.type] = [];
        }
        groupedRelationships[rel.type].push(rel);
    });

    // Render each relationship type
    Object.keys(groupedRelationships).forEach(type => {
        // Create type header
        const typeHeader = document.createElement('h3');
        typeHeader.textContent = type;
        container.appendChild(typeHeader);

        // Render relationships of this type
        groupedRelationships[type].forEach(rel => {
            // Create relationship card
            const card = document.createElement('div');
            card.className = 'relationship-card';

            // Calculate confidence percentage
            const confidencePercent = Math.round((rel.confidence || 0.5) * 100);

            // Create card content
            card.innerHTML = `
                <div class="relationship-type">${rel.type}</div>
                <div class="relationship-entities">
                    <div class="entity-source">${rel.source}</div>
                    <div class="relationship-arrow">→</div>
                    <div class="entity-target">${rel.target}</div>
                </div>
                <div class="confidence-meter">
                    <div class="confidence-level" style="width: ${confidencePercent}%"></div>
                </div>
                <div class="confidence-text">Confidence: ${confidencePercent}%</div>
                ${rel.evidence ? `<div class="relationship-evidence">"${rel.evidence}"</div>` : ''}
            `;

            // Add card to container
            container.appendChild(card);
        });
    });
}

// Function to set up advanced search
function setupAdvancedSearch() {
    const searchForm = document.getElementById('advanced-search-form');
    if (!searchForm) return;

    searchForm.addEventListener('submit', (event) => {
        event.preventDefault();

        // Get form data
        const formData = new FormData(searchForm);
        const searchParams = {
            query: formData.get('query'),
            entity_type: formData.get('entity_type'),
            relationship_type: formData.get('relationship_type'),
            min_confidence: formData.get('min_confidence')
        };

        // Perform search
        performAdvancedSearch(searchParams);
    });
}

// Function to perform advanced search
function performAdvancedSearch(params) {
    // Convert params to query string
    const queryString = Object.keys(params)
        .filter(key => params[key])
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');

    // Fetch search results
    fetch(`/api/search?${queryString}`)
        .then(response => response.json())
        .then(data => {
            renderSearchResults(data);
        })
        .catch(error => {
            console.error('Error performing search:', error);
        });
}

// Function to render search results
function renderSearchResults(results) {
    const container = document.getElementById('search-results');
    if (!container) return;

    // Clear container
    container.innerHTML = '';

    // Create results header
    const header = document.createElement('h3');
    header.textContent = `Found ${results.length} results`;
    container.appendChild(header);

    // Render each result
    results.forEach(result => {
        // Create result card
        const card = document.createElement('div');
        card.className = 'search-result-card';

        // Create card content based on result type
        if (result.type === 'entity') {
            renderEntityResult(card, result);
        } else if (result.type === 'relationship') {
            renderRelationshipResult(card, result);
        }

        // Add card to container
        container.appendChild(card);
    });
}

// Function to render entity result
function renderEntityResult(card, entity) {
    card.innerHTML = `
        <h4>${entity.name}</h4>
        <div class="entity-type">${entity.entity_type}</div>
        <div class="entity-description">${entity.description || ''}</div>
        ${entity.attributes ? renderEntityAttributes(entity.attributes) : ''}
    `;
}

// Function to render relationship result
function renderRelationshipResult(card, relationship) {
    // Calculate confidence percentage
    const confidencePercent = Math.round((relationship.confidence || 0.5) * 100);

    card.innerHTML = `
        <div class="relationship-type">${relationship.type}</div>
        <div class="relationship-entities">
            <div class="entity-source">${relationship.source}</div>
            <div class="relationship-arrow">→</div>
            <div class="entity-target">${relationship.target}</div>
        </div>
        <div class="confidence-meter">
            <div class="confidence-level" style="width: ${confidencePercent}%"></div>
        </div>
        <div class="confidence-text">Confidence: ${confidencePercent}%</div>
        ${relationship.evidence ? `<div class="relationship-evidence">"${relationship.evidence}"</div>` : ''}
    `;
}

// Function to render entity attributes
function renderEntityAttributes(attributes) {
    let html = '<div class="entity-attributes">';

    Object.keys(attributes).forEach(key => {
        html += `
            <div class="attribute">
                <div class="attribute-name">${key}</div>
                <div class="attribute-value">${attributes[key]}</div>
            </div>
        `;
    });

    html += '</div>';
    return html;
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', initEnhancedVisualizations);
    