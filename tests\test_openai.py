"""
Simple script to test if the OpenAI API is working correctly with GPT-4.1-mini.
"""

import os
import sys
from dotenv import load_dotenv
import openai

# Load environment variables
load_dotenv()

# Get OpenAI API key
openai_api_key = os.environ.get('OPENAI_API_KEY')
qa_llm_model = os.environ.get('QA_LLM_MODEL', 'gpt-4.1-mini')

if not openai_api_key:
    print("Error: OpenAI API key not found in environment variables.")
    sys.exit(1)

print(f"Using OpenAI API key: {openai_api_key[:5]}...{openai_api_key[-4:]}")
print(f"Using model: {qa_llm_model}")

try:
    # Initialize OpenAI client
    client = openai.OpenAI(api_key=openai_api_key)
    
    # Test simple completion
    print("Sending test request to OpenAI API...")
    response = client.chat.completions.create(
        model=qa_llm_model,
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Tell me about antioxidants in 2-3 sentences."}
        ],
        temperature=0.3,
        max_tokens=100
    )
    
    print("\nResponse from OpenAI:")
    print(f"Model used: {response.model}")
    print(f"Answer: {response.choices[0].message.content}")
    print("\nAPI test successful!")
    
except Exception as e:
    print(f"\nError using OpenAI API: {e}")
    print("\nAPI test failed.")
