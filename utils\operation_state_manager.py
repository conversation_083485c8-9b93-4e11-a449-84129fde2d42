"""
Operation State Manager for persistent tracking of upload/processing operations.
Handles state persistence across page refreshes and application restarts.
"""

import json
import sqlite3
import threading
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

class OperationStateManager:
    """
    Manages persistent state for upload/processing operations.
    Uses SQLite for reliable persistence across application restarts.
    """
    
    def __init__(self, db_path: str = "data/operation_states.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
        self._lock = threading.Lock()
        self._init_database()
    
    def _init_database(self):
        """Initialize the SQLite database with required tables."""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS operation_states (
                    operation_id TEXT PRIMARY KEY,
                    filename TEXT NOT NULL,
                    status TEXT NOT NULL,
                    progress_percentage INTEGER DEFAULT 0,
                    current_step INTEGER DEFAULT 0,
                    total_steps INTEGER DEFAULT 7,
                    current_step_name TEXT DEFAULT 'Initializing',
                    start_time TEXT NOT NULL,
                    last_update_time TEXT NOT NULL,
                    completion_time TEXT,
                    error_message TEXT,
                    details TEXT,  -- JSON string
                    statistics TEXT,  -- JSON string
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_operation_status 
                ON operation_states(status)
            """)
            
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_operation_created 
                ON operation_states(created_at)
            """)
            
            conn.commit()
    
    def create_operation(self, operation_id: str, filename: str, 
                        total_steps: int = 7) -> bool:
        """
        Create a new operation record.
        
        Args:
            operation_id: Unique operation identifier
            filename: Name of the file being processed
            total_steps: Total number of processing steps
            
        Returns:
            True if created successfully, False if already exists
        """
        now = datetime.utcnow().isoformat()
        
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.execute("""
                        INSERT INTO operation_states (
                            operation_id, filename, status, progress_percentage,
                            current_step, total_steps, current_step_name,
                            start_time, last_update_time, details, statistics,
                            created_at, updated_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        operation_id, filename, 'processing', 0,
                        0, total_steps, 'Initializing',
                        now, now, '{}', '{}',
                        now, now
                    ))
                    conn.commit()
                    return True
            except sqlite3.IntegrityError:
                # Operation already exists
                return False
            except Exception as e:
                logger.error(f"Error creating operation {operation_id}: {e}")
                return False
    
    def update_operation(self, operation_id: str, **kwargs) -> bool:
        """
        Update an operation's state.
        
        Args:
            operation_id: Operation identifier
            **kwargs: Fields to update (status, progress_percentage, etc.)
            
        Returns:
            True if updated successfully, False otherwise
        """
        if not kwargs:
            return True
            
        # Prepare update fields
        update_fields = []
        values = []
        
        allowed_fields = {
            'status', 'progress_percentage', 'current_step', 'current_step_name',
            'completion_time', 'error_message', 'details', 'statistics'
        }
        
        for field, value in kwargs.items():
            if field in allowed_fields:
                update_fields.append(f"{field} = ?")
                if field in ('details', 'statistics') and isinstance(value, dict):
                    values.append(json.dumps(value))
                else:
                    values.append(value)
        
        if not update_fields:
            return True
        
        # Always update last_update_time and updated_at
        update_fields.extend(['last_update_time = ?', 'updated_at = ?'])
        now = datetime.utcnow().isoformat()
        values.extend([now, now])
        
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    query = f"""
                        UPDATE operation_states 
                        SET {', '.join(update_fields)}
                        WHERE operation_id = ?
                    """
                    values.append(operation_id)
                    
                    cursor = conn.execute(query, values)
                    conn.commit()
                    
                    return cursor.rowcount > 0
            except Exception as e:
                logger.error(f"Error updating operation {operation_id}: {e}")
                return False
    
    def get_operation(self, operation_id: str) -> Optional[Dict[str, Any]]:
        """
        Get operation state by ID.
        
        Args:
            operation_id: Operation identifier
            
        Returns:
            Operation data dictionary or None if not found
        """
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute("""
                        SELECT * FROM operation_states WHERE operation_id = ?
                    """, (operation_id,))
                    
                    row = cursor.fetchone()
                    if row:
                        data = dict(row)
                        # Parse JSON fields
                        if data['details']:
                            try:
                                data['details'] = json.loads(data['details'])
                            except json.JSONDecodeError:
                                data['details'] = {}
                        else:
                            data['details'] = {}
                            
                        if data['statistics']:
                            try:
                                data['statistics'] = json.loads(data['statistics'])
                            except json.JSONDecodeError:
                                data['statistics'] = {}
                        else:
                            data['statistics'] = {}
                            
                        return data
                    return None
            except Exception as e:
                logger.error(f"Error getting operation {operation_id}: {e}")
                return None
    
    def get_active_operations(self) -> List[Dict[str, Any]]:
        """
        Get all active (processing) operations.
        
        Returns:
            List of active operation data dictionaries
        """
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute("""
                        SELECT * FROM operation_states 
                        WHERE status = 'processing'
                        ORDER BY created_at DESC
                    """)
                    
                    operations = []
                    for row in cursor.fetchall():
                        data = dict(row)
                        # Parse JSON fields
                        try:
                            data['details'] = json.loads(data['details']) if data['details'] else {}
                            data['statistics'] = json.loads(data['statistics']) if data['statistics'] else {}
                        except json.JSONDecodeError:
                            data['details'] = {}
                            data['statistics'] = {}
                        operations.append(data)
                    
                    return operations
            except Exception as e:
                logger.error(f"Error getting active operations: {e}")
                return []
    
    def get_recent_operations(self, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get recent operations (all statuses).
        
        Args:
            limit: Maximum number of operations to return
            
        Returns:
            List of recent operation data dictionaries
        """
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    conn.row_factory = sqlite3.Row
                    cursor = conn.execute("""
                        SELECT * FROM operation_states 
                        ORDER BY created_at DESC
                        LIMIT ?
                    """, (limit,))
                    
                    operations = []
                    for row in cursor.fetchall():
                        data = dict(row)
                        # Parse JSON fields
                        try:
                            data['details'] = json.loads(data['details']) if data['details'] else {}
                            data['statistics'] = json.loads(data['statistics']) if data['statistics'] else {}
                        except json.JSONDecodeError:
                            data['details'] = {}
                            data['statistics'] = {}
                        operations.append(data)
                    
                    return operations
            except Exception as e:
                logger.error(f"Error getting recent operations: {e}")
                return []
    
    def cleanup_old_operations(self, days_old: int = 7) -> int:
        """
        Clean up operations older than specified days.
        
        Args:
            days_old: Number of days after which to delete operations
            
        Returns:
            Number of operations deleted
        """
        cutoff_date = (datetime.utcnow() - timedelta(days=days_old)).isoformat()
        
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("""
                        DELETE FROM operation_states 
                        WHERE created_at < ? AND status IN ('completed', 'failed', 'cancelled')
                    """, (cutoff_date,))
                    conn.commit()
                    
                    deleted_count = cursor.rowcount
                    logger.info(f"Cleaned up {deleted_count} old operations")
                    return deleted_count
            except Exception as e:
                logger.error(f"Error cleaning up old operations: {e}")
                return 0
    
    def delete_operation(self, operation_id: str) -> bool:
        """
        Delete a specific operation.
        
        Args:
            operation_id: Operation identifier
            
        Returns:
            True if deleted successfully, False otherwise
        """
        with self._lock:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("""
                        DELETE FROM operation_states WHERE operation_id = ?
                    """, (operation_id,))
                    conn.commit()
                    
                    return cursor.rowcount > 0
            except Exception as e:
                logger.error(f"Error deleting operation {operation_id}: {e}")
                return False

# Global instance
_operation_state_manager = None

def get_operation_state_manager() -> OperationStateManager:
    """Get the global operation state manager instance."""
    global _operation_state_manager
    if _operation_state_manager is None:
        _operation_state_manager = OperationStateManager()
    return _operation_state_manager
