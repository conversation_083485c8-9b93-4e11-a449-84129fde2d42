"""
Example demonstrating how to use Graphiti with Google Gemini
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone

from dotenv import load_dotenv
import google.generativeai as genai

# Add custom Gemini client implementation
class CustomGeminiClient:
    def __init__(self, api_key, model_name="gemini-1.5-flash"):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)

    async def generate_response(self, prompt):
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error generating response: {e}")
            return f"Error: {e}"

class CustomGeminiEmbedder:
    def __init__(self, api_key, model_name="embedding-001"):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)

    async def create_embedding(self, text):
        try:
            # Use the embedding API directly
            result = genai.embed_content(model=self.model_name, content=text)
            return result['embedding']
        except Exception as e:
            print(f"Error creating embedding: {e}")
            return []

def setup_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )

async def main():
    try:
        # Load environment variables
        load_dotenv()
        setup_logging()

        # Get Google API key
        google_api_key = os.environ.get('GOOGLE_API_KEY')
        if not google_api_key:
            print("No Google API key found in environment variables")
            return

        print(f"API key found: {google_api_key[:5]}...{google_api_key[-5:]}")

        # Initialize custom Gemini clients
        llm_client = CustomGeminiClient(api_key=google_api_key, model_name="gemini-1.5-flash")
        embedder = CustomGeminiEmbedder(api_key=google_api_key, model_name="embedding-001")

        # Test LLM generation
        prompt = "Explain what a knowledge graph is and how it can be used in AI applications."
        print("\nGenerating text with Gemini...")
        response = await llm_client.generate_response(prompt)
        print(f"Response: {response}")

        # Test embedding creation
        text = "This is a test sentence for embedding."
        print("\nCreating embeddings with Gemini...")
        embedding = await embedder.create_embedding(text)
        if embedding:
            print(f"Embedding dimension: {len(embedding)}")
            print(f"First 5 values: {embedding[:5]}")

        print("\nExample completed successfully!")
    except Exception as e:
        print(f"Error in main function: {e}")

if __name__ == "__main__":
    asyncio.run(main())
