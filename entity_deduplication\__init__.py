"""
Entity Deduplication Module for Graphiti.

This module provides functionality for deduplicating entities in the knowledge graph.
It can be used as a standalone utility or integrated into the document ingestion pipeline.
"""

from entity_deduplication.deduplicator import EntityDeduplicator
from entity_deduplication.models import DeduplicationResult, EntityMatch

__all__ = ['EntityDeduplicator', 'DeduplicationResult', 'EntityMatch']
