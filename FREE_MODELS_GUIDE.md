# Free OpenRouter Models Guide

## Overview

Your Graphiti knowledge graph system now has access to **63 free OpenRouter models** that you can use for Q&A without any cost. The system has been updated to prioritize and clearly label these free models in the UI.

## How to Access Free Models

1. **Navigate to Q&A Interface**: Go to http://127.0.0.1:9753/qa
2. **Model Selection**: Click the model dropdown in the top-right corner
3. **Free Models Section**: Look for the "🆓 OpenRouter Free Models" section at the top
4. **Model Status**: Each model shows a green "FREE" badge

## Available Free Models (Top Recommendations)

### 🚀 **Recommended for General Q&A**
- `meta-llama/llama-3.3-8b-instruct:free` - Excellent for general questions
- `deepseek/deepseek-r1-0528:free` - Great reasoning capabilities
- `microsoft/phi-4-reasoning:free` - Strong analytical skills

### 🧠 **Large Context Models**
- `moonshotai/kimi-dev-72b:free` - Very large model, good for complex queries
- `nousresearch/deephermes-3-mistral-24b-preview:free` - Good for detailed responses

### ⚡ **Fast Response Models**
- `mistralai/devstral-small:free` - Quick responses
- `google/gemma-3n-e4b-it:free` - Efficient processing

### 🔬 **Specialized Models**
- `sarvamai/sarvam-m:free` - Multilingual capabilities
- `deepseek/deepseek-r1-0528-qwen3-8b:free` - Code and reasoning

## Current System Status

✅ **System Working**: FastAPI application running on port 9753
✅ **Knowledge Base**: Contains cocoa research data and other documents
✅ **Free Models**: 63 models available without cost
✅ **Q&A Functional**: Successfully answering questions with citations
✅ **Error Handling**: Improved error messages and timeout handling

## Testing Results

Recent tests confirm:
- **Q&A Response**: Generated 2012-character detailed answer about cocoa benefits
- **Knowledge Retrieval**: Found 5 relevant facts about cocoa
- **Source Citations**: Identified 3 sources for answers
- **Model Access**: All 63 free models accessible

## Usage Tips

1. **Start with Free Models**: Always try free models first
2. **Model Selection**: Choose based on your query complexity:
   - Simple questions → `mistralai/devstral-small:free`
   - Complex analysis → `meta-llama/llama-3.3-8b-instruct:free`
   - Reasoning tasks → `microsoft/phi-4-reasoning:free`

3. **Settings Optimization**:
   - Temperature: 0.3-0.7 for factual answers
   - Context Limit: 5-10 for most questions
   - Max Tokens: 2048 for detailed responses

## Troubleshooting

### If Models Don't Load
1. Check internet connection
2. Verify OpenRouter API key in `.env` file
3. Refresh the page

### If Q&A Doesn't Work
1. Ensure a model is selected
2. Check that the question isn't empty
3. Wait for response (some models take 30-60 seconds)

### If No Knowledge Found
1. Verify documents are uploaded and processed
2. Try different question phrasing
3. Check entity extraction completed

## Next Steps

The system is now fully functional with:
- ✅ Free models prioritized and labeled
- ✅ Better error handling
- ✅ Knowledge base responding to questions
- ✅ Comprehensive model selection

You can now ask questions about cocoa or any other topics in your knowledge base using any of the 63 free models available!
