"""
Example script for processing PDFs and adding them to Graphiti
"""

import os
import asyncio
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any

import PyPDF2  # pip install PyPDF2
from dotenv import load_dotenv

# Import our custom Gemini client (since the official one has import issues)
from examples.gemini_example_fixed import CustomGeminiClient, CustomGeminiEmbedder

# For a real implementation, you would use the official Graphiti imports
# from graphiti_core import Graphiti
# from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
# from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core import Graphiti
from graphiti_core.nodes import EpisodeType
from graphiti_core.utils.maintenance.graph_data_operations import clear_data

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path: str) -> str:
    """Extract text from a PDF file."""
    logger.info(f"Extracting text from {pdf_path}")
    
    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                text += page.extract_text() + "\n\n"
        
        logger.info(f"Extracted {len(text)} characters from {pdf_path}")
        return text
    except Exception as e:
        logger.error(f"Error extracting text from {pdf_path}: {e}")
        return ""

def chunk_text(text: str, chunk_size: int = 4000, overlap: int = 200) -> List[str]:
    """Split text into chunks of specified size with overlap."""
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = min(start + chunk_size, len(text))
        
        # Try to find a good breaking point (newline or period)
        if end < len(text):
            # Look for a newline
            newline_pos = text.rfind('\n', start, end)
            period_pos = text.rfind('. ', start, end)
            
            if newline_pos > start + chunk_size // 2:
                end = newline_pos + 1
            elif period_pos > start + chunk_size // 2:
                end = period_pos + 2
        
        chunks.append(text[start:end])
        start = end - overlap
    
    logger.info(f"Split text into {len(chunks)} chunks")
    return chunks

async def process_pdf_directory(directory_path: str, graphiti: Graphiti):
    """Process all PDFs in a directory and add them to Graphiti."""
    logger.info(f"Processing PDFs in {directory_path}")
    
    # Get all PDF files in the directory
    pdf_files = [f for f in os.listdir(directory_path) if f.lower().endswith('.pdf')]
    logger.info(f"Found {len(pdf_files)} PDF files")
    
    for pdf_file in pdf_files:
        pdf_path = os.path.join(directory_path, pdf_file)
        
        # Extract text from PDF
        text = extract_text_from_pdf(pdf_path)
        if not text:
            logger.warning(f"No text extracted from {pdf_file}, skipping")
            continue
        
        # Split into chunks if the text is long
        chunks = chunk_text(text)
        
        # Add each chunk as an episode
        for i, chunk in enumerate(chunks):
            chunk_name = f"{pdf_file} - Part {i+1}" if len(chunks) > 1 else pdf_file
            
            logger.info(f"Adding episode: {chunk_name}")
            try:
                episode_id = await graphiti.add_episode(
                    name=chunk_name,
                    episode_body=chunk,
                    source=EpisodeType.text,
                    reference_time=datetime.now(timezone.utc),
                    source_description=f"PDF Document: {pdf_file}",
                    metadata={
                        "file_name": pdf_file,
                        "chunk_index": i,
                        "total_chunks": len(chunks)
                    }
                )
                logger.info(f"Added episode with ID: {episode_id}")
            except Exception as e:
                logger.error(f"Error adding episode for {chunk_name}: {e}")

async def main():
    """Main function to process PDFs and add them to Graphiti."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        logger.error("No Google API key found in environment variables")
        return
    
    # Directory containing PDF files
    pdf_directory = "documents"  # Change this to your PDF directory
    
    # Create the directory if it doesn't exist
    os.makedirs(pdf_directory, exist_ok=True)
    
    try:
        logger.info("Initializing custom Gemini clients")
        
        # Initialize custom Gemini clients
        llm_client = CustomGeminiClient(api_key=google_api_key, model_name="models/gemini-1.5-flash")
        embedder = CustomGeminiEmbedder(api_key=google_api_key, model_name="models/embedding-001")
        
        # For a real implementation, you would use the official Graphiti clients
        # Initialize Graphiti with Gemini clients
        # graphiti = Graphiti(
        #     neo4j_uri,
        #     neo4j_user,
        #     neo4j_password,
        #     llm_client=GeminiClient(
        #         config=LLMConfig(
        #             api_key=google_api_key,
        #             model="models/gemini-1.5-flash"
        #         )
        #     ),
        #     embedder=GeminiEmbedder(
        #         config=GeminiEmbedderConfig(
        #             api_key=google_api_key,
        #             embedding_model="models/embedding-001"
        #         )
        #     )
        # )
        
        # For now, we'll use OpenAI since it's already configured
        from graphiti_core.llm_client.openai_client import OpenAIClient
        from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
        
        openai_api_key = os.environ.get('OPENAI_API_KEY')
        
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=OpenAIClient(
                config=LLMConfig(
                    api_key=openai_api_key,
                    model="gpt-4o"
                )
            ),
            embedder=OpenAIEmbedder(
                config=OpenAIEmbedderConfig(
                    api_key=openai_api_key,
                    embedding_model="text-embedding-3-small"
                )
            )
        )
        
        logger.info("Setting up indices and constraints")
        await graphiti.build_indices_and_constraints()
        
        # Check if there are any PDF files in the directory
        if not os.listdir(pdf_directory):
            logger.warning(f"No files found in {pdf_directory}. Please add PDF files to process.")
            return
        
        # Process PDFs
        await process_pdf_directory(pdf_directory, graphiti)
        
        logger.info("PDF processing completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'graphiti' in locals():
            await graphiti.driver.close()

if __name__ == "__main__":
    asyncio.run(main())
