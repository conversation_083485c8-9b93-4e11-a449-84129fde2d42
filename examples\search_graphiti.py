"""
Script to search the Graphiti knowledge graph
"""

import os
import sys
import asyncio
import logging
from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def search_graphiti(graphiti, query, limit=10):
    """Search the Graphiti knowledge graph."""
    logger.info(f"Searching for: {query}")
    
    try:
        # Perform the search
        results = await graphiti.search(query)
        
        logger.info(f"Found {len(results)} results")
        
        # Display the results
        for i, result in enumerate(results[:limit]):
            logger.info(f"\nResult {i+1}:")
            logger.info(f"Score: {result.score:.4f}")
            logger.info(f"Fact: {result.fact[:200]}...")
            
            # Get the episode that contains this fact
            episode = await graphiti.get_episode_by_id(result.episode_id)
            if episode:
                logger.info(f"From document: {episode.name}")
        
        return results
    except Exception as e:
        logger.error(f"Error searching Graphiti: {e}")
        return []

async def main():
    """Main function to search Graphiti."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    
    if not openai_api_key:
        logger.error("No OpenAI API key found in environment variables. Please add OPENAI_API_KEY to your .env file.")
        return
    
    # Check if a search query was specified
    if len(sys.argv) < 2:
        logger.error("Please specify a search query")
        logger.info("Usage: python search_graphiti.py <search_query>")
        return
    
    # Get the search query from command line arguments
    search_query = " ".join(sys.argv[1:])
    
    try:
        # Initialize Graphiti with OpenAI
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=OpenAIClient(
                config=LLMConfig(
                    api_key=openai_api_key,
                    model="gpt-4o"
                )
            ),
            embedder=OpenAIEmbedder(
                config=OpenAIEmbedderConfig(
                    api_key=openai_api_key,
                    embedding_model="text-embedding-3-small"
                )
            )
        )
        
        # Search Graphiti
        await search_graphiti(graphiti, search_query)
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'graphiti' in locals():
            await graphiti.driver.close()

if __name__ == "__main__":
    asyncio.run(main())
