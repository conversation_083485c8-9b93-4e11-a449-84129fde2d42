"""
Configuration settings for entity deduplication.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Deduplication thresholds
NAME_SIMILARITY_THRESHOLD = float(os.environ.get('ENTITY_NAME_SIMILARITY_THRESHOLD', '0.85'))
TYPE_MATCH_REQUIRED = os.environ.get('ENTITY_TYPE_MATCH_REQUIRED', 'true').lower() == 'true'
MIN_OVERALL_SIMILARITY = float(os.environ.get('ENTITY_MIN_OVERALL_SIMILARITY', '0.8'))

# Processing settings
BATCH_SIZE = int(os.environ.get('ENTITY_DEDUP_BATCH_SIZE', '100'))
FETCH_BATCH_SIZE = int(os.environ.get('ENTITY_DEDUP_FETCH_BATCH_SIZE', '5000'))
MAX_ENTITIES_PER_REQUEST = int(os.environ.get('ENTITY_DEDUP_MAX_PER_REQUEST', '0'))  # 0 means no limit

# LLM settings for semantic deduplication
USE_LLM_FOR_DEDUPLICATION = os.environ.get('USE_LLM_FOR_ENTITY_DEDUPLICATION', 'true').lower() == 'true'
LLM_PROVIDER = os.environ.get('ENTITY_DEDUP_LLM_PROVIDER', os.environ.get('ENTITY_EXTRACTION_PROVIDER', 'openrouter'))
LLM_MODEL = os.environ.get('ENTITY_DEDUP_LLM_MODEL', os.environ.get('ENTITY_EXTRACTION_MODEL', 'meta-llama/llama-4-maverick'))

# Logging settings
VERBOSE_LOGGING = os.environ.get('ENTITY_DEDUP_VERBOSE_LOGGING', 'false').lower() == 'true'
