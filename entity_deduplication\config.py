"""
Configuration settings for entity deduplication.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Legacy static thresholds (kept for backward compatibility)
NAME_SIMILARITY_THRESHOLD = float(os.environ.get('ENTITY_NAME_SIMILARITY_THRESHOLD', '0.85'))
TYPE_MATCH_REQUIRED = os.environ.get('ENTITY_TYPE_MATCH_REQUIRED', 'true').lower() == 'true'
MIN_OVERALL_SIMILARITY = float(os.environ.get('ENTITY_MIN_OVERALL_SIMILARITY', '0.8'))

# Dynamic threshold settings
USE_DYNAMIC_THRESHOLDS = os.environ.get('USE_DYNAMIC_ENTITY_THRESHOLDS', 'true').lower() == 'true'
DYNAMIC_THRESHOLD_LEARNING_RATE = float(os.environ.get('DYNAMIC_THRESHOLD_LEARNING_RATE', '0.1'))
DYNAMIC_THRESHOLD_MIN_SAMPLES = int(os.environ.get('DYNAMIC_THRESHOLD_MIN_SAMPLES', '10'))

# Multi-dimensional scoring settings
USE_MULTIDIMENSIONAL_SCORING = os.environ.get('USE_MULTIDIMENSIONAL_ENTITY_SCORING', 'true').lower() == 'true'
MULTIDIMENSIONAL_WEIGHTS = {
    'semantic': float(os.environ.get('MULTIDIM_SEMANTIC_WEIGHT', '0.35')),
    'syntactic': float(os.environ.get('MULTIDIM_SYNTACTIC_WEIGHT', '0.25')),
    'contextual': float(os.environ.get('MULTIDIM_CONTEXTUAL_WEIGHT', '0.20')),
    'attribute': float(os.environ.get('MULTIDIM_ATTRIBUTE_WEIGHT', '0.15')),
    'confidence': float(os.environ.get('MULTIDIM_CONFIDENCE_WEIGHT', '0.05'))
}

# Entity type-specific base thresholds
ENTITY_TYPE_THRESHOLDS = {
    'Person': {
        'name_similarity': float(os.environ.get('PERSON_NAME_SIMILARITY_THRESHOLD', '0.90')),
        'semantic_similarity': float(os.environ.get('PERSON_SEMANTIC_SIMILARITY_THRESHOLD', '0.85')),
        'confidence_weight': float(os.environ.get('PERSON_CONFIDENCE_WEIGHT', '1.2'))
    },
    'Organization': {
        'name_similarity': float(os.environ.get('ORG_NAME_SIMILARITY_THRESHOLD', '0.88')),
        'semantic_similarity': float(os.environ.get('ORG_SEMANTIC_SIMILARITY_THRESHOLD', '0.82')),
        'confidence_weight': float(os.environ.get('ORG_CONFIDENCE_WEIGHT', '1.1'))
    },
    'Location': {
        'name_similarity': float(os.environ.get('LOCATION_NAME_SIMILARITY_THRESHOLD', '0.92')),
        'semantic_similarity': float(os.environ.get('LOCATION_SEMANTIC_SIMILARITY_THRESHOLD', '0.88')),
        'confidence_weight': float(os.environ.get('LOCATION_CONFIDENCE_WEIGHT', '1.3'))
    },
    'default': {
        'name_similarity': NAME_SIMILARITY_THRESHOLD,
        'semantic_similarity': MIN_OVERALL_SIMILARITY,
        'confidence_weight': 1.0
    }
}

# Processing settings
BATCH_SIZE = int(os.environ.get('ENTITY_DEDUP_BATCH_SIZE', '100'))
FETCH_BATCH_SIZE = int(os.environ.get('ENTITY_DEDUP_FETCH_BATCH_SIZE', '5000'))
MAX_ENTITIES_PER_REQUEST = int(os.environ.get('ENTITY_DEDUP_MAX_PER_REQUEST', '0'))  # 0 means no limit

# LLM settings for semantic deduplication
USE_LLM_FOR_DEDUPLICATION = os.environ.get('USE_LLM_FOR_ENTITY_DEDUPLICATION', 'true').lower() == 'true'
LLM_PROVIDER = os.environ.get('ENTITY_DEDUP_LLM_PROVIDER', os.environ.get('ENTITY_EXTRACTION_PROVIDER', 'openrouter'))
LLM_MODEL = os.environ.get('ENTITY_DEDUP_LLM_MODEL', os.environ.get('ENTITY_EXTRACTION_MODEL', 'meta-llama/llama-4-maverick'))

# Logging settings
VERBOSE_LOGGING = os.environ.get('ENTITY_DEDUP_VERBOSE_LOGGING', 'false').lower() == 'true'
