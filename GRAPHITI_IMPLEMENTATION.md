# Graphiti Implementation

This is a custom implementation of a knowledge graph system using Graphiti and Neo4j.

## Features

- **PDF Document Processing**: Processes PDF documents into a knowledge graph with smart chunking
- **Conversational Interface**: Chat with <PERSON>, a specialized AI assistant for natural medicine
- **Vector Search**: Semantic search capabilities for finding relevant information
- **Entity Extraction**: Identifies entities and relationships in text

## Configuration

The system uses environment variables for configuration. See the `.env` file for details:

```
# Neo4j Database Configuration
NEO4J_URI=bolt://localhost:7689
NEO4J_USER=neo4j
NEO4J_PASSWORD=Triathlon16!
NEO4J_PORT=7689

# OpenAI API Configuration
OPENAI_API_KEY=your-api-key-here

# LLM Models Configuration
# Primary LLM for question answering (Avery assistant)
QA_LLM_PROVIDER=openai
QA_LLM_MODEL=gpt-4o

# Embedding Models Configuration
# Used for semantic search and vector similarity
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-small

# Alternative LLM Providers
# Google Gemini API key (alternative LLM and embeddings)
GOOGLE_API_KEY=your-google-api-key-here

# Mistral API key (alternative LLM and OCR)
MISTRAL_API_KEY=your-mistral-api-key-here
```

## Components

1. **PDF Processing**:
   - `pdf_to_knowledge_graph.py`: Processes PDFs and adds them to the knowledge graph
   - `batch_process_pdfs.py`: Batch processes multiple PDF files

2. **Web Interface**:
   - `web_interface.py`: FastAPI web interface for interacting with the knowledge graph
   - `static/conversation.js`: Handles conversation memory and UI

3. **Knowledge Graph**:
   - Uses Neo4j as the backend database
   - Stores documents as Episode nodes with Fact nodes for chunks
   - Maintains relationships between entities

## Chunking Strategy

The system uses a sophisticated chunking strategy:
- Default chunk size: 1200 characters
- Default overlap: 50 characters
- Smart boundary detection for natural text breaks
- Preserves document structure in Neo4j

## AI Assistant (Avery)

Avery is a specialized AI assistant configured for:
- Natural Medicine
- Herbal Medicine
- Nutritional Medicine
- Integrative Medicine
- Pathology
- Psychology
- Dietetics

## Next Steps

1. **Enhance entity extraction** with domain-specific types
2. **Optimize vector database** with fine-tuned thresholds
3. **Refine UI** with authentication and saved conversations

## Usage

1. Start the Neo4j database
2. Run the web interface: `python web_interface.py`
3. Access the interface at http://localhost:8023
4. Process PDFs using: `python pdf_to_knowledge_graph.py process <pdf_file>`
5. Batch process PDFs: `python batch_process_pdfs.py <directory>`

## Search and Question Answering

The system supports multiple search methods:
- Semantic search using vector embeddings
- Hybrid search combining vector similarity and keyword matching
- Question answering with conversation memory
