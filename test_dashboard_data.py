#!/usr/bin/env python3
"""
Test script to verify dashboard data is working correctly.
"""

import requests
import time

def test_dashboard_endpoints():
    """Test the endpoints used by the dashboard"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Dashboard Data Endpoints")
    print("=" * 50)
    
    # Test the exact endpoints used by the dashboard
    endpoints = [
        ("/api/fast/graph-stats", "Graph Stats"),
        ("/api/fast/documents?limit=5", "Recent Documents"),
        ("/api/fast/entities?limit=5", "Top Entities"),
        ("/api/system-status", "System Status")
    ]
    
    for endpoint, name in endpoints:
        print(f"\n📡 Testing {name}: {endpoint}")
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if "graph-stats" in endpoint:
                    print(f"   📊 Episodes: {data.get('total_episodes', 0)}")
                    print(f"   📊 Entities: {data.get('total_entities', 0)}")
                    print(f"   📊 Relationships: {data.get('total_relationships', 0)}")
                    
                elif "documents" in endpoint:
                    docs = data.get('documents', [])
                    print(f"   📄 Documents returned: {len(docs)}")
                    if docs:
                        print(f"   📄 Sample: '{docs[0].get('name', 'N/A')}'")
                        print(f"   📄 Created: {docs[0].get('created_at', 'N/A')}")
                        
                elif "entities" in endpoint:
                    entities = data.get('entities', [])
                    print(f"   🏷️  Entities returned: {len(entities)}")
                    if entities:
                        print(f"   🏷️  Sample: '{entities[0].get('name', 'N/A')}' ({entities[0].get('type', 'N/A')})")
                        
                elif "system-status" in endpoint:
                    print(f"   ⚙️  Status: {data.get('status', 'unknown')}")
                    
            else:
                print(f"   ❌ Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    print(f"\n🎯 Dashboard Test Complete!")
    print(f"📍 View dashboard at: {base_url}")

if __name__ == "__main__":
    test_dashboard_endpoints()
