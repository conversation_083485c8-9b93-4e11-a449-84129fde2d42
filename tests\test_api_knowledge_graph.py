"""
Integration tests for the knowledge graph API endpoints.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

@patch("services.knowledge_graph_service.get_knowledge_graph")
def test_get_knowledge_graph_endpoint(mock_get_knowledge_graph, test_client):
    """
    Test the get knowledge graph endpoint.
    
    Args:
        mock_get_knowledge_graph: Mocked get_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the get_knowledge_graph function
    mock_get_knowledge_graph.return_value = {
        "nodes": [
            {
                "uuid": "node1",
                "type": "Person",
                "name": "Person 1",
                "properties": {"uuid": "node1", "type": "Person", "name": "Person 1"}
            },
            {
                "uuid": "node2",
                "type": "Organization",
                "name": "Organization 1",
                "properties": {"uuid": "node2", "type": "Organization", "name": "Organization 1"}
            }
        ],
        "relationships": [
            {
                "source": "node1",
                "target": "node2",
                "type": "WORKS_FOR",
                "properties": {"confidence": 0.9}
            }
        ]
    }
    
    # Make a request to the knowledge graph endpoint
    response = test_client.get("/api/knowledge-graph")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "nodes" in data
    assert "relationships" in data
    assert len(data["nodes"]) == 2
    assert len(data["relationships"]) == 1
    assert data["nodes"][0]["uuid"] == "node1"
    assert data["nodes"][1]["uuid"] == "node2"
    assert data["relationships"][0]["source"] == "node1"
    assert data["relationships"][0]["target"] == "node2"
    assert data["relationships"][0]["type"] == "WORKS_FOR"

@patch("services.knowledge_graph_service.get_knowledge_graph")
def test_get_knowledge_graph_endpoint_with_limit(mock_get_knowledge_graph, test_client):
    """
    Test the get knowledge graph endpoint with a limit.
    
    Args:
        mock_get_knowledge_graph: Mocked get_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the get_knowledge_graph function
    mock_get_knowledge_graph.return_value = {
        "nodes": [
            {
                "uuid": "node1",
                "type": "Person",
                "name": "Person 1",
                "properties": {"uuid": "node1", "type": "Person", "name": "Person 1"}
            }
        ],
        "relationships": []
    }
    
    # Make a request to the knowledge graph endpoint with a limit
    response = test_client.get("/api/knowledge-graph?limit=1")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "nodes" in data
    assert "relationships" in data
    assert len(data["nodes"]) == 1
    assert len(data["relationships"]) == 0
    
    # Check that the limit was passed to the get_knowledge_graph function
    mock_get_knowledge_graph.assert_called_once_with(1)

@patch("services.knowledge_graph_service.get_knowledge_graph")
def test_get_knowledge_graph_endpoint_error(mock_get_knowledge_graph, test_client):
    """
    Test the get knowledge graph endpoint with an error.
    
    Args:
        mock_get_knowledge_graph: Mocked get_knowledge_graph function
        test_client: FastAPI test client
    """
    # Mock the get_knowledge_graph function to raise an exception
    mock_get_knowledge_graph.side_effect = Exception("Test error")
    
    # Make a request to the knowledge graph endpoint
    response = test_client.get("/api/knowledge-graph")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting knowledge graph" in response.json()["detail"]

@patch("services.knowledge_graph_service.execute_graph_query")
def test_query_graph_endpoint(mock_execute_graph_query, test_client):
    """
    Test the query graph endpoint.
    
    Args:
        mock_execute_graph_query: Mocked execute_graph_query function
        test_client: FastAPI test client
    """
    # Mock the execute_graph_query function
    mock_execute_graph_query.return_value = {
        "headers": ["n.name", "n.type"],
        "data": [["Person 1", "Person"], ["Organization 1", "Organization"]],
        "summary": {"query": "MATCH (n) RETURN n.name, n.type LIMIT 10"}
    }
    
    # Make a request to the query graph endpoint
    response = test_client.post(
        "/api/knowledge-graph/query",
        json={
            "query": "MATCH (n) RETURN n.name, n.type LIMIT 10",
            "params": None
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "headers" in data
    assert "data" in data
    assert "summary" in data
    assert len(data["headers"]) == 2
    assert len(data["data"]) == 2
    assert data["headers"][0] == "n.name"
    assert data["headers"][1] == "n.type"
    assert data["data"][0][0] == "Person 1"
    assert data["data"][0][1] == "Person"
    assert data["data"][1][0] == "Organization 1"
    assert data["data"][1][1] == "Organization"

@patch("services.knowledge_graph_service.execute_graph_query")
def test_query_graph_endpoint_with_params(mock_execute_graph_query, test_client):
    """
    Test the query graph endpoint with parameters.
    
    Args:
        mock_execute_graph_query: Mocked execute_graph_query function
        test_client: FastAPI test client
    """
    # Mock the execute_graph_query function
    mock_execute_graph_query.return_value = {
        "headers": ["n.name", "n.type"],
        "data": [["Person 1", "Person"]],
        "summary": {"query": "MATCH (n) WHERE n.type = $type RETURN n.name, n.type", "params": {"type": "Person"}}
    }
    
    # Make a request to the query graph endpoint with parameters
    response = test_client.post(
        "/api/knowledge-graph/query",
        json={
            "query": "MATCH (n) WHERE n.type = $type RETURN n.name, n.type",
            "params": {"type": "Person"}
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "headers" in data
    assert "data" in data
    assert "summary" in data
    assert len(data["headers"]) == 2
    assert len(data["data"]) == 1
    assert data["headers"][0] == "n.name"
    assert data["headers"][1] == "n.type"
    assert data["data"][0][0] == "Person 1"
    assert data["data"][0][1] == "Person"
    
    # Check that the parameters were passed to the execute_graph_query function
    mock_execute_graph_query.assert_called_once_with(
        "MATCH (n) WHERE n.type = $type RETURN n.name, n.type",
        {"type": "Person"}
    )

@patch("services.knowledge_graph_service.execute_graph_query")
def test_query_graph_endpoint_error(mock_execute_graph_query, test_client):
    """
    Test the query graph endpoint with an error.
    
    Args:
        mock_execute_graph_query: Mocked execute_graph_query function
        test_client: FastAPI test client
    """
    # Mock the execute_graph_query function to raise an exception
    mock_execute_graph_query.side_effect = Exception("Test error")
    
    # Make a request to the query graph endpoint
    response = test_client.post(
        "/api/knowledge-graph/query",
        json={
            "query": "MATCH (n) RETURN n.name, n.type LIMIT 10",
            "params": None
        }
    )
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error executing graph query" in response.json()["detail"]
