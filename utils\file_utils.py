"""
File handling utilities for the Graphiti application.
"""

import os
import uuid
import shutil
import mimetypes
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Define supported file types
SUPPORTED_EXTENSIONS = {
    # Document formats
    '.pdf': 'application/pdf',
    '.txt': 'text/plain',
    '.md': 'text/markdown',
    '.markdown': 'text/markdown',
    '.rtf': 'application/rtf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.odt': 'application/vnd.oasis.opendocument.text',
    '.pages': 'application/x-iwork-pages-sffpages',
    '.wpd': 'application/vnd.wordperfect',
    
    # Web formats
    '.html': 'text/html',
    '.htm': 'text/html',
    '.xml': 'application/xml',
    
    # Spreadsheet formats
    '.csv': 'text/csv',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ods': 'application/vnd.oasis.opendocument.spreadsheet',
    '.numbers': 'application/x-iwork-numbers-sffnumbers',
    '.tsv': 'text/tab-separated-values',
    
    # Presentation formats
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    '.odp': 'application/vnd.oasis.opendocument.presentation',
    '.key': 'application/x-iwork-keynote-sffkey',

    # Note-taking formats
    '.one': 'application/onenote',
    
    # E-book formats
    '.epub': 'application/epub+zip',
    '.mobi': 'application/x-mobipocket-ebook',
    '.azw': 'application/vnd.amazon.ebook',
    '.azw3': 'application/vnd.amazon.ebook',
    
    # Image formats
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.tiff': 'image/tiff',
    '.tif': 'image/tiff',
    '.bmp': 'image/bmp',
    '.webp': 'image/webp',
    '.svg': 'image/svg+xml',

    # Data formats
    '.json': 'application/json',
    '.yaml': 'application/x-yaml',
    '.yml': 'application/x-yaml',
}

def is_supported_file(filename: str) -> bool:
    """
    Check if a file is supported based on its extension.
    
    Args:
        filename: Name of the file
        
    Returns:
        True if the file is supported, False otherwise
    """
    ext = os.path.splitext(filename.lower())[1]
    return ext in SUPPORTED_EXTENSIONS

def get_file_type(filename: str) -> str:
    """
    Get the MIME type of a file based on its extension.
    
    Args:
        filename: Name of the file
        
    Returns:
        MIME type of the file
    """
    ext = os.path.splitext(filename.lower())[1]
    return SUPPORTED_EXTENSIONS.get(ext, 'application/octet-stream')

def get_file_category(filename: str) -> str:
    """
    Get the category of a file based on its MIME type.
    
    Args:
        filename: Name of the file
        
    Returns:
        Category of the file (document, image, etc.)
    """
    mime_type = get_file_type(filename)
    
    if mime_type.startswith('application/pdf'):
        return 'pdf'
    elif mime_type.startswith('text/'):
        return 'text'
    elif mime_type.startswith('application/vnd.openxmlformats-officedocument.wordprocessingml'):
        return 'word'
    elif mime_type.startswith('application/msword'):
        return 'word'
    elif mime_type.startswith('application/vnd.oasis.opendocument.text'):
        return 'word'
    elif mime_type.startswith('application/x-iwork-pages'):
        return 'word'
    elif mime_type.startswith('application/vnd.wordperfect'):
        return 'word'
    elif mime_type.startswith('text/html') or mime_type.startswith('application/xml'):
        return 'html'
    elif mime_type.startswith('application/epub') or mime_type.startswith('application/x-mobipocket') or mime_type.startswith('application/vnd.amazon'):
        return 'ebook'
    elif mime_type.startswith('text/csv') or mime_type.startswith('application/vnd.ms-excel') or mime_type.startswith('application/vnd.openxmlformats-officedocument.spreadsheetml') or mime_type.startswith('application/vnd.oasis.opendocument.spreadsheet') or mime_type.startswith('application/x-iwork-numbers') or mime_type.startswith('text/tab-separated'):
        return 'spreadsheet'
    elif mime_type.startswith('application/vnd.ms-powerpoint') or mime_type.startswith('application/vnd.openxmlformats-officedocument.presentationml') or mime_type.startswith('application/vnd.oasis.opendocument.presentation') or mime_type.startswith('application/x-iwork-keynote'):
        return 'presentation'
    elif mime_type.startswith('application/onenote'):
        return 'onenote'
    elif mime_type.startswith('image/'):
        return 'image'
    elif mime_type.startswith('application/json') or mime_type.startswith('application/x-yaml'):
        return 'data'
    else:
        return 'other'

def generate_unique_filename(original_filename: str) -> str:
    """
    Generate a unique filename by prepending a UUID.
    
    Args:
        original_filename: Original filename
        
    Returns:
        Unique filename
    """
    file_id = str(uuid.uuid4())
    return f"{file_id}_{original_filename}"

def save_uploaded_file(file_content: bytes, filename: str, directory: Union[str, Path]) -> Path:
    """
    Save an uploaded file to the specified directory.
    
    Args:
        file_content: Content of the file
        filename: Name of the file
        directory: Directory to save the file to
        
    Returns:
        Path to the saved file
    """
    # Ensure the directory exists
    os.makedirs(directory, exist_ok=True)
    
    # Generate a unique filename
    unique_filename = generate_unique_filename(filename)
    
    # Create the full path
    file_path = Path(directory) / unique_filename
    
    # Save the file
    with open(file_path, 'wb') as f:
        f.write(file_content)
    
    logger.info(f"Saved file: {file_path}")
    return file_path

def delete_file(file_path: Union[str, Path]) -> bool:
    """
    Delete a file.
    
    Args:
        file_path: Path to the file
        
    Returns:
        True if the file was deleted, False otherwise
    """
    try:
        os.remove(file_path)
        logger.info(f"Deleted file: {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {e}")
        return False

def copy_file(source_path: Union[str, Path], dest_path: Union[str, Path]) -> bool:
    """
    Copy a file.
    
    Args:
        source_path: Path to the source file
        dest_path: Path to the destination file
        
    Returns:
        True if the file was copied, False otherwise
    """
    try:
        # Ensure the destination directory exists
        os.makedirs(os.path.dirname(dest_path), exist_ok=True)
        
        shutil.copy2(source_path, dest_path)
        logger.info(f"Copied file from {source_path} to {dest_path}")
        return True
    except Exception as e:
        logger.error(f"Error copying file from {source_path} to {dest_path}: {e}")
        return False
