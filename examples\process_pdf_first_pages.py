"""
<PERSON><PERSON><PERSON> to process just the first few pages of a PDF file and add them to Graphiti
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone
import PyPDF2

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.nodes import EpisodeType

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_text_from_pdf_pages(pdf_path, max_pages=5):
    """Extract text from the first few pages of a PDF file using PyPDF2."""
    logger.info(f"Extracting text from the first {max_pages} pages of {pdf_path}")

    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            num_pages = min(len(reader.pages), max_pages)

            for page_num in range(num_pages):
                page = reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += f"--- Page {page_num + 1} ---\n{page_text}\n\n"

        logger.info(f"Extracted {len(text)} characters from {num_pages} pages of {pdf_path}")
        return text
    except Exception as e:
        logger.error(f"Error extracting text from {pdf_path}: {e}")
        return ""

async def process_pdf_and_add_to_graphiti(file_path, graphiti, max_pages=5):
    """Process the first few pages of a PDF file and add it to Graphiti."""
    logger.info(f"Processing PDF: {file_path}")

    # Extract text from PDF
    text = extract_text_from_pdf_pages(file_path, max_pages)
    if not text:
        logger.warning(f"No text extracted from {file_path}, skipping")
        return

    # Get document title from filename
    document_title = os.path.basename(file_path)

    # Add the document as an episode
    document_episode_id = await graphiti.add_episode(
        name=document_title,
        episode_body=f"Document: {document_title}\nSource: {file_path}\nFirst {max_pages} pages only",
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        source_description=f"PDF Document: {file_path} (first {max_pages} pages)"
    )

    logger.info(f"Added document episode with ID: {document_episode_id}")

    # Add the text as a fact connected to the document episode
    logger.info(f"Adding fact for document text")
    fact_id = await graphiti.add_fact(
        fact_body=text,
        episode_id=document_episode_id
    )

    logger.info(f"Added fact with ID: {fact_id}")
    logger.info(f"Successfully processed document: {file_path}")
    return document_episode_id

async def main():
    """Main function to process a single PDF and add it to Graphiti."""
    # Load environment variables
    load_dotenv()

    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')

    if not openai_api_key:
        logger.error("No OpenAI API key found in environment variables. Please add OPENAI_API_KEY to your .env file.")
        return

    # Check if a PDF file was specified
    if len(sys.argv) < 2:
        logger.error("Please specify a PDF file to process")
        logger.info("Usage: python process_pdf_first_pages.py <pdf_file> [max_pages]")
        return

    pdf_file = sys.argv[1]
    if not os.path.exists(pdf_file):
        logger.error(f"PDF file not found: {pdf_file}")
        return

    # Get max pages from command line arguments
    max_pages = 5
    if len(sys.argv) >= 3:
        try:
            max_pages = int(sys.argv[2])
        except ValueError:
            logger.warning(f"Invalid max_pages value: {sys.argv[2]}. Using default: 5")

    try:
        # Initialize Graphiti with OpenAI
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=OpenAIClient(
                config=LLMConfig(
                    api_key=openai_api_key,
                    model="gpt-4o"
                )
            ),
            embedder=OpenAIEmbedder(
                config=OpenAIEmbedderConfig(
                    api_key=openai_api_key,
                    embedding_model="text-embedding-3-small"
                )
            )
        )

        logger.info("Setting up indices and constraints")
        await graphiti.build_indices_and_constraints()

        # Process the PDF
        document_id = await process_pdf_and_add_to_graphiti(pdf_file, graphiti, max_pages)

        if document_id:
            logger.info(f"PDF processing completed successfully! Document ID: {document_id}")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'graphiti' in locals():
            await graphiti.driver.close()

if __name__ == "__main__":
    asyncio.run(main())
