#!/usr/bin/env python3
"""
Test script for entity deduplication system
"""

import asyncio
import logging
import time
from database.falkordb_adapter import FalkorDBAdapter
from entity_deduplication.deduplicator import EntityDeduplicator
from utils.auto_deduplication import deduplicate_all_entities

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_entity_deduplication():
    """Test the entity deduplication system"""
    
    print("🔍 Testing Entity Deduplication System")
    print("=" * 50)
    
    # Initialize database
    db = FalkorDBAdapter()
    
    # Get initial entity count
    print("📊 Getting initial entity statistics...")
    entities = db.get_nodes_by_label("Entity", limit=10000)
    initial_count = len(entities)
    print(f"Initial entity count: {initial_count}")
    
    # Show entity type breakdown before deduplication
    entity_types = {}
    for entity in entities:
        # Parse entity properties
        entity_props = {}
        if isinstance(entity, list) and len(entity) >= 3:
            properties = entity[2][1]
            for prop in properties:
                if len(prop) >= 2:
                    entity_props[prop[0]] = prop[1]
        
        entity_type = entity_props.get('type', 'Unknown')
        entity_types[entity_type] = entity_types.get(entity_type, 0) + 1
    
    print("\n📈 Entity types before deduplication:")
    for entity_type, count in sorted(entity_types.items()):
        print(f"  {entity_type}: {count}")
    
    # Run entity deduplication
    print(f"\n🚀 Starting entity deduplication...")
    start_time = time.time()
    
    try:
        success = await deduplicate_all_entities()
        
        if success:
            print("✅ Entity deduplication completed successfully!")
        else:
            print("❌ Entity deduplication failed!")
            return
            
    except Exception as e:
        print(f"❌ Error during deduplication: {e}")
        return
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    # Get final entity count
    print("\n📊 Getting final entity statistics...")
    final_entities = db.get_nodes_by_label("Entity", limit=10000)
    final_count = len(final_entities)
    
    # Calculate results
    entities_removed = initial_count - final_count
    reduction_percentage = (entities_removed / initial_count) * 100 if initial_count > 0 else 0
    
    print(f"\n🎯 Deduplication Results:")
    print(f"  Initial entities: {initial_count}")
    print(f"  Final entities: {final_count}")
    print(f"  Entities removed: {entities_removed}")
    print(f"  Reduction: {reduction_percentage:.1f}%")
    print(f"  Processing time: {processing_time:.1f} seconds")
    
    # Show entity type breakdown after deduplication
    final_entity_types = {}
    for entity in final_entities:
        # Parse entity properties
        entity_props = {}
        if isinstance(entity, list) and len(entity) >= 3:
            properties = entity[2][1]
            for prop in properties:
                if len(prop) >= 2:
                    entity_props[prop[0]] = prop[1]
        
        entity_type = entity_props.get('type', 'Unknown')
        final_entity_types[entity_type] = final_entity_types.get(entity_type, 0) + 1
    
    print("\n📈 Entity types after deduplication:")
    for entity_type, count in sorted(final_entity_types.items()):
        before_count = entity_types.get(entity_type, 0)
        reduction = before_count - count
        print(f"  {entity_type}: {count} (removed {reduction})")

async def test_specific_entity_type(entity_type: str):
    """Test deduplication for a specific entity type"""
    
    print(f"🔍 Testing Entity Deduplication for type: {entity_type}")
    print("=" * 50)
    
    # Initialize deduplicator
    deduplicator = EntityDeduplicator()
    
    try:
        # Run deduplication for specific type
        result = await deduplicator.deduplicate_entities(
            entity_type=entity_type,
            merge=True
        )
        
        print(f"✅ Deduplication completed for {entity_type}!")
        print(f"  Total entities processed: {result.total_entities}")
        print(f"  Duplicate groups found: {result.duplicate_groups}")
        print(f"  Total duplicate matches: {result.total_duplicates}")
        print(f"  Entities merged: {result.merged_entities}")
        
    except Exception as e:
        print(f"❌ Error during deduplication for {entity_type}: {e}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        # Test specific entity type
        entity_type = sys.argv[1]
        asyncio.run(test_specific_entity_type(entity_type))
    else:
        # Test all entities
        asyncio.run(test_entity_deduplication())
