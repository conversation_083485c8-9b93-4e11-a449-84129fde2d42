# Knowledge Graph Project

This project demonstrates how to build a knowledge graph from PDF documents, extract entities and relationships, and provide a web interface for exploration.

## Overview

The system consists of several components:
1. PDF processing and chunking
2. Knowledge graph creation in Neo4j
3. Entity and relationship extraction
4. Web interface for search, Q&A, and visualization

## Components

### PDF Processing

- `pdf_to_knowledge_graph.py`: Main script for processing PDFs and adding them to Neo4j
- `batch_process_pdfs.py`: Script for batch processing multiple PDFs
- `cleanup_knowledge_graph.py`: Script for cleaning up the knowledge graph

### Entity Extraction

- `entity_extraction.py`: Script for extracting entities and relationships from facts
- `update_entity_uuids.py`: Script for updating entities with UUIDs

### Web Interface

- `web_interface.py`: Web interface for exploring the knowledge graph
- `static/graph_visualization.js`: JavaScript for visualizing the knowledge graph

## Usage

### Processing PDFs

To process a PDF and add it to the knowledge graph:

```bash
python pdf_to_knowledge_graph.py process "path/to/your/document.pdf" [max_pages] [chunk_size] [overlap]
```

To batch process multiple PDFs:

```bash
python batch_process_pdfs.py "directory" [pattern] [max_pages] [chunk_size] [overlap]
```

### Extracting Entities

To extract entities from facts:

```bash
python entity_extraction.py extract-entities [batch_size] [max_batches]
```

To extract relationships between entities:

```bash
python entity_extraction.py extract-relationships [batch_size] [max_batches]
```

To view entity statistics:

```bash
python entity_extraction.py stats
```

### Running the Web Interface

To run the web interface:

```bash
python web_interface.py
```

Then open a browser and navigate to http://localhost:8023

## Knowledge Graph Structure

The knowledge graph consists of:
- **Episode nodes**: Represent the source documents
- **Fact nodes**: Contain chunks of text from the documents
- **Entity nodes**: Represent entities extracted from the facts
- **CONTAINS relationships**: Connect Episodes to their Facts
- **MENTIONS relationships**: Connect Facts to the Entities they mention
- **RELATED_TO relationships**: Connect related Entities

## Next Steps

Future enhancements could include:
1. Improving entity extraction with domain-specific types, disambiguation, and attributes
2. Enhancing relationship extraction with specific types, temporal/causal relationships, and confidence scores
3. Adding hierarchical categorization with IS_A and PART_OF relationships
4. Integrating entities with search and Q&A

## Requirements

- Python 3.8+
- Neo4j 5.x+
- OpenAI API key (for entity extraction and Q&A)
- Required Python packages: neo4j, openai, fastapi, uvicorn, jinja2, PyPDF2, dotenv

## Setup

1. Install required packages:
```bash
pip install neo4j openai fastapi uvicorn jinja2 PyPDF2 python-dotenv
```

2. Set up environment variables:
```
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=your_password
OPENAI_API_KEY=your_openai_api_key
```

3. Start Neo4j database

4. Run the web interface:
```bash
python web_interface.py
```
