#!/usr/bin/env python3
"""
Test the simplified OneNote processor that converts to PDF and uses standard processing.
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_simplified_onenote_processor():
    """Test the simplified OneNote processor."""
    
    print("🔄 TESTING SIMPLIFIED ONENOTE PROCESSOR")
    print("=" * 60)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Testing with: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        
        # Initialize processor
        processor = OneNoteProcessor()
        print(f"✅ Initialized: {processor.processor_name}")
        
        # Check processor info
        info = processor.get_processor_info()
        print(f"📋 Features: {len(info['features'])} available")
        print(f"   OneNote library: {info['onenote_available']}")
        print(f"   PDF creation: {info['pdf_creation_available']}")
        
        # Test the processing
        print(f"\n🚀 Starting OneNote → PDF → Standard Processing...")
        result = await processor.extract_text(str(onenote_file))
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            text = result.get('text', '')
            metadata = result.get('metadata', {})
            
            print(f"✅ Text extracted: {len(text):,} characters")
            print(f"✅ Word count: {metadata.get('word_count', 0):,}")
            print(f"✅ Processing method: {metadata.get('conversion_method', 'unknown')}")
            print(f"✅ Original format: {metadata.get('original_format', 'unknown')}")
            
            # Show metadata details
            print(f"\n📋 METADATA DETAILS:")
            for key, value in metadata.items():
                if key not in ['text']:  # Skip large text field
                    print(f"  {key}: {value}")
            
            # Show first part of extracted text
            print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
            print("=" * 60)
            print(text[:1000])
            if len(text) > 1000:
                print(f"\n... [Content continues for {len(text)-1000:,} more characters]")
            print("=" * 60)
            
            # Save full content for inspection
            output_file = Path("simplified_onenote_processing_result.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== SIMPLIFIED ONENOTE PROCESSING RESULT ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Processing method: {metadata.get('conversion_method', 'unknown')}\n")
                f.write(f"Original format: {metadata.get('original_format', 'unknown')}\n")
                f.write(f"Text length: {len(text):,} characters\n")
                f.write(f"Word count: {metadata.get('word_count', 0):,}\n\n")
                f.write("=== METADATA ===\n\n")
                for key, value in metadata.items():
                    f.write(f"{key}: {value}\n")
                f.write("\n=== FULL EXTRACTED TEXT ===\n\n")
                f.write(text)
            
            print(f"💾 Saved full processing result to: {output_file}")
            
            # Compare with previous attempts
            print(f"\n🎉 COMPARISON WITH PREVIOUS ATTEMPTS:")
            print(f"Previous OneNote extraction: ~1,880 characters (metadata only)")
            print(f"Simplified processor: {len(text):,} characters")
            
            if len(text) > 1880:
                improvement = len(text) / 1880
                print(f"Improvement factor: {improvement:.1f}x more content!")
                
                if len(text) > 10000:
                    print("🎉 EXCELLENT! This is substantial content extraction!")
                elif len(text) > 5000:
                    print("✅ GOOD! Significant improvement in content extraction!")
                else:
                    print("✅ BETTER! Some improvement in content extraction!")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ Processing failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Error during simplified processing test: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_pdf_conversion_only():
    """Test just the PDF conversion part."""
    
    print("\n🔄 TESTING PDF CONVERSION ONLY")
    print("=" * 60)
    
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found")
        return False
    
    onenote_file = onenote_files[-1]
    print(f"🔍 Converting: {onenote_file.name}")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        
        processor = OneNoteProcessor()
        
        # Test just the PDF conversion
        pdf_path = await processor._convert_onenote_to_pdf(Path(onenote_file))
        
        if pdf_path and pdf_path.exists():
            pdf_size = pdf_path.stat().st_size
            print(f"✅ PDF created: {pdf_path.name}")
            print(f"📁 PDF size: {pdf_size:,} bytes")
            
            # Save the PDF for inspection
            saved_pdf = Path("onenote_converted.pdf")
            pdf_path.rename(saved_pdf)
            print(f"💾 Saved PDF as: {saved_pdf}")
            
            return True
        else:
            print("❌ PDF conversion failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing PDF conversion: {e}")
        return False


def main():
    """Run simplified OneNote processor tests."""
    print("🔄 SIMPLIFIED ONENOTE PROCESSOR TESTS")
    print("=" * 60)
    
    async def run_tests():
        # Test PDF conversion first
        pdf_success = await test_pdf_conversion_only()
        
        # Test full processing
        full_success = await test_simplified_onenote_processor()
        
        return pdf_success and full_success
    
    success = asyncio.run(run_tests())
    
    print("\n" + "=" * 60)
    print("🎯 SIMPLIFIED PROCESSOR TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 Simplified OneNote processor is working!")
        print("✅ OneNote → PDF conversion successful")
        print("✅ Standard document processing pipeline working")
        print("✅ Much simpler and more reliable approach")
        print("\nBenefits of simplified approach:")
        print("- Uses proven PDF processing pipeline")
        print("- Leverages existing Mistral OCR integration")
        print("- Simpler codebase and fewer failure points")
        print("- Standard entity extraction and knowledge graph integration")
    else:
        print("❌ Simplified processor needs work")
        print("Check the logs above for specific issues")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
