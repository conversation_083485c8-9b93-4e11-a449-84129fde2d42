"""
Script to extract entities from all facts in a specific episode.
"""

import os
import sys
import asyncio
import argparse
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from entity_extraction import extract_entities
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def extract_entities_from_episode(episode_id: str):
    """
    Extract entities from all facts in a specific episode.
    
    Args:
        episode_id: ID of the episode
    """
    logger.info(f"Extracting entities from episode: {episode_id}")
    
    # Connect to FalkorDB
    adapter = GraphitiFalkorDBAdapter()
    
    # Check if connected
    if not adapter.is_connected():
        logger.error("Failed to connect to FalkorDB")
        return
    
    logger.info("Connected to FalkorDB")
    
    # Get facts for this episode
    facts_query = f"""
    MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
    RETURN f.uuid as uuid, f.body as body
    """
    
    facts_result = adapter.execute_cypher(facts_query)
    
    if not facts_result or len(facts_result) < 2 or not facts_result[1]:
        logger.error(f"No facts found for episode {episode_id}")
        return
    
    facts = facts_result[1]
    logger.info(f"Found {len(facts)} facts for episode {episode_id}")
    
    # Extract entities from each fact
    entities_extracted = 0
    
    for fact in facts:
        fact_uuid = fact[0]
        fact_body = fact[1]
        
        logger.info(f"Processing fact: {fact_uuid}")
        logger.info(f"Fact body sample: {fact_body[:100]}...")
        
        # Extract entities from the fact
        api_key = os.environ.get('OPENAI_API_KEY')
        entities = extract_entities(fact_body, api_key)
        
        logger.info(f"Extracted {len(entities)} entities from fact {fact_uuid}")
        
        # Create entity nodes and relationships
        for entity in entities:
            logger.info(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")
            
            # Escape special characters in entity name and description
            entity_name = entity["name"].replace("'", "\\'")
            entity_type = entity["type"]
            entity_description = entity.get("description", "").replace("'", "\\'")
            
            # Get current timestamp
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # Create entity node and link to fact
            create_entity_query = f"""
            MERGE (e:Entity {{name: '{entity_name}', type: '{entity_type}'}})
            ON CREATE SET
                e.description = '{entity_description}',
                e.created_at = '{timestamp}'
            WITH e
            MATCH (f:Fact {{uuid: '{fact_uuid}'}})
            MERGE (f)-[r:MENTIONS]->(e)
            RETURN e.name as name
            """
            
            result = adapter.execute_cypher(create_entity_query)
            if result and len(result) > 1 and len(result[1]) > 0:
                logger.info(f"  Created entity node and relationship for {entity['name']}")
                entities_extracted += 1
            else:
                logger.error(f"  Failed to create entity node for {entity['name']}")
    
    logger.info(f"Extracted and created {entities_extracted} entities for episode {episode_id}")
    
    # Close the connection
    adapter.close()

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Extract entities from all facts in a specific episode")
    parser.add_argument("episode_id", help="ID of the episode")
    
    args = parser.parse_args()
    
    # Extract entities from the episode
    await extract_entities_from_episode(args.episode_id)

if __name__ == "__main__":
    asyncio.run(main())
