#!/usr/bin/env python3
"""
Test script to verify Q&A functionality with cocoa questions
"""

import requests
import json

def test_cocoa_qa():
    """Test the Q&A system with cocoa questions"""
    base_url = 'http://localhost:9753'
    
    print("Testing Q&A system with cocoa questions...")
    
    # Test 1: Simple cocoa question
    print("\n1. Testing: 'What are the benefits of cocoa?'")
    try:
        response = requests.post(f'{base_url}/api/qa/answer', 
                               json={
                                   'question': 'What are the benefits of cocoa?',
                                   'llm_provider': 'openrouter',
                                   'llm_model': 'meta-llama/llama-3.3-8b-instruct:free',
                                   'max_facts': 10
                               }, 
                               headers={'Content-Type': 'application/json'}, 
                               timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            answer = result.get('answer', '')
            references = result.get('references', [])
            sources = result.get('sources', [])
            
            print(f"✅ Success! Answer length: {len(answer)} characters")
            print(f"📚 References found: {len(references)}")
            print(f"🔗 Sources: {len(sources)}")
            
            if answer:
                print(f"📝 Answer preview: {answer[:200]}...")
                
            if references:
                print("📖 References:")
                for i, ref in enumerate(references[:3]):
                    content = ref.get('content', 'No content')
                    print(f"   [{i+1}] {content[:100]}...")
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

    # Test 2: Check available facts about cocoa
    print("\n2. Testing: Getting facts about cocoa")
    try:
        response = requests.get(f'{base_url}/api/qa/facts?question=cocoa&limit=5', timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            facts = result.get('facts', [])
            print(f"✅ Found {len(facts)} facts about cocoa")
            
            for i, fact in enumerate(facts[:3]):
                content = fact.get('content', 'No content')
                print(f"   Fact {i+1}: {content[:100]}...")
        else:
            print(f"❌ Error getting facts: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception getting facts: {e}")

    # Test 3: Check available models
    print("\n3. Testing: Available models")
    try:
        response = requests.get(f'{base_url}/api/settings/models', timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            models = result.get('available_models', {})
            
            openrouter_models = models.get('openrouter', [])
            ollama_models = models.get('ollama', [])
            
            free_models = [m for m in openrouter_models if ':free' in m or 'free' in m.lower()]
            
            print(f"✅ OpenRouter models: {len(openrouter_models)} total")
            print(f"🆓 Free models: {len(free_models)}")
            print(f"🏠 Ollama models: {len(ollama_models)}")
            
            if free_models:
                print("🆓 First 5 free models:")
                for model in free_models[:5]:
                    print(f"   - {model}")
        else:
            print(f"❌ Error getting models: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exception getting models: {e}")

    print("\nTest completed!")

if __name__ == "__main__":
    test_cocoa_qa()
