/**
 * Graphiti UI Part 2 - Additional functionality for the Graphiti Knowledge Graph UI
 *
 * This file contains functions for the Search, Graph, and Documents tabs.
 */

// Global data storage
const knowledgeGraphData = {
    graph: null
};

/**
 * Initialize the Search tab
 */
function initializeSearchTab() {
    console.log("Initializing Search tab");

    // Set up event listeners for search
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');

    if (searchInput && searchButton) {
        // Add event listener for Enter key
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Add event listener for search button
        searchButton.addEventListener('click', function() {
            performSearch();
        });
    }
}

/**
 * Perform a search
 */
function performSearch() {
    // Get search query
    const searchQuery = document.getElementById('search-input')?.value || '';

    if (!searchQuery) {
        return;
    }

    // Get search type
    const searchType = document.querySelector('input[name="searchType"]:checked')?.value || 'semantic';

    // Show loading spinner
    const loadingSpinner = document.getElementById('search-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Clear previous results
    const searchResults = document.getElementById('search-results');
    if (searchResults) {
        searchResults.innerHTML = '';
    }

    // Build request data
    const requestData = {
        query: searchQuery,
        top_k: 10,
        hybrid: searchType === 'hybrid'
    };

    // Perform search
    fetch('/api/search/semantic', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Hide loading spinner
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }

        // Display results
        displaySearchResults(data);
    })
    .catch(error => {
        console.error('Error performing search:', error);

        // Hide loading spinner
        if (loadingSpinner) {
            loadingSpinner.style.display = 'none';
        }

        // Show error message
        if (searchResults) {
            searchResults.innerHTML = `<div class="alert alert-danger">Error performing search: ${error.message}</div>`;
        }
    });
}

/**
 * Display search results
 *
 * @param {Object} data - Search results data
 */
function displaySearchResults(data) {
    const searchResults = document.getElementById('search-results');
    if (!searchResults) {
        console.error("Search results element not found");
        return;
    }

    // Clear previous results
    searchResults.innerHTML = '';

    // If no results, show message
    if (!data.results || data.results.length === 0) {
        searchResults.innerHTML = '<div class="alert alert-info">No results found.</div>';
        return;
    }

    // Create results container
    const resultsContainer = document.createElement('div');
    resultsContainer.className = 'mt-4';

    // Add results count
    const resultsCount = document.createElement('p');
    resultsCount.className = 'mb-3';
    resultsCount.textContent = `Found ${data.results.length} results for "${data.query}"`;
    resultsContainer.appendChild(resultsCount);

    // Add results list
    data.results.forEach((result, index) => {
        const resultCard = document.createElement('div');
        resultCard.className = 'card mb-3';

        const cardBody = document.createElement('div');
        cardBody.className = 'card-body';

        // Add result title
        const resultTitle = document.createElement('h5');
        resultTitle.className = 'card-title';
        resultTitle.textContent = result.title || 'Untitled';
        cardBody.appendChild(resultTitle);

        // Add result metadata
        if (result.metadata) {
            const metadata = document.createElement('div');
            metadata.className = 'card-subtitle mb-2 text-muted';

            // Add document info if available
            if (result.metadata.document_id) {
                const documentLink = document.createElement('a');
                documentLink.href = `/documents/${result.metadata.document_id}`;
                documentLink.textContent = result.metadata.document_title || 'Document';
                metadata.appendChild(documentLink);
            }

            // Add score if available
            if (result.score) {
                const score = document.createElement('span');
                score.className = 'badge bg-info ms-2';
                score.textContent = `Score: ${result.score.toFixed(2)}`;
                metadata.appendChild(score);
            }

            cardBody.appendChild(metadata);
        }

        // Add result content
        const resultContent = document.createElement('p');
        resultContent.className = 'card-text';
        resultContent.textContent = result.content || '';
        cardBody.appendChild(resultContent);

        // Add view button if document_id is available
        if (result.metadata && result.metadata.document_id) {
            const viewButton = document.createElement('a');
            viewButton.className = 'btn btn-sm btn-primary';
            viewButton.href = `/documents/${result.metadata.document_id}`;
            viewButton.textContent = 'View Document';
            cardBody.appendChild(viewButton);
        }

        resultCard.appendChild(cardBody);
        resultsContainer.appendChild(resultCard);
    });

    searchResults.appendChild(resultsContainer);
}

/**
 * Initialize the Graph tab
 */
function initializeGraphTab() {
    console.log("Initializing Graph tab");

    // Load knowledge graph data
    loadKnowledgeGraph();
}

/**
 * Load knowledge graph data
 */
function loadKnowledgeGraph() {
    console.log("Loading knowledge graph data...");

    // Show loading spinner
    const loadingSpinner = document.getElementById('graph-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch knowledge graph data
    fetch('/api/knowledge-graph?limit=100')
        .then(response => {
            console.log("API response received:", response.status);
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("Knowledge graph data received:", data);

            try {
                // Store knowledge graph data - using window to make it globally accessible
                window.knowledgeGraphData = {
                    graph: data
                };

                // Render knowledge graph
                renderKnowledgeGraph(data);

                // Hide loading spinner
                if (loadingSpinner) {
                    loadingSpinner.style.display = 'none';
                }
            } catch (err) {
                console.error("Error processing knowledge graph data:", err);
                throw err;
            }
        })
        .catch(error => {
            console.error('Error loading knowledge graph:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const graphContainer = document.getElementById('graph-container');
            if (graphContainer) {
                graphContainer.innerHTML = `<div class="alert alert-danger">Error loading knowledge graph: ${error.message}</div>`;
            }
        });
}

/**
 * Render knowledge graph
 *
 * @param {Object} data - Knowledge graph data
 */
function renderKnowledgeGraph(data) {
    const graphContainer = document.getElementById('graph-container');
    if (!graphContainer) {
        console.error("Graph container element not found");
        return;
    }

    // Clear previous content
    graphContainer.innerHTML = '';

    // If no nodes, show message
    if (!data.nodes || data.nodes.length === 0) {
        graphContainer.innerHTML = '<div class="alert alert-info">No nodes found in the knowledge graph.</div>';

        // Update statistics
        document.getElementById('node-count').textContent = '0';
        document.getElementById('edge-count').textContent = '0';
        document.getElementById('entity-type-count').textContent = '0';

        return;
    }

    // Prepare data for visualization
    const nodes = data.nodes.map(node => ({
        id: node.uuid,
        label: node.name || node.uuid,
        group: node.type,
        properties: node.properties || {}
    }));

    const edges = (data.relationships || []).map(rel => ({
        id: `${rel.source}-${rel.target}`,
        from: rel.source,
        to: rel.target,
        label: rel.type,
        arrows: 'to',
        properties: rel.properties || {}
    }));

    // Store processed data for later use
    window.graphData = {
        nodes: nodes,
        edges: edges
    };

    // Initialize network visualization
    initializeNetwork({
        nodes: nodes,
        edges: edges
    });

    // Update statistics
    document.getElementById('node-count').textContent = nodes.length;
    document.getElementById('edge-count').textContent = edges.length;

    // Count unique entity types
    const entityTypes = [...new Set(nodes.map(node => node.group))];
    document.getElementById('entity-type-count').textContent = entityTypes.length;
}
