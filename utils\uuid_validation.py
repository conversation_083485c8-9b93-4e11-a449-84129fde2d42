"""
Utility functions for UUID validation and management.

This module provides functions to:
1. Validate UUID format
2. Generate UUIDs
3. Check for duplicate UUIDs
4. Ensure UUIDs are properly referenced across the system
"""

import uuid
import logging
from typing import Dict, List, Any, Optional, Union, Set

# Set up logging
logger = logging.getLogger(__name__)

def is_valid_uuid(uuid_str: str) -> bool:
    """
    Check if a string is a valid UUID.
    
    Args:
        uuid_str: String to check
        
    Returns:
        True if the string is a valid UUID, False otherwise
    """
    if not uuid_str:
        return False
    
    try:
        uuid_obj = uuid.UUID(uuid_str)
        return str(uuid_obj) == uuid_str
    except (ValueError, AttributeError, TypeError):
        return False

def generate_uuid() -> str:
    """
    Generate a new UUID.
    
    Returns:
        String representation of a UUID
    """
    return str(uuid.uuid4())

def ensure_uuid(obj: Dict[str, Any]) -> Dict[str, Any]:
    """
    Ensure a dictionary has a UUID.
    
    Args:
        obj: Dictionary to check
        
    Returns:
        Dictionary with a UUID
    """
    if "uuid" not in obj or not is_valid_uuid(obj["uuid"]):
        obj["uuid"] = generate_uuid()
    
    return obj

def find_duplicate_uuids(uuids: List[str]) -> Dict[str, List[int]]:
    """
    Find duplicate UUIDs in a list.
    
    Args:
        uuids: List of UUIDs to check
        
    Returns:
        Dictionary mapping duplicate UUIDs to lists of indices where they appear
    """
    seen = {}
    duplicates = {}
    
    for i, uuid_str in enumerate(uuids):
        if uuid_str in seen:
            if uuid_str not in duplicates:
                duplicates[uuid_str] = [seen[uuid_str]]
            duplicates[uuid_str].append(i)
        else:
            seen[uuid_str] = i
    
    return duplicates

def validate_uuid_references(source_uuids: Set[str], target_uuids: Set[str]) -> Set[str]:
    """
    Validate that all source UUIDs reference valid target UUIDs.
    
    Args:
        source_uuids: Set of source UUIDs
        target_uuids: Set of target UUIDs
        
    Returns:
        Set of invalid source UUIDs that don't reference a valid target UUID
    """
    return source_uuids - target_uuids

def normalize_uuid(uuid_str: str) -> str:
    """
    Normalize a UUID string to the standard format.
    
    Args:
        uuid_str: UUID string to normalize
        
    Returns:
        Normalized UUID string
    """
    if not uuid_str:
        return ""
    
    try:
        return str(uuid.UUID(uuid_str))
    except (ValueError, AttributeError, TypeError):
        return uuid_str

def validate_entity_uuid(entity: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize an entity's UUID.
    
    Args:
        entity: Entity dictionary
        
    Returns:
        Entity with validated UUID
    """
    if "uuid" not in entity or not entity["uuid"]:
        entity["uuid"] = generate_uuid()
        logger.warning(f"Generated new UUID for entity: {entity.get('name', 'Unknown')}")
    elif not is_valid_uuid(entity["uuid"]):
        old_uuid = entity["uuid"]
        entity["uuid"] = generate_uuid()
        logger.warning(f"Replaced invalid UUID '{old_uuid}' with new UUID for entity: {entity.get('name', 'Unknown')}")
    
    return entity

def validate_relationship_uuids(relationship: Dict[str, Any], 
                               source_uuid: str, 
                               target_uuid: str) -> Dict[str, Any]:
    """
    Validate and normalize a relationship's UUIDs.
    
    Args:
        relationship: Relationship dictionary
        source_uuid: Source node UUID
        target_uuid: Target node UUID
        
    Returns:
        Relationship with validated UUIDs
    """
    # Ensure relationship has its own UUID
    if "uuid" not in relationship or not relationship["uuid"]:
        relationship["uuid"] = generate_uuid()
    elif not is_valid_uuid(relationship["uuid"]):
        old_uuid = relationship["uuid"]
        relationship["uuid"] = generate_uuid()
        logger.warning(f"Replaced invalid relationship UUID '{old_uuid}' with new UUID")
    
    # Ensure source and target UUIDs are set correctly
    relationship["source_uuid"] = source_uuid
    relationship["target_uuid"] = target_uuid
    
    return relationship

def validate_document_uuid(document: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize a document's UUID.
    
    Args:
        document: Document dictionary
        
    Returns:
        Document with validated UUID
    """
    if "uuid" not in document or not document["uuid"]:
        document["uuid"] = generate_uuid()
        logger.warning(f"Generated new UUID for document: {document.get('name', 'Unknown')}")
    elif not is_valid_uuid(document["uuid"]):
        old_uuid = document["uuid"]
        document["uuid"] = generate_uuid()
        logger.warning(f"Replaced invalid UUID '{old_uuid}' with new UUID for document: {document.get('name', 'Unknown')}")
    
    return document
