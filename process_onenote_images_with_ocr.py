#!/usr/bin/env python3
"""
Process OneNote embedded images with OCR to extract actual content.

This script extracts the embedded images from OneNote files and processes them
with Mistral OCR to get the actual text content from the OneNote pages.
"""

import sys
import os
import logging
import asyncio
import tempfile
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def process_onenote_images_with_ocr():
    """Process OneNote embedded images with OCR to extract actual content."""
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Processing images from: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from one_extract import OneNoteExtractor, OneNoteExtractorError
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Initialize Mistral OCR
        mistral_ocr = MistralOCRProcessor()
        print("✅ Initialized Mistral OCR processor")
        
        # Read the OneNote file
        with open(onenote_file, 'rb') as f:
            file_data = f.read()
        
        # Extract content with one-extract
        try:
            extractor = OneNoteExtractor(data=file_data)
            print("✅ Initialized OneNote extractor")
        except OneNoteExtractorError as e:
            print(f"❌ OneNote extraction failed: {e}")
            return False
        
        # Extract embedded files (images)
        print("\n🖼️ EXTRACTING AND PROCESSING IMAGES:")
        print("=" * 60)
        
        embedded_files = list(extractor.extract_files())
        print(f"📎 Found {len(embedded_files)} embedded files")
        
        total_extracted_text = ""
        successful_ocr_count = 0
        
        for i, file_data in enumerate(embedded_files):
            print(f"\n📷 Processing embedded file {i+1}:")
            print(f"   Size: {len(file_data):,} bytes")
            
            # Check if it's an image
            if file_data.startswith(b'\x89PNG'):
                file_type = "PNG"
                file_ext = ".png"
            elif file_data.startswith(b'\xff\xd8\xff'):
                file_type = "JPEG"
                file_ext = ".jpg"
            elif file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
                file_type = "GIF"
                file_ext = ".gif"
            else:
                print(f"   ⚠️ Not an image file (skipping)")
                continue
            
            print(f"   🖼️ Image type: {file_type}")
            
            # Save image to temporary file
            with tempfile.NamedTemporaryFile(suffix=file_ext, delete=False) as temp_file:
                temp_file.write(file_data)
                temp_image_path = temp_file.name
            
            try:
                # Process with Mistral OCR
                print(f"   🔍 Processing with Mistral OCR...")
                extracted_text = await mistral_ocr.extract_text_from_document(temp_image_path)
                
                if extracted_text and len(extracted_text.strip()) > 10:
                    print(f"   ✅ OCR SUCCESS: Extracted {len(extracted_text)} characters")
                    print(f"   📝 Preview: {extracted_text[:200]}...")
                    
                    total_extracted_text += f"\n\n=== IMAGE {i+1} CONTENT ({file_type}) ===\n"
                    total_extracted_text += extracted_text
                    successful_ocr_count += 1
                else:
                    print(f"   ❌ OCR returned no text")
                
            except Exception as e:
                print(f"   ❌ OCR error: {e}")
            
            finally:
                # Clean up temporary file
                try:
                    Path(temp_image_path).unlink()
                except Exception:
                    pass
        
        # Summary
        print(f"\n📊 OCR PROCESSING SUMMARY:")
        print("=" * 60)
        print(f"Total embedded files: {len(embedded_files)}")
        print(f"Successful OCR extractions: {successful_ocr_count}")
        print(f"Total text extracted: {len(total_extracted_text):,} characters")
        
        if total_extracted_text:
            # Save the extracted content
            output_file = Path("onenote_ocr_extracted_content.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== ONENOTE OCR EXTRACTED CONTENT ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Successful OCR extractions: {successful_ocr_count}/{len(embedded_files)}\n")
                f.write(f"Total text extracted: {len(total_extracted_text):,} characters\n\n")
                f.write("=== EXTRACTED TEXT FROM IMAGES ===\n")
                f.write(total_extracted_text)
            
            print(f"💾 Saved OCR results to: {output_file}")
            
            # Show preview of extracted content
            print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
            print("=" * 60)
            print(total_extracted_text[:1000])
            if len(total_extracted_text) > 1000:
                print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
            print("=" * 60)
            
            # Compare with previous extraction
            print(f"\n🎉 COMPARISON WITH PREVIOUS EXTRACTION:")
            print(f"Previous OneNote extraction: ~1,880 characters (mostly metadata)")
            print(f"OCR from images: {len(total_extracted_text):,} characters (actual content)")
            
            if len(total_extracted_text) > 1880:
                improvement = len(total_extracted_text) / 1880
                print(f"Improvement factor: {improvement:.1f}x more actual content!")
            
            return True
        else:
            print("❌ No text was extracted from any images")
            print("This could mean:")
            print("- Images don't contain text content")
            print("- Images are too complex for OCR")
            print("- Mistral OCR is not working properly")
            return False
            
    except ImportError:
        print("❌ Required libraries not available (one-extract, mistral_ocr)")
        return False
    except Exception as e:
        print(f"❌ Error during OCR processing: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_single_image_ocr():
    """Test OCR on a single image to verify it's working."""
    print("\n🧪 TESTING SINGLE IMAGE OCR:")
    print("=" * 60)
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Create a simple test image with text
        try:
            from PIL import Image, ImageDraw, ImageFont
            
            # Create a simple image with text
            img = Image.new('RGB', (400, 200), color='white')
            draw = ImageDraw.Draw(img)
            
            # Add some text
            text = "This is a test image\nfor OCR processing\nGinger neuroprotective effects\nBaicalein inhibits TNF"
            draw.text((10, 10), text, fill='black')
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_file:
                img.save(temp_file.name)
                test_image_path = temp_file.name
            
            try:
                # Test OCR
                mistral_ocr = MistralOCRProcessor()
                extracted_text = await mistral_ocr.extract_text_from_document(test_image_path)
                
                if extracted_text:
                    print(f"✅ Test OCR successful: {len(extracted_text)} characters")
                    print(f"📝 Extracted: {extracted_text}")
                    return True
                else:
                    print("❌ Test OCR returned no text")
                    return False
                    
            finally:
                # Clean up
                try:
                    Path(test_image_path).unlink()
                except Exception:
                    pass
                    
        except ImportError:
            print("⚠️ PIL not available for test image creation")
            return True  # Skip test but don't fail
            
    except Exception as e:
        print(f"❌ Test OCR error: {e}")
        return False


def main():
    """Run OneNote image OCR processing."""
    print("🖼️ ONENOTE IMAGE OCR PROCESSING")
    print("=" * 60)
    
    async def run_processing():
        # Test OCR first
        test_success = await test_single_image_ocr()
        
        # Process actual OneNote images
        success = await process_onenote_images_with_ocr()
        
        return success
    
    success = asyncio.run(run_processing())
    
    print("\n" + "=" * 60)
    print("🎯 OCR PROCESSING SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 OneNote image OCR processing successful!")
        print("✅ Actual content extracted from OneNote images")
        print("✅ This is likely the REAL OneNote content")
        print("✅ Much better than just metadata extraction")
        print("\nNext steps:")
        print("1. Integrate OCR results into OneNote processor")
        print("2. Use extracted content for entity extraction")
        print("3. Process references from actual content")
    else:
        print("❌ OneNote image OCR processing needs work")
        print("The images may not contain readable text or OCR needs adjustment")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
