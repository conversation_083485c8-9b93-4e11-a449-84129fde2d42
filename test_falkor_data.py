#!/usr/bin/env python3
"""
Test script to examine FalkorDB data structure
"""

import asyncio
from database.database_service import get_falkordb_adapter

async def test_falkor_data():
    """Test FalkorDB data structure"""
    print('Testing FalkorDB data structure...')
    
    adapter = await get_falkordb_adapter()
    
    # Test 1: Check what a single entity looks like
    print('\n1. Testing single entity query:')
    query = 'MATCH (n:Entity) RETURN n LIMIT 1'
    result = adapter.execute_cypher(query)
    print(f'Raw result: {result}')
    
    if result and len(result) > 1 and len(result[1]) > 0:
        node_data = result[1][0][0]
        print(f'Node data type: {type(node_data)}')
        print(f'Node data: {node_data}')
        
        if isinstance(node_data, dict):
            print(f'Node keys: {list(node_data.keys())}')
            print(f'UUID: {node_data.get("uuid", "NOT_FOUND")}')
            print(f'Name: {node_data.get("name", "NOT_FOUND")}')
            print(f'Type: {node_data.get("type", "NOT_FOUND")}')
    
    # Test 2: Check entity properties directly
    print('\n2. Testing entity properties query:')
    query = 'MATCH (n:Entity) RETURN n.uuid, n.name, n.type LIMIT 3'
    result = adapter.execute_cypher(query)
    print(f'Properties result: {result}')
    
    # Test 3: Check if entities have different property names
    print('\n3. Testing property names:')
    query = 'MATCH (n:Entity) RETURN keys(n) LIMIT 3'
    result = adapter.execute_cypher(query)
    print(f'Keys result: {result}')
    
    # Test 4: Check for entities with actual names
    print('\n4. Testing entities with names:')
    query = 'MATCH (n:Entity) WHERE n.name IS NOT NULL AND n.name <> "" RETURN n.uuid, n.name, n.type LIMIT 5'
    result = adapter.execute_cypher(query)
    print(f'Named entities result: {result}')
    
    # Test 5: Check total count and sample
    print('\n5. Testing entity count and sample:')
    query = 'MATCH (n:Entity) RETURN count(n) as total'
    result = adapter.execute_cypher(query)
    print(f'Total entities: {result}')
    
    query = 'MATCH (n:Entity) RETURN n LIMIT 5'
    result = adapter.execute_cypher(query)
    print(f'Sample entities: {result}')

if __name__ == "__main__":
    asyncio.run(test_falkor_data())
