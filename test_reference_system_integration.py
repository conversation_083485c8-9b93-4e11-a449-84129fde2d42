#!/usr/bin/env python3
"""
Test script for reference system integration and frontend UI
"""

import asyncio
import logging
import requests
import time
from pathlib import Path
from services.reference_processor import ReferenceProcessor

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_reference_system_integration():
    """Test the complete reference system integration"""
    
    print("🔍 Testing Reference System Integration")
    print("=" * 60)
    
    # Test 1: Reference Processor
    print("📚 Testing Reference Processor...")
    processor = ReferenceProcessor()
    
    # Test with existing document if available
    test_doc_path = Path("uploads/a8bd26e6-fe2e-43eb-82bd-82620ecbc322_Echinacea.pdf")
    
    if test_doc_path.exists():
        print(f"Found test document: {test_doc_path.name}")
        
        try:
            # Test reference extraction
            print("Extracting references...")
            result = await processor._extract_references_with_mistral_ocr(str(test_doc_path))
            
            if result.get("success"):
                print(f"✅ Reference extraction successful!")
                print(f"  Total references: {result.get('total_reference_count', 0)}")
                print(f"  Extraction method: {result.get('extraction_method', 'unknown')}")
                
                # Test enhanced metadata extraction
                references = result.get("regex_references", [])
                if references:
                    print(f"\n📋 Testing Enhanced Metadata Extraction:")
                    for i, ref in enumerate(references[:3], 1):  # Test first 3 references
                        metadata = ref.get("metadata", {})
                        print(f"  Reference {i}:")
                        print(f"    Authors: <AUTHORS>
                        print(f"    Title: {metadata.get('title', 'Not extracted')}")
                        print(f"    Year: {metadata.get('year', 'Not extracted')}")
                        print(f"    Journal: {metadata.get('journal', 'Not extracted')}")
                        print(f"    DOI: {metadata.get('doi', 'Not extracted')}")
                        print(f"    Confidence: {metadata.get('confidence', 0.0):.2f}")
                        print()
                
                # Test file saving
                print("📄 Testing Reference File Saving...")
                file_paths = await processor.save_references_to_files(result)
                if file_paths:
                    print(f"✅ References saved to files:")
                    for format_type, path in file_paths.items():
                        print(f"  {format_type.upper()}: {path}")
                else:
                    print("❌ Failed to save references to files")
                
            else:
                print(f"❌ Reference extraction failed: {result.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Error testing reference processor: {e}")
    else:
        print("No test document found - skipping reference extraction test")
    
    # Test 2: API Endpoints
    print(f"\n🌐 Testing Reference API Endpoints...")
    base_url = "http://127.0.0.1:9753"
    
    try:
        # Test references API
        response = requests.get(f"{base_url}/api/references", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ References API working - found {len(data.get('references', []))} references")
        else:
            print(f"❌ References API failed with status {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing references API: {e}")
    
    # Test 3: Frontend UI Pages
    print(f"\n🖥️ Testing Frontend UI Pages...")
    
    ui_pages = [
        ("/references", "References Page"),
        ("/references_modern", "Modern References Page"),
        ("/enhanced_upload", "Enhanced Upload Page"),
    ]
    
    for endpoint, name in ui_pages:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                print(f"✅ {name} accessible")
            else:
                print(f"❌ {name} failed with status {response.status_code}")
        except Exception as e:
            print(f"❌ Error accessing {name}: {e}")
    
    # Test 4: JavaScript Integration
    print(f"\n📜 Testing JavaScript Integration...")
    
    js_files = [
        "static/js/reference_display.js",
        "static/js/graphiti_ui_part5.js",
        "static/js/graphiti_ui_part6.js",
        "static/references_enhanced.js",
        "static/flask_references.js"
    ]
    
    for js_file in js_files:
        if Path(js_file).exists():
            print(f"✅ {js_file} exists")
        else:
            print(f"❌ {js_file} missing")
    
    # Test 5: Template Integration
    print(f"\n📄 Testing Template Integration...")
    
    templates = [
        "templates/references.html",
        "templates/references_modern.html",
        "templates/enhanced_upload.html",
        "templates/enhanced_progress.html"
    ]
    
    for template in templates:
        if Path(template).exists():
            print(f"✅ {template} exists")
        else:
            print(f"❌ {template} missing")
    
    print(f"\n🎉 Reference System Integration Test Complete!")
    print("=" * 60)

def test_reference_deduplication():
    """Test the enhanced reference deduplication"""
    
    print("\n🔄 Testing Enhanced Reference Deduplication...")
    
    # Sample references for testing
    test_references = [
        "Smith, J. A. (2020). The effects of echinacea on immune function. Journal of Herbal Medicine, 15(3), 123-135.",
        "Smith, J.A. (2020). The effects of echinacea on immune function. J Herbal Med, 15(3), 123-135.",  # Similar
        "Johnson, B. et al. (2019). Clinical trials of herbal supplements. Nature Medicine, 25(4), 456-467.",
        "Brown, C. (2021). Antioxidant properties of medicinal plants. Phytotherapy Research, 30(2), 89-102.",
        "Smith, J. A. The effects of echinacea on immune function. Journal of Herbal Medicine 2020;15:123-135.",  # Similar format
    ]
    
    # Create processor instance
    processor = ReferenceProcessor()
    
    # Test deduplication
    deduplicated = processor._filter_and_deduplicate_references(test_references)
    
    print(f"Original references: {len(test_references)}")
    print(f"After deduplication: {len(deduplicated)}")
    print(f"Duplicates removed: {len(test_references) - len(deduplicated)}")
    
    print("\nDeduplicated references:")
    for i, ref in enumerate(deduplicated, 1):
        print(f"  {i}. {ref[:100]}...")

if __name__ == "__main__":
    asyncio.run(test_reference_system_integration())
    test_reference_deduplication()
