"""
Relationship model for the knowledge graph.

This module defines the Relationship data model used in the knowledge graph. The Relationship
class represents a relationship between two entities in the graph, with properties such as
type, description, directionality, magnitude, conditions, and confidence.

The model uses Pydantic for data validation and serialization, making it easy to convert
between different representations (e.g., Python objects, dictionaries, JSON) while
ensuring data integrity.

Example:
    ```python
    from entity_extraction.models.relationship import Relationship

    # Create a relationship from a dictionary
    relationship_data = {
        "source": "Vitamin C",
        "target": "Immune System",
        "type": "ENHANCES",
        "description": "Vitamin C enhances immune system function",
        "directionality": "positive",
        "confidence": "high"
    }
    relationship = Relationship.from_dict(relationship_data)

    # Access relationship properties
    print(f"Relationship: {relationship.source} {relationship.type} {relationship.target}")

    # Convert relationship to dictionary
    relationship_dict = relationship.to_dict()
    ```
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class Relationship(BaseModel):
    """
    Relationship model for the knowledge graph.

    This class represents a relationship between two entities in the knowledge graph.
    It includes properties such as source, target, type, description, directionality,
    magnitude, conditions, confidence, and additional attributes.

    Relationships connect entities in the knowledge graph, expressing how they are related
    to each other. Each relationship has a type that describes the nature of the connection
    (e.g., INHIBITS, CONTAINS, TREATS).

    Attributes:
        source (str): The name of the source entity
        target (str): The name of the target entity
        type (str): The type of the relationship
        description (Optional[str]): A description of the relationship
        directionality (Optional[str]): The directionality of the relationship (positive, negative, neutral)
        magnitude (Optional[str]): The magnitude of the relationship
        conditions (Optional[str]): The conditions under which the relationship holds
        confidence (Optional[str]): The confidence level of the relationship (high, medium, low)
        created_at (Optional[datetime]): The creation timestamp
        updated_at (Optional[datetime]): The last update timestamp
        attributes (Optional[Dict[str, Any]]): Additional attributes of the relationship
    """

    source: str = Field(..., description="The name of the source entity")
    target: str = Field(..., description="The name of the target entity")
    type: str = Field(..., description="The type of the relationship")
    description: Optional[str] = Field("", description="The description of the relationship")
    directionality: Optional[str] = Field("neutral", description="The directionality of the relationship (positive, negative, neutral)")
    magnitude: Optional[str] = Field("", description="The magnitude of the relationship")
    conditions: Optional[str] = Field("", description="The conditions under which the relationship holds")
    confidence: Optional[str] = Field("medium", description="The confidence level of the relationship (high, medium, low)")
    created_at: Optional[datetime] = Field(None, description="The creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="The last update timestamp")
    attributes: Optional[Dict[str, Any]] = Field({}, description="Additional attributes of the relationship")

    class Config:
        """Pydantic configuration."""

        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the relationship to a dictionary.

        This method converts the Relationship object to a dictionary representation, which can
        be used for serialization to JSON or other formats. It handles the conversion of
        datetime objects to ISO format strings.

        The resulting dictionary includes all the relationship's properties, with datetime
        objects converted to ISO format strings for easier serialization.

        Returns:
            Dict[str, Any]: Dictionary representation of the relationship with the following keys:
                - source (str): The name of the source entity
                - target (str): The name of the target entity
                - type (str): The type of the relationship
                - description (Optional[str]): A description of the relationship
                - directionality (Optional[str]): The directionality of the relationship
                - magnitude (Optional[str]): The magnitude of the relationship
                - conditions (Optional[str]): The conditions under which the relationship holds
                - confidence (Optional[str]): The confidence level of the relationship
                - created_at (Optional[str]): The creation timestamp as ISO format string
                - updated_at (Optional[str]): The last update timestamp as ISO format string
                - attributes (Optional[Dict[str, Any]]): Additional attributes of the relationship
        """
        return {
            "source": self.source,
            "target": self.target,
            "type": self.type,
            "description": self.description,
            "directionality": self.directionality,
            "magnitude": self.magnitude,
            "conditions": self.conditions,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "attributes": self.attributes
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Relationship":
        """
        Create a relationship from a dictionary.

        This class method creates a Relationship object from a dictionary representation.
        It's useful for deserializing relationships from JSON or other dictionary-based
        formats.

        The method handles missing optional fields by providing default values.

        Example:
            ```python
            relationship_data = {
                "source": "Vitamin C",
                "target": "Immune System",
                "type": "ENHANCES",
                "description": "Vitamin C enhances immune system function"
            }
            relationship = Relationship.from_dict(relationship_data)
            ```

        Args:
            data (Dict[str, Any]): Dictionary representation of the relationship with at least
                the following required keys:
                - source (str): The name of the source entity
                - target (str): The name of the target entity
                - type (str): The type of the relationship

                And these optional keys:
                - description (Optional[str]): A description of the relationship
                - directionality (Optional[str]): The directionality of the relationship
                - magnitude (Optional[str]): The magnitude of the relationship
                - conditions (Optional[str]): The conditions under which the relationship holds
                - confidence (Optional[str]): The confidence level of the relationship
                - created_at (Optional[str/datetime]): The creation timestamp
                - updated_at (Optional[str/datetime]): The last update timestamp
                - attributes (Optional[Dict[str, Any]]): Additional attributes

        Returns:
            Relationship: A new Relationship instance created from the dictionary data
        """
        return cls(
            source=data["source"],
            target=data["target"],
            type=data["type"],
            description=data.get("description", ""),
            directionality=data.get("directionality", "neutral"),
            magnitude=data.get("magnitude", ""),
            conditions=data.get("conditions", ""),
            confidence=data.get("confidence", "medium"),
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
            attributes=data.get("attributes", {})
        )

    @classmethod
    def from_record(cls, record: Dict[str, Any]) -> "Relationship":
        """
        Create a relationship from a database record.

        This class method creates a Relationship object from a database record. It's specifically
        designed to handle records returned from database queries, which may have a slightly
        different structure than the dictionaries used in from_dict.

        The method is particularly useful when retrieving relationships from the database and
        converting them to Relationship objects for further processing.

        Example:
            ```python
            # Assuming 'record' is a record from a database query
            relationship = Relationship.from_record(record)
            ```

        Args:
            record (Dict[str, Any]): Database record containing relationship data with at least
                the following required keys:
                - source (str): The name of the source entity
                - target (str): The name of the target entity
                - type (str): The type of the relationship

                And potentially these optional keys:
                - description (Optional[str]): A description of the relationship
                - directionality (Optional[str]): The directionality of the relationship
                - magnitude (Optional[str]): The magnitude of the relationship
                - conditions (Optional[str]): The conditions under which the relationship holds
                - confidence (Optional[str]): The confidence level of the relationship
                - created_at (Optional[datetime]): The creation timestamp
                - updated_at (Optional[datetime]): The last update timestamp
                - attributes (Optional[Dict[str, Any]]): Additional attributes

        Returns:
            Relationship: A new Relationship instance created from the database record
        """
        return cls(
            source=record["source"],
            target=record["target"],
            type=record["type"],
            description=record.get("description", ""),
            directionality=record.get("directionality", "neutral"),
            magnitude=record.get("magnitude", ""),
            conditions=record.get("conditions", ""),
            confidence=record.get("confidence", "medium"),
            created_at=record.get("created_at"),
            updated_at=record.get("updated_at"),
            attributes=record.get("attributes", {})
        )
