"""
Personalized ranking system for knowledge graph search.

This module implements user-specific ranking that adapts to:
- Individual user preferences and interests
- Search history and interaction patterns
- Domain expertise levels
- Temporal usage patterns
- Collaborative filtering from similar users
"""

import logging
import time
import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, asdict
from collections import defaultdict, Counter
import os
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass
class UserProfile:
    """Represents a user's profile and preferences."""
    user_id: str
    domain_expertise: Dict[str, float]  # domain -> expertise level (0-1)
    entity_preferences: Dict[str, float]  # entity_type -> preference score
    topic_interests: Dict[str, float]  # topic -> interest score
    search_patterns: Dict[str, Any]  # search behavior patterns
    interaction_history: List[str]  # recent interaction UUIDs
    created_at: float
    last_updated: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'UserProfile':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class SearchSession:
    """Represents a user's search session."""
    session_id: str
    user_id: str
    queries: List[str]
    clicked_results: List[str]
    dwell_times: List[float]
    start_time: float
    end_time: Optional[float] = None
    context: Optional[Dict[str, Any]] = None


class UserProfileManager:
    """Manages user profiles and learning from interactions."""
    
    def __init__(self, storage_path: str = "user_profiles.json"):
        self.storage_path = storage_path
        self.profiles: Dict[str, UserProfile] = {}
        self.sessions: Dict[str, SearchSession] = {}
        self._load_profiles()
    
    def get_or_create_profile(self, user_id: str) -> UserProfile:
        """Get existing profile or create new one."""
        if user_id not in self.profiles:
            self.profiles[user_id] = UserProfile(
                user_id=user_id,
                domain_expertise={},
                entity_preferences={},
                topic_interests={},
                search_patterns={},
                interaction_history=[],
                created_at=time.time(),
                last_updated=time.time()
            )
        return self.profiles[user_id]
    
    def update_profile_from_interaction(
        self,
        user_id: str,
        query: str,
        result: Dict[str, Any],
        interaction_type: str,
        dwell_time: Optional[float] = None
    ):
        """Update user profile based on interaction."""
        profile = self.get_or_create_profile(user_id)
        
        # Update entity preferences
        entity_type = result.get('type', 'unknown')
        if entity_type in profile.entity_preferences:
            profile.entity_preferences[entity_type] = self._update_preference(
                profile.entity_preferences[entity_type], interaction_type, dwell_time
            )
        else:
            profile.entity_preferences[entity_type] = self._initial_preference(interaction_type, dwell_time)
        
        # Update topic interests based on result content
        topics = self._extract_topics(result)
        for topic in topics:
            if topic in profile.topic_interests:
                profile.topic_interests[topic] = self._update_preference(
                    profile.topic_interests[topic], interaction_type, dwell_time
                )
            else:
                profile.topic_interests[topic] = self._initial_preference(interaction_type, dwell_time)
        
        # Update domain expertise
        domain = self._infer_domain(query, result)
        if domain:
            current_expertise = profile.domain_expertise.get(domain, 0.0)
            profile.domain_expertise[domain] = min(1.0, current_expertise + 0.01)  # Gradual learning
        
        # Update search patterns
        self._update_search_patterns(profile, query, result, interaction_type)
        
        # Add to interaction history
        profile.interaction_history.append(result.get('uuid', ''))
        if len(profile.interaction_history) > 100:  # Keep last 100 interactions
            profile.interaction_history = profile.interaction_history[-100:]
        
        profile.last_updated = time.time()
        
        # Save periodically
        if len(profile.interaction_history) % 10 == 0:
            self._save_profiles()
    
    def _update_preference(self, current_score: float, interaction_type: str, dwell_time: Optional[float]) -> float:
        """Update preference score based on interaction."""
        learning_rate = 0.1
        
        # Calculate interaction value
        if interaction_type == 'click':
            interaction_value = 0.3
        elif interaction_type == 'dwell':
            # Longer dwell time = higher value
            interaction_value = min(0.8, (dwell_time or 0) / 60.0) if dwell_time else 0.2
        elif interaction_type == 'feedback_positive':
            interaction_value = 1.0
        elif interaction_type == 'feedback_negative':
            interaction_value = -0.5
        elif interaction_type == 'bookmark':
            interaction_value = 0.7
        elif interaction_type == 'share':
            interaction_value = 0.6
        else:
            interaction_value = 0.1
        
        # Update with exponential moving average
        new_score = current_score + learning_rate * (interaction_value - current_score)
        return max(0.0, min(1.0, new_score))
    
    def _initial_preference(self, interaction_type: str, dwell_time: Optional[float]) -> float:
        """Calculate initial preference score."""
        if interaction_type == 'feedback_positive':
            return 0.8
        elif interaction_type == 'dwell' and dwell_time and dwell_time > 30:
            return 0.6
        elif interaction_type == 'bookmark':
            return 0.7
        elif interaction_type == 'click':
            return 0.4
        else:
            return 0.3
    
    def _extract_topics(self, result: Dict[str, Any]) -> List[str]:
        """Extract topics from result content."""
        topics = []
        
        # Extract from name and summary
        text = f"{result.get('name', '')} {result.get('summary', '')}"
        words = text.lower().split()
        
        # Simple topic extraction (could be enhanced with NLP)
        medical_terms = {'vitamin', 'health', 'disease', 'treatment', 'medicine', 'therapy', 'diagnosis'}
        research_terms = {'study', 'research', 'analysis', 'investigation', 'experiment'}
        nutrition_terms = {'nutrition', 'diet', 'food', 'supplement', 'nutrient'}
        
        if any(term in words for term in medical_terms):
            topics.append('medical')
        if any(term in words for term in research_terms):
            topics.append('research')
        if any(term in words for term in nutrition_terms):
            topics.append('nutrition')
        
        return topics
    
    def _infer_domain(self, query: str, result: Dict[str, Any]) -> Optional[str]:
        """Infer domain from query and result."""
        text = f"{query} {result.get('name', '')} {result.get('summary', '')}".lower()
        
        if any(term in text for term in ['vitamin', 'health', 'medical', 'disease', 'treatment']):
            return 'healthcare'
        elif any(term in text for term in ['research', 'study', 'analysis', 'science']):
            return 'research'
        elif any(term in text for term in ['nutrition', 'diet', 'food', 'supplement']):
            return 'nutrition'
        
        return None
    
    def _update_search_patterns(self, profile: UserProfile, query: str, result: Dict[str, Any], interaction_type: str):
        """Update search behavior patterns."""
        patterns = profile.search_patterns
        
        # Query length preference
        query_length = len(query.split())
        if 'avg_query_length' in patterns:
            patterns['avg_query_length'] = (patterns['avg_query_length'] * 0.9 + query_length * 0.1)
        else:
            patterns['avg_query_length'] = query_length
        
        # Time of day patterns
        hour = datetime.now().hour
        if 'active_hours' not in patterns:
            patterns['active_hours'] = {}
        patterns['active_hours'][str(hour)] = patterns['active_hours'].get(str(hour), 0) + 1
        
        # Interaction type preferences
        if 'interaction_types' not in patterns:
            patterns['interaction_types'] = {}
        patterns['interaction_types'][interaction_type] = patterns['interaction_types'].get(interaction_type, 0) + 1
    
    def get_user_similarity(self, user1_id: str, user2_id: str) -> float:
        """Calculate similarity between two users."""
        if user1_id not in self.profiles or user2_id not in self.profiles:
            return 0.0
        
        profile1 = self.profiles[user1_id]
        profile2 = self.profiles[user2_id]
        
        # Calculate similarity based on preferences
        similarities = []
        
        # Entity preferences similarity
        entity_sim = self._calculate_preference_similarity(
            profile1.entity_preferences, profile2.entity_preferences
        )
        similarities.append(entity_sim)
        
        # Topic interests similarity
        topic_sim = self._calculate_preference_similarity(
            profile1.topic_interests, profile2.topic_interests
        )
        similarities.append(topic_sim)
        
        # Domain expertise similarity
        domain_sim = self._calculate_preference_similarity(
            profile1.domain_expertise, profile2.domain_expertise
        )
        similarities.append(domain_sim)
        
        return sum(similarities) / len(similarities) if similarities else 0.0
    
    def _calculate_preference_similarity(self, prefs1: Dict[str, float], prefs2: Dict[str, float]) -> float:
        """Calculate cosine similarity between preference dictionaries."""
        if not prefs1 or not prefs2:
            return 0.0
        
        # Get common keys
        common_keys = set(prefs1.keys()) & set(prefs2.keys())
        if not common_keys:
            return 0.0
        
        # Calculate cosine similarity
        vec1 = np.array([prefs1[key] for key in common_keys])
        vec2 = np.array([prefs2[key] for key in common_keys])
        
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return np.dot(vec1, vec2) / (norm1 * norm2)
    
    def get_similar_users(self, user_id: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """Get most similar users to the given user."""
        if user_id not in self.profiles:
            return []
        
        similarities = []
        for other_user_id in self.profiles:
            if other_user_id != user_id:
                similarity = self.get_user_similarity(user_id, other_user_id)
                similarities.append((other_user_id, similarity))
        
        similarities.sort(key=lambda x: x[1], reverse=True)
        return similarities[:top_k]
    
    def _save_profiles(self):
        """Save user profiles to file."""
        try:
            data = {user_id: profile.to_dict() for user_id, profile in self.profiles.items()}
            with open(self.storage_path, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save user profiles: {e}")
    
    def _load_profiles(self):
        """Load user profiles from file."""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r') as f:
                    data = json.load(f)
                
                for user_id, profile_data in data.items():
                    self.profiles[user_id] = UserProfile.from_dict(profile_data)
        except Exception as e:
            logger.error(f"Failed to load user profiles: {e}")


class PersonalizedRanker:
    """Implements personalized ranking based on user profiles."""
    
    def __init__(self, profile_manager: UserProfileManager):
        self.profile_manager = profile_manager
    
    def personalize_ranking(
        self,
        user_id: str,
        query: str,
        results: List[Dict[str, Any]],
        base_scores: List[float]
    ) -> List[Tuple[Dict[str, Any], float]]:
        """Apply personalized ranking to search results."""
        if not results:
            return []
        
        profile = self.profile_manager.get_or_create_profile(user_id)
        
        # Calculate personalized scores for each result
        personalized_results = []
        
        for i, result in enumerate(results):
            base_score = base_scores[i] if i < len(base_scores) else 0.5
            
            # Calculate personalization factors
            entity_factor = self._calculate_entity_preference_factor(profile, result)
            topic_factor = self._calculate_topic_interest_factor(profile, result)
            domain_factor = self._calculate_domain_expertise_factor(profile, query, result)
            novelty_factor = self._calculate_novelty_factor(profile, result)
            collaborative_factor = self._calculate_collaborative_factor(user_id, result)
            
            # Combine factors with weights
            personalization_score = (
                entity_factor * 0.25 +
                topic_factor * 0.25 +
                domain_factor * 0.20 +
                novelty_factor * 0.15 +
                collaborative_factor * 0.15
            )
            
            # Combine with base score
            final_score = base_score * 0.7 + personalization_score * 0.3
            
            personalized_results.append((result, final_score))
        
        # Sort by personalized score
        personalized_results.sort(key=lambda x: x[1], reverse=True)
        
        return personalized_results
    
    def _calculate_entity_preference_factor(self, profile: UserProfile, result: Dict[str, Any]) -> float:
        """Calculate factor based on entity type preferences."""
        entity_type = result.get('type', 'unknown')
        return profile.entity_preferences.get(entity_type, 0.5)
    
    def _calculate_topic_interest_factor(self, profile: UserProfile, result: Dict[str, Any]) -> float:
        """Calculate factor based on topic interests."""
        topics = self.profile_manager._extract_topics(result)
        if not topics:
            return 0.5
        
        topic_scores = [profile.topic_interests.get(topic, 0.5) for topic in topics]
        return sum(topic_scores) / len(topic_scores)
    
    def _calculate_domain_expertise_factor(self, profile: UserProfile, query: str, result: Dict[str, Any]) -> float:
        """Calculate factor based on domain expertise."""
        domain = self.profile_manager._infer_domain(query, result)
        if not domain:
            return 0.5
        
        expertise = profile.domain_expertise.get(domain, 0.0)
        
        # Higher expertise users might prefer more complex/detailed results
        # Lower expertise users might prefer simpler/overview results
        result_complexity = self._estimate_result_complexity(result)
        
        if expertise > 0.7:  # Expert user
            return 0.3 + 0.7 * result_complexity  # Prefer complex results
        elif expertise < 0.3:  # Novice user
            return 0.8 - 0.3 * result_complexity  # Prefer simple results
        else:  # Intermediate user
            return 0.6  # Neutral preference
    
    def _estimate_result_complexity(self, result: Dict[str, Any]) -> float:
        """Estimate complexity of a result (0=simple, 1=complex)."""
        # Simple heuristic based on summary length and technical terms
        summary = result.get('summary', '')
        
        # Length factor
        length_factor = min(1.0, len(summary) / 200.0)
        
        # Technical terms factor
        technical_terms = ['analysis', 'mechanism', 'pathway', 'molecular', 'clinical', 'systematic']
        tech_count = sum(1 for term in technical_terms if term in summary.lower())
        tech_factor = min(1.0, tech_count / 3.0)
        
        return (length_factor + tech_factor) / 2.0
    
    def _calculate_novelty_factor(self, profile: UserProfile, result: Dict[str, Any]) -> float:
        """Calculate novelty factor (prefer results user hasn't seen)."""
        result_uuid = result.get('uuid', '')
        
        if result_uuid in profile.interaction_history:
            # User has seen this before, reduce novelty
            return 0.3
        else:
            # New result, higher novelty
            return 0.8
    
    def _calculate_collaborative_factor(self, user_id: str, result: Dict[str, Any]) -> float:
        """Calculate collaborative filtering factor."""
        # Get similar users
        similar_users = self.profile_manager.get_similar_users(user_id, top_k=3)
        
        if not similar_users:
            return 0.5
        
        # Check if similar users have interacted positively with this result
        result_uuid = result.get('uuid', '')
        positive_interactions = 0
        total_interactions = 0
        
        for similar_user_id, similarity in similar_users:
            similar_profile = self.profile_manager.profiles.get(similar_user_id)
            if similar_profile and result_uuid in similar_profile.interaction_history:
                total_interactions += 1
                # This is a simplified check - in practice, you'd track interaction types
                positive_interactions += 1
        
        if total_interactions == 0:
            return 0.5
        
        # Weight by user similarity
        weighted_score = 0.0
        total_weight = 0.0
        
        for similar_user_id, similarity in similar_users:
            similar_profile = self.profile_manager.profiles.get(similar_user_id)
            if similar_profile and result_uuid in similar_profile.interaction_history:
                weighted_score += similarity * 1.0  # Positive interaction
                total_weight += similarity
        
        return weighted_score / total_weight if total_weight > 0 else 0.5
    
    def get_personalization_explanation(
        self,
        user_id: str,
        query: str,
        result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Get explanation for why a result was ranked for this user."""
        profile = self.profile_manager.get_or_create_profile(user_id)
        
        entity_factor = self._calculate_entity_preference_factor(profile, result)
        topic_factor = self._calculate_topic_interest_factor(profile, result)
        domain_factor = self._calculate_domain_expertise_factor(profile, query, result)
        novelty_factor = self._calculate_novelty_factor(profile, result)
        collaborative_factor = self._calculate_collaborative_factor(user_id, result)
        
        return {
            'entity_preference': entity_factor,
            'topic_interest': topic_factor,
            'domain_expertise': domain_factor,
            'novelty': novelty_factor,
            'collaborative': collaborative_factor,
            'explanation': self._generate_explanation(
                profile, result, entity_factor, topic_factor, domain_factor, novelty_factor
            )
        }
    
    def _generate_explanation(
        self,
        profile: UserProfile,
        result: Dict[str, Any],
        entity_factor: float,
        topic_factor: float,
        domain_factor: float,
        novelty_factor: float
    ) -> str:
        """Generate human-readable explanation for ranking."""
        explanations = []
        
        if entity_factor > 0.7:
            entity_type = result.get('type', 'unknown')
            explanations.append(f"You frequently interact with {entity_type} entities")
        
        if topic_factor > 0.7:
            topics = self.profile_manager._extract_topics(result)
            if topics:
                explanations.append(f"This matches your interest in {', '.join(topics)}")
        
        if domain_factor > 0.7:
            explanations.append("This matches your expertise level")
        
        if novelty_factor > 0.7:
            explanations.append("This is new content you haven't seen before")
        
        if not explanations:
            explanations.append("This result matches your general preferences")
        
        return "; ".join(explanations)


# Global instance
_personalized_ranker = None


def get_personalized_ranker() -> PersonalizedRanker:
    """Get the global personalized ranker."""
    global _personalized_ranker
    if _personalized_ranker is None:
        profile_manager = UserProfileManager()
        _personalized_ranker = PersonalizedRanker(profile_manager)
    return _personalized_ranker
