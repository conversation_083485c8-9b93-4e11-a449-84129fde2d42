"""
Entity-related data models for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from enum import Enum

class EntityType(str, Enum):
    """Entity type enumeration"""
    HERB = "Herb"
    NUTRIENT = "Nutrient"
    DISEASE = "Disease"
    MEDICATION = "Medication"
    SYMPTOM = "Symptom"
    PROCESS = "Process"
    TREATMENT = "Treatment"
    RESEARCH = "Research"
    ORGANIZATION = "Organization"
    PERSON = "Person"
    FOOD = "Food"
    CONCEPT = "Concept"
    LOCATION = "Location"
    CHEMICAL = "Chemical"
    PROTEIN = "Protein"
    PLANT = "Plant"
    INGREDIENT = "Ingredient"
    HORMONE = "Hormone"
    STUDY = "Study"
    OTHER = "Other"

class RelationshipType(str, Enum):
    """Relationship type enumeration"""
    IS_A = "IS_A"
    PART_OF = "PART_OF"
    TREATS = "TREATS"
    CAUSES = "CAUSES"
    PREVENTS = "PREVENTS"
    CONTAINS = "CONTAINS"
    INTERACTS_WITH = "INTERACTS_WITH"
    CONTRAINDICATES = "CONTRAINDICATES"
    INCREASES = "INCREASES"
    DECREASES = "DECREASES"
    NEEDS = "NEEDS"
    INHIBITS = "INHIBITS"
    ACTIVATES = "ACTIVATES"
    REGULATES = "REGULATES"
    CONVERTS_TO = "CONVERTS_TO"
    DERIVED_FROM = "DERIVED_FROM"
    USED_FOR = "USED_FOR"
    MEASURED_BY = "MEASURED_BY"
    ASSOCIATED_WITH = "ASSOCIATED_WITH"
    STUDIED_BY = "STUDIED_BY"
    MENTIONS = "MENTIONS"
    OTHER = "OTHER"

class EntityAttributes(BaseModel):
    """Entity attributes model"""
    # Common attributes
    description: Optional[str] = None
    synonyms: Optional[List[str]] = None
    external_ids: Optional[Dict[str, str]] = None

    # Herb attributes
    active_compounds: Optional[List[str]] = None
    traditional_uses: Optional[List[str]] = None
    dosage_range: Optional[str] = None
    contraindications: Optional[List[str]] = None
    side_effects: Optional[List[str]] = None
    interactions: Optional[List[str]] = None
    preparation_methods: Optional[List[str]] = None

    # Nutrient attributes
    food_sources: Optional[List[str]] = None
    daily_requirements: Optional[str] = None
    deficiency_symptoms: Optional[List[str]] = None
    toxicity_symptoms: Optional[List[str]] = None
    functions: Optional[List[str]] = None
    absorption_factors: Optional[List[str]] = None

    # Disease attributes
    symptoms: Optional[List[str]] = None
    causes: Optional[List[str]] = None
    risk_factors: Optional[List[str]] = None
    treatments: Optional[List[str]] = None
    prevention: Optional[List[str]] = None

    # Custom attributes
    custom_attributes: Optional[Dict[str, Any]] = None

class Entity(BaseModel):
    """Entity model"""
    uuid: str
    name: str
    type: str
    attributes: Optional[EntityAttributes] = None
    confidence: Optional[float] = None
    source_documents: Optional[List[str]] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

class EntitySummary(BaseModel):
    """Entity summary model"""
    uuid: str
    name: str
    type: str
    mention_count: int = 0

class EntityList(BaseModel):
    """Entity list model"""
    entities: List[EntitySummary]
    total: int
    page: int
    page_size: int

class EntityTypeCount(BaseModel):
    """Entity type count model"""
    type: str
    count: int

class EntityTypeCounts(BaseModel):
    """Entity type counts model"""
    counts: List[EntityTypeCount]
    total: int

class Relationship(BaseModel):
    """Relationship model"""
    source_uuid: str
    target_uuid: str
    type: str
    confidence: Optional[float] = None
    properties: Optional[Dict[str, Any]] = None

class EntityWithRelationships(BaseModel):
    """Entity with relationships model"""
    entity: Entity
    relationships: List[Relationship]
