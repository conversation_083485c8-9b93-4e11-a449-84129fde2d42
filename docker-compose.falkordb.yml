version: '3.8'

services:
  falkordb:
    image: falkordb/falkordb:latest
    ports:
      - "6379:6379"  # Redis/FalkorDB port
      - "7688:7687"  # BOLT protocol port (mapped to 7688 externally)
    volumes:
      - falkordb_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  graphiti-mcp:
    build:
      context: .
      dockerfile: Dockerfile
    env_file:
      - path: .env.falkordb
      - path: .env
        required: false
    depends_on:
      falkordb:
        condition: service_healthy
    environment:
      - NEO4J_URI=bolt://falkordb:7687
      - NEO4J_USER=default
      - NEO4J_PASSWORD=${FALKORDB_PASSWORD:-Triathlon16!}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_NAME=${MODEL_NAME}
      - PATH=/root/.local/bin:${PATH}
    ports:
      - "9753:9753"
    command: ["uv", "run", "graphiti_mcp_server.py", "--transport", "sse"]

volumes:
  falkordb_data:
