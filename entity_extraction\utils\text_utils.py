"""
Utility functions for text processing in entity extraction.
"""

import re
import logging
from typing import List, Dict, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)


def clean_text(text: str) -> str:
    """
    Clean text for entity extraction.

    Args:
        text: Text to clean

    Returns:
        Cleaned text
    """
    # Remove excessive whitespace
    text = re.sub(r'\s+', ' ', text)
    
    # Remove special characters that might interfere with JSON parsing
    text = text.replace('\n', ' ').replace('\r', ' ').replace('\t', ' ')
    
    # Trim whitespace
    text = text.strip()
    
    return text


def split_text(text: str, max_length: int = 4000) -> List[str]:
    """
    Split text into chunks for processing.

    Args:
        text: Text to split
        max_length: Maximum length of each chunk

    Returns:
        List of text chunks
    """
    # Clean the text first
    text = clean_text(text)
    
    # If the text is already short enough, return it as is
    if len(text) <= max_length:
        return [text]
    
    # Split the text into sentences
    sentences = re.split(r'(?<=[.!?])\s+', text)
    
    chunks = []
    current_chunk = ""
    
    for sentence in sentences:
        # If adding this sentence would exceed the max length, start a new chunk
        if len(current_chunk) + len(sentence) + 1 > max_length:
            if current_chunk:
                chunks.append(current_chunk)
            current_chunk = sentence
        else:
            if current_chunk:
                current_chunk += " " + sentence
            else:
                current_chunk = sentence
    
    # Add the last chunk if it's not empty
    if current_chunk:
        chunks.append(current_chunk)
    
    return chunks


def extract_json_from_text(text: str) -> Optional[Dict[str, Any]]:
    """
    Extract JSON from text.

    Args:
        text: Text containing JSON

    Returns:
        Extracted JSON as a dictionary, or None if no JSON was found
    """
    try:
        # Look for JSON-like structure in the response
        json_start = text.find('{')
        json_end = text.rfind('}') + 1
        
        if json_start >= 0 and json_end > json_start:
            json_str = text[json_start:json_end]
            import json
            return json.loads(json_str)
        
        return None
    except Exception as e:
        logger.error(f"Error extracting JSON from text: {e}")
        return None


def normalize_entity_name(name: str) -> str:
    """
    Normalize entity name.

    Args:
        name: Entity name to normalize

    Returns:
        Normalized entity name
    """
    # Remove leading/trailing whitespace
    name = name.strip()
    
    # Convert to title case for consistency
    name = name.title()
    
    # Handle special cases
    name = re.sub(r'\bAnd\b', 'and', name)
    name = re.sub(r'\bOf\b', 'of', name)
    name = re.sub(r'\bThe\b', 'the', name)
    name = re.sub(r'\bIn\b', 'in', name)
    name = re.sub(r'\bOn\b', 'on', name)
    name = re.sub(r'\bWith\b', 'with', name)
    
    return name
