"""
Entity deduplication processor for the worker system.

This module provides the processor function for entity deduplication tasks.
"""

import logging
from typing import Dict, Any, Callable, Optional

from entity_deduplication import EntityDeduplicator
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def process_entity_deduplication_task(
    task: Dict[str, Any],
    add_task_callback: Optional[Callable] = None
) -> Dict[str, Any]:
    """
    Process an entity deduplication task.
    
    Args:
        task: Entity deduplication task to process
        add_task_callback: Callback function to add new tasks to the queue
        
    Returns:
        Result of the entity deduplication
    """
    document_id = task.get("document_id")
    entity_type = task.get("entity_type")
    merge = task.get("merge", True)
    
    logger.info(f"Deduplicating entities for document {document_id}")
    
    # Initialize the entity deduplicator
    deduplicator = EntityDeduplicator()
    
    # Run deduplication
    result = await deduplicator.deduplicate_entities(
        document_id=document_id,
        entity_type=entity_type,
        merge=merge
    )
    
    logger.info(f"Deduplication completed for document {document_id}")
    logger.info(f"Found {result.duplicate_groups} duplicate groups with {result.total_duplicates} total duplicates")
    logger.info(f"Merged {result.merged_entities} entities")
    
    # If we have a callback, queue a notification task
    if add_task_callback:
        from workers.base import WorkerType
        
        await add_task_callback(
            WorkerType.DATABASE_WRITER,
            {
                "id": f"deduplication_{document_id}",
                "type": "deduplication_result",
                "document_id": document_id,
                "duplicates_found": result.total_duplicates,
                "duplicate_groups": result.duplicate_groups,
                "entities_merged": result.merged_entities,
                "processing_time": result.processing_time
            }
        )
    
    return {
        "success": True,
        "document_id": document_id,
        "duplicates_found": result.total_duplicates,
        "duplicate_groups": result.duplicate_groups,
        "entities_merged": result.merged_entities,
        "processing_time": result.processing_time
    }
