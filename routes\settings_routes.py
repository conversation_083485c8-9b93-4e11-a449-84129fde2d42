"""
Settings-related routes for the Graphiti application.

This module provides a unified interface for settings operations by delegating
to specialized route modules.
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any

from utils.logging_utils import get_logger
from routes.settings.model_routes import router as model_router, get_dynamic_available_models
from routes.settings.config_routes import router as config_router
from routes.settings.settings_utils import (
    AllSettings, update_env_file, get_current_settings,
    validate_llm_settings, validate_embedding_settings, validate_database_settings
)

# Set up logger
logger = get_logger(__name__)

# Create main router
router = APIRouter(prefix="/api", tags=["settings"])

# Include sub-routers
router.include_router(model_router, prefix="", tags=["models"])
router.include_router(config_router, prefix="", tags=["config"])


@router.get("/settings")
async def get_all_settings():
    """
    Get all current settings.

    Returns:
        All application settings
    """
    try:
        current_settings = get_current_settings()
        available_models = await get_dynamic_available_models()

        return {
            **current_settings,
            "available_models": available_models
        }
    except Exception as e:
        logger.error(f"Error getting all settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting settings: {str(e)}"
        )


@router.post("/settings")
async def update_all_settings(settings: dict):
    """
    Update all settings.

    Args:
        settings: All settings as a dictionary

    Returns:
        Updated settings
    """
    try:
        updated_sections = []

        # Update LLM settings
        if 'llm_settings' in settings and settings['llm_settings']:
            llm_settings = settings['llm_settings']

            # Validate LLM settings
            if not validate_llm_settings(llm_settings):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid LLM settings"
                )

            config_updates = {
                "QA_LLM_PROVIDER": llm_settings.get('provider', 'openrouter'),
                "QA_LLM_MODEL": llm_settings.get('model', 'meta-llama/llama-4-maverick')
            }

            # Add LLM parameters if provided
            if 'temperature' in llm_settings:
                config_updates["QA_LLM_TEMPERATURE"] = str(llm_settings['temperature'])
            if 'max_tokens' in llm_settings:
                config_updates["QA_LLM_MAX_TOKENS"] = str(llm_settings['max_tokens'])
            if 'top_k' in llm_settings:
                config_updates["QA_LLM_TOP_K"] = str(llm_settings['top_k'])
            if 'top_p' in llm_settings:
                config_updates["QA_LLM_TOP_P"] = str(llm_settings['top_p'])

            # Update API key if provided
            if 'api_key' in llm_settings and llm_settings['api_key']:
                if llm_settings.get('provider') == "openrouter":
                    config_updates["OPENROUTER_API_KEY"] = llm_settings['api_key']
                elif llm_settings.get('provider') == "openai":
                    config_updates["OPENAI_API_KEY"] = llm_settings['api_key']

            # Update USE_LOCAL_LLM if provided
            if 'use_local' in llm_settings:
                config_updates["USE_LOCAL_LLM"] = str(llm_settings['use_local']).lower()

            # Update the .env file
            if update_env_file(config_updates):
                updated_sections.append("llm")

        # Update embedding settings
        if 'embedding_settings' in settings and settings['embedding_settings']:
            embedding_settings = settings['embedding_settings']

            # Validate embedding settings
            if not validate_embedding_settings(embedding_settings):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid embedding settings"
                )

            config_updates = {
                "EMBEDDING_PROVIDER": embedding_settings.get('provider', 'openai'),
                "EMBEDDING_MODEL": embedding_settings.get('model', 'text-embedding-3-small'),
                "USE_LOCAL_EMBEDDINGS": str(embedding_settings.get('use_local', False)).lower(),
                "CHUNK_SIZE": str(embedding_settings.get('chunk_size', 1200)),
                "CHUNK_OVERLAP": str(embedding_settings.get('chunk_overlap', 0)),
                "RECURSIVE_CHUNKING": str(embedding_settings.get('recursive_chunking', True)).lower()
            }

            # Update the .env file
            if update_env_file(config_updates):
                updated_sections.append("embedding")

        # Update database settings
        if 'database_settings' in settings and settings['database_settings']:
            database_settings = settings['database_settings']

            # Validate database settings
            if not validate_database_settings(database_settings):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid database settings"
                )

            config_updates = {
                "FALKORDB_HOST": database_settings.get('host', 'localhost'),
                "FALKORDB_PORT": str(database_settings.get('port', 6379))
            }

            # Update the .env file
            if update_env_file(config_updates):
                updated_sections.append("database")

        # Update system settings
        if 'system_settings' in settings and settings['system_settings']:
            system_settings = settings['system_settings']
            config_updates = {
                "MAX_FILE_SIZE": str(system_settings.get('max_file_size', 50)),
                "MAX_PARALLEL_PROCESSES": str(system_settings.get('max_parallel_processes', 4)),
                "LOG_LEVEL": system_settings.get('log_level', 'INFO')
            }

            # Update the .env file
            if update_env_file(config_updates):
                updated_sections.append("system")

        return {
            "success": True,
            "updated_sections": updated_sections,
            "message": f"Updated {len(updated_sections)} setting sections"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating all settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error updating settings: {str(e)}"
        )


@router.get("/settings/current")
async def get_current_settings_endpoint():
    """
    Get current settings from environment variables.

    Returns:
        Current application settings
    """
    try:
        current_settings = get_current_settings()
        available_models = await get_dynamic_available_models()

        return {
            **current_settings,
            "available_models": available_models
        }
    except Exception as e:
        logger.error(f"Error getting current settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting current settings: {str(e)}"
        )


@router.post("/settings/reset")
async def reset_settings():
    """
    Reset settings to default values.

    Returns:
        Success message
    """
    try:
        default_config = {
            "QA_LLM_PROVIDER": "openrouter",
            "QA_LLM_MODEL": "meta-llama/llama-4-maverick",
            "QA_LLM_TEMPERATURE": "0.7",
            "QA_LLM_MAX_TOKENS": "2048",
            "QA_LLM_TOP_K": "40",
            "QA_LLM_TOP_P": "0.9",
            "USE_LOCAL_LLM": "false",
            "EMBEDDING_PROVIDER": "openai",
            "EMBEDDING_MODEL": "text-embedding-3-small",
            "USE_LOCAL_EMBEDDINGS": "false",
            "CHUNK_SIZE": "1200",
            "CHUNK_OVERLAP": "0",
            "RECURSIVE_CHUNKING": "true",
            "FALKORDB_HOST": "localhost",
            "FALKORDB_PORT": "6379",
            "MAX_FILE_SIZE": "50",
            "MAX_PARALLEL_PROCESSES": "4",
            "LOG_LEVEL": "INFO"
        }

        if update_env_file(default_config):
            return {
                "success": True,
                "message": "Settings reset to default values"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to reset settings"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resetting settings: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error resetting settings: {str(e)}"
        )
