"""
Simple script to test if the local LLM (Ollama) is working correctly.
"""

import os
import sys
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get Ollama configuration
ollama_base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')
qa_llm_model = os.environ.get('QA_LLM_MODEL', 'qwen3:4b')

print(f"Using Ollama base URL: {ollama_base_url}")
print(f"Using model: {qa_llm_model}")

try:
    # Test simple completion with Ollama
    print("Sending test request to Ollama API...")

    response = requests.post(
        f"{ollama_base_url}/api/chat",
        json={
            "model": qa_llm_model,
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "Tell me about antioxidants in 2-3 sentences."}
            ],
            "stream": False,
            "options": {
                "temperature": 0.3,
                "num_predict": 100
            }
        }
    )

    if response.status_code == 200:
        result = response.json()
        print("\nResponse from Ollama:")
        print(f"Model used: {result.get('model', 'unknown')}")
        print(f"Answer: {result.get('message', {}).get('content', 'No content')}")
        print("\nAPI test successful!")
    else:
        print(f"\nError: Received status code {response.status_code}")
        print(f"Response: {response.text}")
        print("\nAPI test failed.")

except Exception as e:
    print(f"\nError using Ollama API: {e}")
    print("\nAPI test failed.")
