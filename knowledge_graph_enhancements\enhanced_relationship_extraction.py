"""
Module for enhanced relationship extraction between entities in the knowledge graph.
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from neo4j import AsyncGraphDatabase
from dotenv import load_dotenv
import openai

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Get Neo4j connection details
neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7689')
neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

# Get OpenAI API key
openai_api_key = os.environ.get('OPENAI_API_KEY')

async def get_neo4j_driver():
    """Get a Neo4j driver."""
    driver = AsyncGraphDatabase.driver(
        neo4j_uri,
        auth=(neo4j_user, neo4j_password)
    )
    return driver

def load_taxonomy():
    """Load the taxonomy from the JSON file."""
    try:
        with open('taxonomy.json', 'r') as f:
            taxonomy_data = json.load(f)
        return taxonomy_data
    except Exception as e:
        logger.error(f"Error loading taxonomy: {e}")
        return None

async def get_relationship_types(driver):
    """Get all relationship types from the knowledge graph."""
    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (r:RelationshipType)
                RETURN r.name AS name, r.description AS description, r.example AS example
                """
            )

            relationship_types = []
            async for record in result:
                relationship_types.append({
                    "name": record["name"],
                    "description": record["description"],
                    "example": record["example"]
                })

            return relationship_types
    except Exception as e:
        logger.error(f"Error getting relationship types: {e}")
        return []

async def get_facts_with_multiple_entities(driver, batch_size=5):
    """Get facts that mention multiple entities but don't have specific relationships between them."""
    logger.info(f"Getting facts with multiple entities (batch size: {batch_size})")

    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)-[:MENTIONS]->(e1:Entity)
                MATCH (f)-[:MENTIONS]->(e2:Entity)
                WHERE e1 <> e2
                AND NOT EXISTS((e1)-[:IS_A|PART_OF|TREATS|CAUSES|PREVENTS|ASSOCIATED_WITH|STUDIED_BY|CONTAINS|INTERACTS_WITH|CONTRAINDICATES]->(e2))
                AND NOT EXISTS((e2)-[:IS_A|PART_OF|TREATS|CAUSES|PREVENTS|ASSOCIATED_WITH|STUDIED_BY|CONTAINS|INTERACTS_WITH|CONTRAINDICATES]->(e1))
                WITH f, collect(DISTINCT e1) AS entities
                WHERE size(entities) > 1
                RETURN f.uuid AS uuid, f.body AS body
                LIMIT $batch_size
                """,
                {"batch_size": batch_size}
            )

            facts = []
            async for record in result:
                facts.append({
                    "uuid": record["uuid"],
                    "body": record["body"]
                })

            logger.info(f"Found {len(facts)} facts with multiple entities without specific relationships")
            return facts
    except Exception as e:
        logger.error(f"Error getting facts with multiple entities: {e}")
        return []

async def extract_specific_relationships(driver, api_key, fact, relationship_types):
    """Extract specific relationships between entities mentioned in a fact."""
    logger.info(f"Extracting specific relationships for fact {fact['uuid']}")

    try:
        async with driver.session() as session:
            # Get entities mentioned in this fact
            result = await session.run(
                """
                MATCH (f:Fact {uuid: $fact_uuid})-[:MENTIONS]->(e:Entity)
                RETURN e.uuid AS uuid, e.name AS name, e.type AS type, e.description AS description
                """,
                {"fact_uuid": fact["uuid"]}
            )

            entities = []
            async for record in result:
                entities.append({
                    "uuid": record["uuid"],
                    "name": record["name"],
                    "type": record["type"],
                    "description": record["description"]
                })

            if len(entities) < 2:
                logger.info(f"Not enough entities found for fact {fact['uuid']}")
                return 0

            # Create a system prompt that specifies the relationship extraction task
            entity_names = [e["name"] for e in entities]
            entity_types = {e["name"]: e["type"] for e in entities}

            # Format relationship types for the prompt
            relationship_descriptions = []
            for rel_type in relationship_types:
                relationship_descriptions.append(f"{rel_type['name']}: {rel_type['description']} (Example: {rel_type['example']})")

            system_prompt = f"""
            Analyze the relationships between these entities mentioned in the text:
            {', '.join(entity_names)}

            For each pair of related entities, provide:
            1. source: The name of the source entity
            2. target: The name of the target entity
            3. relationship: One of the following specific relationship types:
               {', '.join([rel['name'] for rel in relationship_types])}
            4. confidence: A number between 0 and 1 indicating your confidence in this relationship
            5. evidence: The specific text from the document that supports this relationship

            Relationship types and their descriptions:
            {chr(10).join(relationship_descriptions)}

            Return a JSON object with a "relationships" array containing the extracted relationships.
            If no relationships are found, return an empty array.

            Example output format:
            {{
                "relationships": [
                    {{
                        "source": "Vitamin C",
                        "target": "Cancer",
                        "relationship": "TREATS",
                        "confidence": 0.8,
                        "evidence": "Vitamin C has been used in the treatment of cancer"
                    }},
                    {{
                        "source": "Smoking",
                        "target": "Cancer",
                        "relationship": "CAUSES",
                        "confidence": 0.95,
                        "evidence": "Smoking is a known cause of cancer"
                    }}
                ]
            }}
            """

            # Use OpenAI for relationship extraction
            logger.info("Using OpenAI for enhanced relationship extraction")
            client = openai.OpenAI(api_key=api_key)

            response = client.chat.completions.create(
                model=os.environ.get('ENTITY_EXTRACTION_MODEL', 'gpt-4.1-mini'),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": fact["body"]}
                ],
                response_format={"type": "json_object"}
            )

            # Parse the response
            content = response.choices[0].message.content

            # Process the content
            try:
                data = json.loads(content)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON response: {e}")
                logger.error(f"Response content: {content}")
                data = {"relationships": []}

            relationships = data.get("relationships", [])
            logger.info(f"Extracted {len(relationships)} specific relationships from fact {fact['uuid']}")

            # Create relationships in Neo4j
            created_count = 0
            for rel in relationships:
                source = rel["source"]
                target = rel["target"]
                relationship_type = rel["relationship"]
                confidence = rel.get("confidence", 0.5)
                evidence = rel.get("evidence", "")

                # Validate that source and target entities exist
                if source not in entity_names or target not in entity_names:
                    logger.warning(f"Source or target entity not found: {source} -> {target}")
                    continue

                # Create the relationship
                # Use a parameterized query to avoid issues with special relationship names like DECREASES
                result = await session.run(
                    """
                    MATCH (e1:Entity {name: $source, type: $source_type})
                    MATCH (e2:Entity {name: $target, type: $target_type})
                    WITH e1, e2
                    CALL apoc.create.relationship(e1, $relationship_type, {
                        confidence: $confidence,
                        evidence: $evidence,
                        fact_uuid: $fact_uuid,
                        created_at: datetime($timestamp)
                    }, e2) YIELD rel
                    RETURN type(rel) AS type
                    """,
                    {
                        "source": source,
                        "source_type": entity_types[source],
                        "target": target,
                        "target_type": entity_types[target],
                        "relationship_type": relationship_type,
                        "confidence": confidence,
                        "evidence": evidence,
                        "fact_uuid": fact["uuid"],
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
                )
                await result.consume()
                created_count += 1

            return created_count
    except Exception as e:
        logger.error(f"Error extracting specific relationships: {e}")
        return 0

async def extract_hierarchical_relationships(driver, api_key, batch_size=5, max_batches=None):
    """Extract hierarchical relationships (IS_A, PART_OF) between entities."""
    logger.info("Extracting hierarchical relationships between entities")

    try:
        # Get relationship types
        relationship_types = await get_relationship_types(driver)
        if not relationship_types:
            logger.error("No relationship types found")
            return 0, 0

        batch_count = 0
        total_processed = 0
        total_relationships = 0

        while True:
            # Check if we've reached the maximum number of batches
            if max_batches is not None and batch_count >= max_batches:
                logger.info(f"Reached maximum number of batches ({max_batches})")
                break

            # Get facts with multiple entities
            facts = await get_facts_with_multiple_entities(driver, batch_size)

            if not facts:
                logger.info("No more facts with multiple entities without specific relationships")
                break

            # Process each fact
            for fact in facts:
                # Extract specific relationships
                relationships_created = await extract_specific_relationships(driver, api_key, fact, relationship_types)
                total_relationships += relationships_created

            batch_count += 1
            total_processed += len(facts)

            logger.info(f"Processed batch {batch_count} ({total_processed} facts, {total_relationships} relationships total)")

            # Sleep briefly to avoid rate limits
            await asyncio.sleep(1)

        logger.info(f"Finished extracting hierarchical relationships from {total_processed} facts ({total_relationships} relationships total)")
        return total_processed, total_relationships
    except Exception as e:
        logger.error(f"Error extracting hierarchical relationships: {e}")
        return 0, 0

async def get_relationship_statistics(driver):
    """Get statistics about relationships in the knowledge graph."""
    logger.info("Getting relationship statistics")

    try:
        async with driver.session() as session:
            # Count relationships by type
            result = await session.run(
                """
                MATCH (e1:Entity)-[r]->(e2:Entity)
                WHERE type(r) <> 'MENTIONS' AND type(r) <> 'RELATED_TO'
                RETURN type(r) AS type, count(r) AS count
                ORDER BY count DESC
                """
            )

            relationship_counts = []
            async for record in result:
                relationship_counts.append({
                    "type": record["type"],
                    "count": record["count"]
                })

            logger.info("Relationship counts by type:")
            for rel_count in relationship_counts:
                logger.info(f"  {rel_count['type']}: {rel_count['count']}")

            # Get top entity pairs by relationship count
            result = await session.run(
                """
                MATCH (e1:Entity)-[r]->(e2:Entity)
                WHERE type(r) <> 'MENTIONS' AND type(r) <> 'RELATED_TO'
                WITH e1, e2, count(r) AS rel_count
                ORDER BY rel_count DESC
                LIMIT 10
                RETURN e1.name AS source, e1.type AS source_type,
                       e2.name AS target, e2.type AS target_type,
                       rel_count
                """
            )

            top_entity_pairs = []
            async for record in result:
                top_entity_pairs.append({
                    "source": record["source"],
                    "source_type": record["source_type"],
                    "target": record["target"],
                    "target_type": record["target_type"],
                    "rel_count": record["rel_count"]
                })

            logger.info("Top entity pairs by relationship count:")
            for pair in top_entity_pairs:
                logger.info(f"  {pair['source']} ({pair['source_type']}) -> {pair['target']} ({pair['target_type']}): {pair['rel_count']} relationships")

            return {
                "relationship_counts": relationship_counts,
                "top_entity_pairs": top_entity_pairs
            }
    except Exception as e:
        logger.error(f"Error getting relationship statistics: {e}")
        return {}

async def main():
    """Main function to extract enhanced relationships between entities."""
    # Check if OpenAI API key is available
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return

    try:
        # Connect to Neo4j
        driver = await get_neo4j_driver()

        # Check command line arguments
        import sys
        if len(sys.argv) < 2:
            logger.error("Please specify a command: extract-relationships or stats")
            logger.info("Usage:")
            logger.info("  python enhanced_relationship_extraction.py extract-relationships [batch_size] [max_batches]")
            logger.info("  python enhanced_relationship_extraction.py stats")
            return

        command = sys.argv[1].lower()

        if command == "extract-relationships":
            # Get optional parameters
            batch_size = 5
            max_batches = None

            if len(sys.argv) >= 3:
                try:
                    batch_size = int(sys.argv[2])
                except ValueError:
                    logger.warning(f"Invalid batch_size value: {sys.argv[2]}. Using default: 5")

            if len(sys.argv) >= 4:
                try:
                    max_batches = int(sys.argv[3])
                except ValueError:
                    logger.warning(f"Invalid max_batches value: {sys.argv[3]}. Using default: None (unlimited)")

            # Extract hierarchical relationships
            await extract_hierarchical_relationships(driver, openai_api_key, batch_size, max_batches)

        elif command == "stats":
            # Get relationship statistics
            await get_relationship_statistics(driver)

        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: extract-relationships, stats")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
