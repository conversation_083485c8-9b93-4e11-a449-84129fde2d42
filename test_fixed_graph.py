#!/usr/bin/env python3
"""
Test the fixed knowledge graph endpoint
"""

import requests

def test_fixed_graph():
    """Test the updated knowledge graph endpoint"""
    print('Testing fixed knowledge graph endpoint...')
    
    # Test the updated knowledge graph endpoint
    response = requests.get('http://localhost:9753/api/knowledge-graph?limit=10')
    if response.status_code == 200:
        data = response.json()
        nodes = data.get('nodes', [])
        relationships = data.get('relationships', [])
        
        print(f'Nodes: {len(nodes)}')
        print(f'Relationships: {len(relationships)}')
        
        if nodes:
            print('\nSample nodes:')
            for i, node in enumerate(nodes[:3]):
                print(f'  {i+1}. {node["name"]} ({node["type"]}) - {node["uuid"]}')
        
        if relationships:
            print('\nSample relationships:')
            for i, rel in enumerate(relationships[:3]):
                print(f'  {i+1}. {rel["source"]} --[{rel["type"]}]--> {rel["target"]}')
        else:
            print('No relationships found')
            
        return len(nodes) > 0 and len(relationships) > 0
    else:
        print(f'Error: {response.status_code}')
        print(f'Response: {response.text}')
        return False

if __name__ == "__main__":
    success = test_fixed_graph()
    if success:
        print('\n✅ Knowledge graph is working!')
    else:
        print('\n❌ Knowledge graph still has issues')
