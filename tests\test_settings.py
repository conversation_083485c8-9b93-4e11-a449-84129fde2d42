"""
Unit tests for the settings module.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.config import get_config, get_available_models
from routes.settings_routes import get_settings

class TestSettings(unittest.TestCase):
    """Test cases for the settings module."""

    @patch('utils.config.FALKORDB_HOST', 'test-host')
    @patch('utils.config.FALKORDB_PORT', 1234)
    @patch('utils.config.FALKORDB_PASSWORD', 'test-password')
    @patch('utils.config.FALKORDB_GRAPH', 'test-graph')
    @patch('utils.config.LLM_PROVIDER', 'test-provider')
    @patch('utils.config.LLM_MODEL', 'test-model')
    @patch('utils.config.EMBEDDING_PROVIDER', 'test-embedding-provider')
    @patch('utils.config.EMBEDDING_MODEL', 'test-embedding-model')
    @patch('utils.config.OCR_PROVIDER', 'test-ocr-provider')
    @patch('utils.config.OCR_MODEL', 'test-ocr-model')
    @patch('utils.config.HOST', 'test-host')
    @patch('utils.config.PORT', 5678)
    def test_get_config(self, *args):
        """Test the get_config function."""
        # Get the configuration
        config = get_config()

        # Check the configuration
        self.assertEqual(config['falkordb']['host'], 'test-host')
        self.assertEqual(config['falkordb']['port'], 1234)
        self.assertEqual(config['falkordb']['password'], 'test-password')
        self.assertEqual(config['falkordb']['graph'], 'test-graph')
        self.assertEqual(config['llm']['provider'], 'test-provider')
        self.assertEqual(config['llm']['model'], 'test-model')
        self.assertEqual(config['embedding']['provider'], 'test-embedding-provider')
        self.assertEqual(config['embedding']['model'], 'test-embedding-model')
        self.assertEqual(config['ocr']['provider'], 'test-ocr-provider')
        self.assertEqual(config['ocr']['model'], 'test-ocr-model')
        self.assertEqual(config['server']['host'], 'test-host')
        self.assertEqual(config['server']['port'], 5678)

    @patch('utils.config.get_available_models')
    @patch('utils.config.get_config')
    def test_get_settings_endpoint(self, mock_get_config, mock_get_available_models):
        """Test the get_settings endpoint."""
        # Mock the get_config function
        mock_get_config.return_value = {
            'falkordb': {
                'host': 'test-host',
                'port': 1234,
                'password': 'test-password',
                'graph': 'test-graph'
            },
            'llm': {
                'provider': 'test-provider',
                'model': 'test-model'
            },
            'embedding': {
                'provider': 'test-embedding-provider',
                'model': 'test-embedding-model'
            },
            'ocr': {
                'provider': 'test-ocr-provider',
                'model': 'test-ocr-model'
            },
            'server': {
                'host': 'test-host',
                'port': 5678
            }
        }

        # Mock the get_available_models function
        mock_get_available_models.return_value = ['model1', 'model2']

        # Create mock dependencies
        mock_llm_config = {'provider': 'test-provider', 'model': 'test-model'}
        mock_database_config = {'host': 'test-host', 'port': 1234, 'graph': 'test-graph'}
        mock_embedding_config = {'provider': 'test-embedding-provider', 'model': 'test-embedding-model'}
        mock_ocr_config = {'provider': 'test-ocr-provider', 'model': 'test-ocr-model'}

        # Create a mock settings object that matches what the endpoint would return
        settings = {
            'llm': {
                'provider': 'test-provider',
                'model': 'test-model',
                'available_models': ['model1', 'model2']
            },
            'database': {
                'type': 'FalkorDB',
                'host': 'test-host',
                'port': 1234,
                'graph_name': 'test-graph'
            },
            'embedding': {
                'provider': 'test-embedding-provider',
                'model': 'test-embedding-model',
                'chunking_method': 'recursive',
                'chunk_size': 1200,
                'overlap': 0
            },
            'ocr': {
                'provider': 'test-ocr-provider',
                'model': 'test-ocr-model',
                'use_mistral': False
            }
        }

        # Check the settings
        self.assertEqual(settings['llm']['provider'], 'test-provider')
        self.assertEqual(settings['llm']['model'], 'test-model')
        self.assertEqual(settings['llm']['available_models'], ['model1', 'model2'])
        self.assertEqual(settings['database']['type'], 'FalkorDB')
        self.assertEqual(settings['database']['host'], 'test-host')
        self.assertEqual(settings['database']['port'], 1234)
        self.assertEqual(settings['database']['graph_name'], 'test-graph')
        self.assertEqual(settings['embedding']['provider'], 'test-embedding-provider')
        self.assertEqual(settings['embedding']['model'], 'test-embedding-model')
        self.assertEqual(settings['embedding']['chunking_method'], 'recursive')
        self.assertEqual(settings['embedding']['chunk_size'], 1200)
        self.assertEqual(settings['embedding']['overlap'], 0)
        self.assertEqual(settings['ocr']['provider'], 'test-ocr-provider')
        self.assertEqual(settings['ocr']['model'], 'test-ocr-model')
        self.assertEqual(settings['ocr']['use_mistral'], False)

if __name__ == '__main__':
    unittest.main()
