<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Graphiti Knowledge Graph Explorer{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            padding-top: 20px;
        }
        .result-card {
            margin-bottom: 15px;
            border-left: 4px solid #0d6efd;
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .score-badge {
            float: right;
            margin-left: 10px;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .source-link {
            font-size: 0.8rem;
            color: #6c757d;
        }
        #graph-container {
            background-color: #f8f9fa;
        }
        .vis-network {
            outline: none;
        }
        #dropzone {
            border: 2px dashed #0d6efd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            background-color: #f8f9fa;
            transition: all 0.3s ease;
        }
        #dropzone.dragover {
            background-color: #e3f2fd;
            border-color: #0d6efd;
        }
        .dropzone-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .dropzone-content i {
            font-size: 48px;
            color: #0d6efd;
            margin-bottom: 15px;
        }
        .file-item {
            padding: 10px;
            margin-bottom: 10px;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
    </style>
    {% block head %}{% endblock %}
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graphiti Knowledge Graph Explorer</h1>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs mb-4" id="mainTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/" id="home-tab">Home</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if request.url.path == '/batch-upload' %}active{% endif %}" href="/batch-upload" id="batch-upload-tab"><i class="bi bi-files"></i> Batch Upload</a>
            </li>
            <li class="nav-item" role="presentation">
                <a class="nav-link {% if request.url.path == '/docs' %}active{% endif %}" href="/docs" id="docs-tab">API Docs</a>
            </li>
        </ul>

        <!-- Main Content -->
        <div class="main-content">
            {% block content %}{% endblock %}
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
