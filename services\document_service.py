"""
Document processing service for the Graphiti application.
"""

import os
import uuid
from pathlib import Path
from typing import Dict, Any, Union
from datetime import datetime

from fastapi import UploadFile, HTTPException

from utils.config import UPLOADS_DIR
from utils.logging_utils import get_logger
from utils.file_utils import save_uploaded_file, get_file_category
from utils.text_utils import split_text_recursively
from database.database_service import get_falkordb_adapter, create_episode_node, create_fact_node
from models.document_simplified import (
    DocumentProcessingOptions, DocumentUploadResponse, DocumentType,
    DocumentSummary, DocumentList, DocumentDetails
)

# Import PDF processor
from processors.pdf_processor import PDFProcessor

# Set up logger
logger = get_logger(__name__)

async def process_document(
    file: UploadFile,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a document file.

    Args:
        file: Uploaded file
        options: Processing options

    Returns:
        Processing result
    """
    # Generate a unique ID for the file
    file_id = str(uuid.uuid4())

    # Save the file
    file_content = await file.read()
    file_path = save_uploaded_file(file_content, file.filename, UPLOADS_DIR)

    logger.info(f"File saved: {file_path}")

    # Determine file type
    file_category = get_file_category(file.filename)

    # Process based on file type
    if file_category == 'pdf':
        return await process_pdf_document(file_path, file_id, options)
    elif file_category == 'text':
        return await process_text_document(file_path, file_id, options)
    elif file_category == 'word':
        return await process_word_document(file_path, file_id, options)
    elif file_category == 'html':
        return await process_html_document(file_path, file_id, options)
    else:
        return {
            "error": f"Unsupported file type: {file_category}",
            "success": False
        }

async def process_pdf_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a PDF document.

    Args:
        file_path: Path to the PDF file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing PDF document: {file_path} with chunk_size={options.chunk_size}, overlap={options.overlap}")

    # Process the PDF using the PDF processor
    pdf_processor = PDFProcessor()
    result = await pdf_processor.process_file(
        file_path=str(file_path),
        chunk_size=options.chunk_size,
        overlap=options.overlap
    )

    return result

async def process_text_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a text document.

    Args:
        file_path: Path to the text file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing text document: {file_path}")

    try:
        # Read the text file
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()

        # Split text into chunks
        chunks = split_text_recursively(text, options.chunk_size, options.overlap)

        # Create Episode node
        episode_name = os.path.basename(file_path)
        episode_uuid = await create_episode_node(episode_name, {
            "file_path": str(file_path),
            "file_id": file_id,
            "processed_at": datetime.now().isoformat()
        })

        # Create Fact nodes
        for i, chunk in enumerate(chunks):
            await create_fact_node(chunk, episode_uuid, {
                "chunk_num": i + 1,
                "chunk_size": len(chunk)
            })

        return {
            "success": True,
            "episode_id": episode_uuid,
            "chunks": len(chunks),
            "file_path": str(file_path),
            "file_id": file_id,
            "ocr_provider": "text"
        }

    except Exception as e:
        logger.error(f"Error processing text document: {e}")
        return {
            "error": f"Error processing text document: {str(e)}",
            "success": False
        }

async def process_word_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a Word document.

    Args:
        file_path: Path to the Word file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing Word document: {file_path}")

    # TODO: Implement Word document processing
    return {
        "error": "Word document processing not yet implemented",
        "success": False
    }

async def process_html_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process an HTML document.

    Args:
        file_path: Path to the HTML file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing HTML document: {file_path}")

    # TODO: Implement HTML document processing
    return {
        "error": "HTML document processing not yet implemented",
        "success": False
    }

async def get_document_list(page: int = 1, page_size: int = 10) -> DocumentList:
    """
    Get a list of documents.

    Args:
        page: Page number
        page_size: Number of documents per page

    Returns:
        DocumentList: List of documents conforming to the DocumentList model

    Raises:
        HTTPException: If there is an error retrieving the documents
    """
    try:
        adapter = await get_falkordb_adapter()

        # Query to get documents
        query = f"""
        MATCH (e:Episode)
        RETURN e.uuid as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at
        ORDER BY e.processed_at DESC
        SKIP {(page - 1) * page_size}
        LIMIT {page_size}
        """

        result = adapter.execute_cypher(query)

        documents = []
        if result and len(result) > 1:
            headers = result[0]
            for row in result[1]:
                doc = {}
                for i, header in enumerate(headers):
                    doc[header] = row[i]

                # Get document details
                doc_details = await get_document_details(doc["uuid"])
                doc.update(doc_details)

                # Format document to match DocumentSummary model
                file_type = doc["file_type"] if doc["file_type"] in [e.value for e in DocumentType] else DocumentType.OTHER

                try:
                    document_summary = DocumentSummary(
                        uuid=doc["uuid"],
                        filename=doc["name"],
                        file_type=file_type,
                        upload_date=datetime.fromisoformat(doc["processed_at"]) if doc["processed_at"] else datetime.now(),
                        chunks=doc.get("chunks", 0),
                        entities=doc.get("entities", 0),
                        references=doc.get("references", 0)
                    )
                    documents.append(document_summary)
                except Exception as validation_error:
                    logger.warning(f"Validation error for document {doc['uuid']}: {str(validation_error)}")
                    # Skip invalid documents instead of failing the entire request
                    continue

        # Get total count
        count_query = """
        MATCH (e:Episode)
        RETURN count(e) as count
        """

        count_result = adapter.execute_cypher(count_query)
        total = 0
        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            total = count_result[1][0][0]

        # Create the response data using the Pydantic model
        try:
            document_list = DocumentList(
                documents=documents,
                total=total,
                page=page,
                page_size=page_size
            )
            return document_list
        except Exception as validation_error:
            logger.error(f"Validation error for document list: {str(validation_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error validating document list: {str(validation_error)}"
            )

    except Exception as e:
        logger.error(f"Error getting document list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document list: {str(e)}")

async def get_document_count() -> int:
    """
    Get the total number of documents.

    Returns:
        Total number of documents
    """
    try:
        adapter = await get_falkordb_adapter()

        query = """
        MATCH (e:Episode)
        RETURN COUNT(e) AS count
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            return result[1][0][0]

        return 0

    except Exception as e:
        logger.error(f"Error getting document count: {str(e)}")
        return 0

async def get_document_details(document_id: str) -> DocumentDetails:
    """
    Get details of a document.

    Args:
        document_id: Document ID

    Returns:
        DocumentDetails: Document details with validated data

    Raises:
        HTTPException: If there is an error retrieving the document details
    """
    try:
        adapter = await get_falkordb_adapter()

        # Query to get document details by episode UUID
        query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})
        OPTIONAL MATCH (e)-[:CONTAINS]->(f:Fact)
        OPTIONAL MATCH (f)-[:MENTIONS]->(entity:Entity)
        RETURN e.uuid as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at,
               count(DISTINCT f) as chunks, count(DISTINCT entity) as entities
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            headers = result[0]
            row = result[1][0]

            doc = {}
            for i, header in enumerate(headers):
                doc[header] = row[i]

            # Determine file type
            if "file_path" in doc and doc["file_path"]:
                file_type = get_file_category(doc["file_path"])
                # Ensure file_type is a valid enum value
                if file_type not in [e.value for e in DocumentType]:
                    logger.warning(f"Invalid file_type: {file_type}, defaulting to 'other'")
                    file_type = DocumentType.OTHER
            else:
                file_type = DocumentType.OTHER

            # References are handled separately from the graph database (in CSV)
            # So we don't count them here - they're accessed via the /api/references endpoint
            references_count = 0

            # Create the document details using the Pydantic model
            try:
                document_details = DocumentDetails(
                    uuid=doc["uuid"],
                    name=doc["name"],
                    file_path=doc.get("file_path"),
                    processed_at=doc.get("processed_at"),
                    chunks=int(doc.get("chunks", 0)),
                    entities=int(doc.get("entities", 0)),
                    references=int(references_count),
                    file_type=file_type
                )
                return document_details
            except Exception as validation_error:
                logger.error(f"Validation error for document {document_id}: {str(validation_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error validating document details: {str(validation_error)}"
                )

        # Return default document details for non-existent document
        try:
            default_details = DocumentDetails(
                uuid=document_id,
                name="Unknown document",
                file_path=None,
                processed_at=None,
                chunks=0,
                entities=0,
                references=0,
                file_type=DocumentType.OTHER
            )
            return default_details
        except Exception as validation_error:
            logger.error(f"Validation error for default document details: {str(validation_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating default document details: {str(validation_error)}"
            )

    except Exception as e:
        logger.error(f"Error getting document details for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document details: {str(e)}")
