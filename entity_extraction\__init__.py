"""
Entity extraction package for the Graphiti Knowledge Graph.

This package contains modules for extracting entities and relationships from text.
"""

# Import main classes and functions for easier access
from entity_extraction.base import EntityExtractor, ENTITY_TYPES
from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
from entity_extraction.main import (
    extract_entities_from_text,
    extract_entities_from_facts,
    extract_relationships_between_entities,
    get_entity_statistics
)
from entity_extraction.models.entity import Entity
from entity_extraction.models.relationship import Relationship
from entity_extraction.processors.entity_processor import EntityProcessor
from entity_extraction.processors.relationship_processor import RelationshipProcessor

# Define public API
__all__ = [
    'EntityExtractor',
    'ENTITY_TYPES',
    'LLMEntityExtractor',
    'extract_entities_from_text',
    'extract_entities_from_facts',
    'extract_relationships_between_entities',
    'get_entity_statistics',
    'Entity',
    'Relationship',
    'EntityProcessor',
    'RelationshipProcessor'
]
