"""
Unified document processing service for the Graphiti application.

This service provides a complete workflow for processing documents:
1. Extract text from documents
2. Chunk the text
3. Store the chunks in the database
4. Extract entities from the chunks
5. Deduplicate entities
6. Extract references from the document
7. Generate embeddings for the chunks
"""

import os
import asyncio
from typing import Dict, Any, Union
from pathlib import Path

from processors.pdf_processor import PDFProcessor
from utils.config import get_config
from utils.logging_utils import get_logger
from utils.file_utils import is_supported_file
from utils.progress_tracker import ProgressTracker
from services.entity_processor import EntityProcessor
from services.reference_processor import ReferenceProcessor
from services.metadata_processor import MetadataProcessor
from services.embedding_processor import EmbeddingProcessor

# Set up logger
logger = get_logger(__name__)

class DocumentProcessingService:
    """Service for processing documents in a unified workflow."""

    def __init__(self):
        """Initialize the document processing service."""
        self.config = get_config()

        # Initialize modular processors
        self.entity_processor = EntityProcessor()
        self.reference_processor = ReferenceProcessor(llm_provider=self.config['llm']['provider'])
        self.metadata_processor = MetadataProcessor()
        self.embedding_processor = EmbeddingProcessor()

        # Processing state
        self.processing_queue = asyncio.Queue()
        self.is_processing = False
        self.processed_count = 0
        self.failed_count = 0
        self.current_document = None
        self.max_parallel_processes = 4
        self.active_processes = 0
        self.semaphore = asyncio.Semaphore(self.max_parallel_processes)
        self.document_progress = {}
        self.current_document_progress = {
            "document_id": None,
            "filename": None,
            "total_steps": 6,  # Added deduplication step
            "current_step": 0,
            "step_name": "No document processing",
            "progress_percentage": 0,
            "status": "idle",
            "details": {}
        }

        # Create directories if they don't exist
        os.makedirs(self.config['paths']['processed_dir'], exist_ok=True)
        os.makedirs(self.config['paths']['references_dir'], exist_ok=True)



    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_entities: bool = True,
        extract_references: bool = True,
        extract_metadata: bool = True,
        generate_embeddings: bool = True
    ) -> Dict[str, Any]:
        """
        Process a document through the complete workflow.

        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks in characters
            overlap: Overlap between chunks in characters
            extract_entities: Whether to extract entities
            extract_references: Whether to extract references
            extract_metadata: Whether to extract metadata
            generate_embeddings: Whether to generate embeddings for facts

        Returns:
            Result dictionary with processing details
        """
        file_path = Path(file_path)
        logger.info(f"Processing document: {file_path}")

        # Initialize enhanced progress tracking
        document_id = str(file_path.stem)
        filename = file_path.name
        progress_tracker = ProgressTracker(document_id, filename)

        # Update current document progress with the tracker data
        self.current_document_progress = progress_tracker.get_progress_data()

        try:
            # Step 1: Process the document and add it to the knowledge graph
            progress_tracker.update_progress(
                step=1,
                step_name="Extracting text from document",
                progress_percentage=10,
                status="processing"
            )
            self.current_document_progress = progress_tracker.get_progress_data()

            pdf_processor = PDFProcessor()
            pdf_result = await pdf_processor.process_file(str(file_path), chunk_size, overlap)

            if not pdf_result.get("success", False):
                logger.error(f"Failed to process document: {file_path}")
                progress_tracker.fail(pdf_result.get("error", "Unknown error"))
                self.current_document_progress = progress_tracker.get_progress_data()
                return pdf_result

            episode_id = pdf_result["episode_id"]
            progress_tracker.update_progress(
                step=1,
                step_name="Extracting text from document",
                progress_percentage=20,
                status="processing",
                details={"chunks": pdf_result.get("chunks", 0)}
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            logger.info(f"Document processed successfully. Episode ID: {episode_id}")

            # Step 2: Extract entities if requested
            entities_result = {"entities_extracted": 0}
            if extract_entities:
                progress_tracker.update_progress(
                    step=2,
                    step_name="Extracting entities",
                    progress_percentage=30,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                entities_result = await self.entity_processor.extract_entities_from_document(episode_id)
                entities_count = entities_result.get("entities_extracted", 0)
                progress_tracker.update_entities_count(entities_count)
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Extracted {entities_count} entities from document")

            # Step 3: Deduplicate entities if entities were extracted
            deduplication_result = {"duplicates_found": 0, "entities_merged": 0}
            if extract_entities and entities_result.get("entities_extracted", 0) > 0:
                progress_tracker.update_progress(
                    step=3,
                    step_name="Deduplicating entities",
                    progress_percentage=45,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                deduplication_result = await self.entity_processor.deduplicate_entities_for_document(episode_id)
                duplicates_found = deduplication_result.get("duplicates_found", 0)
                entities_merged = deduplication_result.get("entities_merged", 0)

                progress_tracker.update_progress(
                    step=3,
                    step_name="Deduplicating entities",
                    progress_percentage=55,
                    status="processing",
                    details={
                        "duplicates_found": duplicates_found,
                        "entities_merged": entities_merged
                    }
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Found {duplicates_found} duplicate entities and merged {entities_merged} entities")

            # Step 4: Extract references if requested
            references_result = {"references_extracted": 0, "total_reference_count": 0}
            if extract_references:
                progress_tracker.update_progress(
                    step=4,
                    step_name="Extracting references",
                    progress_percentage=60,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                references_result = await self.reference_processor.extract_references_from_document(str(file_path))
                references_count = references_result.get("total_reference_count", 0)
                progress_tracker.update_references_count(references_count)
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Extracted {references_count} references from document")

            # Step 5: Extract metadata if requested
            metadata_result = {"metadata_extracted": False}
            if extract_metadata:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Extracting metadata",
                    progress_percentage=75,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                metadata_result = await self.metadata_processor.extract_metadata_from_document(str(file_path), episode_id)
                metadata_extracted = metadata_result.get("metadata_extracted", False)

                progress_tracker.update_progress(
                    step=5,
                    step_name="Extracting metadata",
                    progress_percentage=85,
                    status="processing",
                    details={"metadata_extracted": metadata_extracted}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Metadata extraction {'successful' if metadata_extracted else 'failed'}")

            # Step 6: Generate embeddings if requested
            embeddings_result = {"embeddings_generated": 0}
            if generate_embeddings:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Generating embeddings",
                    progress_percentage=90,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()

                embeddings_result = await self.embedding_processor.generate_embeddings_for_document(episode_id)
                embeddings_count = embeddings_result.get("embeddings_generated", 0)
                embedding_model = embeddings_result.get("embedding_model", "none")

                progress_tracker.update_embeddings_count(embeddings_count)
                progress_tracker.update_progress(
                    step=5,
                    step_name="Generating embeddings",
                    progress_percentage=95,
                    status="processing",
                    details={"embedding_model": embedding_model}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                logger.info(f"Generated {embeddings_count} embeddings for document using model {embedding_model}")

            # Combine results
            result = {
                **pdf_result,
                "entities_extracted": entities_result.get("entities_extracted", 0),
                "duplicates_found": deduplication_result.get("duplicates_found", 0),
                "entities_merged": deduplication_result.get("entities_merged", 0),
                "references_extracted": references_result.get("total_reference_count", 0),
                "metadata_extracted": metadata_result.get("metadata_extracted", False),
                "embeddings_generated": embeddings_result.get("embeddings_generated", 0),
                "embedding_model": embeddings_result.get("embedding_model", "none")
            }

            # Update final progress
            progress_tracker.complete(details=result)
            self.current_document_progress = progress_tracker.get_progress_data()

            return result

        except Exception as e:
            logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
            progress_tracker.fail(str(e))
            self.current_document_progress = progress_tracker.get_progress_data()
            return {
                "success": False,
                "error": str(e),
                "file_path": str(file_path)
            }















    async def add_document_to_queue(self, file_path: Union[str, Path]) -> bool:
        """
        Add a document to the processing queue.

        Args:
            file_path: Path to the document file

        Returns:
            True if the document was added to the queue, False otherwise
        """
        file_path = Path(file_path)

        if not file_path.exists():
            logger.error(f"File not found: {file_path}")
            return False

        if not is_supported_file(file_path.name):
            logger.error(f"Unsupported file type: {file_path}")
            return False

        await self.processing_queue.put(file_path)
        logger.info(f"Added document to processing queue: {file_path}")

        # Start the processing loop if it's not already running
        if not self.is_processing:
            asyncio.create_task(self.process_queue())

        return True

    async def process_queue(self):
        """Process documents in the queue."""
        if self.is_processing:
            return

        self.is_processing = True
        logger.info("Started processing queue")

        try:
            # Create a list to hold all processing tasks
            tasks = []

            # Process documents in parallel up to max_parallel_processes
            while not self.processing_queue.empty():
                file_path = await self.processing_queue.get()

                # Create a task for processing this document
                task = asyncio.create_task(self.process_document_with_semaphore(file_path))
                tasks.append(task)

                # Don't wait for the task to complete - it will be processed in parallel

            # Wait for all tasks to complete
            if tasks:
                await asyncio.gather(*tasks)

        except Exception as e:
            logger.error(f"Error processing queue: {e}", exc_info=True)

        finally:
            self.is_processing = False
            logger.info("Finished processing queue")

    async def process_document_with_semaphore(self, file_path):
        """
        Process a document with semaphore to limit parallel processing.

        Args:
            file_path: Path to the document file

        Returns:
            Processing result
        """
        async with self.semaphore:
            self.active_processes += 1
            logger.info(f"Processing document from queue: {file_path} (Active processes: {self.active_processes}/{self.max_parallel_processes})")

            try:
                # Store the current document being processed
                document_id = str(Path(file_path).stem)
                self.document_progress[document_id] = {
                    "document_id": document_id,
                    "filename": Path(file_path).name,
                    "total_steps": 5,
                    "current_step": 0,
                    "step_name": "Starting document processing",
                    "progress_percentage": 0,
                    "status": "processing",
                    "details": {}
                }

                # Process the document
                result = await self.process_document(file_path)

                # Update statistics
                if result.get("success", False):
                    self.processed_count += 1
                    logger.info(f"Successfully processed document: {file_path}")
                    logger.info(f"Entities extracted: {result.get('entities_extracted', 0)}")
                    logger.info(f"References extracted: {result.get('references_extracted', 0)}")
                    logger.info(f"Embeddings generated: {result.get('embeddings_generated', 0)}")

                    # Update progress
                    self.document_progress[document_id]["status"] = "completed"
                    self.document_progress[document_id]["progress_percentage"] = 100
                    self.document_progress[document_id]["step_name"] = "Processing complete"
                else:
                    self.failed_count += 1
                    logger.error(f"Failed to process document: {file_path}")

                    # Update progress
                    self.document_progress[document_id]["status"] = "failed"
                    self.document_progress[document_id]["details"]["error"] = result.get("error", "Unknown error")

                # Mark the task as done
                self.processing_queue.task_done()

                return result

            except Exception as e:
                logger.error(f"Error processing document {file_path}: {e}", exc_info=True)
                self.failed_count += 1

                # Mark the task as done
                self.processing_queue.task_done()

                return {
                    "success": False,
                    "error": str(e),
                    "file_path": str(file_path)
                }

            finally:
                self.active_processes -= 1

    async def get_queue_status(self) -> Dict[str, Any]:
        """
        Get the status of the processing queue.

        Returns:
            Status dictionary
        """
        return {
            "is_processing": self.is_processing,
            "queue_size": self.processing_queue.qsize(),
            "processed_count": self.processed_count,
            "failed_count": self.failed_count,
            "active_processes": self.active_processes,
            "max_parallel_processes": self.max_parallel_processes,
            "current_document": str(self.current_document) if self.current_document else None,
            "current_progress": self.current_document_progress,
            "documents_progress": self.document_progress
        }

# Create a singleton instance
_document_processing_service = None

async def get_document_processing_service() -> DocumentProcessingService:
    """
    Get the document processing service instance.

    Returns:
        DocumentProcessingService instance
    """
    global _document_processing_service

    if _document_processing_service is None:
        _document_processing_service = DocumentProcessingService()

    return _document_processing_service
