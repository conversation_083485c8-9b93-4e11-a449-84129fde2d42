<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knowledge Graph Explorer - Settings</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            padding-top: 20px;
            overflow-x: hidden;
        }
        /* Sidebar styles */
        .app-container {
            display: flex;
            width: 100%;
            min-height: calc(100vh - 40px);
        }

        #sidebar {
            width: 250px;
            background-color: #f8f9fa;
            border-right: 1px solid #dee2e6;
            transition: all 0.3s;
            height: calc(100vh - 40px);
            position: sticky;
            top: 20px;
            overflow-y: auto;
        }

        #main-content {
            flex: 1;
            transition: all 0.3s;
            padding: 0 15px;
        }

        .sidebar-header {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .sidebar-header h4 {
            margin: 0;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-nav {
            padding: 15px 0;
        }

        .sidebar-nav-item {
            padding: 10px 15px;
            display: flex;
            align-items: center;
            color: #495057;
            text-decoration: none;
            cursor: pointer;
        }

        .sidebar-nav-item:hover {
            background-color: #e9ecef;
        }

        .sidebar-nav-item.active {
            background-color: #e9ecef;
            font-weight: bold;
        }

        .sidebar-nav-item i {
            margin-right: 10px;
            width: 20px;
            text-align: center;
        }

        /* Settings panel styles */
        .settings-panel {
            display: none;
            padding: 15px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <div id="sidebar">
            <div class="sidebar-header">
                <h4>Graphiti</h4>
                <button id="sidebar-toggle">
                    <i class="bi bi-chevron-left"></i>
                </button>
            </div>
            <div class="sidebar-nav">
                <div class="sidebar-nav-item active" data-target="llm-settings-panel">
                    <i class="bi bi-cpu"></i>
                    <span>LLM Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="embedding-settings-panel">
                    <i class="bi bi-diagram-3"></i>
                    <span>Embedding Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="database-settings-panel">
                    <i class="bi bi-database"></i>
                    <span>Database Settings</span>
                </div>
                <div class="sidebar-nav-item" data-target="system-settings-panel">
                    <i class="bi bi-gear"></i>
                    <span>System Settings</span>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div id="main-content">
            <div class="container">
                <h1 class="mb-4">Settings</h1>
                <p class="text-muted">Configure your Graphiti knowledge graph settings.</p>

                <!-- Settings Panels -->
                <div class="settings-panels">
                    <!-- LLM Settings Panel -->
                    <div id="llm-settings-panel" class="settings-panel" style="display: block;">
                        <h5 class="mb-3">LLM Settings</h5>
                        <p class="text-muted">Configure the Large Language Model used for question answering.</p>

                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current LLM Configuration</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Provider:</span>
                                        <span class="badge bg-primary">openrouter</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Model:</span>
                                        <span class="badge bg-primary">meta-llama/llama-4-maverick</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> LLM settings configuration will be available in a future update.
                        </div>
                    </div>

                    <!-- Embedding Settings Panel -->
                    <div id="embedding-settings-panel" class="settings-panel">
                        <h5 class="mb-3">Embedding Settings</h5>
                        <p class="text-muted">Configure the embedding model used for semantic search.</p>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current Embedding Configuration</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Provider:</span>
                                        <span class="badge bg-primary">openai</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Model:</span>
                                        <span class="badge bg-primary">text-embedding-3-small</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunking Method:</span>
                                        <span class="badge bg-primary">Recursive</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunk Size:</span>
                                        <span class="badge bg-primary">1200 characters</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Overlap:</span>
                                        <span class="badge bg-primary">0 characters</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i> Embedding settings configuration will be available in a future update.
                        </div>
                    </div>

                    <!-- Database Settings Panel -->
                    <div id="database-settings-panel" class="settings-panel">
                        <h5 class="mb-3">Database Settings</h5>
                        <p class="text-muted">Configure your FalkorDB database connection.</p>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Current FalkorDB Configuration</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Host:</span>
                                        <span class="badge bg-primary">localhost</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Port:</span>
                                        <span class="badge bg-primary">6379</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Graph Name:</span>
                                        <span class="badge bg-primary">graphiti</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i> FalkorDB settings configuration will be available in a future update.
                        </div>
                    </div>

                    <!-- System Settings Panel -->
                    <div id="system-settings-panel" class="settings-panel">
                        <h5 class="mb-3">System Settings</h5>
                        <p class="text-muted">Configure general system settings.</p>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">Chunking Settings</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunking Method:</span>
                                        <span class="badge bg-primary">Recursive</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Chunk Size:</span>
                                        <span class="badge bg-primary">1200 characters</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Overlap:</span>
                                        <span class="badge bg-primary">0 characters</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">OCR Settings</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Provider:</span>
                                        <span class="badge bg-primary">mistral</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span>Model:</span>
                                        <span class="badge bg-primary">mistral-ocr-latest</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i> System settings configuration will be available in a future update.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Add event listeners for sidebar navigation
            document.querySelectorAll('.sidebar-nav-item').forEach(item => {
                item.addEventListener('click', function() {
                    // Remove active class from all items
                    document.querySelectorAll('.sidebar-nav-item').forEach(i => {
                        i.classList.remove('active');
                    });

                    // Add active class to clicked item
                    this.classList.add('active');

                    // Show the corresponding settings panel
                    const targetPanel = this.getAttribute('data-target');
                    document.querySelectorAll('.settings-panel').forEach(panel => {
                        panel.style.display = 'none';
                    });
                    document.getElementById(targetPanel).style.display = 'block';
                });
            });

            // Toggle sidebar
            document.getElementById('sidebar-toggle').addEventListener('click', function() {
                document.getElementById('sidebar').classList.toggle('collapsed');
                document.getElementById('main-content').classList.toggle('expanded');
            });
        });
    </script>
</body>
</html>
