#!/usr/bin/env python3
"""
Test script to verify herb filtering is fixed.
"""

import requests

def test_herb_filter_fixed():
    """Test that herb filtering is now working"""
    base_url = "http://127.0.0.1:9753"
    
    print("🌿 Testing Fixed Herb Filter")
    print("=" * 40)
    
    try:
        # Test with correct parameter name
        response = requests.get(f"{base_url}/api/entities?entity_type=Herb&limit=10", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            
            print(f"✅ Entities returned: {len(entities)}")
            print(f"✅ Entity type filter: {data.get('entity_type', 'N/A')}")
            print(f"✅ Total herb count: {data.get('count', 0)}")
            
            if entities:
                print(f"\n🌿 Top 10 Herbs (by frequency, then alphabetical):")
                for i, entity in enumerate(entities):
                    name = entity.get('name', 'N/A')
                    entity_type = entity.get('type', 'N/A')
                    mentions = entity.get('mention_count', 0)
                    
                    # Verify it's actually a herb
                    if entity_type == 'Herb':
                        print(f"  ✅ {i+1:2d}. {name} - {mentions} mentions")
                    else:
                        print(f"  ❌ {i+1:2d}. {name} ({entity_type}) - NOT A HERB!")
            else:
                print("❌ No herbs returned")
                
            # Check if all returned entities are actually herbs
            herb_count = sum(1 for e in entities if e.get('type') == 'Herb')
            if herb_count == len(entities):
                print(f"\n✅ All {len(entities)} entities are correctly filtered as Herbs!")
            else:
                print(f"\n❌ Only {herb_count}/{len(entities)} entities are Herbs - filtering not working!")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_herb_filter_fixed()
