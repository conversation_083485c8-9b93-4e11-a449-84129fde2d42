/**
 * WebSocket-based Enhanced Document Upload with Real-time Progress Tracking
 * Replaces polling with WebSocket connections for instant updates and cancellation support
 */

class WebSocketDocumentUploader {
    constructor() {
        this.activeOperations = new Map();
        this.websockets = new Map();
        this.supportedTypes = [];
        this.maxFileSize = 100 * 1024 * 1024; // 100MB default

        this.initializeElements();
        this.setupEventListeners();
        this.loadSupportedTypes();
        this.restoreActiveOperations();
    }
    
    initializeElements() {
        // Main elements
        this.dropZone = document.getElementById('enhanced-dropzone');
        this.fileInput = document.getElementById('enhanced-file-input');
        this.fileList = document.getElementById('enhanced-file-list');
        this.progressContainer = document.getElementById('enhanced-progress-container');
        this.previewContainer = document.getElementById('enhanced-preview-container');
        
        // Settings
        this.chunkSizeInput = document.getElementById('chunk-size');
        this.overlapInput = document.getElementById('overlap');
        this.extractEntitiesCheck = document.getElementById('extract-entities');
        this.extractReferencesCheck = document.getElementById('extract-references');
        this.extractMetadataCheck = document.getElementById('extract-metadata');
        this.generateEmbeddingsCheck = document.getElementById('generate-embeddings');
        
        // Create elements if they don't exist
        this.createMissingElements();
    }
    
    createMissingElements() {
        if (!this.progressContainer) {
            this.progressContainer = this.createElement('div', 'enhanced-progress-container', 'progress-container mt-3');
            if (this.fileList) {
                this.fileList.after(this.progressContainer);
            } else {
                document.body.appendChild(this.progressContainer);
            }
        }
    }
    
    createElement(tag, id, className) {
        const element = document.createElement(tag);
        element.id = id;
        element.className = className;
        return element;
    }
    
    setupEventListeners() {
        // Drag and drop
        if (this.dropZone) {
            this.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
            this.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
            this.dropZone.addEventListener('drop', this.handleDrop.bind(this));
        }
        
        // File input
        if (this.fileInput) {
            this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        }
        
        // Page unload cleanup
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }
    
    handleDragOver(e) {
        e.preventDefault();
        this.dropZone.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }
    
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);
    }
    
    async processFiles(files) {
        if (files.length === 0) return;
        
        this.showAlert(`Processing ${files.length} file(s)...`, 'info');
        
        for (const file of files) {
            try {
                await this.uploadSingleFile(file);
            } catch (error) {
                console.error('Error uploading file:', error);
                this.showAlert(`Error uploading ${file.name}: ${error.message}`, 'danger');
            }
        }
    }
    
    async uploadSingleFile(file, retryCount = 0) {
        // Validate file
        if (!this.validateFile(file)) {
            return;
        }

        const formData = this.createFormData(file);
        const maxRetries = 3;

        try {
            // Add timeout to prevent hanging uploads
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 60000); // 60 second timeout

            const response = await fetch('/api/enhanced/enhanced-upload', {
                method: 'POST',
                body: formData,
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (!response.ok) {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

            const result = await response.json();

            if (result.operation_id) {
                this.startWebSocketTracking(result.operation_id, file.name);
                this.showAlert(`Upload started for ${file.name}`, 'success');
            } else {
                throw new Error('No operation ID received from server');
            }

        } catch (error) {
            console.error('Upload error:', error);

            // Handle different types of errors
            if (error.name === 'AbortError') {
                this.showAlert(`Upload timeout for ${file.name}. Please try again.`, 'warning');
            } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                // Network error - attempt retry
                if (retryCount < maxRetries) {
                    const delay = Math.pow(2, retryCount) * 1000; // Exponential backoff
                    this.showAlert(`Network error uploading ${file.name}. Retrying in ${delay/1000} seconds...`, 'warning');

                    setTimeout(() => {
                        this.uploadSingleFile(file, retryCount + 1);
                    }, delay);
                } else {
                    this.showAlert(`Failed to upload ${file.name} after ${maxRetries} attempts: Network error`, 'danger');
                }
            } else if (error.message.includes('413') || error.message.includes('too large')) {
                this.showAlert(`File ${file.name} is too large for upload`, 'danger');
            } else if (error.message.includes('415') || error.message.includes('Unsupported')) {
                this.showAlert(`File type not supported for ${file.name}`, 'danger');
            } else {
                // Generic error
                if (retryCount < maxRetries) {
                    const delay = Math.pow(2, retryCount) * 1000;
                    this.showAlert(`Error uploading ${file.name}. Retrying in ${delay/1000} seconds...`, 'warning');

                    setTimeout(() => {
                        this.uploadSingleFile(file, retryCount + 1);
                    }, delay);
                } else {
                    this.showAlert(`Failed to upload ${file.name} after ${maxRetries} attempts: ${error.message}`, 'danger');
                }
            }
        }
    }
    
    validateFile(file) {
        if (file.size > this.maxFileSize) {
            this.showAlert(`File ${file.name} is too large (max ${this.maxFileSize / 1024 / 1024}MB)`, 'warning');
            return false;
        }
        return true;
    }
    
    createFormData(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('chunk_size', this.getChunkSize());
        formData.append('overlap', this.getOverlap());
        formData.append('extract_entities', this.getExtractEntities());
        formData.append('extract_references', this.getExtractReferences());
        formData.append('extract_metadata', this.getExtractMetadata());
        formData.append('generate_embeddings', this.getGenerateEmbeddings());
        
        return formData;
    }
    
    getChunkSize() {
        return this.chunkSizeInput ? parseInt(this.chunkSizeInput.value) || 1200 : 1200;
    }
    
    getOverlap() {
        return this.overlapInput ? parseInt(this.overlapInput.value) || 0 : 0;
    }
    
    getExtractEntities() {
        return this.extractEntitiesCheck ? this.extractEntitiesCheck.checked : true;
    }
    
    getExtractReferences() {
        return this.extractReferencesCheck ? this.extractReferencesCheck.checked : true;
    }
    
    getExtractMetadata() {
        return this.extractMetadataCheck ? this.extractMetadataCheck.checked : true;
    }
    
    getGenerateEmbeddings() {
        return this.generateEmbeddingsCheck ? this.generateEmbeddingsCheck.checked : true;
    }
    

    
    handleWebSocketMessage(operationId, message) {
        switch (message.type) {
            case 'connection_established':
                console.log(`Connection established for operation ${operationId}`);
                break;
                
            case 'progress_update':
                this.updateProgressCard(operationId, message.data);
                break;
                
            case 'operation_complete':
                this.handleOperationComplete(operationId, message.data);
                break;
                
            case 'operation_error':
                this.handleOperationError(operationId, message.error, message.details);
                break;
                
            case 'operation_cancelled':
                this.handleOperationCancelled(operationId);
                break;
                
            case 'pong':
                // Handle ping/pong for connection health
                break;
                
            default:
                console.log(`Unknown message type: ${message.type}`);
        }
    }
    
    handleOperationComplete(operationId, data) {
        const operation = this.activeOperations.get(operationId);
        if (operation) {
            this.showAlert(`Successfully processed ${operation.filename}`, 'success');
            this.updateProgressCard(operationId, data);
            
            // Close WebSocket after a delay
            setTimeout(() => {
                const websocket = this.websockets.get(operationId);
                if (websocket) {
                    websocket.close();
                }
            }, 5000);
        }
    }
    
    handleOperationError(operationId, error, details) {
        const operation = this.activeOperations.get(operationId);
        if (operation) {
            this.showAlert(`Failed to process ${operation.filename}: ${error}`, 'danger');
            
            // Update progress card to show error
            const card = document.getElementById(`progress-${operationId}`);
            if (card) {
                card.classList.add('border-danger');
                const stepBadge = card.querySelector(`#step-badge-${operationId}`);
                if (stepBadge) {
                    stepBadge.className = 'badge bg-danger me-2';
                    stepBadge.textContent = 'Failed';
                }
            }
        }
    }
    
    handleOperationCancelled(operationId) {
        const operation = this.activeOperations.get(operationId);
        if (operation) {
            this.showAlert(`Processing cancelled for ${operation.filename}`, 'warning');

            // Update progress card to show cancellation
            const card = document.getElementById(`progress-${operationId}`);
            if (card) {
                card.classList.add('border-warning');
                const stepBadge = card.querySelector(`#step-badge-${operationId}`);
                if (stepBadge) {
                    stepBadge.className = 'badge bg-warning me-2';
                    stepBadge.textContent = 'Cancelled';
                }
            }
        }
    }

    createProgressCard(operationId, filename) {
        const card = document.createElement('div');
        card.className = 'card mb-3 shadow-sm';
        card.id = `progress-${operationId}`;
        card.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="bi bi-file-earmark"></i> ${filename}</h6>
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" id="step-badge-${operationId}">Step 0/7</span>
                    <button class="btn btn-sm btn-outline-danger me-2" onclick="websocketUploader.cancelOperation('${operationId}')"
                            id="cancel-btn-${operationId}" title="Cancel Processing">
                        <i class="bi bi-x-circle"></i>
                    </button>
                    <span class="text-muted small" id="eta-${operationId}"></span>
                </div>
            </div>
            <div class="card-body">
                <!-- Overall Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small fw-bold">Overall Progress</span>
                        <span class="small fw-bold" id="overall-percentage-${operationId}">0%</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="overall-progress-${operationId}" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Current Step Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small text-muted" id="current-step-${operationId}">Initializing...</span>
                        <span class="small text-muted" id="step-percentage-${operationId}">0%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" id="step-progress-${operationId}"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Processing Statistics -->
                <div class="row text-center" id="stats-${operationId}">
                    <div class="col-3">
                        <div class="small text-muted">Facts</div>
                        <div class="fw-bold text-primary" id="facts-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">Entities</div>
                        <div class="fw-bold text-success" id="entities-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">References</div>
                        <div class="fw-bold text-warning" id="references-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">Embeddings</div>
                        <div class="fw-bold text-info" id="embeddings-${operationId}">-</div>
                    </div>
                </div>

                <!-- Expandable Details -->
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-secondary w-100" type="button"
                            data-bs-toggle="collapse" data-bs-target="#details-${operationId}">
                        <i class="bi bi-chevron-down"></i> View Details
                    </button>
                    <div class="collapse mt-2" id="details-${operationId}">
                        <div class="card card-body bg-light">
                            <div class="small">
                                <div><strong>Operation ID:</strong> ${operationId}</div>
                                <div><strong>Start Time:</strong> <span id="start-time-${operationId}">-</span></div>
                                <div><strong>Elapsed Time:</strong> <span id="elapsed-time-${operationId}">-</span></div>
                                <div><strong>Processing Speed:</strong> <span id="speed-${operationId}">-</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    updateProgressCard(operationId, progressData) {
        const card = document.getElementById(`progress-${operationId}`);
        if (!card) return;

        // Update step badge
        const stepBadge = document.getElementById(`step-badge-${operationId}`);
        if (stepBadge) {
            stepBadge.textContent = `Step ${progressData.current_step || 0}/${progressData.total_steps || 7}`;

            // Update badge color based on status
            stepBadge.className = 'badge me-2 ' + (
                progressData.status === 'completed' ? 'bg-success' :
                progressData.status === 'failed' ? 'bg-danger' : 'bg-primary'
            );
        }

        // Update ETA
        const eta = document.getElementById(`eta-${operationId}`);
        if (eta && progressData.estimated_remaining_time > 0) {
            const minutes = Math.floor(progressData.estimated_remaining_time / 60);
            const seconds = Math.round(progressData.estimated_remaining_time % 60);
            eta.textContent = minutes > 0 ? `ETA: ${minutes}m ${seconds}s` : `ETA: ${seconds}s`;
        }

        // Update overall progress
        const overallProgress = document.getElementById(`overall-progress-${operationId}`);
        const overallPercentage = document.getElementById(`overall-percentage-${operationId}`);
        if (overallProgress && overallPercentage) {
            overallProgress.style.width = `${progressData.progress_percentage}%`;
            overallProgress.setAttribute('aria-valuenow', progressData.progress_percentage);
            overallPercentage.textContent = `${progressData.progress_percentage}%`;

            // Update progress bar color based on status
            overallProgress.className = 'progress-bar progress-bar-striped progress-bar-animated ' + (
                progressData.status === 'completed' ? 'bg-success' :
                progressData.status === 'failed' ? 'bg-danger' : ''
            );
        }

        // Update current step
        const currentStep = document.getElementById(`current-step-${operationId}`);
        if (currentStep) {
            currentStep.textContent = progressData.current_step_name || 'Processing...';
        }

        // Update statistics
        this.updateStatistics(operationId, progressData);

        // Update detailed information
        this.updateDetailedInfo(operationId, progressData);

        // Hide cancel button if completed or failed
        if (progressData.status === 'completed' || progressData.status === 'failed') {
            const cancelBtn = document.getElementById(`cancel-btn-${operationId}`);
            if (cancelBtn) {
                cancelBtn.style.display = 'none';
            }
        }
    }

    updateStatistics(operationId, progressData) {
        // Handle both nested details and direct properties
        const details = progressData.details || progressData;

        // Update facts count
        const factsElement = document.getElementById(`facts-${operationId}`);
        if (factsElement) {
            const factsCount = details.facts_count || progressData.facts_count || '-';
            factsElement.textContent = factsCount;
        }

        // Update entities count
        const entitiesElement = document.getElementById(`entities-${operationId}`);
        if (entitiesElement) {
            const entitiesCount = details.entities_count || progressData.entities_count || '-';
            entitiesElement.textContent = entitiesCount;
        }

        // Update references count
        const referencesElement = document.getElementById(`references-${operationId}`);
        if (referencesElement) {
            const referencesCount = details.references_count || progressData.references_count || '-';
            referencesElement.textContent = referencesCount;
        }

        // Update embeddings count
        const embeddingsElement = document.getElementById(`embeddings-${operationId}`);
        if (embeddingsElement) {
            const embeddingsCount = details.embeddings_count || details.embeddings_in_redis_count ||
                                   progressData.embeddings_count || progressData.embeddings_in_redis_count || '-';
            embeddingsElement.textContent = embeddingsCount;
        }
    }

    updateDetailedInfo(operationId, progressData) {
        const statistics = progressData.statistics || {};

        // Update start time
        const startTimeElement = document.getElementById(`start-time-${operationId}`);
        const startTimeValue = statistics.start_time || progressData.start_time;
        if (startTimeElement && startTimeValue) {
            const startTime = new Date(startTimeValue);
            startTimeElement.textContent = startTime.toLocaleTimeString();
        }

        // Update elapsed time
        const elapsedTimeElement = document.getElementById(`elapsed-time-${operationId}`);
        const elapsedTimeValue = statistics.elapsed_time || progressData.processing_time;
        if (elapsedTimeElement && elapsedTimeValue) {
            let elapsedSeconds;

            if (typeof elapsedTimeValue === 'string') {
                const startTime = new Date(progressData.start_time);
                const endTime = progressData.completion_time ? new Date(progressData.completion_time) : new Date();
                elapsedSeconds = (endTime - startTime) / 1000;
            } else {
                elapsedSeconds = elapsedTimeValue;
            }

            const minutes = Math.floor(elapsedSeconds / 60);
            const seconds = Math.round(elapsedSeconds % 60);
            elapsedTimeElement.textContent = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        }

        // Update processing speed
        const speedElement = document.getElementById(`speed-${operationId}`);
        const elapsedForSpeed = statistics.elapsed_time ||
                              (progressData.processing_time && typeof progressData.processing_time === 'number' ? progressData.processing_time : null);

        if (speedElement && elapsedForSpeed > 0) {
            const details = progressData.details || progressData;
            const totalItems = (details.facts_count || 0) + (details.entities_count || 0);
            if (totalItems > 0) {
                const itemsPerSecond = totalItems / elapsedForSpeed;
                speedElement.textContent = `${itemsPerSecond.toFixed(1)} items/sec`;
            } else {
                speedElement.textContent = 'Processing complete';
            }
        }
    }

    async cancelOperation(operationId) {
        const operation = this.activeOperations.get(operationId);
        if (!operation) {
            this.showAlert('Operation not found', 'warning');
            return;
        }

        try {
            // Send cancellation via WebSocket
            const websocket = operation.websocket;
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({
                    type: 'cancel_operation',
                    operation_id: operationId
                }));
            }

            // Also send HTTP request as backup
            const response = await fetch(`/ws/cancel/${operationId}`, {
                method: 'POST'
            });

            if (response.ok) {
                this.showAlert(`Cancellation requested for ${operation.filename}`, 'info');

                // Disable cancel button
                const cancelBtn = document.getElementById(`cancel-btn-${operationId}`);
                if (cancelBtn) {
                    cancelBtn.disabled = true;
                    cancelBtn.innerHTML = '<i class="bi bi-hourglass-split"></i>';
                }
            } else {
                throw new Error('Failed to cancel operation');
            }

        } catch (error) {
            console.error('Error cancelling operation:', error);
            this.showAlert(`Failed to cancel ${operation.filename}: ${error.message}`, 'danger');
        }
    }

    async loadSupportedTypes() {
        try {
            const response = await fetch('/api/enhanced/supported-types');
            if (response.ok) {
                const data = await response.json();
                this.supportedTypes = data.supported_types || [];
                this.maxFileSize = data.max_file_size || this.maxFileSize;
            }
        } catch (error) {
            console.error('Error loading supported types:', error);
        }
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || document.body;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }

    async restoreActiveOperations() {
        /**
         * Restore active operations after page refresh by checking persistent storage.
         */
        try {
            const response = await fetch('/ws/operations/active-persistent');
            if (!response.ok) {
                console.warn('Failed to fetch active operations');
                return;
            }

            const data = await response.json();
            const activeOps = data.operations || [];

            console.log(`Restoring ${activeOps.length} active operations`);

            for (const operation of activeOps) {
                // Only restore operations that are still processing
                if (operation.status === 'processing') {
                    this.restoreOperation(operation);
                }
            }

        } catch (error) {
            console.error('Error restoring active operations:', error);
        }
    }

    restoreOperation(operationData) {
        /**
         * Restore a single operation from persistent data.
         */
        const operationId = operationData.operation_id;
        const filename = operationData.filename;

        // Create progress card
        const progressCard = this.createProgressCard(operationId, filename);
        this.progressContainer.appendChild(progressCard);

        // Update progress card with current data
        this.updateProgressCard(operationId, {
            current_step: operationData.current_step,
            total_steps: operationData.total_steps,
            current_step_name: operationData.current_step_name,
            progress_percentage: operationData.progress_percentage,
            status: operationData.status,
            details: operationData.details,
            statistics: operationData.statistics,
            estimated_remaining_time: 0 // Will be recalculated
        });

        // Start WebSocket tracking for this operation
        this.startWebSocketTracking(operationId, filename, true);

        this.showAlert(`Restored tracking for ${filename}`, 'info');
    }

    startWebSocketTracking(operationId, filename, isRestore = false) {
        // Don't create progress card if this is a restore (already created)
        if (!isRestore) {
            const progressCard = this.createProgressCard(operationId, filename);
            this.progressContainer.appendChild(progressCard);
        }

        // Create WebSocket connection
        const wsUrl = `ws://${window.location.host}/ws/progress/${operationId}`;
        const websocket = new WebSocket(wsUrl);

        websocket.onopen = () => {
            console.log(`WebSocket connected for operation ${operationId}`);
            this.websockets.set(operationId, websocket);
            this.activeOperations.set(operationId, {
                filename: filename,
                startTime: new Date(),
                websocket: websocket,
                isRestored: isRestore
            });
        };

        websocket.onmessage = (event) => {
            try {
                const message = JSON.parse(event.data);
                this.handleWebSocketMessage(operationId, message);
            } catch (error) {
                console.error('Error parsing WebSocket message:', error);
            }
        };

        websocket.onerror = (error) => {
            console.error(`WebSocket error for operation ${operationId}:`, error);
            this.showAlert(`Connection error for ${filename}`, 'warning');

            // Attempt to reconnect after a delay
            setTimeout(() => {
                this.attemptReconnection(operationId, filename);
            }, 5000);
        };

        websocket.onclose = (event) => {
            console.log(`WebSocket closed for operation ${operationId}. Code: ${event.code}, Reason: ${event.reason}`);
            this.websockets.delete(operationId);

            // Only attempt reconnection if it wasn't a normal closure and operation is still active
            if (event.code !== 1000 && this.activeOperations.has(operationId)) {
                setTimeout(() => {
                    this.attemptReconnection(operationId, filename);
                }, 3000);
            } else {
                this.activeOperations.delete(operationId);
            }
        };
    }

    async attemptReconnection(operationId, filename, maxAttempts = 3) {
        /**
         * Attempt to reconnect to a WebSocket for an operation.
         */
        const operation = this.activeOperations.get(operationId);
        if (!operation) {
            console.log(`Operation ${operationId} no longer active, skipping reconnection`);
            return;
        }

        // Check if operation is still processing via HTTP
        try {
            const response = await fetch(`/ws/operation/${operationId}/status`);
            if (!response.ok) {
                console.log(`Operation ${operationId} not found, removing from active operations`);
                this.activeOperations.delete(operationId);
                return;
            }

            const statusData = await response.json();
            const persistentState = statusData.persistent_state;

            if (!persistentState || persistentState.status !== 'processing') {
                console.log(`Operation ${operationId} no longer processing, removing from active operations`);
                this.activeOperations.delete(operationId);

                // Update UI to show final state
                if (persistentState) {
                    this.updateProgressCard(operationId, persistentState);
                }
                return;
            }

            // Attempt reconnection
            console.log(`Attempting to reconnect WebSocket for operation ${operationId}`);
            this.startWebSocketTracking(operationId, filename, true);

        } catch (error) {
            console.error(`Error during reconnection attempt for ${operationId}:`, error);

            // Retry with exponential backoff
            if (maxAttempts > 0) {
                const delay = (4 - maxAttempts) * 5000; // 0s, 5s, 10s delays
                setTimeout(() => {
                    this.attemptReconnection(operationId, filename, maxAttempts - 1);
                }, delay);
            } else {
                this.showAlert(`Failed to reconnect to ${filename} after multiple attempts`, 'danger');
                this.markOperationAsStuck(operationId);
            }
        }
    }

    markOperationAsStuck(operationId) {
        /**
         * Mark an operation as stuck and provide recovery options.
         */
        const card = document.getElementById(`progress-${operationId}`);
        if (card) {
            card.classList.add('border-warning');

            // Add stuck indicator
            const stepBadge = card.querySelector(`#step-badge-${operationId}`);
            if (stepBadge) {
                stepBadge.className = 'badge bg-warning me-2';
                stepBadge.textContent = 'Connection Lost';
            }

            // Add recovery button
            const cardHeader = card.querySelector('.card-header');
            if (cardHeader && !cardHeader.querySelector('.recovery-btn')) {
                const recoveryBtn = document.createElement('button');
                recoveryBtn.className = 'btn btn-sm btn-outline-info recovery-btn me-2';
                recoveryBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> Retry';
                recoveryBtn.onclick = () => {
                    const operation = this.activeOperations.get(operationId);
                    if (operation) {
                        this.attemptReconnection(operationId, operation.filename);
                    }
                };

                const cancelBtn = cardHeader.querySelector(`#cancel-btn-${operationId}`);
                if (cancelBtn) {
                    cancelBtn.before(recoveryBtn);
                }
            }
        }
    }

    async handleNetworkError(operationId, error) {
        /**
         * Handle network-related errors with appropriate recovery strategies.
         */
        console.error(`Network error for operation ${operationId}:`, error);

        const operation = this.activeOperations.get(operationId);
        if (!operation) return;

        // Check if it's a temporary network issue
        try {
            const response = await fetch('/ws/active-operations', {
                method: 'GET',
                signal: AbortSignal.timeout(5000) // 5 second timeout
            });

            if (response.ok) {
                // Network is working, try to reconnect
                this.showAlert(`Network recovered, attempting to reconnect ${operation.filename}`, 'info');
                this.attemptReconnection(operationId, operation.filename);
            } else {
                throw new Error('Network still unavailable');
            }
        } catch (networkError) {
            // Network is still down
            this.showAlert(`Network issues detected. Will retry when connection is restored.`, 'warning');
            this.markOperationAsStuck(operationId);

            // Set up periodic retry
            setTimeout(() => {
                this.handleNetworkError(operationId, error);
            }, 30000); // Retry every 30 seconds
        }
    }

    cleanup() {
        // Close all WebSocket connections
        for (const [operationId, websocket] of this.websockets) {
            if (websocket.readyState === WebSocket.OPEN) {
                websocket.close(1000, 'Page unload'); // Normal closure
            }
        }
        this.websockets.clear();
        this.activeOperations.clear();
    }
}

// Initialize the WebSocket uploader when the page loads
let websocketUploader;
document.addEventListener('DOMContentLoaded', () => {
    websocketUploader = new WebSocketDocumentUploader();
    window.websocketUploader = websocketUploader; // Make it globally accessible
});
