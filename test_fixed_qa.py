#!/usr/bin/env python3
"""
Test script to verify the fixed Q&A service.
"""

import asyncio

async def test_fixed_qa():
    """Test the fixed Q&A service"""
    print("🔍 Testing Fixed Q&A Service")
    print("=" * 30)
    
    # Test 1: Test fact retrieval with fixed query
    print("1. Testing fact retrieval with fixed query...")
    try:
        from services.qa_service import get_relevant_facts
        
        question = "what can you tell me about the herb andrographis"
        facts = await get_relevant_facts(question, limit=10)
        
        print(f"✅ Q&A service returned {len(facts)} facts")
        
        if facts:
            for i, fact in enumerate(facts):
                body = fact.get('body', 'N/A')
                doc_name = fact.get('document_name', 'N/A')
                print(f"  {i+1}. From {doc_name}:")
                print(f"     {body[:200]}...")
                print()
        else:
            print("❌ Still no facts returned")
            
    except Exception as e:
        print(f"❌ Q&A service error: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Test full Q&A pipeline
    print("2. Testing full Q&A pipeline...")
    try:
        import requests
        
        # Test the Q&A API endpoint
        response = requests.post(
            "http://127.0.0.1:9753/api/qa/answer",
            json={
                "question": "what can you tell me about the herb andrographis",
                "max_facts": 10,
                "llm_provider": "openrouter",
                "llm_model": "meta-llama/llama-3.1-8b-instruct:free",
                "response_length": "brief"
            },
            timeout=30
        )
        
        print(f"API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get('answer', 'N/A')
            references = data.get('references', [])
            
            print(f"✅ Q&A API working!")
            print(f"Answer: {answer[:300]}...")
            print(f"References: {len(references)}")
            
            if "I don't have enough information" in answer:
                print("❌ Still getting 'no information' response")
            else:
                print("✅ Got a real answer about Andrographis!")
                
        else:
            print(f"❌ API error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")
    
    # Test 3: Test with different herbs
    print("\n3. Testing with other herbs...")
    try:
        for herb in ["echinacea", "astragalus", "ginseng"]:
            facts = await get_relevant_facts(f"tell me about {herb}", limit=5)
            print(f"  {herb}: {len(facts)} facts found")
            
    except Exception as e:
        print(f"❌ Other herbs test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_fixed_qa())
