#!/usr/bin/env python3
"""
Test script to check entities API.
"""

import requests

def test_entities_api():
    """Test the entities API endpoint"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entities API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/entities?limit=5", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Count: {data.get('count', 0)}")
            print(f"Total Count: {data.get('total_count', 0)}")
            print(f"Total Relationships: {data.get('total_relationships', 0)}")
            print(f"Entity Types: {len(data.get('type_counts', {}))}")
            
            entities = data.get('entities', [])
            print(f"\nSample entities:")
            for i, entity in enumerate(entities[:3]):
                print(f"  {i+1}. {entity.get('name', 'N/A')} ({entity.get('type', 'N/A')}) - {entity.get('mention_count', 0)} mentions")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entities_api()
