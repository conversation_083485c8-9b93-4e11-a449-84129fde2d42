"""
Example demonstrating how to use Graphiti with Google Gemini (fixed version)
"""

import asyncio
import logging
import os
import sys
from datetime import datetime, timezone

from dotenv import load_dotenv
import google.generativeai as genai

# Custom Gemini client implementation to work around import issues
class CustomGeminiClient:
    def __init__(self, api_key, model_name="models/gemini-1.5-flash"):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel(model_name)
        
    async def generate_response(self, prompt):
        try:
            response = self.model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"Error generating response: {e}")
            return f"Error: {e}"

# Custom Gemini embedder implementation to work around import issues
class CustomGeminiEmbedder:
    def __init__(self, api_key, model_name="models/embedding-001"):
        self.api_key = api_key
        self.model_name = model_name
        genai.configure(api_key=api_key)
        
    async def create_embedding(self, text):
        try:
            result = genai.embed_content(
                model=self.model_name,
                content=text,
                task_type="RETRIEVAL_QUERY"
            )
            return result['embedding']
        except Exception as e:
            print(f"Error creating embedding: {e}")
            return []

def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )

async def main():
    """Main function to run the example."""
    # Load environment variables
    load_dotenv()
    setup_logging()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        print("No Google API key found in environment variables. Please add it to your .env file.")
        return
        
    print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")
    
    try:
        print("Initializing custom Gemini clients...")
        
        # Initialize custom Gemini clients
        llm_client = CustomGeminiClient(api_key=google_api_key, model_name="models/gemini-1.5-flash")
        embedder = CustomGeminiEmbedder(api_key=google_api_key, model_name="models/embedding-001")
        
        # Test LLM generation
        prompt = "Explain what a knowledge graph is and how it can be used in AI applications."
        print("\nGenerating text with Gemini...")
        response = await llm_client.generate_response(prompt)
        print(f"Response: {response}")
        
        # Test embedding creation
        text = "This is a test sentence for embedding."
        print("\nCreating embeddings with Gemini...")
        embedding = await embedder.create_embedding(text)
        if embedding:
            print(f"Embedding dimension: {len(embedding)}")
            print(f"First 5 values: {embedding[:5]}")
        
        print("\nExample completed successfully!")
    except Exception as e:
        print(f"Error in main function: {e}")
        print("Detailed error:", sys.exc_info())

if __name__ == "__main__":
    asyncio.run(main())
