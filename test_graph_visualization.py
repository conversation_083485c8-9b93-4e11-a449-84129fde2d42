#!/usr/bin/env python3
"""
Test the graph visualization endpoint
"""

import requests

def test_graph_visualization():
    """Test the graph visualization endpoint"""
    print('Testing graph visualization endpoint...')
    
    # Test the graph visualization endpoint that the frontend uses
    response = requests.get('http://localhost:9753/api/knowledge-graph/graph?limit=20')
    if response.status_code == 200:
        data = response.json()
        nodes = data.get('nodes', [])
        relationships = data.get('relationships', [])
        
        print(f'Graph visualization endpoint - Nodes: {len(nodes)}')
        print(f'Graph visualization endpoint - Relationships: {len(relationships)}')
        
        if nodes:
            print('\nSample nodes:')
            for i, node in enumerate(nodes[:3]):
                print(f'  {i+1}. {node["name"]} ({node["type"]})')
        
        if relationships:
            print('\nSample relationships:')
            for i, rel in enumerate(relationships[:3]):
                print(f'  {i+1}. {rel["source"]} --[{rel["type"]}]--> {rel["target"]}')
        
        return True
    else:
        print(f'Graph visualization endpoint error: {response.status_code}')
        print(f'Response: {response.text}')
        return False

if __name__ == "__main__":
    success = test_graph_visualization()
    if success:
        print('\n✅ Graph visualization endpoint is working!')
    else:
        print('\n❌ Graph visualization endpoint has issues')
