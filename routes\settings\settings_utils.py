"""
Utility functions for settings management.
"""

import os
from typing import Dict, Any, Optional
from pydantic import BaseModel

from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)


# Define models for settings updates
class LLMSettings(BaseModel):
    provider: str
    model: str
    api_key: Optional[str] = None

    class Config:
        extra = "allow"


class EmbeddingSettings(BaseModel):
    provider: str = "ollama"
    model: str = "snowflake-arctic-embed2"
    use_local: bool = True
    chunk_size: int = 1200
    chunk_overlap: int = 0
    recursive_chunking: bool = True

    class Config:
        extra = "allow"


class DatabaseSettings(BaseModel):
    host: str
    port: int

    class Config:
        extra = "allow"


class SystemSettings(BaseModel):
    max_file_size: int = 50
    max_parallel_processes: int = 4
    log_level: str = "INFO"

    class Config:
        extra = "allow"


class AllSettings(BaseModel):
    llm_settings: Optional[LLMSettings] = None
    embedding_settings: Optional[EmbeddingSettings] = None
    database_settings: Optional[DatabaseSettings] = None
    system_settings: Optional[SystemSettings] = None

    class Config:
        # Allow extra fields to be included in the request
        extra = "allow"


def update_env_file(config_updates: Dict[str, str]) -> bool:
    """
    Update the .env file with new configuration values.

    Args:
        config_updates: Dictionary of key-value pairs to update

    Returns:
        True if successful, False otherwise
    """
    try:
        env_file_path = ".env"
        
        # Read existing .env file
        env_vars = {}
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key.strip()] = value.strip()

        # Update with new values
        env_vars.update(config_updates)

        # Write back to .env file
        with open(env_file_path, 'w') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")

        logger.info(f"Updated .env file with {len(config_updates)} configuration changes")
        return True

    except Exception as e:
        logger.error(f"Error updating .env file: {e}")
        return False


def get_current_settings() -> Dict[str, Any]:
    """
    Get current settings from environment variables.

    Returns:
        Dictionary of current settings
    """
    try:
        return {
            "llm": {
                "provider": os.environ.get("QA_LLM_PROVIDER", "openrouter"),
                "model": os.environ.get("QA_LLM_MODEL", "meta-llama/llama-4-maverick"),
                "temperature": float(os.environ.get("QA_LLM_TEMPERATURE", "0.7")),
                "max_tokens": int(os.environ.get("QA_LLM_MAX_TOKENS", "2048")),
                "top_k": int(os.environ.get("QA_LLM_TOP_K", "40")),
                "top_p": float(os.environ.get("QA_LLM_TOP_P", "0.9")),
                "use_local": os.environ.get("USE_LOCAL_LLM", "false").lower() == "true"
            },
            "embedding": {
                "provider": os.environ.get("EMBEDDING_PROVIDER", "openai"),
                "model": os.environ.get("EMBEDDING_MODEL", "text-embedding-3-small"),
                "use_local": os.environ.get("USE_LOCAL_EMBEDDINGS", "false").lower() == "true",
                "chunk_size": int(os.environ.get("CHUNK_SIZE", "1200")),
                "chunk_overlap": int(os.environ.get("CHUNK_OVERLAP", "0")),
                "recursive_chunking": os.environ.get("RECURSIVE_CHUNKING", "true").lower() == "true"
            },
            "database": {
                "type": "FalkorDB",
                "host": os.environ.get("FALKORDB_HOST", "localhost"),
                "port": int(os.environ.get("FALKORDB_PORT", "6379")),
                "graph_name": os.environ.get("FALKORDB_GRAPH", "graphiti")
            },
            "ocr": {
                "provider": os.environ.get("OCR_PROVIDER", "mistral"),
                "model": os.environ.get("OCR_MODEL", "mistral-ocr-latest"),
                "use_mistral": os.environ.get("OCR_PROVIDER", "mistral") == "mistral"
            },
            "system": {
                "max_file_size": int(os.environ.get("MAX_FILE_SIZE", "50")),
                "max_parallel_processes": int(os.environ.get("MAX_PARALLEL_PROCESSES", "4")),
                "log_level": os.environ.get("LOG_LEVEL", "INFO")
            }
        }
    except Exception as e:
        logger.error(f"Error getting current settings: {e}")
        return {}


def validate_llm_settings(settings: Dict[str, Any]) -> bool:
    """
    Validate LLM settings.

    Args:
        settings: LLM settings dictionary

    Returns:
        True if valid, False otherwise
    """
    try:
        required_fields = ["provider", "model"]
        for field in required_fields:
            if field not in settings:
                logger.error(f"Missing required LLM setting: {field}")
                return False

        # Validate provider
        valid_providers = ["openrouter", "openai", "ollama", "gemini", "mistral"]
        if settings["provider"] not in valid_providers:
            logger.error(f"Invalid LLM provider: {settings['provider']}")
            return False

        return True
    except Exception as e:
        logger.error(f"Error validating LLM settings: {e}")
        return False


def validate_embedding_settings(settings: Dict[str, Any]) -> bool:
    """
    Validate embedding settings.

    Args:
        settings: Embedding settings dictionary

    Returns:
        True if valid, False otherwise
    """
    try:
        # Validate chunk size
        if "chunk_size" in settings:
            chunk_size = settings["chunk_size"]
            if not isinstance(chunk_size, int) or chunk_size < 100 or chunk_size > 5000:
                logger.error(f"Invalid chunk size: {chunk_size}")
                return False

        # Validate chunk overlap
        if "chunk_overlap" in settings:
            chunk_overlap = settings["chunk_overlap"]
            if not isinstance(chunk_overlap, int) or chunk_overlap < 0:
                logger.error(f"Invalid chunk overlap: {chunk_overlap}")
                return False

        return True
    except Exception as e:
        logger.error(f"Error validating embedding settings: {e}")
        return False


def validate_database_settings(settings: Dict[str, Any]) -> bool:
    """
    Validate database settings.

    Args:
        settings: Database settings dictionary

    Returns:
        True if valid, False otherwise
    """
    try:
        # Validate port
        if "port" in settings:
            port = settings["port"]
            if not isinstance(port, int) or port < 1 or port > 65535:
                logger.error(f"Invalid database port: {port}")
                return False

        # Validate host
        if "host" in settings:
            host = settings["host"]
            if not isinstance(host, str) or not host.strip():
                logger.error(f"Invalid database host: {host}")
                return False

        return True
    except Exception as e:
        logger.error(f"Error validating database settings: {e}")
        return False
