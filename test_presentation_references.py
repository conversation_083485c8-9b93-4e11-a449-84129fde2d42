#!/usr/bin/env python3
"""
Test script for the presentation reference extraction system.
Specifically designed for PowerPoint presentations converted to PDF.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_presentation_reference_extraction():
    """Test the presentation reference extraction on the pain relief document."""
    
    # Check for Mistral API key
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        print("Please set your Mistral API key to test reference extraction")
        return
    
    # Find the pain relief document
    uploads_dir = Path("uploads")
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if not pain_relief_files:
        print("❌ Pain relief document not found")
        return
    
    test_file = pain_relief_files[0]
    print(f"🎯 Testing presentation reference extraction on: {test_file.name}")
    
    try:
        # Import required modules
        from utils.mistral_ocr import MistralOCRProcessor
        from services.presentation_reference_extractor import PresentationReferenceExtractor
        
        # Initialize processors
        print("🔧 Initializing Mistral OCR processor...")
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        
        print("🔧 Initializing presentation reference extractor...")
        extractor = PresentationReferenceExtractor(mistral_ocr)
        
        # Extract references
        print("🚀 Starting presentation reference extraction...")
        result = await extractor.extract_references(str(test_file))
        
        # Display results
        print("\n" + "="*70)
        print("📊 PRESENTATION REFERENCE EXTRACTION RESULTS")
        print("="*70)
        
        if result.get('success', False):
            print(f"✅ Success: {result['success']}")
            print(f"📄 Document: {result['filename']}")
            print(f"🔍 Method: {result['extraction_method']}")
            print(f"📚 Total References Found: {result['total_reference_count']}")
            print(f"📝 Text Length: {result.get('extracted_text_length', 0):,} characters")
            
            if result.get('csv_path'):
                print(f"💾 CSV Saved: {result['csv_path']}")
            
            # Show references
            references = result.get('references', [])
            if references:
                print(f"\n📋 All {len(references)} References Found:")
                print("-" * 70)
                for i, ref in enumerate(references, 1):
                    print(f"\n{i:2d}. Method: {ref.get('extraction_method', 'unknown')}")
                    print(f"    Text: {ref['text']}")
                    if ref.get('metadata'):
                        metadata = ref['metadata']
                        if metadata.get('year'):
                            print(f"    Year: {metadata['year']}")
                        if metadata.get('journal'):
                            print(f"    Journal: {metadata['journal']}")
                        if metadata.get('first_author'):
                            print(f"    First Author: {metadata['first_author']}")
                        if metadata.get('doi'):
                            print(f"    DOI: {metadata['doi']}")
            
            print(f"\n🎯 TOTAL REFERENCES FOUND: {result['total_reference_count']}")
            
            # Compare with your manual count
            manual_count = 20  # You mentioned counting over 20
            found_count = result['total_reference_count']
            
            print(f"\n📊 COMPARISON WITH MANUAL COUNT:")
            print(f"   Manual count: ~{manual_count}+ references")
            print(f"   Extracted: {found_count} references")
            
            if found_count >= manual_count:
                print("✅ SUCCESS: Found as many or more references than manual count!")
            elif found_count >= manual_count * 0.8:
                print("🟡 GOOD: Found most of the references (80%+)")
            else:
                print("🔴 NEEDS IMPROVEMENT: Found fewer references than expected")
            
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")
        
        print("="*70)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def compare_all_methods():
    """Compare all three extraction methods on the pain relief document."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if not pain_relief_files:
        print("❌ Pain relief document not found")
        return
    
    test_file = pain_relief_files[0]
    print(f"🔄 Comparing all extraction methods on: {test_file.name}")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        from services.presentation_reference_extractor import PresentationReferenceExtractor
        from services.improved_reference_extractor import ImprovedReferenceExtractor
        from services.reference_processor import ReferenceProcessor
        
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        
        # Test presentation method
        print("🎯 Testing presentation method...")
        presentation_extractor = PresentationReferenceExtractor(mistral_ocr)
        presentation_result = await presentation_extractor.extract_references(str(test_file))
        
        # Test improved method
        print("🚀 Testing improved method...")
        improved_extractor = ImprovedReferenceExtractor(mistral_ocr)
        improved_result = await improved_extractor.extract_references(str(test_file))
        
        # Test old method
        print("🔄 Testing old method...")
        old_processor = ReferenceProcessor()
        old_result = await old_processor.extract_references_from_document(str(test_file))
        
        # Compare results
        print("\n" + "="*70)
        print("📊 COMPREHENSIVE COMPARISON RESULTS")
        print("="*70)
        
        presentation_count = presentation_result.get('total_reference_count', 0)
        improved_count = improved_result.get('total_reference_count', 0)
        old_count = old_result.get('total_reference_count', 0)
        
        print(f"🎯 Presentation Method: {presentation_count} references")
        print(f"🆕 Improved Method: {improved_count} references")
        print(f"🔄 Old Method: {old_count} references")
        
        best_count = max(presentation_count, improved_count, old_count)
        
        if presentation_count == best_count:
            print("🏆 WINNER: Presentation method!")
        elif improved_count == best_count:
            print("🏆 WINNER: Improved method!")
        else:
            print("🏆 WINNER: Old method!")
        
        print(f"\n📈 Improvements over old method:")
        print(f"   Presentation: {presentation_count - old_count:+d} references")
        print(f"   Improved: {improved_count - old_count:+d} references")
        
        print("="*70)
        
    except Exception as e:
        print(f"❌ Error during comparison: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🎯 Presentation Reference Extraction Test Suite")
    print("="*50)
    
    # Run presentation-specific test
    print("\n1️⃣ Testing presentation reference extraction...")
    asyncio.run(test_presentation_reference_extraction())
    
    # Run comprehensive comparison
    print("\n2️⃣ Comparing all extraction methods...")
    asyncio.run(compare_all_methods())
    
    print("\n✅ Testing complete!")
