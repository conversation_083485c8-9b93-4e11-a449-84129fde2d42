"""
Test script to check Gemini imports
"""

try:
    print("Importing google.generativeai...")
    import google.generativeai
    print("Successfully imported google.generativeai")
except Exception as e:
    print(f"Error importing google.generativeai: {e}")

try:
    print("\nImporting from graphiti_core.llm_client.gemini_client...")
    from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
    print("Successfully imported GeminiClient and LLMConfig")
except Exception as e:
    print(f"Error importing from graphiti_core.llm_client.gemini_client: {e}")

try:
    print("\nImporting from graphiti_core.embedder.gemini...")
    from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
    print("Successfully imported GeminiEmbedder and GeminiEmbedderConfig")
except Exception as e:
    print(f"Error importing from graphiti_core.embedder.gemini: {e}")

print("\nTest complete")
