"""
Worker Manager for Graphiti document processing pipeline.

This module provides a worker manager that coordinates different types of workers
for processing documents in parallel.
"""

import os
import asyncio
import logging
import multiprocessing
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path
from enum import Enum
import time
import json

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

class WorkerType(Enum):
    """Types of workers in the document processing pipeline."""
    DOCUMENT_PROCESSOR = "document_processor"  # Handles initial document processing and text extraction
    ENTITY_EXTRACTOR = "entity_extractor"      # Extracts entities from text chunks
    ENTITY_DEDUPLICATOR = "entity_deduplicator" # Deduplicates entities in the knowledge graph
    REFERENCE_EXTRACTOR = "reference_extractor" # Extracts references from documents
    EMBEDDING_GENERATOR = "embedding_generator" # Generates embeddings for text chunks
    DATABASE_WRITER = "database_writer"        # Writes data to the database

class WorkerStatus(Enum):
    """Status of a worker."""
    IDLE = "idle"
    BUSY = "busy"
    ERROR = "error"
    TERMINATED = "terminated"

class WorkerManager:
    """
    Manager for document processing workers.

    This class manages different types of workers for processing documents in parallel.
    It distributes tasks to workers and monitors their status.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the worker manager.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}

        # Default worker counts
        self.default_worker_counts = {
            WorkerType.DOCUMENT_PROCESSOR: 2,
            WorkerType.ENTITY_EXTRACTOR: 3,
            WorkerType.ENTITY_DEDUPLICATOR: 1,
            WorkerType.REFERENCE_EXTRACTOR: 2,
            WorkerType.EMBEDDING_GENERATOR: 2,
            WorkerType.DATABASE_WRITER: 2
        }

        # Worker counts from config or defaults
        self.worker_counts = {
            worker_type: self.config.get(f"{worker_type.value}_workers",
                                         self.default_worker_counts[worker_type])
            for worker_type in WorkerType
        }

        # Task queues for each worker type
        self.queues = {
            worker_type: asyncio.Queue()
            for worker_type in WorkerType
        }

        # Worker processes/tasks
        self.workers = {
            worker_type: []
            for worker_type in WorkerType
        }

        # Worker status
        self.worker_status = {
            worker_type: {}
            for worker_type in WorkerType
        }

        # Results queue
        self.results_queue = asyncio.Queue()

        # Processing statistics
        self.stats = {
            "documents_processed": 0,
            "entities_extracted": 0,
            "entities_merged": 0,
            "references_extracted": 0,
            "embeddings_generated": 0,
            "errors": 0,
            "start_time": None,
            "end_time": None
        }

        # Flag to indicate if the manager is running
        self.is_running = False

    async def start(self):
        """Start the worker manager and all workers."""
        if self.is_running:
            logger.warning("Worker manager is already running")
            return

        logger.info("Starting worker manager")
        self.is_running = True
        self.stats["start_time"] = time.time()

        # Start workers for each type
        for worker_type in WorkerType:
            await self._start_workers(worker_type)

        # Start the result collector
        asyncio.create_task(self._collect_results())

        logger.info("Worker manager started")

    async def stop(self):
        """Stop the worker manager and all workers."""
        if not self.is_running:
            logger.warning("Worker manager is not running")
            return

        logger.info("Stopping worker manager")
        self.is_running = False
        self.stats["end_time"] = time.time()

        # Stop all workers
        for worker_type in WorkerType:
            await self._stop_workers(worker_type)

        logger.info("Worker manager stopped")

    async def _start_workers(self, worker_type: WorkerType):
        """
        Start workers of a specific type.

        Args:
            worker_type: Type of workers to start
        """
        worker_count = self.worker_counts[worker_type]
        logger.info(f"Starting {worker_count} {worker_type.value} workers")

        for i in range(worker_count):
            worker_id = f"{worker_type.value}_{i}"
            worker_task = asyncio.create_task(
                self._worker_loop(worker_type, worker_id)
            )
            self.workers[worker_type].append(worker_task)
            self.worker_status[worker_type][worker_id] = WorkerStatus.IDLE

        logger.info(f"Started {worker_count} {worker_type.value} workers")

    async def _stop_workers(self, worker_type: WorkerType):
        """
        Stop workers of a specific type.

        Args:
            worker_type: Type of workers to stop
        """
        logger.info(f"Stopping {worker_type.value} workers")

        # Cancel all worker tasks
        for worker_task in self.workers[worker_type]:
            worker_task.cancel()

        # Wait for all worker tasks to complete
        if self.workers[worker_type]:
            await asyncio.gather(*self.workers[worker_type], return_exceptions=True)

        # Clear the worker list
        self.workers[worker_type] = []

        # Update worker status
        for worker_id in self.worker_status[worker_type]:
            self.worker_status[worker_type][worker_id] = WorkerStatus.TERMINATED

        logger.info(f"Stopped {worker_type.value} workers")

    async def _worker_loop(self, worker_type: WorkerType, worker_id: str):
        """
        Main loop for a worker.

        Args:
            worker_type: Type of worker
            worker_id: Unique ID for the worker
        """
        logger.info(f"Worker {worker_id} started")

        try:
            while self.is_running:
                # Update status to idle
                self.worker_status[worker_type][worker_id] = WorkerStatus.IDLE

                # Get a task from the queue
                try:
                    task = await asyncio.wait_for(
                        self.queues[worker_type].get(),
                        timeout=5.0  # Check is_running every 5 seconds
                    )
                except asyncio.TimeoutError:
                    # No task available, check if we should continue running
                    continue

                # Update status to busy
                self.worker_status[worker_type][worker_id] = WorkerStatus.BUSY

                # Process the task
                try:
                    logger.info(f"Worker {worker_id} processing task: {task.get('id', 'unknown')}")
                    result = await self._process_task(worker_type, task)

                    # Put the result in the results queue
                    await self.results_queue.put({
                        "worker_id": worker_id,
                        "worker_type": worker_type.value,
                        "task_id": task.get("id"),
                        "success": True,
                        "result": result
                    })

                except Exception as e:
                    logger.error(f"Worker {worker_id} error processing task: {e}")

                    # Update status to error
                    self.worker_status[worker_type][worker_id] = WorkerStatus.ERROR

                    # Put the error in the results queue
                    await self.results_queue.put({
                        "worker_id": worker_id,
                        "worker_type": worker_type.value,
                        "task_id": task.get("id"),
                        "success": False,
                        "error": str(e)
                    })

                    # Increment error count
                    self.stats["errors"] += 1

                finally:
                    # Mark the task as done
                    self.queues[worker_type].task_done()

        except asyncio.CancelledError:
            logger.info(f"Worker {worker_id} cancelled")

        except Exception as e:
            logger.error(f"Worker {worker_id} unexpected error: {e}")

        finally:
            # Update status to terminated
            self.worker_status[worker_type][worker_id] = WorkerStatus.TERMINATED
            logger.info(f"Worker {worker_id} stopped")

    async def _process_task(self, worker_type: WorkerType, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a task based on the worker type.

        Args:
            worker_type: Type of worker
            task: Task to process

        Returns:
            Result of the task
        """
        # Process the task based on the worker type
        if worker_type == WorkerType.DOCUMENT_PROCESSOR:
            return await self._process_document_task(task)
        elif worker_type == WorkerType.ENTITY_EXTRACTOR:
            return await self._process_entity_task(task)
        elif worker_type == WorkerType.ENTITY_DEDUPLICATOR:
            return await self._process_entity_deduplication_task(task)
        elif worker_type == WorkerType.REFERENCE_EXTRACTOR:
            return await self._process_reference_task(task)
        elif worker_type == WorkerType.EMBEDDING_GENERATOR:
            return await self._process_embedding_task(task)
        elif worker_type == WorkerType.DATABASE_WRITER:
            return await self._process_database_task(task)
        else:
            raise ValueError(f"Unknown worker type: {worker_type}")

    async def _collect_results(self):
        """Collect and process results from workers."""
        logger.info("Result collector started")

        try:
            while self.is_running:
                try:
                    # Get a result from the queue with timeout
                    result = await asyncio.wait_for(
                        self.results_queue.get(),
                        timeout=5.0  # Check is_running every 5 seconds
                    )

                    # Process the result
                    await self._process_result(result)

                    # Mark the result as processed
                    self.results_queue.task_done()

                except asyncio.TimeoutError:
                    # No result available, check if we should continue running
                    continue

        except asyncio.CancelledError:
            logger.info("Result collector cancelled")

        except Exception as e:
            logger.error(f"Result collector error: {e}")

        finally:
            logger.info("Result collector stopped")

    async def _process_result(self, result: Dict[str, Any]):
        """
        Process a result from a worker.

        Args:
            result: Result from a worker
        """
        # Update statistics based on the worker type and result
        worker_type = result.get("worker_type")

        if result.get("success", False):
            if worker_type == WorkerType.DOCUMENT_PROCESSOR.value:
                self.stats["documents_processed"] += 1
            elif worker_type == WorkerType.ENTITY_EXTRACTOR.value:
                self.stats["entities_extracted"] += len(result.get("result", {}).get("entities", []))
            elif worker_type == WorkerType.ENTITY_DEDUPLICATOR.value:
                self.stats["entities_merged"] = self.stats.get("entities_merged", 0) + result.get("result", {}).get("entities_merged", 0)
            elif worker_type == WorkerType.REFERENCE_EXTRACTOR.value:
                self.stats["references_extracted"] += len(result.get("result", {}).get("references", []))
            elif worker_type == WorkerType.EMBEDDING_GENERATOR.value:
                self.stats["embeddings_generated"] += len(result.get("result", {}).get("embeddings", []))
        else:
            # Log the error
            logger.error(f"Worker {result.get('worker_id')} error: {result.get('error')}")

            # Increment error count
            self.stats["errors"] += 1

    async def _process_document_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a document task.

        Args:
            task: Document task to process

        Returns:
            Result of the document processing
        """
        from services.document_processing_service import get_document_processing_service

        file_path = task.get("file_path")
        chunk_size = task.get("chunk_size", 1200)
        overlap = task.get("overlap", 0)

        logger.info(f"Processing document: {file_path}")

        # Get the document processing service
        service = await get_document_processing_service()

        # Process the document (text extraction only)
        result = await service.process_document(
            file_path,
            chunk_size=chunk_size,
            overlap=overlap,
            extract_entities=False,  # We'll do this in a separate worker
            extract_references=False,  # We'll do this in a separate worker
            extract_metadata=True,
            generate_embeddings=False  # We'll do this in a separate worker
        )

        # If successful, queue tasks for entity extraction, reference extraction, and embedding generation
        if result.get("success", False):
            # Queue entity extraction tasks for each chunk
            for chunk_id, chunk in enumerate(result.get("chunks", [])):
                await self.add_entity_extraction_task({
                    "id": f"{result.get('episode_id')}_{chunk_id}",
                    "document_id": result.get("episode_id"),
                    "chunk_id": chunk_id,
                    "text": chunk.get("body"),
                    "fact_id": chunk.get("uuid")
                })

            # Queue entity deduplication task for the document
            await self.add_entity_deduplication_task({
                "id": f"dedup_{result.get('episode_id')}",
                "document_id": result.get("episode_id"),
                "merge": True
            })

            # Queue reference extraction task
            await self.add_reference_extraction_task({
                "id": result.get("episode_id"),
                "document_id": result.get("episode_id"),
                "file_path": file_path
            })

            # Queue embedding generation tasks for each chunk
            for chunk_id, chunk in enumerate(result.get("chunks", [])):
                await self.add_embedding_generation_task({
                    "id": f"{result.get('episode_id')}_{chunk_id}",
                    "document_id": result.get("episode_id"),
                    "chunk_id": chunk_id,
                    "text": chunk.get("body"),
                    "fact_id": chunk.get("uuid")
                })

        return result

    async def _process_entity_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an entity extraction task.

        Args:
            task: Entity extraction task to process

        Returns:
            Result of the entity extraction
        """
        from entity_extraction import extract_entities

        document_id = task.get("document_id")
        fact_id = task.get("fact_id")
        text = task.get("text")

        logger.info(f"Extracting entities from chunk {task.get('chunk_id')} of document {document_id}")

        # Extract entities from the text
        entities = await extract_entities(text)

        # Queue database write task for the entities
        await self.add_database_write_task({
            "id": f"entities_{task.get('id')}",
            "type": "entities",
            "document_id": document_id,
            "fact_id": fact_id,
            "entities": entities
        })

        return {
            "document_id": document_id,
            "chunk_id": task.get("chunk_id"),
            "entities": entities
        }

    async def _process_entity_deduplication_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an entity deduplication task.

        Args:
            task: Entity deduplication task to process

        Returns:
            Result of the entity deduplication
        """
        from entity_deduplication import EntityDeduplicator

        document_id = task.get("document_id")
        entity_type = task.get("entity_type")
        merge = task.get("merge", True)

        logger.info(f"Deduplicating entities for document {document_id}")

        # Initialize the entity deduplicator
        deduplicator = EntityDeduplicator()

        # Run deduplication
        result = await deduplicator.deduplicate_entities(
            document_id=document_id,
            entity_type=entity_type,
            merge=merge
        )

        logger.info(f"Deduplication completed for document {document_id}")
        logger.info(f"Found {result.duplicate_groups} duplicate groups with {result.total_duplicates} total duplicates")
        logger.info(f"Merged {result.merged_entities} entities")

        return {
            "document_id": document_id,
            "duplicates_found": result.total_duplicates,
            "duplicate_groups": result.duplicate_groups,
            "entities_merged": result.merged_entities,
            "processing_time": result.processing_time
        }

    async def _process_reference_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a reference extraction task.

        Args:
            task: Reference extraction task to process

        Returns:
            Result of the reference extraction
        """
        from reference_extraction import ReferenceExtractor
        from utils.config import get_config

        document_id = task.get("document_id")
        file_path = task.get("file_path")

        logger.info(f"Extracting references from document {document_id}")

        # Get the reference extractor
        config = get_config()
        reference_extractor = ReferenceExtractor(llm_provider=config['llm']['provider'])

        # Extract references from the document
        references = await reference_extractor.extract_references(file_path)

        # Queue database write task for the references
        await self.add_database_write_task({
            "id": f"references_{task.get('id')}",
            "type": "references",
            "document_id": document_id,
            "references": references
        })

        return {
            "document_id": document_id,
            "references": references
        }

    async def _process_embedding_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process an embedding generation task.

        Args:
            task: Embedding generation task to process

        Returns:
            Result of the embedding generation
        """
        from utils.embedding_utils import generate_embeddings

        document_id = task.get("document_id")
        fact_id = task.get("fact_id")
        text = task.get("text")

        logger.info(f"Generating embeddings for chunk {task.get('chunk_id')} of document {document_id}")

        # Generate embeddings for the text
        embedding = await generate_embeddings([text])

        # Queue database write task for the embedding
        if embedding and len(embedding) > 0:
            await self.add_database_write_task({
                "id": f"embedding_{task.get('id')}",
                "type": "embedding",
                "document_id": document_id,
                "fact_id": fact_id,
                "embedding": embedding[0]
            })

        return {
            "document_id": document_id,
            "chunk_id": task.get("chunk_id"),
            "embeddings": embedding
        }

    async def _process_database_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a database write task.

        Args:
            task: Database write task to process

        Returns:
            Result of the database write
        """
        from database.database_service import get_falkordb_adapter

        task_type = task.get("type")
        document_id = task.get("document_id")

        logger.info(f"Writing {task_type} to database for document {document_id}")

        # Get the database adapter
        adapter = await get_falkordb_adapter()

        if task_type == "entities":
            # Write entities to the database
            fact_id = task.get("fact_id")
            entities = task.get("entities", [])

            # Create entity nodes and link to fact
            for entity in entities:
                # Create entity node
                entity_uuid = await adapter.create_entity_node(
                    name=entity.get("name", "Unknown"),
                    entity_type=entity.get("type", "Other"),
                    properties={
                        "document_id": document_id,
                        "confidence": entity.get("confidence", 1.0)
                    }
                )

                # Link entity to fact
                if entity_uuid and fact_id:
                    await adapter.link_entity_to_fact(entity_uuid, fact_id, {
                        "confidence": entity.get("confidence", 1.0)
                    })

            return {
                "document_id": document_id,
                "entities_written": len(entities)
            }

        elif task_type == "references":
            # Write references to the database
            references = task.get("references", [])

            # Import the reference adapter
            from database.reference_adapter import store_references_in_database

            # Store references in database
            references_stored = await store_references_in_database(document_id, references)

            return {
                "document_id": document_id,
                "references_written": references_stored
            }

        elif task_type == "embedding":
            # Write embedding to the database
            fact_id = task.get("fact_id")
            embedding = task.get("embedding")

            if fact_id and embedding:
                # Update fact with embedding
                await adapter.update_fact_embedding(fact_id, embedding)

            return {
                "document_id": document_id,
                "embedding_written": bool(embedding)
            }

        else:
            raise ValueError(f"Unknown database task type: {task_type}")

    # Task addition methods

    async def add_document_processing_task(self, task: Dict[str, Any]):
        """
        Add a document processing task to the queue.

        Args:
            task: Document processing task
        """
        await self.queues[WorkerType.DOCUMENT_PROCESSOR].put(task)

    async def add_entity_extraction_task(self, task: Dict[str, Any]):
        """
        Add an entity extraction task to the queue.

        Args:
            task: Entity extraction task
        """
        await self.queues[WorkerType.ENTITY_EXTRACTOR].put(task)

    async def add_entity_deduplication_task(self, task: Dict[str, Any]):
        """
        Add an entity deduplication task to the queue.

        Args:
            task: Entity deduplication task
        """
        await self.queues[WorkerType.ENTITY_DEDUPLICATOR].put(task)

    async def add_reference_extraction_task(self, task: Dict[str, Any]):
        """
        Add a reference extraction task to the queue.

        Args:
            task: Reference extraction task
        """
        await self.queues[WorkerType.REFERENCE_EXTRACTOR].put(task)

    async def add_embedding_generation_task(self, task: Dict[str, Any]):
        """
        Add an embedding generation task to the queue.

        Args:
            task: Embedding generation task
        """
        await self.queues[WorkerType.EMBEDDING_GENERATOR].put(task)

    async def add_database_write_task(self, task: Dict[str, Any]):
        """
        Add a database write task to the queue.

        Args:
            task: Database write task
        """
        await self.queues[WorkerType.DATABASE_WRITER].put(task)

    # Status and monitoring methods

    def get_status(self) -> Dict[str, Any]:
        """
        Get the status of the worker manager.

        Returns:
            Status dictionary
        """
        return {
            "is_running": self.is_running,
            "worker_counts": {k.value: v for k, v in self.worker_counts.items()},
            "queue_sizes": {k.value: v.qsize() for k, v in self.queues.items()},
            "worker_status": {k.value: {wid: s.value for wid, s in v.items()} for k, v in self.worker_status.items()},
            "stats": self.stats
        }

    def get_queue_sizes(self) -> Dict[str, int]:
        """
        Get the sizes of all queues.

        Returns:
            Dictionary of queue sizes
        """
        return {k.value: v.qsize() for k, v in self.queues.items()}
