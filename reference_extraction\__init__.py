"""
Reference extraction package for the Graphiti Knowledge Graph.

This package contains modules for extracting references from text.
"""

# Define the ReferenceExtractor class
class ReferenceExtractor:
    """Reference extractor for scientific documents"""

    def __init__(self, llm_provider: str = 'openai'):
        """
        Initialize the reference extractor

        Args:
            llm_provider: The LLM provider to use ('openai', 'mistral', or None)
        """
        self.llm_provider = llm_provider

        # Initialize LLM clients
        self.openai_client = None
        self.mistral_client = None

        # Try to initialize LLM clients if available
        try:
            if llm_provider == 'openai':
                try:
                    import openai
                    import os
                    api_key = os.environ.get('OPENAI_API_KEY')
                    if api_key:
                        self.openai_client = openai.OpenAI(api_key=api_key)
                except ImportError:
                    pass
            elif llm_provider == 'mistral':
                try:
                    from mistralai import Mistral
                    import os
                    api_key = os.environ.get('MISTRAL_API_KEY')
                    if api_key:
                        self.mistral_client = Mistral(api_key=api_key)
                except ImportError:
                    pass
        except Exception:
            # Ignore any errors during initialization
            pass

    async def process_document(self, file_path):
        """
        Process a document to extract references

        Args:
            file_path: Path to the document file

        Returns:
            Dictionary with extraction results
        """
        # This is a placeholder implementation
        # The actual implementation is in reference_extraction.py
        return {
            "success": True,
            "regex_references": [],
            "llm_references": [],
            "total_reference_count": 0,
            "message": "Reference extraction not implemented yet"
        }

    def save_references_to_json(self, references, json_path=None):
        """
        Save references to a JSON file

        Args:
            references: References to save
            json_path: Path to save the JSON file
        """
        if json_path:
            import json
            with open(json_path, 'w') as f:
                json.dump(references, f, indent=2)

    def save_references_to_csv(self, references, csv_path=None):
        """
        Save references to a CSV file

        Args:
            references: References to save
            csv_path: Path to save the CSV file
        """
        if csv_path:
            import csv
            with open(csv_path, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow(['text', 'extraction_method'])
                for ref in references.get('references', []):
                    writer.writerow([ref.get('text', ''), ref.get('extraction_method', 'unknown')])

# Define public API
__all__ = ['ReferenceExtractor']
