"""
Main UUID validation script - orchestrates all UUID validation tasks.

This script coordinates smaller, focused validation modules:
- Episode UUID validation
- Fact UUID validation
- Entity UUID validation
- Relationship UUID validation
- Merged entity validation
- Duplicate UUID detection

Usage:
    python scripts/ensure_all_uuids.py [--fix] [--verbose]
"""

import asyncio
import argparse
import logging
import time
from pathlib import Path
import sys

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from scripts.uuid_validators.episode_validator import EpisodeValidator
from scripts.uuid_validators.fact_validator import FactValidator
from scripts.uuid_validators.entity_validator import EntityValidator
from scripts.uuid_validators.relationship_validator import RelationshipValidator
from scripts.uuid_validators.merge_validator import MergeValidator
from scripts.uuid_validators.duplicate_validator import DuplicateValidator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class UUIDValidationOrchestrator:
    """Orchestrates all UUID validation tasks using modular validators."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        """
        Initialize the orchestrator.

        Args:
            fix: Whether to fix issues
            verbose: Whether to enable verbose logging
        """
        self.fix = fix
        self.verbose = verbose

        # Initialize validators
        self.episode_validator = EpisodeValidator(fix, verbose)
        self.fact_validator = FactValidator(fix, verbose)
        self.entity_validator = EntityValidator(fix, verbose)
        self.relationship_validator = RelationshipValidator(fix, verbose)
        self.merge_validator = MergeValidator(fix, verbose)
        self.duplicate_validator = DuplicateValidator(fix, verbose)

        self.all_stats = {}

    async def run_all_validations(self):
        """Run all UUID validations using modular validators."""
        start_time = time.time()

        logger.info("Starting UUID validation...")

        # Run all validators
        await self.episode_validator.validate()
        await self.fact_validator.validate()
        await self.entity_validator.validate()
        await self.relationship_validator.validate()
        await self.merge_validator.validate()
        await self.duplicate_validator.validate()

        # Collect all statistics
        self.all_stats.update(self.episode_validator.get_stats())
        self.all_stats.update(self.fact_validator.get_stats())
        self.all_stats.update(self.entity_validator.get_stats())
        self.all_stats.update(self.relationship_validator.get_stats())
        self.all_stats.update(self.merge_validator.get_stats())
        self.all_stats.update(self.duplicate_validator.get_stats())

        # Print summary
        self._print_summary()

        end_time = time.time()
        logger.info(f"\nValidation completed in {end_time - start_time:.2f} seconds")

    def _print_summary(self):
        """Print validation summary."""
        logger.info("\nUUID Validation Summary:")

        # Episodes
        logger.info(f"Episodes: {self.all_stats.get('episodes_missing_uuid', 0)} out of {self.all_stats.get('episodes_total', 0)} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('episodes_fixed', 0)}")

        # Facts
        logger.info(f"Facts: {self.all_stats.get('facts_missing_uuid', 0)} out of {self.all_stats.get('facts_total', 0)} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('facts_fixed', 0)}")

        # Entities
        logger.info(f"Entities: {self.all_stats.get('entities_missing_uuid', 0)} out of {self.all_stats.get('entities_total', 0)} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('entities_fixed', 0)}")

        # Relationships
        logger.info(f"Relationships: {self.all_stats.get('relationships_missing_uuid', 0)} out of {self.all_stats.get('relationships_total', 0)} missing UUIDs")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('relationships_fixed', 0)}")

        # Orphaned relationships
        logger.info(f"Orphaned relationships: {self.all_stats.get('orphaned_relationships', 0)}")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('orphaned_relationships_fixed', 0)}")

        # Inconsistent relationships
        logger.info(f"Inconsistent relationships: {self.all_stats.get('inconsistent_relationships', 0)}")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('inconsistent_relationships_fixed', 0)}")

        # Duplicate UUIDs
        logger.info(f"Duplicate UUIDs: {self.all_stats.get('duplicate_uuids', 0)}")
        if self.fix:
            logger.info(f"  - Fixed: {self.all_stats.get('duplicate_uuids_fixed', 0)}")

        # Calculate totals
        total_issues = sum([
            self.all_stats.get('episodes_missing_uuid', 0),
            self.all_stats.get('facts_missing_uuid', 0),
            self.all_stats.get('entities_missing_uuid', 0),
            self.all_stats.get('relationships_missing_uuid', 0),
            self.all_stats.get('orphaned_relationships', 0),
            self.all_stats.get('inconsistent_relationships', 0),
            self.all_stats.get('duplicate_uuids', 0)
        ])

        total_fixed = sum([
            self.all_stats.get('episodes_fixed', 0),
            self.all_stats.get('facts_fixed', 0),
            self.all_stats.get('entities_fixed', 0),
            self.all_stats.get('relationships_fixed', 0),
            self.all_stats.get('orphaned_relationships_fixed', 0),
            self.all_stats.get('inconsistent_relationships_fixed', 0),
            self.all_stats.get('duplicate_uuids_fixed', 0)
        ])

        logger.info(f"\nTotal issues: {total_issues}")
        if self.fix:
            logger.info(f"Total fixed: {total_fixed}")



async def main():
    """Main function."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Ensure all elements have correct UUIDs")
    parser.add_argument("--fix", action="store_true", help="Fix any issues found")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    try:
        # Create orchestrator
        orchestrator = UUIDValidationOrchestrator(fix=args.fix, verbose=args.verbose)

        # Run all validations
        await orchestrator.run_all_validations()

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
