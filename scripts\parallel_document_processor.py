"""
<PERSON><PERSON><PERSON> to process documents in parallel.

This script processes multiple documents in parallel using the document processing service.
"""

import os
import sys
import asyncio
import argparse
import logging
import json
from pathlib import Path
from typing import List, Dict, Any
import concurrent.futures
from datetime import datetime, timezone

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.document_processing_service import DocumentProcessingService
from utils.config import get_config
from utils.logging_utils import get_logger
from utils.file_utils import is_supported_file
from database.falkordb_adapter import GraphitiFalkorDBAdapter
from dotenv import load_dotenv
import openai
from openai import OpenAI

# Set up logger
logger = get_logger(__name__)

# Load environment variables
load_dotenv()

async def generate_embeddings(api_key: str, texts: List[str], model: str = "text-embedding-3-small") -> List[List[float]]:
    """
    Generate embeddings for a list of texts using OpenAI.

    Args:
        api_key: OpenAI API key
        texts: List of texts to embed
        model: Embedding model to use

    Returns:
        List of embedding vectors
    """
    logger.info(f"Generating embeddings for {len(texts)} texts using model {model}")

    try:
        client = OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=texts,
            model=model
        )

        embeddings = [data.embedding for data in response.data]
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise

async def create_vector_index(adapter: GraphitiFalkorDBAdapter) -> bool:
    """
    Create a vector index in FalkorDB for Fact nodes.

    Args:
        adapter: FalkorDB adapter

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Creating vector index for Fact nodes")

    try:
        # Check if the index already exists
        check_index_query = """
        CALL db.indexes() YIELD name, label, properties, type
        WHERE name = 'fact_embedding_index' AND type = 'VECTOR'
        RETURN count(*) > 0 AS exists
        """

        result = adapter.execute_cypher(check_index_query)

        if result and len(result) > 1 and result[1] and result[1][0][0]:
            logger.info("Vector index 'fact_embedding_index' already exists")
            return True

        # Create the vector index
        create_index_query = """
        CREATE VECTOR INDEX fact_embedding_index
        FOR (f:Fact)
        ON (f.embedding)
        OPTIONS {
          dimension: 1536,
          similarity: 'cosine'
        }
        """

        adapter.execute_cypher(create_index_query)
        logger.info("Created vector index 'fact_embedding_index'")
        return True
    except Exception as e:
        logger.error(f"Error creating vector index: {e}")
        return False

async def get_facts_for_episode(adapter: GraphitiFalkorDBAdapter, episode_id: str) -> List[Dict[str, Any]]:
    """
    Get all facts for a specific episode.

    Args:
        adapter: FalkorDB adapter
        episode_id: Episode UUID

    Returns:
        List of facts
    """
    logger.info(f"Getting facts for episode {episode_id}")

    try:
        query = f"""
        MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
        RETURN f.uuid AS uuid, f.body AS body
        """

        result = adapter.execute_cypher(query)

        facts = []
        if result and len(result) > 1:
            for row in result[1]:
                facts.append({
                    "uuid": row[0],
                    "body": row[1]
                })

        logger.info(f"Found {len(facts)} facts for episode {episode_id}")
        return facts
    except Exception as e:
        logger.error(f"Error getting facts for episode: {e}")
        return []

async def update_facts_with_embeddings(adapter: GraphitiFalkorDBAdapter, facts: List[Dict[str, Any]], embeddings: List[List[float]]) -> bool:
    """
    Update Fact nodes with their embeddings.

    Args:
        adapter: FalkorDB adapter
        facts: List of facts to update
        embeddings: List of embedding vectors

    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Updating {len(facts)} Fact nodes with embeddings")

    try:
        for i, fact in enumerate(facts):
            # Convert embedding to JSON string to store in FalkorDB
            embedding_json = json.dumps(embeddings[i])
            timestamp = datetime.now(timezone.utc).isoformat()

            query = f"""
            MATCH (f:Fact {{uuid: '{fact["uuid"]}'}})
            SET f.embedding = '{embedding_json}',
                f.embedding_model = 'text-embedding-3-small',
                f.embedding_updated_at = '{timestamp}'
            RETURN f.uuid
            """

            result = adapter.execute_cypher(query)
            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"Failed to update fact {fact['uuid']} with embedding")

        logger.info(f"Updated {len(facts)} Fact nodes with embeddings")
        return True
    except Exception as e:
        logger.error(f"Error updating Facts with embeddings: {e}")
        return False

async def add_embeddings_to_episode(episode_id: str) -> Dict[str, Any]:
    """
    Add embeddings to all facts in an episode.

    Args:
        episode_id: Episode UUID

    Returns:
        Result dictionary
    """
    logger.info(f"Adding embeddings to facts in episode {episode_id}")

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return {"success": False, "error": "OpenAI API key not found"}

    try:
        # Connect to FalkorDB
        adapter = GraphitiFalkorDBAdapter()

        # Create vector index if it doesn't exist
        if not await create_vector_index(adapter):
            logger.error("Failed to create vector index")
            return {"success": False, "error": "Failed to create vector index"}

        # Get facts for the episode
        facts = await get_facts_for_episode(adapter, episode_id)

        if not facts:
            logger.error(f"No facts found for episode {episode_id}")
            return {"success": False, "error": "No facts found for episode"}

        # Generate embeddings for the facts
        texts = [fact["body"] for fact in facts]
        embeddings = await generate_embeddings(openai_api_key, texts)

        # Update facts with embeddings
        if not await update_facts_with_embeddings(adapter, facts, embeddings):
            logger.error("Failed to update facts with embeddings")
            return {"success": False, "error": "Failed to update facts with embeddings"}

        logger.info(f"Successfully added embeddings to {len(facts)} facts in episode {episode_id}")

        # Close FalkorDB connection
        adapter.close()

        return {
            "success": True,
            "episode_id": episode_id,
            "embeddings_added": len(facts)
        }
    except Exception as e:
        logger.error(f"Error adding embeddings to episode: {e}", exc_info=True)
        return {"success": False, "error": str(e)}

async def process_document(service: DocumentProcessingService, file_path: Path, args: argparse.Namespace) -> Dict[str, Any]:
    """
    Process a single document.

    Args:
        service: Document processing service
        file_path: Path to the document file
        args: Command line arguments

    Returns:
        Result dictionary
    """
    logger.info(f"Processing document: {file_path}")

    result = await service.process_document(
        file_path,
        chunk_size=args.chunk_size,
        overlap=args.overlap,
        extract_entities=args.extract_entities,
        extract_references=args.extract_references,
        extract_metadata=args.extract_metadata
    )

    if result.get("success", False):
        logger.info(f"Successfully processed document: {file_path}")

        # Add embeddings if requested
        if args.add_embeddings:
            logger.info(f"Adding embeddings to document: {file_path}")
            episode_id = result.get("episode_id")
            if episode_id:
                embedding_result = await add_embeddings_to_episode(episode_id)
                if embedding_result.get("success", False):
                    result["embeddings_added"] = embedding_result.get("embeddings_added", 0)
                    logger.info(f"Successfully added embeddings to document: {file_path}")
                else:
                    logger.error(f"Failed to add embeddings to document: {file_path}")
                    result["embedding_error"] = embedding_result.get("error")
            else:
                logger.error(f"No episode ID found for document: {file_path}")
                result["embedding_error"] = "No episode ID found"
    else:
        logger.error(f"Failed to process document: {file_path}")

    return result

async def process_documents_in_parallel(
    service: DocumentProcessingService,
    file_paths: List[Path],
    args: argparse.Namespace
) -> List[Dict[str, Any]]:
    """
    Process multiple documents in parallel.

    Args:
        service: Document processing service
        file_paths: List of paths to document files
        args: Command line arguments

    Returns:
        List of result dictionaries
    """
    logger.info(f"Processing {len(file_paths)} documents in parallel with {args.max_workers} workers")

    # Create tasks for each document
    tasks = []
    for file_path in file_paths:
        task = asyncio.create_task(process_document(service, file_path, args))
        tasks.append(task)

    # Wait for all tasks to complete
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Process results
    successful = 0
    failed = 0

    for i, result in enumerate(results):
        if isinstance(result, Exception):
            logger.error(f"Error processing document {file_paths[i]}: {result}")
            failed += 1
        elif result.get("success", False):
            successful += 1
        else:
            failed += 1

    logger.info(f"Processed {len(results)} documents: {successful} successful, {failed} failed")

    return results

async def process_directory(
    service: DocumentProcessingService,
    directory_path: Path,
    args: argparse.Namespace
) -> List[Dict[str, Any]]:
    """
    Process all documents in a directory.

    Args:
        service: Document processing service
        directory_path: Path to the directory
        args: Command line arguments

    Returns:
        List of result dictionaries
    """
    logger.info(f"Processing documents in directory: {directory_path}")

    # Get all supported files in the directory
    file_paths = []
    for file_path in directory_path.iterdir():
        if file_path.is_file() and is_supported_file(file_path.name):
            file_paths.append(file_path)

    logger.info(f"Found {len(file_paths)} supported files in {directory_path}")

    # Process the files in parallel
    return await process_documents_in_parallel(service, file_paths, args)

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Process documents in parallel")
    parser.add_argument("path", help="Path to a document file or directory")
    parser.add_argument("--chunk-size", type=int, default=1200, help="Size of text chunks in characters")
    parser.add_argument("--overlap", type=int, default=0, help="Overlap between chunks in characters")
    parser.add_argument("--max-workers", type=int, default=4, help="Maximum number of worker processes")
    parser.add_argument("--no-entities", dest="extract_entities", action="store_false", help="Skip entity extraction")
    parser.add_argument("--no-references", dest="extract_references", action="store_false", help="Skip reference extraction")
    parser.add_argument("--no-metadata", dest="extract_metadata", action="store_false", help="Skip metadata extraction")
    parser.add_argument("--add-embeddings", action="store_true", help="Generate and store embeddings for facts")
    parser.set_defaults(extract_entities=True, extract_references=True, extract_metadata=True, add_embeddings=False)

    args = parser.parse_args()

    # Create the document processing service
    service = DocumentProcessingService()

    # Process the path
    path = Path(args.path)

    if path.is_file():
        if not is_supported_file(path.name):
            logger.error(f"Unsupported file type: {path}")
            return

        # Process a single document
        result = await process_document(service, path, args)

        if result.get("success", False):
            print(f"Successfully processed document: {path}")
            print(f"Episode ID: {result.get('episode_id')}")
            print(f"Chunks: {result.get('chunks')}")
            print(f"Entities extracted: {result.get('entities_extracted')}")
            print(f"References extracted: {result.get('references_extracted')}")

            if args.add_embeddings:
                if "embeddings_added" in result:
                    print(f"Embeddings added: {result.get('embeddings_added')}")
                elif "embedding_error" in result:
                    print(f"Embedding error: {result.get('embedding_error')}")
        else:
            print(f"Failed to process document: {path}")
            print(f"Error: {result.get('error')}")

    elif path.is_dir():
        # Process all documents in the directory
        results = await process_directory(service, path, args)

        # Print summary
        successful = sum(1 for r in results if isinstance(r, dict) and r.get("success", False))
        failed = len(results) - successful

        print(f"Processed {len(results)} documents: {successful} successful, {failed} failed")

    else:
        logger.error(f"Path not found: {path}")

if __name__ == "__main__":
    asyncio.run(main())
