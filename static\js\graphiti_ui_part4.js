/**
 * Graphiti UI Part 4 - Additional functionality for the Graphiti Knowledge Graph UI
 *
 * This file contains functions for the References tab (continued) and Settings tab.
 */

/**
 * Load references
 */
function loadReferences() {
    // Show loading spinner
    const loadingSpinner = document.getElementById('references-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch references
    fetch('/api/references?limit=20')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Render references
            renderReferences(data);

            // Update reference statistics
            updateReferenceStatistics(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Enable export button
            const exportButton = document.getElementById('export-references-button');
            if (exportButton) {
                exportButton.disabled = false;
            }

            // Enable visualize button
            const visualizeButton = document.getElementById('visualize-references-button');
            if (visualizeButton) {
                visualizeButton.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error loading references:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const referencesList = document.getElementById('references-list');
            if (referencesList) {
                referencesList.innerHTML = `<div class="alert alert-danger">Error loading references: ${error.message}</div>`;
            }
        });
}

/**
 * Render references in the UI
 *
 * @param {Object} data - Reference data from the API
 */
function renderReferences(data) {
    const referencesList = document.getElementById('references-list');
    if (!referencesList) {
        console.error("References list element not found");
        return;
    }

    // Clear existing content
    referencesList.innerHTML = '';

    // If no references, show message
    if (!data.references || data.references.length === 0) {
        referencesList.innerHTML = '<div class="alert alert-info">No references found.</div>';
        return;
    }

    // Create references table
    const table = document.createElement('table');
    table.className = 'table table-striped table-hover';

    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Title</th>
            <th>Authors</th>
            <th>Year</th>
            <th>Journal</th>
            <th>DOI</th>
            <th>Document</th>
        </tr>
    `;
    table.appendChild(thead);

    // Create table body
    const tbody = document.createElement('tbody');

    // Add reference rows
    data.references.forEach(ref => {
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>${ref.title || 'Unknown'}</td>
            <td>${ref.authors?.join(', ') || 'Unknown'}</td>
            <td>${ref.year || 'Unknown'}</td>
            <td>${ref.journal || 'Unknown'}</td>
            <td>${ref.doi ? `<a href="https://doi.org/${ref.doi}" target="_blank">${ref.doi}</a>` : 'None'}</td>
            <td>${ref.document_id ? `<a href="/documents/${ref.document_id}">${ref.document_title || 'View'}</a>` : 'None'}</td>
        `;

        tbody.appendChild(row);
    });

    table.appendChild(tbody);
    referencesList.appendChild(table);

    // Add pagination if available
    if (data.pagination) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'mt-4 d-flex justify-content-center';

        const pagination = document.createElement('nav');
        pagination.setAttribute('aria-label', 'Reference pagination');

        const paginationList = document.createElement('ul');
        paginationList.className = 'pagination';

        // Previous page button
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${data.pagination.has_prev ? '' : 'disabled'}`;

        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (data.pagination.has_prev) {
                loadReferencesPage(data.pagination.page - 1);
            }
        });

        prevItem.appendChild(prevLink);
        paginationList.appendChild(prevItem);

        // Page numbers
        for (let i = 1; i <= data.pagination.total_pages; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === data.pagination.page ? 'active' : ''}`;

            const pageLink = document.createElement('a');
            pageLink.className = 'page-link';
            pageLink.href = '#';
            pageLink.textContent = i;
            pageLink.addEventListener('click', function(e) {
                e.preventDefault();
                loadReferencesPage(i);
            });

            pageItem.appendChild(pageLink);
            paginationList.appendChild(pageItem);
        }

        // Next page button
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${data.pagination.has_next ? '' : 'disabled'}`;

        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (data.pagination.has_next) {
                loadReferencesPage(data.pagination.page + 1);
            }
        });

        nextItem.appendChild(nextLink);
        paginationList.appendChild(nextItem);

        pagination.appendChild(paginationList);
        paginationContainer.appendChild(pagination);
        referencesList.appendChild(paginationContainer);
    }
}

/**
 * Update reference statistics
 *
 * @param {Object} data - Reference data from the API
 */
function updateReferenceStatistics(data) {
    // Show statistics container
    const statisticsContainer = document.getElementById('reference-statistics');
    if (statisticsContainer) {
        statisticsContainer.style.display = 'block';
    }

    // Update total references count
    const totalReferencesCount = document.getElementById('total-references-count');
    if (totalReferencesCount) {
        totalReferencesCount.textContent = data.total || 0;
    }

    // Update documents count
    const documentsCount = document.getElementById('documents-count');
    if (documentsCount) {
        documentsCount.textContent = data.document_count || 0;
    }

    // Update journals count
    const journalsCount = document.getElementById('journals-count');
    if (journalsCount) {
        journalsCount.textContent = data.journal_count || 0;
    }

    // Update authors count
    const authorsCount = document.getElementById('authors-count');
    if (authorsCount) {
        authorsCount.textContent = data.author_count || 0;
    }
}

/**
 * Load a specific page of references
 *
 * @param {number} page - Page number to load
 */
function loadReferencesPage(page) {
    // Get document filter
    const documentId = document.getElementById('document-selector')?.value || '';

    // Show loading spinner
    const loadingSpinner = document.getElementById('references-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Build URL
    let url = `/api/references?limit=20&page=${page}`;
    if (documentId) {
        url += `&document_id=${documentId}`;
    }

    // Fetch references
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Render references
            renderReferences(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading references page:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const referencesList = document.getElementById('references-list');
            if (referencesList) {
                referencesList.innerHTML = `<div class="alert alert-danger">Error loading references: ${error.message}</div>`;
            }
        });
}

/**
 * Filter references by document
 */
function filterReferencesByDocument() {
    // Get document ID
    const documentId = document.getElementById('document-selector')?.value || '';

    // Show loading spinner
    const loadingSpinner = document.getElementById('references-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Build URL
    let url = '/api/references?limit=20';
    if (documentId) {
        url += `&source_document=${encodeURIComponent(documentId)}`;
    }

    // Fetch references
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Render references
            renderReferences(data);

            // Update reference statistics
            updateReferenceStatistics(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error filtering references:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const referencesList = document.getElementById('references-list');
            if (referencesList) {
                referencesList.innerHTML = `<div class="alert alert-danger">Error filtering references: ${error.message}</div>`;
            }
        });
}
