"""
Example demonstrating how to use Graphiti with Google Gemini
for knowledge extraction from text.
"""

import asyncio
import logging
import os
import json
from datetime import datetime, timezone
from typing import List, Dict, Any

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.gemini_client import GeminiClient, LLMConfig
from graphiti_core.embedder.gemini import GeminiEmbedder, GeminiEmbedderConfig
from graphiti_core.nodes import EpisodeType, Fact
from graphiti_core.utils.maintenance.graph_data_operations import clear_data


def setup_logging():
    """Set up logging configuration."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    )


async def extract_entities_and_relationships(llm_client, text: str) -> Dict[str, Any]:
    """Extract entities and relationships from text using Gemini."""
    prompt = f"""
    Extract entities and relationships from the following text:
    
    {text}
    
    Return the result as a JSON object with the following structure:
    {{
        "entities": [
            {{
                "name": "entity name",
                "type": "entity type (e.g., Person, Organization, Concept, etc.)",
                "description": "brief description of the entity"
            }}
        ],
        "relationships": [
            {{
                "source": "source entity name",
                "target": "target entity name",
                "type": "relationship type (e.g., works_for, created_by, part_of, etc.)",
                "description": "brief description of the relationship"
            }}
        ]
    }}
    
    Only include entities and relationships that are explicitly mentioned in the text.
    """
    
    response = await llm_client.generate_response(prompt)
    
    try:
        # Extract the JSON part from the response
        json_str = response
        if "```json" in response:
            json_str = response.split("```json")[1].split("```")[0].strip()
        elif "```" in response:
            json_str = response.split("```")[1].split("```")[0].strip()
            
        return json.loads(json_str)
    except Exception as e:
        print(f"Error parsing JSON response: {e}")
        print(f"Raw response: {response}")
        return {"entities": [], "relationships": []}


async def add_extracted_knowledge_to_graph(graphiti: Graphiti, text: str, source_name: str):
    """Extract knowledge from text and add it to the knowledge graph."""
    print(f"\n=== Extracting Knowledge from '{source_name}' ===")
    print(f"Text: {text[:100]}...")
    
    # Extract entities and relationships
    extracted_data = await extract_entities_and_relationships(graphiti.llm_client, text)
    
    # Print extracted data
    print(f"\nExtracted {len(extracted_data.get('entities', []))} entities and {len(extracted_data.get('relationships', []))} relationships")
    
    # Add the original text as an episode
    episode_id = await graphiti.add_episode(
        name=source_name,
        episode_body=text,
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        source_description="Extracted Text",
    )
    
    # Add entities as facts
    entity_ids = {}
    for entity in extracted_data.get("entities", []):
        print(f"Adding entity: {entity['name']} ({entity['type']})")
        fact_id = await graphiti.add_fact(
            fact_body=f"{entity['name']}: {entity['description']}",
            episode_id=episode_id,
            metadata={"entity_type": entity["type"]}
        )
        entity_ids[entity["name"]] = fact_id
    
    # Add relationships as connections between facts
    for rel in extracted_data.get("relationships", []):
        if rel["source"] in entity_ids and rel["target"] in entity_ids:
            print(f"Adding relationship: {rel['source']} --[{rel['type']}]--> {rel['target']}")
            source_id = entity_ids[rel["source"]]
            target_id = entity_ids[rel["target"]]
            
            # Add the relationship as a fact that connects two other facts
            await graphiti.add_fact(
                fact_body=f"{rel['description']}",
                episode_id=episode_id,
                metadata={"relationship_type": rel["type"]},
                connected_fact_ids=[source_id, target_id]
            )


async def main():
    """Main function to run the example."""
    # Load environment variables
    load_dotenv()
    setup_logging()
    
    # Get Neo4j connection details from environment variables
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Get Google API key from environment variable
    google_api_key = os.environ.get('GOOGLE_API_KEY')
    if not google_api_key:
        print("No Google API key found in environment variables. Please add it to your .env file.")
        return
        
    print(f"Using Google API key: {google_api_key[:5]}...{google_api_key[-5:]}")
    
    try:
        print("Initializing Graphiti with Gemini clients...")
        # Initialize Graphiti with Gemini clients
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=GeminiClient(
                config=LLMConfig(
                    api_key=google_api_key,
                    model="models/gemini-1.5-pro"  # Using Pro model for better extraction
                )
            ),
            embedder=GeminiEmbedder(
                config=GeminiEmbedderConfig(
                    api_key=google_api_key,
                    embedding_model="models/embedding-001"
                )
            )
        )
        
        print("Connecting to Neo4j database...")
        
        # Clear existing data (optional - remove this in production)
        print("Clearing existing data...")
        await clear_data(graphiti.driver)
        
        # Set up indices and constraints
        print("Setting up indices and constraints...")
        await graphiti.build_indices_and_constraints()
        
        # Sample texts for knowledge extraction
        sample_texts = [
            {
                "name": "Artificial Intelligence Overview",
                "text": """
                Artificial Intelligence (AI) is a field of computer science focused on creating systems 
                that can perform tasks requiring human intelligence. Machine Learning is a subset of AI 
                that uses statistical techniques to enable machines to improve with experience. Deep Learning 
                is a type of Machine Learning based on artificial neural networks. Google's DeepMind has developed 
                several AI systems, including AlphaGo which defeated the world champion in the game of Go. 
                OpenAI created GPT-4, a large language model capable of generating human-like text.
                """
            },
            {
                "name": "Knowledge Graph Applications",
                "text": """
                Knowledge Graphs are used in various applications across different industries. Google Search 
                uses a Knowledge Graph to enhance search results with semantic information. In healthcare, 
                knowledge graphs like SNOMED CT connect medical terms and concepts. Financial institutions 
                use knowledge graphs to detect fraud by identifying suspicious patterns in transaction networks. 
                Amazon's product knowledge graph helps power their recommendation system by understanding 
                relationships between products, categories, and customer preferences.
                """
            }
        ]
        
        # Process each text
        for sample in sample_texts:
            await add_extracted_knowledge_to_graph(graphiti, sample["text"], sample["name"])
        
        # Perform a search to verify the extracted knowledge
        print("\n=== Searching the Knowledge Graph ===")
        search_queries = [
            "artificial intelligence",
            "knowledge graph applications",
            "Google"
        ]
        
        for query in search_queries:
            print(f"\nSearching for: '{query}'")
            search_results = await graphiti.search(query, limit=5)
            
            print(f"Found {len(search_results.edges)} results:")
            for i, result in enumerate(search_results.edges):
                print(f"{i+1}. {result.fact} (Score: {result.score:.4f})")
        
        print("\nKnowledge extraction example completed successfully!")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Close the driver
        print("Closing Neo4j connection...")
        await graphiti.driver.close()


if __name__ == "__main__":
    asyncio.run(main())
