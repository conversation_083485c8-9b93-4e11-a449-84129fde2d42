# Graphiti Worker System

This directory contains the worker system for the Graphiti application, which enables parallel processing of documents and improves the overall performance of the document ingestion pipeline.

## Directory Structure

```
workers/
├── __init__.py           # Package initialization and global worker manager
├── base.py               # Base classes and enums (WorkerType, WorkerStatus, WorkerStats)
├── manager.py            # Core WorkerManager class with worker management logic
├── processors/           # Task processor implementations
│   ├── __init__.py       # Processor function mapping
│   ├── document_processor.py    # Document processing implementation
│   ├── entity_processor.py      # Entity extraction implementation
│   ├── reference_processor.py   # Reference extraction implementation
│   ├── embedding_processor.py   # Embedding generation implementation
│   └── database_processor.py    # Database write implementation
└── README.md             # This file
```

## Overview

The worker system is designed to break down the document processing pipeline into smaller, independent tasks that can be processed in parallel. This approach has several advantages:

1. **Improved Performance**: By processing multiple documents and tasks concurrently, the overall processing time is reduced.
2. **Better Resource Utilization**: The system can utilize multiple CPU cores and distribute the workload efficiently.
3. **Scalability**: The number of workers can be adjusted based on the available resources and workload.
4. **Fault Tolerance**: If a task fails, only that specific task needs to be retried, not the entire document processing.
5. **Monitoring and Status Tracking**: The system provides detailed status information and statistics.

## Worker Types

The worker system includes the following types of workers:

1. **Document Processor Workers**: Handle the initial document processing, including text extraction and chunking.
2. **Entity Extractor Workers**: Extract entities from text chunks using LLM models.
3. **Reference Extractor Workers**: Extract references from documents.
4. **Embedding Generator Workers**: Generate embeddings for text chunks.
5. **Database Writer Workers**: Write processed data to the database.

## Configuration

The worker system can be configured in the `config/workers.yaml` file:

```yaml
# Worker counts
document_processor_workers: 2
entity_extractor_workers: 3
reference_extractor_workers: 2
embedding_generator_workers: 2
database_writer_workers: 2

# Processing options
chunk_size: 1200
overlap: 0
extract_entities: true
extract_references: true
extract_metadata: true
generate_embeddings: true
```

## Usage

### From Python Code

```python
from workers import get_worker_manager

async def process_document(file_path):
    # Get the worker manager
    worker_manager = await get_worker_manager()

    # Add a document processing task
    await worker_manager.add_document_processing_task({
        "id": "document_1",
        "file_path": file_path,
        "chunk_size": 1200,
        "overlap": 0
    })

    # Get the status
    status = worker_manager.get_status()
    print(f"Queue sizes: {status['queue_sizes']}")
```

### From Command Line

The `parallel_worker_processor.py` script provides a command-line interface for processing documents using the worker system:

```bash
python scripts/parallel_worker_processor.py path/to/document.pdf --chunk-size 1200 --overlap 0
```

To process all documents in a directory:

```bash
python scripts/parallel_worker_processor.py path/to/documents/ --document-workers 3 --entity-workers 5
```

## API Endpoints

The worker system exposes the following API endpoints:

- `GET /workers/status`: Get the status of the worker manager.
- `POST /workers/documents`: Process a document using the worker manager.
- `GET /workers/queue-sizes`: Get the sizes of all worker queues.
- `POST /workers/restart`: Restart the worker manager.

## Performance Considerations

For optimal performance, consider the following:

1. **Worker Counts**: Adjust the number of workers based on your system's resources. More workers are not always better, as they may compete for resources.
2. **Memory Usage**: Entity extraction and embedding generation can be memory-intensive. Monitor memory usage and adjust worker counts accordingly.
3. **Database Connections**: The database writer workers may create multiple connections to the database. Ensure your database can handle the connection load.
4. **LLM API Rate Limits**: If using external LLM APIs for entity extraction, be aware of rate limits and adjust the number of entity extractor workers accordingly.

## Monitoring

The worker system provides detailed status information and statistics:

```python
status = worker_manager.get_status()
print(f"Documents processed: {status['stats']['documents_processed']}")
print(f"Entities extracted: {status['stats']['entities_extracted']}")
print(f"References extracted: {status['stats']['references_extracted']}")
print(f"Embeddings generated: {status['stats']['embeddings_generated']}")
print(f"Errors: {status['stats']['errors']}")
```

## Error Handling

The worker system includes error handling and retry mechanisms:

1. **Task-Level Error Handling**: If a task fails, the error is logged and the task is marked as failed.
2. **Worker Recovery**: If a worker crashes, the worker manager will restart it.
3. **Queue Persistence**: Tasks in the queue are not lost if the worker manager is restarted.

## Future Improvements

Potential future improvements to the worker system:

1. **Persistent Queues**: Store queues in Redis or another persistent store to survive application restarts.
2. **Priority Queues**: Implement priority levels for tasks.
3. **Distributed Workers**: Extend the system to support workers running on multiple machines.
4. **Advanced Monitoring**: Add more detailed monitoring and alerting capabilities.
5. **Task Scheduling**: Add support for scheduled tasks and recurring processing.
