"""
Base processor class for document processing.
All document processors should inherit from this base class.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class BaseDocumentProcessor(ABC):
    """
    Abstract base class for document processors.
    """
    
    def __init__(self):
        """Initialize the base processor."""
        self.supported_extensions = set()
        self.processor_name = self.__class__.__name__
    
    @abstractmethod
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process a document and extract text content.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary containing processing results:
            {
                "success": bool,
                "text": str,
                "metadata": dict,
                "error": str (if success=False)
            }
        """
        pass
    
    @abstractmethod
    def supports_file(self, file_path: str) -> bool:
        """
        Check if this processor supports the given file type.
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if the processor can handle this file type
        """
        pass
    
    def get_file_extension(self, file_path: str) -> str:
        """Get the file extension in lowercase."""
        return Path(file_path).suffix.lower()
    
    def get_file_size(self, file_path: str) -> int:
        """Get the file size in bytes."""
        try:
            return Path(file_path).stat().st_size
        except Exception:
            return 0
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate that the file exists and is readable.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Validation result dictionary
        """
        path = Path(file_path)
        
        if not path.exists():
            return {
                "valid": False,
                "error": f"File does not exist: {file_path}"
            }
        
        if not path.is_file():
            return {
                "valid": False,
                "error": f"Path is not a file: {file_path}"
            }
        
        try:
            # Try to read the file to check permissions
            with open(path, 'rb') as f:
                f.read(1)
        except PermissionError:
            return {
                "valid": False,
                "error": f"Permission denied: {file_path}"
            }
        except Exception as e:
            return {
                "valid": False,
                "error": f"Cannot read file: {e}"
            }
        
        return {
            "valid": True,
            "size": self.get_file_size(file_path),
            "extension": self.get_file_extension(file_path)
        }
    
    async def extract_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        Extract basic metadata from the file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Metadata dictionary
        """
        path = Path(file_path)
        stat = path.stat()
        
        return {
            "filename": path.name,
            "file_path": str(path.absolute()),
            "file_size": stat.st_size,
            "file_extension": self.get_file_extension(file_path),
            "created_time": stat.st_ctime,
            "modified_time": stat.st_mtime,
            "processor": self.processor_name
        }
    
    def log_processing_start(self, file_path: str) -> None:
        """Log the start of document processing."""
        logger.info(f"Starting {self.processor_name} processing: {Path(file_path).name}")
    
    def log_processing_success(self, file_path: str, text_length: int) -> None:
        """Log successful document processing."""
        logger.info(f"Successfully processed {Path(file_path).name}: {text_length} characters extracted")
    
    def log_processing_error(self, file_path: str, error: str) -> None:
        """Log document processing error."""
        logger.error(f"Failed to process {Path(file_path).name}: {error}")
