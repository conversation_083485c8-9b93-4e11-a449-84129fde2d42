# Worker configuration for the Graphiti application

# Worker counts
document_processor_workers: 2
entity_extractor_workers: 3
entity_deduplicator_workers: 1
reference_extractor_workers: 2
embedding_generator_workers: 2
database_writer_workers: 2

# Queue sizes (maximum number of tasks in each queue)
document_processor_queue_size: 100
entity_extractor_queue_size: 500
entity_deduplicator_queue_size: 100
reference_extractor_queue_size: 100
embedding_generator_queue_size: 500
database_writer_queue_size: 1000

# Processing options
chunk_size: 1200
overlap: 0
extract_entities: true
extract_references: true
extract_metadata: true
generate_embeddings: true

# Entity extraction options
entity_extraction_model: "meta-llama/llama-4-maverick"
entity_extraction_provider: "openrouter"

# Entity deduplication options
entity_deduplication_enabled: true
entity_deduplication_merge: true
entity_name_similarity_threshold: 0.85
entity_type_match_required: true
entity_min_overall_similarity: 0.8

# Embedding options
embedding_model: "snowflake-arctic-embed2"
embedding_provider: "ollama"

# Reference extraction options
reference_extraction_model: "meta-llama/llama-4-maverick"
reference_extraction_provider: "openrouter"

# Database options
database_batch_size: 50
database_retry_count: 3
database_retry_delay: 5  # seconds
