name: Build and Start Graphiti Service

on:
  pull_request:
    branches: [ "main" ]
  push:
    tags: [ 'v*.*.*' ]
  workflow_call:
jobs:
  build-and-start:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    env:
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      NEO4J_USER: neo4j
      NEO4J_PASSWORD: testpassword
      NEO4J_PORT: 7687
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Depot CLI
      uses: depot/setup-action@v1

    - name: Build Graphiti service image with Depot
      uses: depot/build-push-action@v1
      with:
        context: .
        push: false
        load: true
        tags: graphiti-service:${{ github.sha }}

    - name: Run Docker Compose
      run: docker compose -f docker-compose.test.yml up -d --wait

    - name: Check service logs
      if: failure()
      run: |
        echo "Graphiti service logs:"
        docker compose -f docker-compose.test.yml logs graph
        echo "Neo4j logs:"
        docker compose -f docker-compose.test.yml logs neo4j

    - name: Cleanup
      if: always()
      run: docker compose -f docker-compose.test.yml down