#!/usr/bin/env python3
"""
Test script to check raw entity data from FalkorDB.
"""

import asyncio
from database.database_service import get_falkordb_adapter

async def test_raw_entities():
    """Test raw entity data from FalkorDB"""
    print('🔍 Testing Raw Entity Data')
    print('=' * 40)
    
    adapter = await get_falkordb_adapter()
    
    # Test the exact query used by the entities API (with filtering)
    query = """
    MATCH (e:Entity)
    WHERE NOT e.name STARTS WITH 'Test'
      AND e.name IS NOT NULL
      AND e.name <> ''
      AND e.type IS NOT NULL
      AND e.type <> ''
    OPTIONAL MATCH (f:Fact)-[:MENTIONS]->(e)
    WITH e, count(f) as mentions
    RETURN e.uuid as uuid, e.name as name, e.type as type, mentions
    ORDER BY e.name
    LIMIT 3
    """
    
    print(f"Query: {query}")
    result = adapter.execute_cypher(query)
    
    print(f"Result type: {type(result)}")
    print(f"Result length: {len(result) if result else 0}")
    
    if result and len(result) > 1:
        print(f"Data rows: {len(result[1])}")
        
        for i, row in enumerate(result[1][:3]):
            print(f"\nRow {i+1}:")
            print(f"  Raw row: {row}")
            print(f"  Row type: {type(row)}")
            print(f"  Row length: {len(row) if hasattr(row, '__len__') else 'N/A'}")
            
            if len(row) >= 4:
                print(f"  UUID: {row[0]} (type: {type(row[0])})")
                print(f"  Name: {row[1]} (type: {type(row[1])})")
                print(f"  Type: {row[2]} (type: {type(row[2])})")
                print(f"  Mentions: {row[3]} (type: {type(row[3])})")
    else:
        print("No data returned")

if __name__ == "__main__":
    asyncio.run(test_raw_entities())
