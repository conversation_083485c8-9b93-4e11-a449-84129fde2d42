"""
Main module for entity extraction.

This module provides the main entry points for the entity extraction package. It includes
functions for extracting entities from text, processing facts from the database, extracting
relationships between entities, and getting statistics about entities in the knowledge graph.

The module serves as a facade for the underlying components, providing a simple interface
for other parts of the application to use the entity extraction functionality without
having to interact with the individual components directly.

Example:
    ```python
    import asyncio
    from entity_extraction.main import extract_entities_from_text, get_entity_statistics

    async def main():
        # Extract entities from text
        text = "Vitamin C is an essential nutrient that helps boost the immune system."
        entities = await extract_entities_from_text(api_key=None, text=text)

        print(f"Extracted {len(entities)} entities:")
        for entity in entities:
            print(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")

        # Get entity statistics
        stats = await get_entity_statistics(driver)
        print(f"Total entities: {sum(ec['count'] for ec in stats['entity_counts'])}")

    # Run the async function
    asyncio.run(main())
    ```
"""

import os
import asyncio
import logging
from typing import List, Dict, Any, Tuple, Optional

from dotenv import load_dotenv

from entity_extraction.extractors.llm_extractor import LLMEntityExtractor
from entity_extraction.processors.entity_processor import EntityProcessor
from entity_extraction.processors.relationship_processor import RelationshipProcessor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def extract_entities_from_text(api_key: Optional[str], text: str) -> List[Dict[str, Any]]:
    """
    Extract entities from text asynchronously.

    This function creates an LLMEntityExtractor and uses it to extract entities from the
    provided text. It's the main entry point for extracting entities from text without
    interacting with the database.

    The function supports various LLM providers through the LLMEntityExtractor, which
    can use OpenRouter, OpenAI, or local LLM models depending on configuration.

    Example:
        ```python
        entities = await extract_entities_from_text(
            api_key="your-api-key",
            text="Vitamin C is an essential nutrient that helps boost the immune system."
        )
        ```

    Args:
        api_key (Optional[str]): API key for the LLM service. If None, the extractor
            will attempt to use environment variables or default credentials.
        text (str): Text to extract entities from. This can be a sentence, paragraph,
            or longer document.

    Returns:
        List[Dict[str, Any]]: A list of extracted entities, where each entity is a
            dictionary with the following keys:
            - name (str): The name of the entity
            - type (str): The type of the entity
            - description (Optional[str]): A description of the entity
    """
    extractor = LLMEntityExtractor(api_key)
    return extractor.extract_entities(text)


async def extract_entities_from_facts(driver, api_key: Optional[str], batch_size=5, max_batches=None) -> Tuple[int, int]:
    """
    Extract entities from facts in batches.

    This function processes facts from the database that don't have entity relationships,
    extracts entities from them, and creates entity nodes and relationships in the database.
    It's the main entry point for batch processing of facts.

    The function:
    1. Creates constraints for Entity nodes if they don't exist
    2. Retrieves facts without entity relationships in batches
    3. Extracts entities from each fact using the LLMEntityExtractor
    4. Creates entity nodes and MENTIONS relationships in the database
    5. Tracks progress and returns statistics about the processing

    The function processes facts in batches to manage memory usage and processing load.
    It can be limited to a maximum number of batches for testing or controlled processing.

    Example:
        ```python
        total_processed, total_entities = await extract_entities_from_facts(
            driver=driver,
            api_key="your-api-key",
            batch_size=10,
            max_batches=5
        )
        print(f"Processed {total_processed} facts, extracted {total_entities} entities")
        ```

    Args:
        driver: Database driver for connecting to the graph database
        api_key (Optional[str]): API key for the LLM service. If None, the extractor
            will attempt to use environment variables or default credentials.
        batch_size (int, optional): Number of facts to process in each batch.
            Defaults to 5.
        max_batches (Optional[int], optional): Maximum number of batches to process.
            If None, processes all available facts. Defaults to None.

    Returns:
        Tuple[int, int]: A tuple containing:
            - total_processed (int): The total number of facts processed
            - total_entities (int): The total number of entities extracted
    """
    logger.info("Extracting entities from facts")

    # Create entity processor
    entity_processor = EntityProcessor(driver)

    # Create constraints for Entity nodes
    await entity_processor.create_entity_constraints()

    batch_count = 0
    total_processed = 0
    total_entities = 0

    # Note: We don't need to create an extractor here since we use extract_entities_from_text

    while True:
        # Check if we've reached the maximum number of batches
        if max_batches is not None and batch_count >= max_batches:
            logger.info(f"Reached maximum number of batches ({max_batches})")
            break

        # Get a batch of facts without entities
        facts = await entity_processor.get_facts_without_entities(batch_size)

        # If there are no more facts without entities, we're done
        if not facts:
            logger.info("No more facts without entities")
            break

        # Process each fact in the batch
        for fact in facts:
            # Extract entities from the fact
            entities = await extract_entities_from_text(api_key, fact["body"])

            # Create entity nodes and relationships
            await entity_processor.create_entity_nodes(fact["uuid"], entities)

            total_entities += len(entities)

        batch_count += 1
        total_processed += len(facts)

        logger.info(f"Processed batch {batch_count} ({total_processed} facts, {total_entities} entities total)")

        # Sleep briefly to avoid rate limits
        await asyncio.sleep(1)

    logger.info(f"Finished extracting entities from {total_processed} facts ({total_entities} entities total)")
    return total_processed, total_entities


async def extract_relationships_between_entities(driver, api_key: Optional[str], batch_size=5, max_batches=None) -> Tuple[int, int]:
    """
    Extract relationships between entities that are mentioned in the same fact.

    This function identifies facts that mention multiple entities but don't have
    relationships between those entities, then uses LLMs to extract meaningful
    relationships from the text. It creates RELATED_TO relationships in the database
    with properties that describe the nature of the relationship.

    The function delegates the actual relationship extraction to the RelationshipProcessor,
    which handles the complexities of interacting with the database and LLM services.

    Example:
        ```python
        total_processed, total_relationships = await extract_relationships_between_entities(
            driver=driver,
            api_key="your-api-key",
            batch_size=10,
            max_batches=5
        )
        print(f"Processed {total_processed} facts, extracted {total_relationships} relationships")
        ```

    Args:
        driver: Database driver for connecting to the graph database
        api_key (Optional[str]): API key for the LLM service. If None, the processor
            will attempt to use environment variables or default credentials.
        batch_size (int, optional): Number of facts to process in each batch.
            Defaults to 5.
        max_batches (Optional[int], optional): Maximum number of batches to process.
            If None, processes all available facts. Defaults to None.

    Returns:
        Tuple[int, int]: A tuple containing:
            - total_processed (int): The total number of facts processed
            - total_relationships (int): The total number of relationships created
    """
    # Create relationship processor
    relationship_processor = RelationshipProcessor(driver, api_key)

    # Extract relationships
    return await relationship_processor.extract_relationships_between_entities(batch_size, max_batches)


async def get_entity_statistics(driver) -> Dict[str, Any]:
    """
    Get statistics about entities in the knowledge graph.

    This function retrieves various statistics about entities in the knowledge graph,
    including counts of entities by type, counts of relationships, and top entities
    by mentions. It delegates the actual database queries to the EntityProcessor.

    The statistics provide insights into the composition and structure of the
    knowledge graph, which can be useful for monitoring, reporting, and analysis.

    Example:
        ```python
        stats = await get_entity_statistics(driver)

        # Print entity counts by type
        print("Entity counts by type:")
        for entity_count in stats["entity_counts"]:
            print(f"  {entity_count['type']}: {entity_count['count']}")

        # Print relationship counts
        print(f"MENTIONS relationships: {stats['mentions_count']}")
        print(f"RELATED_TO relationships: {stats['related_to_count']}")

        # Print top entities
        print("Top entities by mentions:")
        for entity in stats["top_entities"]:
            print(f"  {entity['name']} ({entity['type']}): {entity['mentions']} mentions")
        ```

    Args:
        driver: Database driver for connecting to the graph database

    Returns:
        Dict[str, Any]: A dictionary with the following keys:
            - entity_counts (List[Dict[str, Any]]): Counts of entities by type
            - mentions_count (int): Count of MENTIONS relationships
            - related_to_count (int): Count of RELATED_TO relationships
            - top_entities (List[Dict[str, Any]]): Top entities by mentions
    """
    # Create entity processor
    entity_processor = EntityProcessor(driver)

    # Get entity statistics
    return await entity_processor.get_entity_statistics()


async def main():
    """
    Main function to demonstrate entity extraction functionality.

    This function serves as a simple demonstration of the entity extraction functionality.
    It loads environment variables, extracts entities from a sample text, and prints the
    results.

    The function is intended to be run directly from the command line to test the
    entity extraction functionality without requiring a database connection.
    """
    # Load environment variables
    load_dotenv()

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        exit(1)

    # Test entity extraction
    text = "Vitamin C is an essential nutrient that helps boost the immune system."
    entities = await extract_entities_from_text(openai_api_key, text)

    print(f"Extracted {len(entities)} entities:")
    for entity in entities:
        print(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")


if __name__ == "__main__":
    """
    Entry point for running the module as a script.

    This block is executed when the module is run directly from the command line.
    It runs the main function to demonstrate the entity extraction functionality.
    """
    asyncio.run(main())
