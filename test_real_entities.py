#!/usr/bin/env python3
"""
Test script to find real entities from processed documents.
"""

import asyncio
from database.database_service import get_falkordb_adapter

async def test_real_entities():
    """Test to find real entities from actual documents"""
    print('🔍 Finding Real Entities from Processed Documents')
    print('=' * 60)
    
    adapter = await get_falkordb_adapter()
    
    # 1. Check what entity types we have (excluding test data)
    print('\n1. Real Entity Types (excluding test data):')
    query = """
    MATCH (e:Entity)
    WHERE NOT e.name STARTS WITH 'Test'
    RETURN DISTINCT e.type as type, count(e) as count
    ORDER BY count DESC
    LIMIT 20
    """
    result = adapter.execute_cypher(query)
    if result and len(result) > 1:
        for row in result[1]:
            print(f"   {row[0]}: {row[1]} entities")
    
    # 2. Get real entities with highest mention counts
    print('\n2. Top Real Entities by Mentions:')
    query = """
    MATCH (e:Entity)
    WHERE NOT e.name STARTS WITH 'Test' AND e.mention_count IS NOT NULL
    RETURN e.name as name, e.type as type, e.mention_count as mentions
    ORDER BY e.mention_count DESC
    LIMIT 10
    """
    result = adapter.execute_cypher(query)
    if result and len(result) > 1:
        for row in result[1]:
            print(f"   {row[0]} ({row[1]}): {row[2]} mentions")
    else:
        print("   No entities with mention_count found")
    
    # 3. Get sample real entities from each document
    print('\n3. Sample Real Entities by Document:')
    documents = [
        "1 Herbal Medicine practice Shauna Ashewood.pdf",
        "2 Conventional treatments Karen Bridgman.pdf", 
        "8819f5c6-9570-4616-ac1e-ac6814787464_20 Respiritory Viral  3 - Berris Burgoyne.pdf"
    ]
    
    for doc_name in documents:
        print(f"\n   📄 {doc_name}:")
        query = f"""
        MATCH (d:Episode {{name: '{doc_name}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(e:Entity)
        WHERE NOT e.name STARTS WITH 'Test'
        RETURN DISTINCT e.name as name, e.type as type
        LIMIT 5
        """
        result = adapter.execute_cypher(query)
        if result and len(result) > 1:
            for row in result[1]:
                print(f"      - {row[0]} ({row[1]})")
        else:
            print("      No entities found for this document")
    
    # 4. Check relationships/edges
    print('\n4. Relationships/Edges Count:')
    query = "MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count ORDER BY count DESC LIMIT 10"
    result = adapter.execute_cypher(query)
    if result and len(result) > 1:
        total_relationships = 0
        for row in result[1]:
            count = row[1]
            total_relationships += count
            print(f"   {row[0]}: {count}")
        print(f"\n   Total Relationships: {total_relationships}")
    
    # 5. Check references in CSV files
    print('\n5. References Check:')
    import os
    from pathlib import Path
    
    ref_dir = Path("references")
    if ref_dir.exists():
        csv_files = list(ref_dir.glob("*.csv"))
        total_refs = 0
        
        for csv_file in csv_files[:5]:  # Check first 5 files
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    ref_count = len(lines) - 1  # Subtract header
                    total_refs += ref_count
                    print(f"   {csv_file.name}: {ref_count} references")
            except Exception as e:
                print(f"   {csv_file.name}: Error reading - {e}")
        
        print(f"\n   Total References (from {len(csv_files)} files): {total_refs}")
    else:
        print("   References directory not found")

if __name__ == "__main__":
    asyncio.run(test_real_entities())
