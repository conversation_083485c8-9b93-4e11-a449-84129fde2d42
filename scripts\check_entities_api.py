"""
Check the format of the entities API response
"""

import requests
import json

def check_entities_api():
    """Check the format of the entities API response"""
    url = "http://localhost:9753/api/entities"

    try:
        response = requests.get(url)
        response.raise_for_status()

        data = response.json()

        print("API Response:")
        print(json.dumps(data, indent=2))

        if "entities" in data:
            print(f"\nFound {len(data['entities'])} entities")

            if data["entities"]:
                print("\nFirst entity:")
                print(json.dumps(data["entities"][0], indent=2))

                # Check if entities have mention_count
                has_mention_count = all("mention_count" in entity for entity in data["entities"])
                print(f"\nAll entities have mention_count: {has_mention_count}")

                # Get entity types
                entity_types = set(entity.get("type", "") for entity in data["entities"])
                print(f"\nEntity types: {', '.join(sorted(entity_types))}")
        else:
            print("\nNo 'entities' key in response")

    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_entities_api()
