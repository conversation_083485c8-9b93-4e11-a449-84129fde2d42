/**
 * Graphiti UI Part 7 - Additional functionality for the Graphiti Knowledge Graph UI
 *
 * This file contains functions for the Settings tab.
 */

/**
 * Initialize the Settings tab
 */
function initializeSettingsTab() {
    console.log("Initializing Settings tab");

    // Load settings
    loadSettings();

    // Set up event listeners
    setupSettingsEventListeners();
}

/**
 * Load settings
 */
function loadSettings() {
    // Show loading spinner
    const loadingSpinner = document.getElementById('settings-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch settings
    fetch('/api/settings')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Populate settings form
            populateSettingsForm(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading settings:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const settingsForm = document.getElementById('settings-form');
            if (settingsForm) {
                settingsForm.innerHTML = `<div class="alert alert-danger">Error loading settings: ${error.message}</div>`;
            }
        });
}

/**
 * Populate settings form
 *
 * @param {Object} settings - Settings data from the API
 */
function populateSettingsForm(settings) {
    console.log("Populating settings form with data:", settings);

    // LLM Settings
    const llmProvider = document.getElementById('llm-provider');
    if (llmProvider) {
        llmProvider.value = settings.llm?.provider || 'openrouter';

        // Add event listener for provider change
        llmProvider.addEventListener('change', function() {
            updateLlmModelOptions(settings.available_models);
        });
    }

    // Update LLM model options based on available models
    updateLlmModelOptions(settings.available_models, settings.llm?.model);

    const llmApiKey = document.getElementById('llm-api-key');
    if (llmApiKey) {
        llmApiKey.value = settings.llm?.api_key || '';
    }

    // Embedding Settings
    const embeddingModel = document.getElementById('embedding-model');
    if (embeddingModel) {
        embeddingModel.value = settings.embedding?.model || 'snowflake-arctic-embed2';
    }

    const chunkSize = document.getElementById('chunk-size');
    if (chunkSize) {
        chunkSize.value = settings.embedding?.chunk_size || 1200;
    }

    const chunkOverlap = document.getElementById('chunk-overlap');
    if (chunkOverlap) {
        chunkOverlap.value = settings.embedding?.chunk_overlap || 0;
    }

    // Database Settings
    const falkordbHost = document.getElementById('falkordb-host');
    if (falkordbHost) {
        falkordbHost.value = settings.database?.host || 'localhost';
    }

    const falkordbPort = document.getElementById('falkordb-port');
    if (falkordbPort) {
        falkordbPort.value = settings.database?.port || 6379;
    }

    const redisHost = document.getElementById('redis-host');
    if (redisHost) {
        redisHost.value = settings.database?.host || 'localhost';
    }

    const redisPort = document.getElementById('redis-port');
    if (redisPort) {
        redisPort.value = settings.database?.port || 6380;
    }

    // System Settings
    const maxFileSize = document.getElementById('max-file-size');
    if (maxFileSize) {
        maxFileSize.value = settings.system?.max_file_size || 50;
    }

    const maxParallelProcesses = document.getElementById('max-parallel-processes');
    if (maxParallelProcesses) {
        maxParallelProcesses.value = settings.system?.max_parallel_processes || 4;
    }

    const logLevel = document.getElementById('log-level');
    if (logLevel) {
        logLevel.value = settings.system?.log_level || 'INFO';
    }
}

/**
 * Update LLM model options based on selected provider
 *
 * @param {Object} availableModels - Available models by provider
 * @param {string} currentModel - Currently selected model
 */
function updateLlmModelOptions(availableModels, currentModel = null) {
    const llmProvider = document.getElementById('llm-provider');
    const llmModel = document.getElementById('llm-model');

    if (!llmProvider || !llmModel || !availableModels) {
        console.warn("Missing elements or available models for LLM model update");
        return;
    }

    const provider = llmProvider.value;
    const models = availableModels[provider] || [];

    console.log(`Updating LLM models for provider: ${provider}`, models);

    // Clear existing options
    llmModel.innerHTML = '';

    // Add models for the selected provider
    if (models.length > 0) {
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model;
            option.textContent = model;
            llmModel.appendChild(option);
        });

        // Set current model if provided
        if (currentModel && models.includes(currentModel)) {
            llmModel.value = currentModel;
        } else if (models.length > 0) {
            llmModel.value = models[0]; // Default to first model
        }
    } else {
        // Add a placeholder if no models available
        const option = document.createElement('option');
        option.value = '';
        option.textContent = 'No models available';
        option.disabled = true;
        llmModel.appendChild(option);
    }
}

/**
 * Set up event listeners for the Settings tab
 */
function setupSettingsEventListeners() {
    // Add event listener for save settings button
    const saveSettingsButton = document.getElementById('save-settings-button');
    if (saveSettingsButton) {
        saveSettingsButton.addEventListener('click', function() {
            saveSettings();
        });
    }

    // Add event listener for reset settings button
    const resetSettingsButton = document.getElementById('reset-settings-button');
    if (resetSettingsButton) {
        resetSettingsButton.addEventListener('click', function() {
            resetSettings();
        });
    }

    // Add event listener for test connection button
    const testConnectionButton = document.getElementById('test-connection-button');
    if (testConnectionButton) {
        testConnectionButton.addEventListener('click', function() {
            testDatabaseConnection();
        });
    }
}

/**
 * Save settings
 */
function saveSettings() {
    // Get settings values
    const settings = {
        llm_settings: {
            provider: document.getElementById('llm-provider')?.value || 'openrouter',
            model: document.getElementById('llm-model')?.value || 'meta-llama/llama-4-maverick',
            api_key: document.getElementById('llm-api-key')?.value || ''
        },
        embedding_settings: {
            model: document.getElementById('embedding-model')?.value || 'snowflake-arctic-embed2',
            chunk_size: parseInt(document.getElementById('chunk-size')?.value || 1200),
            chunk_overlap: parseInt(document.getElementById('chunk-overlap')?.value || 0),
            use_local: true,
            provider: 'ollama'
        },
        database_settings: {
            host: document.getElementById('falkordb-host')?.value || 'localhost',
            port: parseInt(document.getElementById('falkordb-port')?.value || 6379)
        },
        system_settings: {
            max_file_size: parseInt(document.getElementById('max-file-size')?.value || 50),
            max_parallel_processes: parseInt(document.getElementById('max-parallel-processes')?.value || 4),
            log_level: document.getElementById('log-level')?.value || 'INFO'
        }
    };

    // Show saving indicator
    const saveStatus = document.getElementById('save-status');
    if (saveStatus) {
        saveStatus.textContent = 'Saving...';
        saveStatus.className = 'text-info';
        saveStatus.style.display = 'block';
    }

    // Save settings
    fetch('/api/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(settings)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Show success message
        if (saveStatus) {
            saveStatus.textContent = 'Settings saved successfully!';
            saveStatus.className = 'text-success';

            // Hide message after a delay
            setTimeout(() => {
                saveStatus.style.display = 'none';
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error saving settings:', error);

        // Show error message
        if (saveStatus) {
            saveStatus.textContent = `Error saving settings: ${error.message}`;
            saveStatus.className = 'text-danger';
        }
    });
}

/**
 * Reset settings to defaults
 */
function resetSettings() {
    // Show confirmation dialog
    if (!confirm('Are you sure you want to reset all settings to defaults?')) {
        return;
    }

    // Show resetting indicator
    const saveStatus = document.getElementById('save-status');
    if (saveStatus) {
        saveStatus.textContent = 'Resetting...';
        saveStatus.className = 'text-info';
        saveStatus.style.display = 'block';
    }

    // Reset settings
    fetch('/api/settings/reset', {
        method: 'POST'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Populate settings form with defaults
        populateSettingsForm(data);

        // Show success message
        if (saveStatus) {
            saveStatus.textContent = 'Settings reset to defaults!';
            saveStatus.className = 'text-success';

            // Hide message after a delay
            setTimeout(() => {
                saveStatus.style.display = 'none';
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error resetting settings:', error);

        // Show error message
        if (saveStatus) {
            saveStatus.textContent = `Error resetting settings: ${error.message}`;
            saveStatus.className = 'text-danger';
        }
    });
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    // Get database settings
    const databaseSettings = {
        falkordb_host: document.getElementById('falkordb-host')?.value || 'localhost',
        falkordb_port: parseInt(document.getElementById('falkordb-port')?.value || 6379),
        redis_host: document.getElementById('redis-host')?.value || 'localhost',
        redis_port: parseInt(document.getElementById('redis-port')?.value || 6380)
    };

    // Show testing indicator
    const connectionStatus = document.getElementById('connection-status');
    if (connectionStatus) {
        connectionStatus.textContent = 'Testing connection...';
        connectionStatus.className = 'text-info';
        connectionStatus.style.display = 'block';
    }

    // Test connection
    fetch('/api/settings/test-connection', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(databaseSettings)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Show success message
        if (connectionStatus) {
            connectionStatus.textContent = 'Connection successful!';
            connectionStatus.className = 'text-success';

            // Hide message after a delay
            setTimeout(() => {
                connectionStatus.style.display = 'none';
            }, 3000);
        }
    })
    .catch(error => {
        console.error('Error testing connection:', error);

        // Show error message
        if (connectionStatus) {
            connectionStatus.textContent = `Connection failed: ${error.message}`;
            connectionStatus.className = 'text-danger';
        }
    });
}
