#!/usr/bin/env python3
"""
Test the modified Mistral OCR processor with Brain.one file.
This should now properly extract all content including the 68 references.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add current directory to path
sys.path.append(os.getcwd())

async def test_brain_mistral_ocr():
    """Test Mistral OCR with Brain.one file."""
    
    print("🧠 TESTING MODIFIED MISTRAL OCR WITH BRAIN.ONE")
    print("=" * 60)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Testing with: {brain_file.name}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    try:
        # Import the modified Mistral OCR processor
        from utils.mistral_ocr import MistralOCRProcessor
        
        print("\n🔄 Initializing Mistral OCR processor...")
        mistral_ocr = MistralOCRProcessor()
        
        print("🔄 Processing Brain.one file with modified Mistral OCR...")
        print("   (This should now convert to images and process with OCR)")
        
        # Process the OneNote file
        extracted_text = await mistral_ocr.extract_text_from_document(str(brain_file))
        
        if extracted_text and len(extracted_text.strip()) > 0:
            print(f"\n🎉 SUCCESS! Extracted {len(extracted_text)} characters!")
            
            # Save the extracted content
            from datetime import datetime
            content_file = f"brain_mistral_extracted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(content_file, "w", encoding="utf-8") as f:
                f.write(extracted_text)
            print(f"💾 Saved content to: {content_file}")
            
            # Show first 1000 characters
            print(f"\n📖 FIRST 1000 CHARACTERS:")
            print("-" * 50)
            print(extracted_text[:1000])
            print("-" * 50)
            
            # Quick reference analysis
            import re
            
            # Look for reference patterns
            numbered_refs = re.findall(r'\d+\.\s+[A-Z]', extracted_text)
            author_year_refs = re.findall(r'[A-Z][a-z]+.*?\(\d{4}\)', extracted_text)
            doi_refs = re.findall(r'doi:', extracted_text, re.IGNORECASE)
            
            print(f"\n🔍 QUICK REFERENCE ANALYSIS:")
            print(f"   Potential numbered references: {len(numbered_refs)}")
            print(f"   Potential author-year references: {len(author_year_refs)}")
            print(f"   DOI references: {len(doi_refs)}")
            
            total_potential = len(numbered_refs) + len(author_year_refs) + len(doi_refs)
            print(f"   Total potential references: {total_potential}")
            
            if total_potential >= 50:
                print(f"🎯 EXCELLENT! Found {total_potential} potential references - this looks promising for the 68 you mentioned!")
            elif total_potential >= 20:
                print(f"✅ Good! Found {total_potential} potential references - getting closer!")
            else:
                print(f"⚠️ Found {total_potential} potential references - may need further refinement")
            
            # Show some sample references
            if numbered_refs:
                print(f"\n📚 SAMPLE NUMBERED REFERENCES:")
                for i, ref in enumerate(numbered_refs[:5]):
                    print(f"   {i+1}. {ref}...")
            
            return True
            
        else:
            print("❌ No text extracted from Brain.one file")
            print("   Possible issues:")
            print("   - OneNote to image conversion failed")
            print("   - Mistral OCR API issues")
            print("   - File format not supported")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_brain_mistral_ocr())
    
    if success:
        print("\n✅ Test completed successfully!")
        print("🔄 Next step: Run comprehensive reference extraction on the extracted content")
    else:
        print("\n❌ Test failed - need to debug the OneNote processing")
