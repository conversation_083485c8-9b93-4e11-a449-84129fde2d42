#!/usr/bin/env python3
"""
Install optional dependencies for enhanced Graphiti functionality.
"""

import subprocess
import sys
import os

def install_package(package_name, description=""):
    """Install a package using pip."""
    print(f"📦 Installing {package_name}...")
    if description:
        print(f"   Purpose: {description}")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        print(f"✅ Successfully installed {package_name}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install {package_name}: {e}")
        return False

def main():
    """Install optional dependencies."""
    print("🚀 Installing Optional Dependencies for Graphiti")
    print("=" * 50)
    
    # List of optional dependencies
    optional_deps = [
        ("xlrd", "Excel file processing (.xls files)"),
        ("openpyxl", "Excel file processing (.xlsx files)"),
        ("python-docx", "Word document processing"),
        ("python-pptx", "PowerPoint presentation processing"),
        ("Pillow", "Image processing and manipulation"),
        ("httpx", "HTTP client for API requests"),
        ("requests", "HTTP library for web requests"),
    ]
    
    installed = 0
    failed = 0
    
    for package, description in optional_deps:
        success = install_package(package, description)
        if success:
            installed += 1
        else:
            failed += 1
        print()  # Add spacing
    
    print("=" * 50)
    print(f"📊 Installation Summary:")
    print(f"✅ Successfully installed: {installed}")
    print(f"❌ Failed to install: {failed}")
    print(f"📦 Total packages: {len(optional_deps)}")
    
    if failed == 0:
        print("\n🎉 All optional dependencies installed successfully!")
        print("💡 Restart the application to use the new features.")
    else:
        print(f"\n⚠️ {failed} packages failed to install.")
        print("💡 The application will still work, but some features may be limited.")
    
    print("\n📚 For more information, see the documentation:")
    print("   - LAUNCH_GUIDE.md")
    print("   - README.md")

if __name__ == "__main__":
    main()
