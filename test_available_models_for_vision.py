#!/usr/bin/env python3
"""
Test available Ollama models to see if any have vision capabilities.
"""

import asyncio
import httpx
import base64
from pathlib import Path


async def test_model_for_vision(model_name: str, image_base64: str) -> bool:
    """Test if a model can process images."""
    
    print(f"\n🧪 Testing {model_name} for vision capabilities...")
    
    # Try vision-specific payload first
    payload = {
        "model": model_name,
        "prompt": "What do you see in this image? Describe any text or content visible.",
        "images": [image_base64],
        "stream": False
    }
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "http://localhost:11434/api/generate",
                json=payload
            )
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '').strip()
                
                if response_text:
                    print(f"   ✅ SUCCESS! {model_name} can process images")
                    print(f"   Response: {response_text[:200]}...")
                    return True
                else:
                    print(f"   ⚠️ Model responded but no text")
            elif response.status_code == 400:
                print(f"   ❌ Model doesn't support images (400 error)")
            elif response.status_code == 404:
                print(f"   ❌ Model not found")
            else:
                error_text = response.text
                if "images" in error_text.lower() or "vision" in error_text.lower():
                    print(f"   ❌ Model doesn't support vision: {error_text}")
                else:
                    print(f"   ❌ Error: {error_text}")
                    
    except Exception as e:
        print(f"   ❌ Request error: {e}")
    
    return False


async def test_all_available_models():
    """Test all available models for vision capabilities."""
    
    print("🔍 TESTING ALL AVAILABLE MODELS FOR VISION")
    print("=" * 60)
    
    # Get test image
    image_dir = Path("extracted_onenote_images")
    test_image = image_dir / "onenote_image_4_jpeg.jpg"
    
    if not test_image.exists():
        image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        if image_files:
            test_image = image_files[0]
        else:
            print("❌ No test images available")
            return None
    
    print(f"🖼️ Using test image: {test_image.name}")
    
    # Read and encode the image
    with open(test_image, 'rb') as f:
        image_data = f.read()
    
    image_base64 = base64.b64encode(image_data).decode('utf-8')
    
    # Get available models
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://localhost:11434/api/tags")
            
            if response.status_code != 200:
                print("❌ Cannot get model list")
                return None
            
            data = response.json()
            models = data.get('models', [])
            model_names = [m.get('name', '') for m in models]
            
    except Exception as e:
        print(f"❌ Error getting models: {e}")
        return None
    
    print(f"📋 Testing {len(model_names)} models...")
    
    # Test each model
    working_models = []
    
    for model_name in model_names:
        if await test_model_for_vision(model_name, image_base64):
            working_models.append(model_name)
    
    if working_models:
        print(f"\n🎉 FOUND {len(working_models)} WORKING VISION MODEL(S):")
        for model in working_models:
            print(f"  ✅ {model}")
        return working_models[0]  # Return the first working model
    else:
        print(f"\n❌ No models with vision capabilities found")
        return None


async def process_onenote_with_working_model(model_name: str):
    """Process OneNote images with the working vision model."""
    
    print(f"\n🖼️ PROCESSING ONENOTE IMAGES WITH {model_name}")
    print("=" * 60)
    
    image_dir = Path("extracted_onenote_images")
    image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
    
    if not image_files:
        print("❌ No image files found")
        return False
    
    print(f"📎 Processing {len(image_files)} images...")
    
    total_extracted_text = ""
    successful_extractions = 0
    
    for i, image_file in enumerate(sorted(image_files)):
        print(f"\n📷 Processing {image_file.name}:")
        
        try:
            # Read and encode image
            with open(image_file, 'rb') as f:
                image_data = f.read()
            
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            payload = {
                "model": model_name,
                "prompt": """Please extract ALL text content from this image. This appears to be from a OneNote document containing research information about brain health, neuroprotection, and bioactive compounds.

Please provide:
1. All visible text, including titles, headings, bullet points, and body text
2. Any research information, chemical names, or scientific terms
3. References, URLs, or citations if visible
4. Table data or structured information if present

Extract the text exactly as it appears, maintaining the structure and organization.""",
                "images": [image_base64],
                "stream": False
            }
            
            async with httpx.AsyncClient(timeout=180.0) as client:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json=payload
                )
                
                if response.status_code == 200:
                    result = response.json()
                    extracted_text = result.get('response', '').strip()
                    
                    if extracted_text and len(extracted_text) > 20:
                        print(f"   ✅ SUCCESS: {len(extracted_text)} characters")
                        print(f"   📝 Preview: {extracted_text[:150]}...")
                        
                        total_extracted_text += f"\n\n=== {image_file.name.upper()} ===\n"
                        total_extracted_text += extracted_text
                        successful_extractions += 1
                    else:
                        print(f"   ❌ No meaningful text extracted")
                else:
                    print(f"   ❌ Request failed: {response.status_code}")
                    
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary
    print(f"\n📊 PROCESSING SUMMARY:")
    print("=" * 60)
    print(f"Model used: {model_name}")
    print(f"Images processed: {len(image_files)}")
    print(f"Successful extractions: {successful_extractions}")
    print(f"Total text extracted: {len(total_extracted_text):,} characters")
    
    if total_extracted_text:
        # Save results
        output_file = Path(f"onenote_{model_name.replace(':', '_')}_vision_results.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== ONENOTE VISION RESULTS ===\n\n")
            f.write(f"Model: {model_name}\n")
            f.write(f"Images processed: {len(image_files)}\n")
            f.write(f"Successful extractions: {successful_extractions}\n")
            f.write(f"Total text: {len(total_extracted_text):,} characters\n\n")
            f.write("=== EXTRACTED TEXT FROM ALL IMAGES ===\n")
            f.write(total_extracted_text)
        
        print(f"💾 Saved results to: {output_file}")
        
        # Show preview
        print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
        print("=" * 60)
        print(total_extracted_text[:1000])
        if len(total_extracted_text) > 1000:
            print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
        print("=" * 60)
        
        # Final comparison
        print(f"\n🎉 FINAL COMPARISON:")
        print(f"Original OneNote extraction: ~1,880 characters (metadata only)")
        print(f"Vision model extraction: {len(total_extracted_text):,} characters (ACTUAL CONTENT)")
        
        if len(total_extracted_text) > 1880:
            improvement = len(total_extracted_text) / 1880
            print(f"Improvement factor: {improvement:.1f}x MORE REAL CONTENT!")
            print("🎉 THIS IS THE ACTUAL ONENOTE RESEARCH CONTENT!")
        
        return True
    else:
        print("❌ No text extracted from any images")
        return False


async def main():
    """Run vision model testing and OneNote processing."""
    
    # Test all models for vision capabilities
    working_model = await test_all_available_models()
    
    if working_model:
        # Process OneNote images with the working model
        success = await process_onenote_with_working_model(working_model)
        
        if success:
            print(f"\n🎉 SUCCESS! Extracted actual OneNote content using {working_model}")
        else:
            print(f"\n❌ Failed to extract content with {working_model}")
    else:
        print(f"\n❌ No vision-capable models found")
        print("You may need to install a vision model like:")
        print("  ollama pull llava:7b")
        print("  ollama pull llava:13b")
        print("  ollama pull bakllava")


if __name__ == "__main__":
    asyncio.run(main())
