"""
Client for local LLM models using the Ollama API
"""

import os
import json
import logging
import httpx
from typing import List, Dict, Any, Optional

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LocalLLMClient:
    """Client for local LLM models using the Ollama API"""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "medllama3-v20"):
        """
        Initialize the LocalLLMClient

        Args:
            base_url: The base URL for the Ollama API
            model: The model to use (default: medllama3-v20)
        """
        self.base_url = base_url
        self.model = model
        logger.info(f"Initialized LocalLLMClient with model: {model}")

    def generate_completion(self, system_prompt: str, user_prompt: str, temperature: float = 0.3, max_tokens: int = 1000) -> str:
        """
        Synchronous method to generate a completion using the local LLM model

        Args:
            system_prompt: The system prompt
            user_prompt: The user prompt
            temperature: Temperature for generation (default: 0.3)
            max_tokens: Maximum number of tokens to generate (default: 1000)

        Returns:
            The generated text
        """
        # Convert prompts to Ollama format
        prompt = f"System: {system_prompt}\n\nUser: {user_prompt}"

        # Prepare the request payload
        payload = {
            "model": self.model,
            "prompt": prompt,
            "temperature": temperature,
            "num_predict": max_tokens,
            "stream": False
        }

        try:
            # Make the API request
            with httpx.Client() as client:
                response = client.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=60.0  # Longer timeout for model inference
                )

                # Check if the request was successful
                response.raise_for_status()
                result = response.json()

                # Return the generated text
                return result.get("response", "I'm sorry, but I couldn't generate a response at this time.")

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            return f"Error: {e}"
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            return f"Error: {e}"
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            return f"Error: {e}"

    async def generate_completion_async(self,
                                 messages: List[Dict[str, str]],
                                 temperature: float = 0.3,
                                 max_tokens: int = 1000,
                                 response_format: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        Asynchronous method to generate a completion using the local LLM model

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            temperature: Temperature for generation (default: 0.3)
            max_tokens: Maximum number of tokens to generate (default: 1000)
            response_format: Optional format specification (e.g., {"type": "json_object"})

        Returns:
            Dictionary with the generated completion
        """
        # Convert OpenAI-style messages to Ollama format
        prompt = self._convert_messages_to_prompt(messages)

        # Prepare the request payload
        payload = {
            "model": self.model,
            "prompt": prompt,
            "temperature": temperature,
            "num_predict": max_tokens,
            "stream": False
        }

        # Add format instruction if JSON is requested
        if response_format and response_format.get("type") == "json_object":
            payload["prompt"] += "\n\nYour response must be a valid JSON object."

        try:
            # Make the API request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/generate",
                    json=payload,
                    timeout=60.0  # Longer timeout for model inference
                )

                # Check if the request was successful
                response.raise_for_status()
                result = response.json()

                # Format the response to match OpenAI's format
                formatted_response = {
                    "choices": [
                        {
                            "message": {
                                "content": result.get("response", "")
                            },
                            "finish_reason": "stop"
                        }
                    ]
                }

                return formatted_response

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            raise Exception(f"HTTP error: {e}")
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"Request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise Exception(f"Unexpected error: {e}")

    def _convert_messages_to_prompt(self, messages: List[Dict[str, str]]) -> str:
        """
        Convert OpenAI-style messages to a prompt string for Ollama

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys

        Returns:
            Prompt string
        """
        prompt_parts = []

        for message in messages:
            role = message.get("role", "").lower()
            content = message.get("content", "")

            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
            else:
                # Handle other roles or just add the content
                prompt_parts.append(content)

        return "\n\n".join(prompt_parts)

class LocalEmbeddingClient:
    """Client for local embedding models using the Ollama API"""

    def __init__(self, base_url: str = "http://localhost:11434", model: str = "bge-m3"):
        """
        Initialize the LocalEmbeddingClient

        Args:
            base_url: The base URL for the Ollama API
            model: The model to use (default: bge-m3)
        """
        self.base_url = base_url
        self.model = model
        logger.info(f"Initialized LocalEmbeddingClient with model: {model}")

    async def create_embedding(self, text: str) -> List[float]:
        """
        Create an embedding for the given text

        Args:
            text: The text to embed

        Returns:
            List of embedding values
        """
        # Prepare the request payload
        payload = {
            "model": self.model,
            "prompt": text
        }

        try:
            # Make the API request
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/api/embeddings",
                    json=payload,
                    timeout=30.0
                )

                # Check if the request was successful
                response.raise_for_status()
                result = response.json()

                # Return the embedding
                return result.get("embedding", [])

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error: {e}")
            raise Exception(f"HTTP error: {e}")
        except httpx.RequestError as e:
            logger.error(f"Request error: {e}")
            raise Exception(f"Request error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error: {e}")
            raise Exception(f"Unexpected error: {e}")
