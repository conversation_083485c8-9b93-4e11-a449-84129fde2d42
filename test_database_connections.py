#!/usr/bin/env python3
"""
Test database connections to diagnose API timeout issues
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

async def test_database_connections():
    print("🔍 Testing Database Connections")
    print("=" * 50)
    
    # Test 1: FalkorDB Connection
    print("\n📊 Testing FalkorDB Connection...")
    try:
        from database.falkordb_adapter import FalkorDBAdapter
        from utils.config import get_config
        
        config = get_config()
        falkor_config = config.get('falkordb', {})
        
        print(f"   Host: {falkor_config.get('host', 'localhost')}")
        print(f"   Port: {falkor_config.get('port', 6379)}")
        print(f"   Graph: {falkor_config.get('graph', 'knowledge_graph')}")
        
        adapter = FalkorDBAdapter()
        
        # Test basic connection
        result = await adapter.execute_query("RETURN 1 as test")
        if result:
            print("   ✅ FalkorDB connection successful")
            
            # Test entity count
            entity_result = await adapter.execute_query("MATCH (e:Entity) RETURN count(e) as count")
            if entity_result and len(entity_result) > 0:
                entity_count = entity_result[0].get('count', 0)
                print(f"   ✅ Found {entity_count} entities")
            else:
                print("   ⚠️  No entities found or query failed")
                
        else:
            print("   ❌ FalkorDB connection failed")
            
    except Exception as e:
        print(f"   ❌ FalkorDB error: {e}")
    
    # Test 2: Redis Connection
    print("\n🔴 Testing Redis Connection...")
    try:
        from database.redis_service import get_redis_connection
        
        redis_client = get_redis_connection()
        
        # Test basic connection
        await redis_client.ping()
        print("   ✅ Redis connection successful")
        
        # Test vector search
        try:
            # Check if we have any vectors
            keys = await redis_client.keys("embedding:*")
            print(f"   ✅ Found {len(keys)} embeddings in Redis")
        except Exception as e:
            print(f"   ⚠️  Redis vector check failed: {e}")
            
    except Exception as e:
        print(f"   ❌ Redis error: {e}")
    
    # Test 3: Simple API Route Test
    print("\n🌐 Testing Simple API Route...")
    try:
        import requests
        
        # Test the simplest possible endpoint
        response = requests.get("http://127.0.0.1:9753/", timeout=5)
        if response.status_code == 200:
            print("   ✅ Basic HTTP connection working")
        else:
            print(f"   ❌ HTTP error: {response.status_code}")
            
    except requests.exceptions.Timeout:
        print("   ❌ HTTP request timed out")
    except requests.exceptions.ConnectionError:
        print("   ❌ HTTP connection refused")
    except Exception as e:
        print(f"   ❌ HTTP error: {e}")
    
    # Test 4: Check if services are hanging
    print("\n⚙️  Testing Service Initialization...")
    try:
        from services.entity_service import get_entity_count
        
        print("   Testing entity service...")
        entity_count = await get_entity_count()
        print(f"   ✅ Entity service working: {entity_count} entities")
        
    except Exception as e:
        print(f"   ❌ Entity service error: {e}")
    
    try:
        from services.document_service import get_document_count
        
        print("   Testing document service...")
        doc_count = await get_document_count()
        print(f"   ✅ Document service working: {doc_count} documents")
        
    except Exception as e:
        print(f"   ❌ Document service error: {e}")
    
    # Test 5: Check for slow queries
    print("\n🐌 Testing Query Performance...")
    try:
        import time
        from database.falkordb_adapter import FalkorDBAdapter
        
        adapter = FalkorDBAdapter()
        
        # Test a simple query timing
        start_time = time.time()
        result = await adapter.execute_query("MATCH (e:Entity) RETURN count(e) LIMIT 1")
        end_time = time.time()
        
        query_time = end_time - start_time
        print(f"   Entity count query took: {query_time:.2f} seconds")
        
        if query_time > 5:
            print("   ⚠️  Query is slow - this might cause timeouts")
        else:
            print("   ✅ Query performance is acceptable")
            
    except Exception as e:
        print(f"   ❌ Query performance test error: {e}")

if __name__ == "__main__":
    asyncio.run(test_database_connections())
