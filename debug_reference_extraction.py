#!/usr/bin/env python3
"""
Debug script to examine reference extraction in detail.
Shows exactly what text is being processed and how references are found.
"""

import asyncio
import os
import sys
import re
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def debug_reference_extraction():
    """Debug reference extraction step by step."""
    
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    uploads_dir = Path("uploads")

    # Look for the specific pain relief document first
    pain_relief_files = list(uploads_dir.glob("*pain releif*"))
    if pain_relief_files:
        test_file = pain_relief_files[0]
        print(f"🎯 Found pain relief document: {test_file.name}")
    else:
        # Look for PDF files
        pdf_files = list(uploads_dir.glob("*.pdf"))
        if not pdf_files:
            print("❌ No PDF files found")
            return
        test_file = pdf_files[0]  # Use the first PDF found

    print(f"🔍 Debugging reference extraction on: {test_file.name}")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Step 1: Extract text with Mistral OCR
        print("\n1️⃣ Extracting text with Mistral OCR...")
        mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
        text = await mistral_ocr.extract_text_from_pdf(str(test_file))
        
        if not text:
            print("❌ No text extracted!")
            return
        
        print(f"✅ Extracted {len(text):,} characters")
        
        # Step 2: Look for reference sections
        print("\n2️⃣ Looking for reference sections...")
        ref_section_patterns = [
            r'(?i)(?:^|\n)\s*(?:references?|bibliography|citations?|literature\s+cited)\s*(?:\n|$)',
            r'(?i)(?:^|\n)\s*\d+\.?\s*(?:references?|bibliography)\s*(?:\n|$)',
            r'(?i)(?:^|\n)\s*#{1,6}\s*(?:references?|bibliography)\s*(?:\n|$)',
        ]
        
        reference_sections = []
        for i, pattern in enumerate(ref_section_patterns):
            matches = list(re.finditer(pattern, text, re.MULTILINE))
            print(f"   Pattern {i+1}: Found {len(matches)} matches")
            for match in matches:
                start = match.end()
                # Look for next 1000 characters to see what's there
                sample = text[start:start+1000].strip()
                reference_sections.append((match.start(), start, sample))
                print(f"      Match at position {match.start()}: '{match.group().strip()}'")
                print(f"      Next 200 chars: {sample[:200]}...")
        
        # Step 3: Look for numbered citations
        print("\n3️⃣ Looking for numbered citations...")
        citation_patterns = [
            r'\[(\d+)\]',  # [1], [2], etc.
            r'\((\d+)\)',  # (1), (2), etc.
        ]
        
        all_citations = set()
        for pattern in citation_patterns:
            matches = re.findall(pattern, text)
            citations = [int(num) for num in matches if num.isdigit() and int(num) <= 100]
            all_citations.update(citations)
            print(f"   Pattern '{pattern}': Found citations {sorted(citations)[:10]}{'...' if len(citations) > 10 else ''}")
        
        print(f"   Total unique citation numbers: {sorted(all_citations)[:20]}{'...' if len(all_citations) > 20 else ''}")
        
        # Step 4: Look for corresponding reference text
        print("\n4️⃣ Looking for reference text for citations...")
        found_refs = []
        
        for num in sorted(all_citations)[:10]:  # Check first 10 citations
            ref_patterns = [
                rf'(?:^|\n)\s*\[{num}\]\s+([^\[\n]+(?:\n(?!\s*\[\d+\])[^\[\n]+)*)',
                rf'(?:^|\n)\s*{num}\.\s+([^\n]+(?:\n(?!\s*\d+\.)[^\n]+)*)',
            ]
            
            for pattern in ref_patterns:
                matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
                if matches:
                    ref_text = matches[0].strip()
                    found_refs.append((num, ref_text))
                    print(f"   [{num}]: {ref_text[:100]}{'...' if len(ref_text) > 100 else ''}")
                    break
        
        # Step 5: Pattern-based extraction
        print("\n5️⃣ Pattern-based reference extraction...")
        patterns = [
            (r'[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*(?:,\s*[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*)*\.\s*[^.]{10,100}\.\s*[A-Z][^.]{5,50}\.\s*\d{4}[;\s]*\d*[:\s]*\d*[-\d]*', 'Journal articles'),
            (r'[A-Z][a-z]+(?:\s+[A-Z]{1,3}\.?)*(?:,\s*[A-Z][a-z]+)*\.\s*[^.]{10,100}\.\s*[A-Z][^,]{5,50},\s*\d{4}', 'Books'),
            (r'[^.\n]*doi:\s*10\.\d+/[^\s\n]+[^.\n]*', 'DOI references'),
            (r'[^.\n]*https?://[^\s\n]+[^.\n]*', 'URLs'),
            (r'[^.\n]*PMID:\s*\d+[^.\n]*', 'PubMed'),
        ]
        
        pattern_matches = []
        for pattern, name in patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.IGNORECASE)
            valid_matches = [m for m in matches if len(m.strip()) > 20]
            pattern_matches.extend(valid_matches)
            print(f"   {name}: {len(valid_matches)} matches")
            for match in valid_matches[:3]:  # Show first 3
                print(f"      {match[:100]}{'...' if len(match) > 100 else ''}")
        
        # Step 6: Summary
        print("\n6️⃣ SUMMARY")
        print("="*50)
        print(f"📄 Document: {test_file.name}")
        print(f"📝 Text length: {len(text):,} characters")
        print(f"📍 Reference sections found: {len(reference_sections)}")
        print(f"🔢 Citation numbers found: {len(all_citations)}")
        print(f"📚 Numbered references found: {len(found_refs)}")
        print(f"🎯 Pattern matches found: {len(pattern_matches)}")
        
        total_potential = len(found_refs) + len(pattern_matches)
        print(f"\n🎯 TOTAL POTENTIAL REFERENCES: {total_potential}")
        
        # Show some sample text around reference sections
        if reference_sections:
            print(f"\n📋 Sample text from reference sections:")
            for i, (start_pos, end_pos, sample) in enumerate(reference_sections[:2]):
                print(f"\nSection {i+1} (position {start_pos}):")
                print("-" * 40)
                print(sample[:500])
                print("-" * 40)
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🔍 Reference Extraction Debug Tool")
    print("="*40)
    asyncio.run(debug_reference_extraction())
