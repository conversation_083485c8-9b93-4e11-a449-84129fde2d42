"""
<PERSON><PERSON>t to validate UUID consistency in merged entities.

This script:
1. Identifies merged entities
2. Checks for orphaned relationships after entity merges
3. Validates that relationships point to the correct merged entity
4. Fixes any inconsistencies found

Usage:
    python scripts/validate_entity_merges.py [--fix] [--verbose]
"""

import os
import asyncio
import logging
import uuid
import argparse
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Tuple

import sys
from pathlib import Path

# Add the project root directory to the Python path
sys.path.append(str(Path(__file__).parent.parent))

from dotenv import load_dotenv
from database.database_service import get_falkordb_adapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def identify_merged_entities() -> List[Dict[str, Any]]:
    """
    Identify entities that have been merged.

    Returns:
        List of merged entities with their details
    """
    adapter = await get_falkordb_adapter()

    # Find entities with multiple incoming MENTIONS relationships
    # This is a heuristic to identify merged entities
    query = """
    MATCH (e:Entity)<-[r:MENTIONS]-()
    WITH e, count(r) as mention_count
    WHERE mention_count > 1
    RETURN e.uuid as uuid, e.name as name, e.type as type, mention_count
    ORDER BY mention_count DESC
    LIMIT 100
    """

    result = adapter.execute_cypher(query)
    merged_entities = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            merged_entities.append({
                "uuid": row[0],
                "name": row[1],
                "type": row[2],
                "mention_count": row[3]
            })

    logger.info(f"Found {len(merged_entities)} potential merged entities")
    return merged_entities

async def check_orphaned_relationships() -> List[Dict[str, Any]]:
    """
    Check for orphaned relationships after entity merges.

    Returns:
        List of orphaned relationships
    """
    adapter = await get_falkordb_adapter()

    # Find relationships pointing to non-existent entities
    query = """
    MATCH ()-[r]->(e:Entity)
    WHERE NOT EXISTS(e.uuid)
    RETURN id(r) as rel_id, type(r) as rel_type
    LIMIT 100
    """

    result = adapter.execute_cypher(query)
    orphaned_relationships = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            orphaned_relationships.append({
                "rel_id": row[0],
                "rel_type": row[1]
            })

    logger.info(f"Found {len(orphaned_relationships)} orphaned relationships")
    return orphaned_relationships

async def fix_orphaned_relationships(orphaned_relationships: List[Dict[str, Any]]) -> int:
    """
    Fix orphaned relationships by removing them.

    Args:
        orphaned_relationships: List of orphaned relationships

    Returns:
        Number of relationships fixed
    """
    adapter = await get_falkordb_adapter()
    fixed_count = 0

    for rel in orphaned_relationships:
        # Delete the orphaned relationship
        query = f"""
        MATCH ()-[r]->()
        WHERE id(r) = {rel['rel_id']}
        DELETE r
        """

        result = adapter.execute_cypher(query)

        if result:
            fixed_count += 1
            logger.info(f"Deleted orphaned relationship of type {rel['rel_type']}")

    return fixed_count

async def check_relationship_consistency() -> List[Dict[str, Any]]:
    """
    Check for inconsistent relationships after entity merges.

    Returns:
        List of inconsistent relationships
    """
    adapter = await get_falkordb_adapter()

    # Find relationships with inconsistent UUIDs
    # This is a complex query that looks for relationships where the target node UUID
    # doesn't match the UUID stored in the relationship properties
    query = """
    MATCH (source)-[r]->(target:Entity)
    WHERE EXISTS(r.target_uuid) AND r.target_uuid <> target.uuid
    RETURN id(r) as rel_id, type(r) as rel_type,
           source.uuid as source_uuid, target.uuid as target_uuid,
           r.target_uuid as stored_target_uuid
    LIMIT 100
    """

    result = adapter.execute_cypher(query)
    inconsistent_relationships = []

    if result and len(result) > 1 and len(result[1]) > 0:
        for row in result[1]:
            inconsistent_relationships.append({
                "rel_id": row[0],
                "rel_type": row[1],
                "source_uuid": row[2],
                "target_uuid": row[3],
                "stored_target_uuid": row[4]
            })

    logger.info(f"Found {len(inconsistent_relationships)} inconsistent relationships")
    return inconsistent_relationships

async def fix_relationship_consistency(inconsistent_relationships: List[Dict[str, Any]]) -> int:
    """
    Fix inconsistent relationships by updating the stored UUID.

    Args:
        inconsistent_relationships: List of inconsistent relationships

    Returns:
        Number of relationships fixed
    """
    adapter = await get_falkordb_adapter()
    fixed_count = 0

    for rel in inconsistent_relationships:
        # Update the relationship to use the correct target UUID
        query = f"""
        MATCH ()-[r]->()
        WHERE id(r) = {rel['rel_id']}
        SET r.target_uuid = '{rel['target_uuid']}',
            r.updated_at = '{datetime.now(timezone.utc).isoformat()}'
        """

        result = adapter.execute_cypher(query)

        if result:
            fixed_count += 1
            logger.info(f"Fixed inconsistent relationship of type {rel['rel_type']}")

    return fixed_count

async def main():
    """Main function."""
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Validate UUID consistency in merged entities")
    parser.add_argument("--fix", action="store_true", help="Fix any inconsistencies found")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Load environment variables
    load_dotenv()

    try:
        # Identify merged entities
        merged_entities = await identify_merged_entities()

        if args.verbose and merged_entities:
            logger.info("Top merged entities:")
            for entity in merged_entities[:10]:
                logger.info(f"  - {entity['name']} ({entity['type']}): {entity['mention_count']} mentions")

        # Check for orphaned relationships
        orphaned_relationships = await check_orphaned_relationships()

        if orphaned_relationships:
            logger.warning(f"Found {len(orphaned_relationships)} orphaned relationships")

            if args.fix:
                fixed_count = await fix_orphaned_relationships(orphaned_relationships)
                logger.info(f"Fixed {fixed_count} orphaned relationships")
            else:
                logger.info("Run with --fix to remove orphaned relationships")

        # Check for relationship consistency
        inconsistent_relationships = await check_relationship_consistency()

        if inconsistent_relationships:
            logger.warning(f"Found {len(inconsistent_relationships)} inconsistent relationships")

            if args.verbose:
                for rel in inconsistent_relationships[:5]:
                    logger.info(f"  - Relationship of type {rel['rel_type']} has target_uuid {rel['stored_target_uuid']} but points to entity with UUID {rel['target_uuid']}")

            if args.fix:
                fixed_count = await fix_relationship_consistency(inconsistent_relationships)
                logger.info(f"Fixed {fixed_count} inconsistent relationships")
            else:
                logger.info("Run with --fix to fix inconsistent relationships")

        # Summary
        if not orphaned_relationships and not inconsistent_relationships:
            logger.info("No issues found with merged entities")
        else:
            total_issues = len(orphaned_relationships) + len(inconsistent_relationships)
            logger.info(f"Found {total_issues} issues with merged entities")

            if not args.fix:
                logger.info("Run with --fix to fix these issues")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
