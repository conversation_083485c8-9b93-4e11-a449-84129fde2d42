"""
Test script for document metadata and reference extraction
"""

import os
import asyncio
import logging
import json
from pathlib import Path
import argparse

from document_metadata import DocumentMetadataExtractor
from reference_extraction import ReferenceExtractor
from enhanced_pdf_processor import EnhancedPDFProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_extraction.log')
    ]
)
logger = logging.getLogger(__name__)

async def test_metadata_extraction(pdf_path):
    """Test metadata extraction"""
    logger.info(f"Testing metadata extraction for {pdf_path}")
    
    # Create output directory
    output_dir = Path("test_output/metadata")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize extractor
    extractor = DocumentMetadataExtractor(llm_provider='openai')
    
    # Extract metadata
    metadata = await extractor.process_document(pdf_path)
    
    # Save metadata
    output_path = output_dir / f"{Path(pdf_path).stem}_metadata.json"
    extractor.save_metadata_to_json(metadata, str(output_path))
    
    # Print summary
    print("\n=== Metadata Extraction Results ===")
    print(f"File: {pdf_path}")
    
    # Print key metadata fields
    if "llm_extracted" in metadata:
        llm_data = metadata["llm_extracted"]
        print(f"Title: {llm_data.get('title', 'Not found')}")
        print(f"Authors: <AUTHORS>
        print(f"Publication Date: {llm_data.get('publication_date', 'Not found')}")
        print(f"Journal: {llm_data.get('journal', 'Not found')}")
        print(f"DOI: {llm_data.get('doi', 'Not found')}")
    else:
        print("No LLM-extracted metadata found")
    
    print(f"Pages: {metadata.get('num_pages', 'Not found')}")
    print(f"Creator: {metadata.get('creator', 'Not found')}")
    print(f"Producer: {metadata.get('producer', 'Not found')}")
    
    print(f"\nFull metadata saved to: {output_path}")
    
    return metadata

async def test_reference_extraction(pdf_path):
    """Test reference extraction"""
    logger.info(f"Testing reference extraction for {pdf_path}")
    
    # Create output directory
    output_dir = Path("test_output/references")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize extractor
    extractor = ReferenceExtractor(llm_provider='openai')
    
    # Extract references
    references = await extractor.process_document(pdf_path)
    
    # Save references
    json_path = output_dir / f"{Path(pdf_path).stem}_references.json"
    csv_path = output_dir / f"{Path(pdf_path).stem}_references.csv"
    extractor.save_references_to_json(references, str(json_path))
    extractor.save_references_to_csv(references, str(csv_path))
    
    # Print summary
    print("\n=== Reference Extraction Results ===")
    print(f"File: {pdf_path}")
    print(f"Regex References: {len(references.get('regex_references', []))}")
    print(f"LLM References: {len(references.get('llm_references', []))}")
    print(f"Total References: {references.get('total_reference_count', 0)}")
    
    # Print sample references
    if references.get('llm_references'):
        print("\nSample LLM References:")
        for i, ref in enumerate(references['llm_references'][:3]):  # Show first 3
            authors = ref.get('authors', '')
            if isinstance(authors, list):
                authors = '; '.join(authors)
            
            print(f"{i+1}. {authors} ({ref.get('year', '')}). {ref.get('title', '')}")
    
    if references.get('regex_references'):
        print("\nSample Regex References:")
        for i, ref in enumerate(references['regex_references'][:3]):  # Show first 3
            print(f"{i+1}. {ref[:100]}...")
    
    print(f"\nReferences saved to: {json_path} and {csv_path}")
    
    return references

async def test_enhanced_processor(pdf_path):
    """Test the enhanced PDF processor"""
    logger.info(f"Testing enhanced PDF processor for {pdf_path}")
    
    # Create output directory
    output_dir = Path("test_output/enhanced")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Initialize processor
    processor = EnhancedPDFProcessor(llm_provider='openai')
    
    # Process PDF
    result = await processor.process_pdf(
        pdf_path,
        chunk_size=1200,
        overlap=50,
        extract_metadata=True,
        extract_references=True,
        output_dir=str(output_dir)
    )
    
    # Save result
    output_path = output_dir / f"{Path(pdf_path).stem}_result.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        # Convert any non-serializable objects to strings
        serializable_result = json.loads(json.dumps(result, default=str))
        json.dump(serializable_result, f, indent=2)
    
    # Print summary
    print("\n=== Enhanced PDF Processor Results ===")
    print(f"File: {pdf_path}")
    
    kg_result = result.get("knowledge_graph", {})
    print(f"Knowledge Graph: {'Success' if kg_result.get('success', False) else 'Failed'}")
    print(f"Chunks: {kg_result.get('chunks', 0)}")
    print(f"OCR Provider: {kg_result.get('ocr_provider', 'Not specified')}")
    
    print(f"Metadata Extracted: {'Yes' if 'metadata' in result else 'No'}")
    print(f"References Extracted: {'Yes' if 'references' in result else 'No'}")
    
    if 'references' in result:
        ref_count = result['references'].get('total_reference_count', 0)
        print(f"Total References: {ref_count}")
    
    print(f"\nFull result saved to: {output_path}")
    
    return result

async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test document metadata and reference extraction")
    parser.add_argument("pdf_path", help="Path to PDF file or directory")
    parser.add_argument("--test", choices=["metadata", "references", "enhanced", "all"], default="all", help="Test to run")
    
    args = parser.parse_args()
    pdf_path = args.pdf_path
    test_type = args.test
    
    if not os.path.exists(pdf_path):
        print(f"Error: File or directory not found: {pdf_path}")
        return
    
    # Process single file
    if os.path.isfile(pdf_path):
        if pdf_path.lower().endswith('.pdf'):
            if test_type in ["metadata", "all"]:
                await test_metadata_extraction(pdf_path)
            
            if test_type in ["references", "all"]:
                await test_reference_extraction(pdf_path)
            
            if test_type in ["enhanced", "all"]:
                await test_enhanced_processor(pdf_path)
        else:
            print(f"Error: Not a PDF file: {pdf_path}")
    
    # Process directory
    elif os.path.isdir(pdf_path):
        pdf_files = [os.path.join(pdf_path, f) for f in os.listdir(pdf_path) if f.lower().endswith('.pdf')]
        
        if not pdf_files:
            print(f"Error: No PDF files found in directory: {pdf_path}")
            return
        
        print(f"Found {len(pdf_files)} PDF files in {pdf_path}")
        
        for i, pdf_file in enumerate(pdf_files):
            print(f"\nProcessing file {i+1}/{len(pdf_files)}: {os.path.basename(pdf_file)}")
            
            if test_type in ["metadata", "all"]:
                await test_metadata_extraction(pdf_file)
            
            if test_type in ["references", "all"]:
                await test_reference_extraction(pdf_file)
            
            if test_type in ["enhanced", "all"]:
                await test_enhanced_processor(pdf_file)

if __name__ == "__main__":
    asyncio.run(main())
