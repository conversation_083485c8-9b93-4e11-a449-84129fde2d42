"""
LLM-based entity extractor.

This module implements entity extraction using Large Language Models (LLMs) through
various providers including OpenRouter, OpenAI, and local LLM deployments via Ollama.
It provides a flexible approach to entity extraction that can adapt to different
LLM providers based on availability and configuration.

The module prioritizes using OpenRouter (particularly with meta-llama/llama-4-maverick),
falls back to local LLM if available, and finally uses OpenAI as a last resort.
This prioritization can be configured through environment variables.

Features:
- Multi-provider support (OpenRouter, OpenAI, local LLM)
- Configurable model selection
- Robust error handling and JSON parsing
- Detailed logging of extraction process

Example:
    ```python
    from entity_extraction.extractors.llm_extractor import LLMEntityExtractor

    # Create an extractor with an optional API key
    extractor = LLMEntityExtractor(api_key="your-api-key")

    # Extract entities from text
    entities = extractor.extract_entities("Vitamin C is an essential nutrient.")

    # Print the extracted entities
    for entity in entities:
        print(f"{entity['name']} ({entity['type']}): {entity.get('description', '')}")
    ```

Environment Variables:
    ENTITY_EXTRACTION_PROVIDER: LLM provider to use ('openrouter', 'openai', 'local')
    ENTITY_EXTRACTION_MODEL: Model to use for entity extraction
    OPEN_ROUTER_API_KEY: API key for OpenRouter
    OPENAI_API_KEY: API key for OpenAI
    OPENAI_ENTITY_MODEL: OpenAI model to use for entity extraction
    OLLAMA_BASE_URL: Base URL for Ollama API
"""

import os
import json
import logging
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

import openai

from entity_extraction.base import EntityExtractor

# Load environment variables
load_dotenv()

# Set up logging
logger = logging.getLogger(__name__)

# Import clients
try:
    from utils.local_llm_client import LocalLLMClient
except ImportError:
    logger.warning("LocalLLMClient not found, will use OpenAI if available")
    LocalLLMClient = None

try:
    from utils.open_router_client import OpenRouterClient
except ImportError:
    logger.warning("OpenRouterClient not found, will use OpenAI if available")
    OpenRouterClient = None


class LLMEntityExtractor(EntityExtractor):
    """
    Entity extractor that uses LLMs (OpenRouter, OpenAI, or local LLM).

    This class implements the EntityExtractor interface using various LLM providers.
    It attempts to use the providers in the following order (configurable via environment variables):
    1. OpenRouter (preferred for meta-llama/llama-4-maverick)
    2. Local LLM via Ollama
    3. OpenAI (fallback)

    The extractor handles the complexities of interacting with different LLM APIs,
    parsing responses, and handling errors, providing a unified interface for entity extraction.

    Attributes:
        api_key (Optional[str]): API key for the LLM service
        llm_provider (str): The LLM provider to use ('openrouter', 'openai', 'local')
    """

    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize the LLM entity extractor.

        Args:
            api_key: API key for the LLM service
        """
        super().__init__(api_key)
        self.llm_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER', 'openrouter').lower()

    def extract_entities(self, text: str) -> List[Dict[str, Any]]:
        """
        Extract entities from text using OpenRouter, OpenAI, or local LLM.

        This method implements the abstract method from the EntityExtractor base class.
        It extracts entities from the given text using the configured LLM provider.

        The method follows this process:
        1. Get the system prompt that instructs the LLM how to extract entities
        2. Determine which LLM provider to use based on configuration
        3. Call the appropriate extraction method for the selected provider
        4. Parse and return the extracted entities

        If an error occurs during extraction, it will be logged and an empty list will be returned.

        Args:
            text (str): Text to extract entities from. This can be a sentence, paragraph,
                or longer document.

        Returns:
            List[Dict[str, Any]]: A list of dictionaries, where each dictionary represents
                an entity with the following keys:
                - name (str): The name of the entity
                - type (str): The type of the entity (one of ENTITY_TYPES)
                - description (Optional[str]): A description of the entity
        """
        logger.info(f"Extracting entities from text ({len(text)} characters)")

        system_prompt = self.get_system_prompt()

        try:
            # Use OpenRouter (preferred for meta-llama/llama-4-maverick)
            if self.llm_provider == 'openrouter' and OpenRouterClient is not None:
                return self._extract_with_openrouter(text, system_prompt)

            # Use local LLM
            if self.llm_provider == 'local' and LocalLLMClient is not None:
                return self._extract_with_local_llm(text, system_prompt)

            # Use OpenAI as fallback
            return self._extract_with_openai(text, system_prompt)

        except Exception as e:
            logger.error(f"Error extracting entities: {e}")
            return []

    def _extract_with_openrouter(self, text: str, system_prompt: str) -> List[Dict[str, Any]]:
        """
        Extract entities using OpenRouter.

        This method uses the OpenRouter API to extract entities from text. OpenRouter
        provides access to various LLM models, with a preference for meta-llama/llama-4-maverick
        for entity extraction tasks.

        The method handles:
        - API key validation
        - Model selection
        - API request formatting
        - Response parsing
        - Error handling

        If the OpenRouter API key is not available, it will fall back to OpenAI.
        If the response cannot be parsed as JSON, it attempts to extract JSON from the text.

        Args:
            text (str): Text to extract entities from
            system_prompt (str): System prompt for the LLM that specifies the extraction task

        Returns:
            List[Dict[str, Any]]: A list of extracted entities
        """
        logger.info("Using OpenRouter for entity extraction")
        model = os.environ.get('ENTITY_EXTRACTION_MODEL', 'meta-llama/llama-4-maverick')
        # Use instance API key first, then try both environment variable names for OpenRouter API key
        openrouter_api_key = self.api_key or os.environ.get('OPEN_ROUTER_API_KEY') or os.environ.get('OPENROUTER_API_KEY')

        if not openrouter_api_key:
            logger.warning("OpenRouter API key not found, falling back to OpenAI")
            return self._extract_with_openai(text, system_prompt)

        client = OpenRouterClient(api_key=openrouter_api_key, model=model)

        # Use the synchronous method with retry logic
        max_retries = 3
        for attempt in range(max_retries):
            try:
                content = client.generate_completion(
                    system_prompt=system_prompt,
                    user_prompt=text,
                    temperature=0.3,
                    max_tokens=4000
                )
                break
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    logger.error("All retry attempts failed")
                    return []

        # Process the content with enhanced retry logic for JSON parsing
        return self._parse_json_with_retry(content, "OpenRouter")

    def _parse_json_with_retry(self, content: str, provider: str) -> List[Dict[str, Any]]:
        """
        Parse JSON response with multiple retry strategies.

        Args:
            content: The response content to parse
            provider: The provider name for logging

        Returns:
            List of extracted entities
        """
        # Strategy 1: Direct JSON parsing
        try:
            data = json.loads(content)
            entities = data.get("entities", [])
            logger.info(f"Extracted {len(entities)} entities from text using {provider}")
            return entities
        except json.JSONDecodeError:
            pass

        # Strategy 2: Extract JSON from markdown code blocks
        try:
            # Look for ```json ... ``` blocks
            json_start = content.find('```json')
            if json_start >= 0:
                json_start = content.find('{', json_start)
                json_end = content.find('```', json_start)
                if json_end > json_start:
                    json_str = content[json_start:json_end].strip()
                    data = json.loads(json_str)
                    entities = data.get("entities", [])
                    logger.info(f"Extracted {len(entities)} entities from text using {provider} (JSON from markdown)")
                    return entities
        except:
            pass

        # Strategy 3: Extract JSON from general text
        try:
            # Look for JSON-like structure in the response
            json_start = content.find('{')
            json_end = content.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = content[json_start:json_end]
                data = json.loads(json_str)
                entities = data.get("entities", [])
                logger.info(f"Extracted {len(entities)} entities from text using {provider} (JSON extracted from response)")
                return entities
        except:
            pass

        logger.error(f"Error parsing JSON response from {provider}")
        logger.error(f"Response content: {content[:500]}...")  # Truncate for logging
        return []

    def _extract_with_local_llm(self, text: str, system_prompt: str) -> List[Dict[str, Any]]:
        """
        Extract entities using local LLM via Ollama.

        This method uses a locally deployed LLM through Ollama to extract entities from text.
        It's useful for scenarios where:
        - Internet connectivity is limited
        - Data privacy is a concern
        - Cost optimization is needed
        - Customized models are preferred

        The method configures:
        - The Ollama base URL (defaults to http://localhost:11434)
        - The model to use (defaults to medllama3-v20)
        - Request parameters like temperature and max tokens

        It handles the API request, response parsing, and error handling.

        Args:
            text (str): Text to extract entities from
            system_prompt (str): System prompt for the LLM that specifies the extraction task

        Returns:
            List[Dict[str, Any]]: A list of extracted entities
        """
        logger.info("Using local LLM for entity extraction")
        local_llm_model = os.environ.get('ENTITY_EXTRACTION_MODEL', 'medllama3-v20')
        ollama_base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')

        local_client = LocalLLMClient(base_url=ollama_base_url, model=local_llm_model)

        response = local_client.generate_completion(
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": text}
            ],
            temperature=0.3,
            max_tokens=4000,
            response_format={"type": "json_object"}
        )

        # Parse the response
        content = response["choices"][0]["message"]["content"]

        # Process the content with enhanced retry logic
        return self._parse_json_with_retry(content, "Local LLM")

    def _extract_with_openai(self, text: str, system_prompt: str) -> List[Dict[str, Any]]:
        """
        Extract entities using OpenAI.

        This method uses the OpenAI API to extract entities from text. It serves as a
        fallback option when OpenRouter or local LLM options are not available.

        The method:
        - Validates the API key (from instance or environment)
        - Creates an OpenAI client
        - Configures the request with appropriate model and parameters
        - Handles the API response
        - Parses the JSON output
        - Manages error cases

        By default, it uses the 'gpt-4o' model, but this can be configured via the
        OPENAI_ENTITY_MODEL environment variable.

        Args:
            text (str): Text to extract entities from
            system_prompt (str): System prompt for the LLM that specifies the extraction task

        Returns:
            List[Dict[str, Any]]: A list of extracted entities
        """
        logger.info("Using OpenAI for entity extraction")
        openai_api_key = self.api_key or os.environ.get('OPENAI_API_KEY')

        if not openai_api_key:
            logger.error("OpenAI API key not found")
            return []

        try:
            client = openai.OpenAI(api_key=openai_api_key)
        except TypeError as e:
            if "proxies" in str(e):
                logger.error(f"OpenAI client initialization failed due to version compatibility issue: {e}")
                logger.error("Consider updating the openai package")
                return []
            else:
                logger.error(f"OpenAI client initialization failed: {e}")
                return []

        response = client.chat.completions.create(
            model=os.environ.get('OPENAI_ENTITY_MODEL', 'gpt-4o'),
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": text}
            ],
            response_format={"type": "json_object"}
        )

        # Parse the response
        content = response.choices[0].message.content

        # Process the content with enhanced retry logic
        return self._parse_json_with_retry(content, "OpenAI")
