"""
Entity relationship service for the Graphiti application.

This service handles entity relationship operations.
"""

from typing import List, Dict, Any

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter

# Set up logger
logger = get_logger(__name__)


async def get_entity_relationships(entity_uuid: str) -> List[Dict[str, Any]]:
    """
    Get relationships for an entity.

    Args:
        entity_uuid: Entity UUID

    Returns:
        List of relationships
    """
    try:
        # First try the direct approach
        relationships = get_entity_relationships_direct(entity_uuid)
        if relationships:
            return relationships

        # Fall back to the original approach if direct approach fails
        adapter = await get_falkordb_adapter()
        relationships = []

        # Get outgoing relationships
        outgoing_query = f"""
        MATCH (e:Entity {{uuid: '{entity_uuid}'}})-[r]->(target:Entity)
        RETURN type(r) as type, r as properties, target.uuid as target_uuid, target.name as target_name, target.type as target_type
        """

        outgoing_result = adapter.execute_cypher(outgoing_query)

        if outgoing_result and len(outgoing_result) > 1:
            for row in outgoing_result[1]:
                rel_type = row[0]
                properties = row[1]
                target_uuid = row[2]
                target_name = row[3]
                target_type = row[4]

                relationships.append({
                    "type": rel_type,
                    "properties": properties,
                    "target_uuid": target_uuid,
                    "target_name": target_name,
                    "target_type": target_type,
                    "direction": "outgoing"
                })

        # Get incoming relationships
        incoming_query = f"""
        MATCH (source:Entity)-[r]->(e:Entity {{uuid: '{entity_uuid}'}})
        RETURN type(r) as type, r as properties, source.uuid as source_uuid, source.name as source_name, source.type as source_type
        """

        incoming_result = adapter.execute_cypher(incoming_query)

        if incoming_result and len(incoming_result) > 1:
            for row in incoming_result[1]:
                rel_type = row[0]
                properties = row[1]
                source_uuid = row[2]
                source_name = row[3]
                source_type = row[4]

                relationships.append({
                    "type": rel_type,
                    "properties": properties,
                    "source_uuid": source_uuid,
                    "source_name": source_name,
                    "source_type": source_type,
                    "direction": "incoming"
                })

        return relationships
    except Exception as e:
        logger.error(f"Error getting entity relationships: {str(e)}")
        return []


def get_entity_relationships_direct(entity_uuid: str) -> List[Dict[str, Any]]:
    """
    Get entity relationships using direct database access.

    Args:
        entity_uuid: Entity UUID

    Returns:
        List of relationships
    """
    try:
        # This is a placeholder for direct database access
        # In a real implementation, this would use a more efficient method
        # For now, return empty list to fall back to the async method
        return []
    except Exception as e:
        logger.error(f"Error in direct entity relationships query: {str(e)}")
        return []


async def create_entity_relationship(source_uuid: str, target_uuid: str, relationship_type: str, properties: Dict[str, Any] = None) -> bool:
    """
    Create a relationship between two entities.

    Args:
        source_uuid: Source entity UUID
        target_uuid: Target entity UUID
        relationship_type: Type of relationship
        properties: Optional relationship properties

    Returns:
        True if successful, False otherwise
    """
    try:
        adapter = await get_falkordb_adapter()

        # Build properties string
        props_str = ""
        if properties:
            prop_parts = []
            for key, value in properties.items():
                if isinstance(value, str):
                    prop_parts.append(f"{key}: '{value}'")
                else:
                    prop_parts.append(f"{key}: {value}")
            if prop_parts:
                props_str = "{" + ", ".join(prop_parts) + "}"

        query = f"""
        MATCH (source:Entity {{uuid: '{source_uuid}'}}), (target:Entity {{uuid: '{target_uuid}'}})
        CREATE (source)-[r:{relationship_type} {props_str}]->(target)
        RETURN r
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            logger.info(f"Created relationship {relationship_type} between {source_uuid} and {target_uuid}")
            return True
        else:
            logger.error(f"Failed to create relationship between {source_uuid} and {target_uuid}")
            return False

    except Exception as e:
        logger.error(f"Error creating entity relationship: {str(e)}")
        return False


async def delete_entity_relationship(source_uuid: str, target_uuid: str, relationship_type: str = None) -> bool:
    """
    Delete a relationship between two entities.

    Args:
        source_uuid: Source entity UUID
        target_uuid: Target entity UUID
        relationship_type: Optional specific relationship type to delete

    Returns:
        True if successful, False otherwise
    """
    try:
        adapter = await get_falkordb_adapter()

        if relationship_type:
            query = f"""
            MATCH (source:Entity {{uuid: '{source_uuid}'}})-[r:{relationship_type}]->(target:Entity {{uuid: '{target_uuid}'}})
            DELETE r
            RETURN COUNT(r) as deleted_count
            """
        else:
            query = f"""
            MATCH (source:Entity {{uuid: '{source_uuid}'}})-[r]->(target:Entity {{uuid: '{target_uuid}'}})
            DELETE r
            RETURN COUNT(r) as deleted_count
            """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            deleted_count = result[1][0][0]
            logger.info(f"Deleted {deleted_count} relationships between {source_uuid} and {target_uuid}")
            return deleted_count > 0
        else:
            logger.warning(f"No relationships found to delete between {source_uuid} and {target_uuid}")
            return False

    except Exception as e:
        logger.error(f"Error deleting entity relationship: {str(e)}")
        return False


async def get_entity_neighbors(entity_uuid: str, max_depth: int = 1) -> List[Dict[str, Any]]:
    """
    Get neighboring entities within a specified depth.

    Args:
        entity_uuid: Entity UUID
        max_depth: Maximum relationship depth to traverse

    Returns:
        List of neighboring entities with relationship information
    """
    try:
        adapter = await get_falkordb_adapter()

        query = f"""
        MATCH path = (e:Entity {{uuid: '{entity_uuid}'}})-[*1..{max_depth}]-(neighbor:Entity)
        RETURN neighbor.uuid as uuid, neighbor.name as name, neighbor.type as type, length(path) as depth
        ORDER BY depth, neighbor.name
        """

        result = adapter.execute_cypher(query)

        neighbors = []
        if result and len(result) > 1:
            for row in result[1]:
                neighbor_uuid = row[0]
                neighbor_name = row[1]
                neighbor_type = row[2]
                depth = row[3]

                neighbors.append({
                    "uuid": neighbor_uuid,
                    "name": neighbor_name,
                    "type": neighbor_type,
                    "depth": depth
                })

        return neighbors

    except Exception as e:
        logger.error(f"Error getting entity neighbors: {str(e)}")
        return []
