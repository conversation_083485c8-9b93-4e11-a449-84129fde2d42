<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Test - Graphiti Knowledge Graph</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    <style>
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        #debug-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 1rem;
            margin-top: 1rem;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Document Test Page</h1>

        <!-- Documents List -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Document List Test</h5>
            </div>
            <div class="card-body">
                <div id="documents-loading" class="loading">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Loading documents...</p>
                </div>
                <div id="documents-list">
                    <!-- Documents will be displayed here -->
                </div>
                <div id="debug-output">
                    <!-- Debug output will be displayed here -->
                </div>
                <button id="test-button" class="btn btn-primary mt-3">Test Document Loading</button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Debug logger
        function log(message) {
            const debugOutput = document.getElementById('debug-output');
            if (debugOutput) {
                const timestamp = new Date().toISOString().substring(11, 23);
                debugOutput.innerHTML += `[${timestamp}] ${message}\n`;
                debugOutput.scrollTop = debugOutput.scrollHeight;
            }
            console.log(message);
        }

        // Direct document loading function
        function loadDocuments() {
            log('Starting document loading test...');
            
            // Show loading spinner
            const loadingSpinner = document.getElementById('documents-loading');
            if (loadingSpinner) {
                loadingSpinner.style.display = 'flex';
                log('Showing loading spinner');
            }
            
            // Fetch documents
            log('Fetching documents from API...');
            fetch('/api/documents?page=1&page_size=10')
                .then(response => {
                    log(`API response status: ${response.status}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    log(`Received ${data.documents ? data.documents.length : 0} documents`);
                    log(`Total documents: ${data.total}, Page: ${data.page}, Page size: ${data.page_size}`);
                    
                    // Render documents
                    renderDocuments(data);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                        log('Hiding loading spinner');
                    }
                })
                .catch(error => {
                    log(`Error loading documents: ${error.message}`);
                    
                    // Hide loading spinner
                    if (loadingSpinner) {
                        loadingSpinner.style.display = 'none';
                        log('Hiding loading spinner due to error');
                    }
                    
                    // Show error message
                    const documentsList = document.getElementById('documents-list');
                    if (documentsList) {
                        documentsList.innerHTML = `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
                    }
                });
        }

        // Render documents
        function renderDocuments(data) {
            log('Rendering documents...');
            
            const documentsList = document.getElementById('documents-list');
            if (!documentsList) {
                log('Error: Documents list element not found');
                return;
            }
            
            // Clear existing content
            documentsList.innerHTML = '';
            
            // If no documents, show message
            if (!data.documents || data.documents.length === 0) {
                documentsList.innerHTML = '<div class="alert alert-info">No documents found.</div>';
                log('No documents found');
                return;
            }
            
            // Create documents table
            const table = document.createElement('table');
            table.className = 'table table-striped table-hover';
            
            // Create table header
            const thead = document.createElement('thead');
            thead.innerHTML = `
                <tr>
                    <th>UUID</th>
                    <th>Filename</th>
                    <th>Type</th>
                    <th>Upload Date</th>
                    <th>Chunks</th>
                    <th>Entities</th>
                    <th>References</th>
                </tr>
            `;
            table.appendChild(thead);
            
            // Create table body
            const tbody = document.createElement('tbody');
            
            // Add document rows
            data.documents.forEach(doc => {
                log(`Processing document: ${doc.uuid} - ${doc.filename}`);
                
                const row = document.createElement('tr');
                
                // Format date
                let formattedDate = 'Unknown';
                try {
                    const uploadDate = new Date(doc.upload_date);
                    formattedDate = uploadDate.toLocaleDateString() + ' ' + uploadDate.toLocaleTimeString();
                } catch (e) {
                    log(`Error formatting date for document ${doc.uuid}: ${e.message}`);
                }
                
                row.innerHTML = `
                    <td>${doc.uuid || 'Unknown'}</td>
                    <td>${doc.filename || 'Unknown'}</td>
                    <td>${doc.file_type || 'Unknown'}</td>
                    <td>${formattedDate}</td>
                    <td>${doc.chunks || 0}</td>
                    <td>${doc.entities || 0}</td>
                    <td>${doc.references || 0}</td>
                `;
                
                tbody.appendChild(row);
            });
            
            table.appendChild(tbody);
            documentsList.appendChild(table);
            log('Documents rendered successfully');
        }

        // Set up event listeners
        document.addEventListener('DOMContentLoaded', function() {
            log('Document test page loaded');
            
            const testButton = document.getElementById('test-button');
            if (testButton) {
                testButton.addEventListener('click', loadDocuments);
                log('Test button event listener added');
            }
        });
    </script>
</body>
</html>
