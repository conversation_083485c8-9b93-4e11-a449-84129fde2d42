#!/usr/bin/env python3
"""
Manually extract references from OneNote pages and add them to the CSV.
Since the automated extraction is failing, we'll parse the structured references manually.
"""

import sys
import os
import csv
import re
from pathlib import Path
from datetime import datetime
import uuid

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def parse_reference_line(ref_line: str) -> dict:
    """Parse a single reference line into structured data."""
    
    # Remove the number prefix (e.g., "1. ", "2. ")
    ref_text = re.sub(r'^\d+\.\s*', '', ref_line.strip())
    
    # Initialize reference data
    ref_data = {
        'reference_text': ref_text,
        'authors': '',
        'title': '',
        'year': '',
        'journal': '',
        'volume': '',
        'issue': '',
        'pages': '',
        'doi': '',
        'url': ''
    }
    
    # Try to parse different reference formats
    
    # Format 1: Author(s). Title. Journal. Year;Volume:Pages.
    # Example: "<PERSON><PERSON>, Singh <PERSON>. Cancer preventive properties of ginger: a brief review. Food Chem Toxicol. 2007;45:683–690."
    pattern1 = r'^([^.]+)\.\s*([^.]+)\.\s*([^.]+)\.\s*(\d{4});?(\d+)?:?([^.]*)\.'
    match1 = re.match(pattern1, ref_text)
    if match1:
        ref_data['authors'] = match1.group(1).strip()
        ref_data['title'] = match1.group(2).strip()
        ref_data['journal'] = match1.group(3).strip()
        ref_data['year'] = match1.group(4).strip()
        if match1.group(5):
            ref_data['volume'] = match1.group(5).strip()
        if match1.group(6):
            ref_data['pages'] = match1.group(6).strip()
        return ref_data
    
    # Format 2: Author(s). Title. Journal. Year;Volume(Issue):Pages.
    # Example: "Peng S, Yao J, Liu Y, et al. Activation of Nrf2 target enzymes conferring protection against oxidative stress in PC12 cells by ginger principal constituent 6-shogaol. Food Funct. 2015;6(8):2813–2823."
    pattern2 = r'^([^.]+)\.\s*([^.]+)\.\s*([^.]+)\.\s*(\d{4});(\d+)\((\d+)\):([^.]*)\.'
    match2 = re.match(pattern2, ref_text)
    if match2:
        ref_data['authors'] = match2.group(1).strip()
        ref_data['title'] = match2.group(2).strip()
        ref_data['journal'] = match2.group(3).strip()
        ref_data['year'] = match2.group(4).strip()
        ref_data['volume'] = match2.group(5).strip()
        ref_data['issue'] = match2.group(6).strip()
        ref_data['pages'] = match2.group(7).strip()
        return ref_data
    
    # Format 3: Book chapter format
    # Example: "Vasala PA. Ginger. In: Peter KV, editor. Handbook of herbs and spices. England: Woodland Publishing Limited; 2001. p. 195."
    pattern3 = r'^([^.]+)\.\s*([^.]+)\.\s*In:\s*([^.]+)\.\s*([^:]+):\s*([^;]+);\s*(\d{4})\.\s*p\.\s*([^.]*)\.'
    match3 = re.match(pattern3, ref_text)
    if match3:
        ref_data['authors'] = match3.group(1).strip()
        ref_data['title'] = match3.group(2).strip()
        ref_data['journal'] = f"In: {match3.group(3).strip()}"  # Book title
        ref_data['year'] = match3.group(6).strip()
        ref_data['pages'] = match3.group(7).strip()
        return ref_data
    
    # If no pattern matches, try to extract at least authors and year
    year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
    if year_match:
        ref_data['year'] = year_match.group()
    
    # Try to extract authors (text before first period)
    author_match = re.match(r'^([^.]+)\.', ref_text)
    if author_match:
        ref_data['authors'] = author_match.group(1).strip()
    
    return ref_data

def extract_references_from_file(file_path: Path) -> list:
    """Extract references from a OneNote page file."""
    
    print(f"📄 Processing: {file_path.name}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the "## Research References" section
        ref_section_match = re.search(r'## Research References\s*\n(.*?)(?=\n##|\n#|$)', content, re.DOTALL)
        
        if not ref_section_match:
            print(f"   ❌ No 'Research References' section found")
            return []
        
        ref_section = ref_section_match.group(1).strip()
        
        # Split into individual reference lines
        ref_lines = []
        for line in ref_section.split('\n'):
            line = line.strip()
            if line and re.match(r'^\d+\.', line):  # Lines starting with numbers
                ref_lines.append(line)
        
        if not ref_lines:
            print(f"   ❌ No numbered references found in section")
            return []
        
        print(f"   ✅ Found {len(ref_lines)} references")
        
        # Parse each reference
        references = []
        for i, ref_line in enumerate(ref_lines, 1):
            ref_data = parse_reference_line(ref_line)
            ref_data['source_document'] = file_path.name
            ref_data['extraction_method'] = 'manual_onenote'
            ref_data['extraction_date'] = datetime.now().isoformat()
            ref_data['confidence_score'] = 0.9
            ref_data['confidence'] = 0.9
            
            references.append(ref_data)
            
            # Show parsed reference
            authors = ref_data['authors'][:50] + "..." if len(ref_data['authors']) > 50 else ref_data['authors']
            title = ref_data['title'][:50] + "..." if len(ref_data['title']) > 50 else ref_data['title']
            year = ref_data['year']
            print(f"     {i}. {authors} ({year}). {title}")
        
        return references
        
    except Exception as e:
        print(f"   ❌ Error processing {file_path.name}: {e}")
        return []

def save_references_to_csv(all_references: list):
    """Save all references to CSV files."""
    
    if not all_references:
        print("❌ No references to save")
        return False
    
    print(f"\n💾 SAVING {len(all_references)} REFERENCES TO CSV")
    print("=" * 60)
    
    # Create references directory
    ref_dir = Path("references")
    ref_dir.mkdir(exist_ok=True)
    
    # CSV headers
    headers = [
        'source_document', 'document_uuid', 'extraction_method', 'reference_text',
        'authors', 'authors_raw', 'title', 'year', 'journal', 'volume', 'issue', 'pages',
        'doi', 'pmid', 'url', 'citation_apa', 'citation_mla', 'citation_chicago',
        'extraction_date', 'confidence_score', 'confidence'
    ]
    
    # Create individual CSV files for each document
    documents = {}
    for ref in all_references:
        doc_name = ref['source_document']
        if doc_name not in documents:
            documents[doc_name] = []
        documents[doc_name].append(ref)
    
    for doc_name, doc_refs in documents.items():
        # Generate document UUID
        doc_uuid = str(uuid.uuid4())
        
        # Create individual CSV file
        csv_filename = f"{doc_uuid}_{doc_name.replace('.txt', '')}_references.csv"
        csv_path = ref_dir / csv_filename
        
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            
            for ref in doc_refs:
                # Add missing fields
                row = {header: ref.get(header, '') for header in headers}
                row['document_uuid'] = doc_uuid
                row['authors_raw'] = ref.get('authors', '')
                
                # Generate citations
                authors = ref.get('authors', '')
                title = ref.get('title', '')
                year = ref.get('year', '')
                journal = ref.get('journal', '')
                volume = ref.get('volume', '')
                issue = ref.get('issue', '')
                pages = ref.get('pages', '')
                
                # APA citation
                apa_parts = []
                if authors: apa_parts.append(f"{authors}")
                if year: apa_parts.append(f"({year})")
                if title: apa_parts.append(f"{title}")
                if journal: apa_parts.append(f"{journal}")
                if volume and issue: apa_parts.append(f"{volume}({issue})")
                elif volume: apa_parts.append(f"{volume}")
                if pages: apa_parts.append(f"{pages}")
                row['citation_apa'] = ". ".join(apa_parts) if apa_parts else ""
                
                # MLA citation (simplified)
                row['citation_mla'] = row['citation_apa']  # Simplified for now
                
                # Chicago citation (simplified)
                row['citation_chicago'] = row['citation_apa']  # Simplified for now
                
                writer.writerow(row)
        
        print(f"✅ Saved {len(doc_refs)} references to: {csv_filename}")
    
    # Update master CSV file
    master_csv = ref_dir / "all_references.csv"
    
    # Read existing references
    existing_refs = []
    if master_csv.exists():
        with open(master_csv, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            existing_refs = list(reader)
    
    # Add new references
    with open(master_csv, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=headers)
        writer.writeheader()
        
        # Write existing references
        for ref in existing_refs:
            writer.writerow(ref)
        
        # Write new references
        for ref in all_references:
            row = {header: ref.get(header, '') for header in headers}
            row['document_uuid'] = str(uuid.uuid4())
            row['authors_raw'] = ref.get('authors', '')
            
            # Generate citations (same as above)
            authors = ref.get('authors', '')
            title = ref.get('title', '')
            year = ref.get('year', '')
            journal = ref.get('journal', '')
            volume = ref.get('volume', '')
            issue = ref.get('issue', '')
            pages = ref.get('pages', '')
            
            apa_parts = []
            if authors: apa_parts.append(f"{authors}")
            if year: apa_parts.append(f"({year})")
            if title: apa_parts.append(f"{title}")
            if journal: apa_parts.append(f"{journal}")
            if volume and issue: apa_parts.append(f"{volume}({issue})")
            elif volume: apa_parts.append(f"{volume}")
            if pages: apa_parts.append(f"{pages}")
            row['citation_apa'] = ". ".join(apa_parts) if apa_parts else ""
            row['citation_mla'] = row['citation_apa']
            row['citation_chicago'] = row['citation_apa']
            
            writer.writerow(row)
    
    print(f"✅ Updated master CSV with {len(all_references)} new references")
    return True

def main():
    """Manually extract references from OneNote pages."""
    
    print("📚 MANUAL ONENOTE REFERENCE EXTRACTION")
    print("=" * 60)
    
    # Find OneNote page documents
    onenote_dir = Path("onenote_page_documents")
    if not onenote_dir.exists():
        print("❌ OneNote page documents directory not found")
        return False
    
    onenote_files = list(onenote_dir.glob("onenote_page_*.txt"))
    if not onenote_files:
        print("❌ No OneNote page documents found")
        return False
    
    print(f"✅ Found {len(onenote_files)} OneNote page documents")
    
    # Extract references from all files
    all_references = []
    for file_path in onenote_files:
        file_refs = extract_references_from_file(file_path)
        all_references.extend(file_refs)
    
    print(f"\n📊 EXTRACTION SUMMARY:")
    print("=" * 60)
    print(f"✅ Processed: {len(onenote_files)} files")
    print(f"📚 Total references extracted: {len(all_references)}")
    
    if all_references:
        # Save to CSV
        success = save_references_to_csv(all_references)
        
        if success:
            print(f"\n🎉 SUCCESS! References extracted and saved")
            print(f"✅ {len(all_references)} references from OneNote pages")
            print(f"✅ Individual CSV files created for each page")
            print(f"✅ Master CSV file updated")
            return True
    
    print(f"\n❌ No references were extracted")
    return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
