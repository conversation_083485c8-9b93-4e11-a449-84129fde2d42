#!/usr/bin/env python3
"""
Manual Brain.one reference extraction script.
Run this manually in your Python environment to extract all 68 references.
"""

print("🧠 MANUAL BRAIN.ONE REFERENCE EXTRACTION")
print("=" * 60)

# Step 1: Import required modules
try:
    import asyncio
    import os
    import sys
    import re
    from pathlib import Path
    from datetime import datetime
    
    # Add current directory to path
    sys.path.append(os.getcwd())
    
    print("✅ Basic imports successful")
    
    # Step 2: Import Mistral OCR
    from utils.mistral_ocr import MistralOCRProcessor
    print("✅ Mistral OCR imported")
    
    # Step 3: Find Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found in uploads directory")
        exit(1)
    
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"✅ Found Brain.one file: {brain_file.name}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    # Step 4: Process with Mistral OCR
    async def extract_content():
        print("\n🔄 Processing with Mistral OCR...")
        
        mistral_ocr = MistralOCRProcessor()
        extracted_text = await mistral_ocr.extract_text_from_document(str(brain_file))
        
        if extracted_text and len(extracted_text.strip()) > 0:
            print(f"🎉 SUCCESS! Extracted {len(extracted_text)} characters!")
            
            # Save content
            content_file = f"brain_extracted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(content_file, "w", encoding="utf-8") as f:
                f.write(extracted_text)
            print(f"💾 Saved to: {content_file}")
            
            # Quick analysis
            lines = extracted_text.splitlines()
            print(f"📊 Content has {len(lines)} lines")
            
            # Look for references
            numbered_refs = re.findall(r'\d+\.\s+[A-Z]', extracted_text)
            print(f"🔍 Found {len(numbered_refs)} potential numbered references")
            
            # Show first few lines
            print(f"\n📖 FIRST 10 LINES:")
            for i, line in enumerate(lines[:10], 1):
                print(f"{i:2d}: {line[:80]}...")
            
            return extracted_text
        else:
            print("❌ No content extracted")
            return None
    
    # Step 5: Run extraction
    print("\n🚀 Starting extraction...")
    content = asyncio.run(extract_content())
    
    if content:
        print("\n✅ EXTRACTION COMPLETE!")
        print("📋 Next steps:")
        print("   1. Check the saved text file for all content")
        print("   2. Run reference extraction on the content")
        print("   3. Look for the 68 references you mentioned")
    else:
        print("\n❌ EXTRACTION FAILED")
        print("📋 Troubleshooting:")
        print("   1. Check if Mistral API key is set")
        print("   2. Verify OneNote file is not corrupted")
        print("   3. Check if OneNote processor can convert to images")

except ImportError as e:
    print(f"❌ Import error: {e}")
    print("📋 Make sure all required packages are installed")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()

print("\n" + "=" * 60)
print("🔚 Script completed")
