"""
Script to generate Cypher queries for visualizing the knowledge graph
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def get_knowledge_graph_stats(driver):
    """Get statistics about the knowledge graph."""
    logger.info("Getting knowledge graph statistics")
    
    try:
        async with driver.session() as session:
            # Count nodes by label
            node_query = """
            MATCH (n)
            RETURN labels(n) AS label, count(n) AS count
            ORDER BY count DESC
            """
            
            result = await session.run(node_query)
            
            node_counts = []
            async for record in result:
                node_counts.append({
                    "label": record["label"],
                    "count": record["count"]
                })
            
            logger.info("Node counts by label:")
            for node_count in node_counts:
                logger.info(f"  {node_count['label']}: {node_count['count']}")
            
            # Count relationships by type
            rel_query = """
            MATCH ()-[r]->()
            RETURN type(r) AS type, count(r) AS count
            ORDER BY count DESC
            """
            
            result = await session.run(rel_query)
            
            rel_counts = []
            async for record in result:
                rel_counts.append({
                    "type": record["type"],
                    "count": record["count"]
                })
            
            logger.info("Relationship counts by type:")
            for rel_count in rel_counts:
                logger.info(f"  {rel_count['type']}: {rel_count['count']}")
            
            # Get a sample of each node type
            for node_count in node_counts:
                label = node_count["label"][0]  # Take the first label
                sample_query = f"""
                MATCH (n:{label})
                RETURN n
                LIMIT 1
                """
                
                result = await session.run(sample_query)
                record = await result.single()
                
                if record:
                    node = record["n"]
                    logger.info(f"\nSample {label} node:")
                    for key, value in node.items():
                        if isinstance(value, str) and len(value) > 100:
                            logger.info(f"  {key}: {value[:100]}...")
                        else:
                            logger.info(f"  {key}: {value}")
            
            # Generate visualization queries
            logger.info("\nCypher queries for visualization:")
            
            logger.info("\n1. View all nodes and relationships:")
            logger.info("""
            MATCH p=()-[]-() 
            RETURN p 
            LIMIT 100
            """)
            
            logger.info("\n2. View Episodes and their Facts:")
            logger.info("""
            MATCH p=(e:Episode)-[:CONTAINS]->(f:Fact) 
            RETURN p 
            LIMIT 100
            """)
            
            logger.info("\n3. Find Facts containing specific text:")
            logger.info("""
            MATCH (f:Fact)
            WHERE f.body CONTAINS "your_search_term"
            RETURN f
            LIMIT 10
            """)
            
            logger.info("\n4. Find Episodes with specific names:")
            logger.info("""
            MATCH (e:Episode)
            WHERE e.name CONTAINS "your_search_term"
            RETURN e
            LIMIT 10
            """)
            
            logger.info("\n5. View the database schema:")
            logger.info("""
            CALL db.schema.visualization()
            """)
            
    except Exception as e:
        logger.error(f"Error getting knowledge graph statistics: {e}")

async def main():
    """Main function to visualize the knowledge graph."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        # Get knowledge graph statistics
        await get_knowledge_graph_stats(driver)
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
