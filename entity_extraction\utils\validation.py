"""
Validation utilities for entity extraction.
"""

import logging
from typing import List, Dict, Any, Optional

from entity_extraction.base import ENTITY_TYPES

# Set up logging
logger = logging.getLogger(__name__)


def validate_entity(entity: Dict[str, Any]) -> bool:
    """
    Validate an entity.

    Args:
        entity: Entity to validate

    Returns:
        True if the entity is valid, False otherwise
    """
    # Check required fields
    if 'name' not in entity:
        logger.warning("Entity missing required field 'name'")
        return False
    
    if 'type' not in entity:
        logger.warning(f"Entity '{entity['name']}' missing required field 'type'")
        return False
    
    # Check entity type
    if entity['type'] not in ENTITY_TYPES:
        logger.warning(f"Entity '{entity['name']}' has invalid type '{entity['type']}'")
        return False
    
    # Check name is not empty
    if not entity['name'].strip():
        logger.warning("Entity has empty name")
        return False
    
    return True


def validate_relationship(relationship: Dict[str, Any]) -> bool:
    """
    Validate a relationship.

    Args:
        relationship: Relationship to validate

    Returns:
        True if the relationship is valid, False otherwise
    """
    # Check required fields
    if 'source' not in relationship:
        logger.warning("Relationship missing required field 'source'")
        return False
    
    if 'target' not in relationship:
        logger.warning(f"Relationship from '{relationship['source']}' missing required field 'target'")
        return False
    
    if 'relationship' not in relationship:
        logger.warning(f"Relationship from '{relationship['source']}' to '{relationship['target']}' missing required field 'relationship'")
        return False
    
    # Check source and target are not empty
    if not relationship['source'].strip():
        logger.warning("Relationship has empty source")
        return False
    
    if not relationship['target'].strip():
        logger.warning("Relationship has empty target")
        return False
    
    # Check source and target are not the same
    if relationship['source'] == relationship['target']:
        logger.warning(f"Relationship has same source and target: '{relationship['source']}'")
        return False
    
    return True


def filter_valid_entities(entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filter out invalid entities.

    Args:
        entities: List of entities to filter

    Returns:
        List of valid entities
    """
    valid_entities = []
    
    for entity in entities:
        if validate_entity(entity):
            valid_entities.append(entity)
    
    if len(valid_entities) < len(entities):
        logger.info(f"Filtered out {len(entities) - len(valid_entities)} invalid entities")
    
    return valid_entities


def filter_valid_relationships(relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Filter out invalid relationships.

    Args:
        relationships: List of relationships to filter

    Returns:
        List of valid relationships
    """
    valid_relationships = []
    
    for relationship in relationships:
        if validate_relationship(relationship):
            valid_relationships.append(relationship)
    
    if len(valid_relationships) < len(relationships):
        logger.info(f"Filtered out {len(relationships) - len(valid_relationships)} invalid relationships")
    
    return valid_relationships
