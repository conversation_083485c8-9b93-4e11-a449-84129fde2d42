# Graphiti Knowledge Graph - Unified Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION (Standardized)
# =============================================================================

# FalkorDB - Primary graph database
FALKORDB_HOST=localhost
FALKORDB_PORT=6379
FALKORDB_PASSWORD=Triathlon16!
FALKORDB_GRAPH=graphiti

# Redis Vector Search - Embeddings storage
REDIS_VECTOR_SEARCH_HOST=localhost
REDIS_VECTOR_SEARCH_PORT=6380
REDIS_VECTOR_SEARCH_PASSWORD=Triathlon16!

# =============================================================================
# API KEYS (Standardized)
# =============================================================================

# OpenAI API (for embeddings and LLM fallback)
OPENAI_API_KEY=your_openai_api_key_here

# OpenRouter API (primary LLM provider)
OPENROUTER_API_KEY=your_openrouter_api_key_here

# Mistral API (for OCR and document processing)
MISTRAL_API_KEY=your_mistral_api_key_here

# Gemini API (optional alternative LLM)
GEMINI_API_KEY=your_gemini_api_key_here

# =============================================================================
# LLM CONFIGURATION (Standardized)
# =============================================================================

# Primary LLM for Q&A
QA_LLM_MODEL=meta-llama/llama-4-maverick
QA_LLM_TEMPERATURE=0.7
QA_LLM_TOP_P=0.9

# Entity extraction LLM
ENTITY_LLM_MODEL=qwen3-4b
ENTITY_LLM_PROVIDER=openrouter

# =============================================================================
# EMBEDDING CONFIGURATION (Standardized)
# =============================================================================

# Embedding provider and model
EMBEDDING_PROVIDER=ollama
EMBEDDING_MODEL=snowflake-arctic-embed2
EMBEDDING_DIMENSIONS=1024

# Ollama configuration
OLLAMA_HOST=localhost
OLLAMA_PORT=11434

# =============================================================================
# APPLICATION SETTINGS (Standardized)
# =============================================================================

# Server configuration
HOST=localhost
PORT=9753
DEBUG=false
LOG_LEVEL=INFO

# Document processing
CHUNK_SIZE=1200
CHUNK_OVERLAP=0

# =============================================================================
# MCP SERVER CONFIGURATION (For MCP Implementation)
# =============================================================================

# MCP specific settings
MCP_MODEL_NAME=meta-llama/llama-4-maverick
MCP_LLM_TEMPERATURE=0.0
MCP_TRANSPORT=sse
MCP_HOST=0.0.0.0
MCP_PORT=8000

# =============================================================================
# DOCKER CONFIGURATION (For Docker Deployments)
# =============================================================================

# Neo4j (if using MCP with Neo4j)
NEO4J_PASSWORD=Triathlon16!
NEO4J_USER=neo4j

# Redis Stack password
REDIS_PASSWORD=Triathlon16!

# =============================================================================
# PROCESSING CONFIGURATION (Advanced)
# =============================================================================

# Entity deduplication thresholds
ENTITY_SIMILARITY_THRESHOLD=0.85
TITLE_SIMILARITY_THRESHOLD=0.85
AUTHOR_SIMILARITY_THRESHOLD=0.75

# Reference extraction settings
EXTRACT_REFERENCES=true
EXTRACT_ENTITIES=true
EXTRACT_METADATA=true
GENERATE_EMBEDDINGS=true

# =============================================================================
# NOTES
# =============================================================================

# 1. Replace all "your_*_api_key_here" with actual API keys
# 2. Adjust ports if you have conflicts (unusual ports preferred)
# 3. For Docker deployments, use service names instead of localhost
# 4. MCP implementation is the recommended deployment method
# 5. Traditional UI is available as fallback option
