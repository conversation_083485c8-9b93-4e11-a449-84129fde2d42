"""
Test script to check if Mistral OCR is loaded and working
"""

import asyncio
import logging
import os
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def main():
    # Load environment variables
    load_dotenv()
    
    # Check if Mistral API key is set
    mistral_api_key = os.environ.get('MISTRAL_API_KEY')
    if not mistral_api_key:
        logger.error("MISTRAL_API_KEY is not set in the environment variables")
        return False
    else:
        logger.info("MISTRAL_API_KEY is set")
    
    # Check if USE_MISTRAL_OCR is enabled
    use_mistral_ocr = os.environ.get('USE_MISTRAL_OCR', 'true').lower() == 'true'
    if not use_mistral_ocr:
        logger.error("USE_MISTRAL_OCR is disabled in the environment variables")
        return False
    else:
        logger.info("USE_MISTRAL_OCR is enabled")
    
    # Try to import MistralOCRProcessor
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        logger.info("Successfully imported MistralOCRProcessor")
    except ImportError as e:
        logger.error(f"Failed to import MistralOCRProcessor: {e}")
        return False
    
    # Try to initialize MistralOCRProcessor
    try:
        processor = MistralOCRProcessor(api_key=mistral_api_key)
        logger.info("Successfully initialized MistralOCRProcessor")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize MistralOCRProcessor: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(main())
    if result:
        print("\nMistral OCR is loaded and working correctly!")
    else:
        print("\nMistral OCR is NOT working correctly. Check the logs above for details.")
