#!/usr/bin/env python3
"""
Test the integrated reference extraction system on the Hawrelak document.
Tests automatic detection and appropriate extractor selection.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

async def test_hawrelak_document():
    """Test the integrated reference extraction on the Hawrelak document."""
    
    # Check for Mistral API key
    mistral_api_key = os.getenv('MISTRAL_API_KEY')
    if not mistral_api_key:
        print("❌ MISTRAL_API_KEY environment variable not found")
        return
    
    # Find the Hawrelak document
    uploads_dir = Path("uploads")
    hawrelak_files = list(uploads_dir.glob("*Hawrelak*"))
    if not hawrelak_files:
        print("❌ Hawrelak document not found")
        return
    
    test_file = hawrelak_files[0]
    print(f"🧪 Testing integrated reference extraction on: {test_file.name}")
    
    try:
        # Import the enhanced document processor
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        
        # Initialize processor
        print("🔧 Initializing enhanced document processor...")
        processor = EnhancedDocumentProcessor()
        
        # Test the improved reference extraction method directly
        print("🚀 Starting integrated reference extraction...")
        result = await processor._extract_references_improved(test_file)
        
        # Display results
        print("\n" + "="*80)
        print("📊 INTEGRATED REFERENCE EXTRACTION RESULTS")
        print("="*80)
        
        if result.get('success', False):
            print(f"✅ Success: {result['success']}")
            print(f"📄 Document: {result['filename']}")
            print(f"🔍 Method: {result['extraction_method']}")
            print(f"📚 Total References Found: {result['total_reference_count']}")
            print(f"📝 Text Length: {result.get('extracted_text_length', 0):,} characters")
            
            if result.get('csv_path'):
                print(f"💾 CSV Saved: {result['csv_path']}")
            
            # Show breakdown if available
            references = result.get('references', [])
            if references:
                # Count by extraction method
                method_counts = {}
                for ref in references:
                    method = ref.get('extraction_method', 'unknown')
                    method_counts[method] = method_counts.get(method, 0) + 1
                
                print(f"\n📋 Extraction Method Breakdown:")
                for method, count in method_counts.items():
                    print(f"   {method}: {count} references")
                
                # Show first 10 references
                print(f"\n📋 First 10 References:")
                print("-" * 80)
                for i, ref in enumerate(references[:10], 1):
                    print(f"\n{i:2d}. Method: {ref.get('extraction_method', 'unknown')}")
                    ref_text = ref['text'][:150] + "..." if len(ref['text']) > 150 else ref['text']
                    print(f"    Text: {ref_text}")
                    if ref.get('metadata'):
                        metadata = ref['metadata']
                        if metadata.get('year'):
                            print(f"    Year: {metadata['year']}")
                        if metadata.get('journal'):
                            print(f"    Journal: {metadata['journal']}")
            
            print(f"\n🎯 TOTAL REFERENCES FOUND: {result['total_reference_count']}")
            
            # Compare with expected count
            expected_count = 100  # You mentioned ~100 references
            found_count = result['total_reference_count']
            
            print(f"\n📊 COMPARISON WITH EXPECTED COUNT:")
            print(f"   Expected: ~{expected_count} references")
            print(f"   Found: {found_count} references")
            
            if found_count >= expected_count * 0.9:
                print("✅ EXCELLENT: Found 90%+ of expected references!")
            elif found_count >= expected_count * 0.7:
                print("🟡 GOOD: Found 70%+ of expected references")
            elif found_count >= expected_count * 0.5:
                print("🟠 FAIR: Found 50%+ of expected references")
            else:
                print("🔴 NEEDS IMPROVEMENT: Found fewer references than expected")
            
        else:
            print(f"❌ Extraction failed: {result.get('error', 'Unknown error')}")
        
        print("="*80)
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

async def test_document_type_detection():
    """Test the document type detection on various documents."""
    
    print("\n🔍 Testing document type detection...")
    
    try:
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        
        processor = EnhancedDocumentProcessor()
        uploads_dir = Path("uploads")
        
        # Test on various documents
        test_files = [
            list(uploads_dir.glob("*Hawrelak*")),
            list(uploads_dir.glob("*pain releif*")),
            list(uploads_dir.glob("*presentation*")),
            list(uploads_dir.glob("*slides*")),
        ]
        
        print("\n📋 Document Type Detection Results:")
        print("-" * 60)
        
        for file_group in test_files:
            for file_list in file_group:
                if file_list:
                    test_file = file_list if isinstance(file_list, Path) else file_list[0]
                    is_presentation = processor._is_presentation_document(test_file)
                    doc_type = "🎯 PRESENTATION" if is_presentation else "📄 DOCUMENT"
                    print(f"{doc_type}: {test_file.name}")
        
    except Exception as e:
        print(f"❌ Error in document type detection test: {e}")

if __name__ == "__main__":
    print("🧪 Integrated Reference Extraction Test")
    print("="*50)
    
    # Test document type detection
    asyncio.run(test_document_type_detection())
    
    # Test Hawrelak document
    print("\n1️⃣ Testing Hawrelak document...")
    asyncio.run(test_hawrelak_document())
    
    print("\n✅ Testing complete!")
