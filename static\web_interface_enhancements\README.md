# Web Interface Enhancements

This directory contains files for enhancing the Graphiti web interface with advanced visualization and search capabilities.

## Files

### 1. enhanced_styles.css

CSS styles for the enhanced knowledge graph visualization.

**Features:**
- Styles for taxonomy visualization
- Styles for relationship visualization
- Styles for entity attributes visualization
- Styles for advanced search interface
- Responsive design elements

### 2. enhanced_visualizations.js

JavaScript for interactive knowledge graph visualization.

**Features:**
- Taxonomy tree visualization
- Relationship visualization with confidence meters
- Entity attributes display
- Advanced search functionality
- Tab-based interface for different visualizations

### 3. taxonomy_visualization.html

HTML template for the hierarchical taxonomy visualization.

**Features:**
- Tab-based interface for different visualizations
- Taxonomy tree display
- Relationship visualization
- Entity attributes display

### 4. advanced_search.html

HTML template for the advanced search interface.

**Features:**
- Entity type filtering
- Relationship type filtering
- Confidence score filtering
- Keyword search
- Combined filtering options
- Search results display

### 5. api_endpoints.py

API endpoints for the enhanced visualizations.

**Features:**
- Taxonomy hierarchy endpoint
- Relationships endpoint
- Entity details endpoint
- Advanced search endpoint

### 6. integration_instructions.md

Instructions for integrating the enhanced components into the main web interface.

**Features:**
- Step-by-step integration guide
- CSS and JavaScript inclusion instructions
- Template integration instructions
- API endpoint integration instructions
- Route handler setup

## Integration

To integrate these enhancements into the main web interface:

1. Add the CSS and JavaScript files to your base template
2. Include the HTML templates in your pages
3. Add the API endpoints to your web_interface_improved.py file
4. Update your navigation menu with links to the new features
5. Create route handlers for the new pages

See `integration_instructions.md` for detailed instructions.
