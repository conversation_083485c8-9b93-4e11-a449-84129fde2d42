# Entity Extraction Package

## Overview

The Entity Extraction package is a comprehensive solution for extracting entities and relationships from text documents in the Graphiti Knowledge Graph system. It uses advanced Large Language Models (LLMs) to identify domain-specific entities and their relationships, creating a rich knowledge graph from unstructured text.

## Features

- **Multi-Provider Support**: Uses OpenRouter, OpenAI, or local LLM models for entity extraction
- **Domain-Specific Entity Types**: Extracts 20+ specialized entity types for scientific health literature
- **Relationship Extraction**: Identifies complex relationships between entities with attributes
- **Confidence Scoring**: Assigns confidence levels to extracted relationships
- **Batch Processing**: Processes documents in configurable batches
- **Database Integration**: Seamlessly integrates with graph databases

## Architecture

The package follows a modular architecture with the following components:

### Core Components

- **Base Module**: Defines the base `EntityExtractor` class and entity types
- **Extractors**: Implements different extraction strategies (LLM-based, rule-based)
- **Processors**: Handles entity and relationship processing and database operations
- **Models**: Defines data models for entities and relationships
- **Utilities**: Provides text processing and validation utilities

### Module Structure

```
entity_extraction/
├── __init__.py           # Package initialization and exports
├── base.py               # Base classes and constants
├── main.py               # Main functionality and entry points
├── extractors/           # Entity extraction implementations
│   ├── __init__.py
│   ├── llm_extractor.py  # LLM-based entity extraction
│   └── rule_based_extractor.py  # Rule-based extraction (future)
├── processors/           # Entity and relationship processors
│   ├── __init__.py
│   ├── entity_processor.py     # Entity node creation and management
│   ├── relationship_processor.py  # Relationship extraction and creation
│   └── text_processor.py       # Text processing for entities
├── models/               # Data models
│   ├── __init__.py
│   ├── entity.py         # Entity data model
│   └── relationship.py   # Relationship data model
└── utils/                # Utility functions
    ├── __init__.py
    ├── text_utils.py     # Text processing utilities
    └── validation.py     # Entity and relationship validation
```

## Entity Types

The package extracts the following entity types:

- **Person**: Individuals mentioned in the text
- **Organization**: Companies, institutions, and other organizations
- **Location**: Geographic locations and places
- **Treatment**: Medical treatments and interventions
- **Herb**: Medicinal herbs and botanical remedies
- **Medication**: Pharmaceutical drugs and medications
- **Disease**: Medical conditions and diseases
- **Symptom**: Signs and symptoms of medical conditions
- **Nutrient**: Vitamins, minerals, and other nutrients
- **Concept**: Abstract concepts and ideas
- **Process**: Biological and physiological processes
- **Research**: Research studies and publications
- **Study**: Clinical trials and scientific studies
- **Bioactive_Compound**: Active compounds in herbs and foods
- **Botanical_Source**: Plant sources of bioactive compounds
- **Biochemical_Pathway**: Metabolic and signaling pathways
- **Health_Condition**: General health states and conditions
- **Genetic_Element**: Genes, alleles, and genetic factors
- **Therapeutic_Intervention**: Therapeutic approaches and interventions
- **Food**: Food items and dietary components

## Relationship Types

The package extracts various relationship types, including:

1. **Mechanistic Relationships**:
   - Compound → Target (e.g., "inhibits", "activates", "modulates")
   - Gene → Protein → Function
   - Pathway → Disease (e.g., "contributes to", "protects against")

2. **Therapeutic Relationships**:
   - Intervention → Condition (e.g., "treats", "prevents", "manages")
   - Compound → Biomarker (e.g., "reduces", "increases", "normalizes")
   - Botanical → Therapeutic Application

3. **Compositional Relationships**:
   - Botanical → Compound (e.g., "contains", "yields", "standardized for")
   - Food → Nutrient → Bioavailability

4. **Physiological Relationships**:
   - Nutrient → Physiological Process
   - Lifestyle Factor → Health Outcome
   - Genetic Variant → Disease Risk

5. **Evidence Relationships**:
   - Study → Finding → Evidence Level
   - Intervention → Outcome → Statistical Significance

6. **Causal Relationships**:
   - Cause → Effect (e.g., "causes", "leads to", "results in")
   - Risk Factor → Disease (e.g., "increases risk of", "predisposes to")

7. **Comparative Relationships**:
   - Intervention A vs Intervention B (e.g., "more effective than", "similar efficacy to")
   - Dose-Response Relationships (e.g., "higher doses produce greater effects")

8. **Taxonomic Relationships**:
   - IS_A (hierarchical classification)
   - PART_OF (compositional hierarchy)

## Usage

### Basic Usage

```python
import asyncio
from entity_extraction import extract_entities_from_text

async def main():
    text = "Vitamin C is an essential nutrient that helps boost the immune system."
    entities = await extract_entities_from_text(api_key=None, text=text)
    
    print(f"Extracted {len(entities)} entities:")
    for entity in entities:
        print(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")

if __name__ == "__main__":
    asyncio.run(main())
```

### Processing Facts from Database

```python
import asyncio
from entity_extraction import extract_entities_from_facts

async def process_facts(driver, api_key=None):
    # Process facts in batches of 5
    total_processed, total_entities = await extract_entities_from_facts(
        driver=driver,
        api_key=api_key,
        batch_size=5,
        max_batches=10  # Process up to 10 batches
    )
    
    print(f"Processed {total_processed} facts, extracted {total_entities} entities")

# Run with your database driver
# asyncio.run(process_facts(driver))
```

### Extracting Relationships

```python
import asyncio
from entity_extraction import extract_relationships_between_entities

async def extract_relationships(driver, api_key=None):
    # Extract relationships between entities in the same facts
    total_processed, total_relationships = await extract_relationships_between_entities(
        driver=driver,
        api_key=api_key,
        batch_size=5,
        max_batches=10
    )
    
    print(f"Processed {total_processed} facts, extracted {total_relationships} relationships")

# Run with your database driver
# asyncio.run(extract_relationships(driver))
```

## Configuration

The package uses the following environment variables:

- `ENTITY_EXTRACTION_PROVIDER`: LLM provider to use ('openrouter', 'openai', 'local')
- `ENTITY_EXTRACTION_MODEL`: Model to use for entity extraction
- `OPEN_ROUTER_API_KEY`: API key for OpenRouter
- `OPENAI_API_KEY`: API key for OpenAI
- `OPENAI_ENTITY_MODEL`: OpenAI model to use for entity extraction
- `USE_LOCAL_LLM`: Whether to use local LLM for relationship extraction
- `OLLAMA_BASE_URL`: Base URL for Ollama API

## Dependencies

- `openai`: OpenAI API client
- `dotenv`: Environment variable management
- `pydantic`: Data validation and settings management
- `asyncio`: Asynchronous I/O

## License

This package is part of the Graphiti Knowledge Graph system and is subject to the same license terms.
