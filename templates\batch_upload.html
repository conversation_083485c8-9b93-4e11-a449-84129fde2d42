{% extends "layouts/base.html" %}

{% block title %}Batch Upload - Graphiti Knowledge Graph{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">Home</a></li>
<li class="breadcrumb-item active" aria-current="page">Batch Upload</li>
{% endblock %}

{% block additional_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
<style>
    .upload-area {
        border: 2px dashed #0d6efd;
        border-radius: 5px;
        padding: 30px;
        text-align: center;
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background-color: #f8f9fa;
    }

    .upload-area.drag-over {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.1);
    }

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
        border-radius: 5px;
        margin-bottom: 8px;
    }

    .file-name {
        flex-grow: 1;
        margin-right: 10px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .file-size {
        margin-right: 10px;
        color: #6c757d;
    }

    .settings-section {
        margin-bottom: 20px;
    }

    .settings-section h5 {
        margin-bottom: 15px;
    }

    /* Progress styles */
    .progress {
        height: 10px;
        margin-bottom: 10px;
    }

    .status-text {
        font-size: 0.85rem;
    }

    #file-progress-list .list-group-item {
        padding: 12px;
        transition: background-color 0.3s ease;
    }

    #file-progress-list .list-group-item:hover {
        background-color: #f8f9fa;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <h2>Batch Document Upload</h2>
    <p class="lead">Upload and process multiple documents at once</p>

            <div class="row">
                <div class="col-md-8">
                    <div id="alert-container"></div>

                    <form id="upload-form" class="mb-4">
                        <div class="upload-area">
                            <p><i class="fas fa-cloud-upload-alt fa-3x mb-3"></i></p>
                            <p>Drag and drop files or folders here</p>
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="button" class="btn btn-primary" id="select-files-button">
                                    <i class="fas fa-file me-1"></i> Select Files
                                </button>
                                <button type="button" class="btn btn-primary" id="select-folder-button">
                                    <i class="fas fa-folder me-1"></i> Select Folder
                                </button>
                            </div>
                            <p class="text-muted small mt-2">Supported formats: PDF, TXT, DOC, DOCX, and more</p>
                            <input type="file" id="file-input" class="form-control d-none" multiple accept=".pdf,.txt,.md,.rtf,.doc,.docx,.odt,.html,.htm,.xml,.csv,.xls,.xlsx,.ppt,.pptx,.epub">
                            <input type="file" id="folder-input" class="form-control d-none" webkitdirectory directory multiple>
                        </div>

                        <div id="file-list" class="mb-4">
                            <!-- Files will be listed here -->
                        </div>

                        <div id="progress-container" class="mb-4 d-none">
                            <p id="progress-text">Uploading files...</p>
                            <div class="progress mb-3">
                                <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>

                            <div class="card">
                                <div class="card-header">
                                    <h6>File Processing Status</h6>
                                </div>
                                <div class="card-body p-0">
                                    <div id="file-progress-list" class="list-group list-group-flush">
                                        <!-- Individual file progress will be displayed here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <button type="submit" id="upload-button" class="btn btn-primary" disabled>
                            <i class="fas fa-upload me-2"></i> Upload Files
                        </button>
                    </form>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">Processing Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="settings-section">
                                <h5>Chunking Settings</h5>
                                <div class="mb-3">
                                    <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                                    <input type="number" id="chunk-size" class="form-control" value="1200" min="100" max="10000">
                                    <div class="form-text">Size of text chunks (100-10000)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="overlap" class="form-label">Overlap (characters)</label>
                                    <input type="number" id="overlap" class="form-control" value="0" min="0" max="500">
                                    <div class="form-text">Overlap between chunks (0-500)</div>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h5>Extraction Settings</h5>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" id="extract-entities" class="form-check-input" checked>
                                    <label for="extract-entities" class="form-check-label">Extract Entities</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" id="extract-references" class="form-check-input" checked>
                                    <label for="extract-references" class="form-check-label">Extract References</label>
                                </div>
                                <div class="mb-3 form-check">
                                    <input type="checkbox" id="extract-metadata" class="form-check-input" checked>
                                    <label for="extract-metadata" class="form-check-label">Extract Metadata</label>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h5>Parallel Processing</h5>
                                <div class="mb-3">
                                    <label for="max-parallel-processes" class="form-label">Max Parallel Processes</label>
                                    <input type="number" id="max-parallel-processes" class="form-control" value="4" min="1" max="10">
                                    <div class="form-text">Maximum number of documents to process in parallel (1-10)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script>
    console.log("Batch upload page loaded");
    // Check if the batch_upload.js file is loaded
    window.addEventListener('load', function() {
        console.log("Window loaded");
    });
</script>
<script src="/static/js/batch_upload.js?v=2"></script>
{% endblock %}
