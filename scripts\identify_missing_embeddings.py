"""
Script to identify facts without embeddings
"""

import os
import asyncio
import logging
from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def identify_facts_without_embeddings(driver):
    """Identify facts without embeddings."""
    logger.info("Identifying facts without embeddings")

    try:
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)
                WHERE f.embedding IS NULL
                RETURN f.uuid AS uuid, f.body AS body, size(f.body) AS body_length
                """
            )

            facts = []
            async for record in result:
                facts.append({
                    "uuid": record["uuid"],
                    "body": record["body"],
                    "body_length": record["body_length"]
                })

            logger.info(f"Found {len(facts)} facts without embeddings")

            for i, fact in enumerate(facts):
                logger.info(f"\nFact {i+1}:")
                logger.info(f"UUID: {fact['uuid']}")
                logger.info(f"Body length: {fact['body_length']}")
                logger.info(f"Body preview: {fact['body'][:100]}...")

            return facts
    except Exception as e:
        logger.error(f"Error identifying facts without embeddings: {e}")
        return []

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()

    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )

        # Identify facts without embeddings
        await identify_facts_without_embeddings(driver)

    except Exception as e:
        logger.error(f"Error in main function: {e}")
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
