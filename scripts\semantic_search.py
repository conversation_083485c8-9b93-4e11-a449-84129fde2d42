"""
Script for semantic search using vector embeddings in the knowledge graph
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timezone

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase
import openai

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def semantic_search(driver, query, limit=10, min_score=0.7):
    """Perform semantic search using vector embeddings."""
    logger.info(f"Performing semantic search for query: '{query}'")

    try:
        # Generate embedding for the query
        client = openai.OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))
        response = client.embeddings.create(
            input=query,
            model="text-embedding-3-small"
        )
        query_embedding = response.data[0].embedding

        # Search using vector similarity
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)
                WHERE f.embedding IS NOT NULL
                WITH f, vector.similarity(f.embedding, $query_embedding) AS score
                WHERE score >= $min_score
                WITH f, score ORDER BY score DESC
                LIMIT $limit
                MATCH (e:Episode)-[:CONTAINS]->(f)
                RETURN f.uuid AS uuid,
                       f.body AS body,
                       substring(f.body, 0, 200) + '...' AS preview,
                       score,
                       e.name AS document,
                       e.source_description AS source
                """,
                {
                    "query_embedding": query_embedding,
                    "limit": limit,
                    "min_score": min_score
                }
            )

            results = []
            async for record in result:
                results.append({
                    "uuid": record["uuid"],
                    "body": record["body"],
                    "preview": record["preview"],
                    "score": record["score"],
                    "document": record["document"],
                    "source": record["source"]
                })

            logger.info(f"Found {len(results)} results for query: '{query}'")

            # Display the results
            for i, result in enumerate(results):
                logger.info(f"\nResult {i+1} (Score: {result['score']:.4f}):")
                logger.info(f"Document: {result['document']}")
                logger.info(f"Source: {result['source']}")
                logger.info(f"Preview: {result['preview']}")

            return results
    except Exception as e:
        logger.error(f"Error performing semantic search: {e}")
        return []

async def hybrid_search(driver, query, limit=10, min_score=0.7):
    """Perform hybrid search using both vector similarity and text matching."""
    logger.info(f"Performing hybrid search for query: '{query}'")

    try:
        # Generate embedding for the query
        client = openai.OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))
        response = client.embeddings.create(
            input=query,
            model="text-embedding-3-small"
        )
        query_embedding = response.data[0].embedding

        # Extract keywords from the query (simple approach)
        keywords = [word.lower() for word in query.split() if len(word) > 3]

        # Search using both vector similarity and text matching
        async with driver.session() as session:
            result = await session.run(
                """
                MATCH (f:Fact)
                WHERE f.embedding IS NOT NULL

                // Calculate vector similarity score
                WITH f, vector.similarity(f.embedding, $query_embedding) AS semantic_score

                // Calculate keyword match score
                WITH f, semantic_score,
                     CASE
                         WHEN any(keyword IN $keywords WHERE toLower(f.body) CONTAINS keyword) THEN 0.2
                         ELSE 0
                     END AS keyword_score

                // Combine scores
                WITH f, semantic_score, keyword_score,
                     semantic_score + keyword_score AS combined_score

                WHERE combined_score >= $min_score
                WITH f, semantic_score, keyword_score, combined_score ORDER BY combined_score DESC
                LIMIT $limit

                MATCH (e:Episode)-[:CONTAINS]->(f)
                RETURN f.uuid AS uuid,
                       f.body AS body,
                       substring(f.body, 0, 200) + '...' AS preview,
                       semantic_score,
                       keyword_score,
                       combined_score AS score,
                       e.name AS document,
                       e.source_description AS source
                """,
                {
                    "query_embedding": query_embedding,
                    "keywords": keywords,
                    "limit": limit,
                    "min_score": min_score
                }
            )

            results = []
            async for record in result:
                results.append({
                    "uuid": record["uuid"],
                    "body": record["body"],
                    "preview": record["preview"],
                    "semantic_score": record["semantic_score"],
                    "keyword_score": record["keyword_score"],
                    "score": record["score"],
                    "document": record["document"],
                    "source": record["source"]
                })

            logger.info(f"Found {len(results)} results for hybrid query: '{query}'")

            # Display the results
            for i, result in enumerate(results):
                logger.info(f"\nResult {i+1} (Score: {result['score']:.4f}, Semantic: {result['semantic_score']:.4f}, Keyword: {result['keyword_score']:.4f}):")
                logger.info(f"Document: {result['document']}")
                logger.info(f"Source: {result['source']}")
                logger.info(f"Preview: {result['preview']}")

            return results
    except Exception as e:
        logger.error(f"Error performing hybrid search: {e}")
        return []

async def answer_question(driver, question, limit=5, min_score=0.7):
    """Answer a question using the knowledge graph and an LLM."""
    logger.info(f"Answering question: '{question}'")

    try:
        # Search the knowledge graph for relevant facts
        relevant_facts = await semantic_search(driver, question, limit=limit, min_score=min_score)

        if not relevant_facts:
            return {
                "question": question,
                "answer": "I don't have enough information in my knowledge graph to answer this question.",
                "sources": []
            }

        # Format the context from relevant facts
        context = "\n\n".join([
            f"FACT {i+1} (from {fact['document']}):\n{fact['body']}"
            for i, fact in enumerate(relevant_facts)
        ])

        # Generate an answer using the LLM
        client = openai.OpenAI(api_key=os.environ.get('OPENAI_API_KEY'))
        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {"role": "system", "content": "You are a helpful assistant that answers questions based on the provided context. Only use information from the context to answer the question. If the context doesn't contain relevant information, say 'I don't have enough information to answer this question.'"},
                {"role": "user", "content": f"Context:\n{context}\n\nQuestion: {question}"}
            ]
        )

        answer = response.choices[0].message.content

        # Format sources
        sources = [
            {
                "document": fact["document"],
                "preview": fact["preview"],
                "score": fact["score"]
            }
            for fact in relevant_facts
        ]

        result = {
            "question": question,
            "answer": answer,
            "sources": sources
        }

        logger.info(f"Generated answer: {answer[:100]}...")
        return result
    except Exception as e:
        logger.error(f"Error answering question: {e}")
        return {
            "question": question,
            "answer": f"An error occurred while answering the question: {str(e)}",
            "sources": []
        }

async def main():
    """Main function for semantic search."""
    # Load environment variables
    load_dotenv()

    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return

    # Check command line arguments
    if len(sys.argv) < 2:
        logger.error("Please specify a command: search, hybrid, or answer")
        logger.info("Usage:")
        logger.info("  python semantic_search.py search <query> [limit] [min_score]")
        logger.info("  python semantic_search.py hybrid <query> [limit] [min_score]")
        logger.info("  python semantic_search.py answer <question> [limit] [min_score]")
        return

    command = sys.argv[1].lower()

    if len(sys.argv) < 3:
        logger.error("Please specify a query or question")
        return

    query = " ".join(sys.argv[2:])
    limit = 10
    min_score = 0.7

    # Parse optional parameters
    args = sys.argv[2:]
    for i, arg in enumerate(args):
        if arg.startswith("--limit="):
            try:
                limit = int(arg.split("=")[1])
                args.pop(i)
            except (ValueError, IndexError):
                pass
        elif arg.startswith("--min-score="):
            try:
                min_score = float(arg.split("=")[1])
                args.pop(i)
            except (ValueError, IndexError):
                pass

    # Reconstruct query without the parameters
    query = " ".join(args)

    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri,
            auth=(neo4j_user, neo4j_password)
        )

        if command == "search":
            await semantic_search(driver, query, limit, min_score)
        elif command == "hybrid":
            await hybrid_search(driver, query, limit, min_score)
        elif command == "answer":
            result = await answer_question(driver, query, limit, min_score)
            print("\nQuestion:", result["question"])
            print("\nAnswer:", result["answer"])
            print("\nSources:")
            for i, source in enumerate(result["sources"]):
                print(f"{i+1}. {source['document']} (Score: {source['score']:.4f})")
                print(f"   {source['preview']}")
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: search, hybrid, answer")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
