"""
<PERSON><PERSON><PERSON> to perform vector similarity search on the FalkorDB knowledge graph
"""

import os
import sys
import asyncio
import logging
import json
import math
import argparse
from typing import List, Dict, Any

from dotenv import load_dotenv
from openai import OpenAI

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.falkordb_adapter import GraphitiFalkorDBAdapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def generate_query_embedding(api_key: str, query: str, model: str = "text-embedding-3-small") -> List[float]:
    """
    Generate embedding for a query using OpenAI.

    Args:
        api_key: OpenAI API key
        query: Query text
        model: Embedding model to use

    Returns:
        Embedding vector
    """
    logger.info(f"Generating embedding for query: '{query}' using model {model}")

    try:
        client = OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=query,
            model=model
        )

        embedding = response.data[0].embedding
        logger.info(f"Generated embedding with {len(embedding)} dimensions")
        return embedding
    except Exception as e:
        logger.error(f"Error generating query embedding: {e}")
        raise

async def vector_similarity_search(adapter: GraphitiFalkorDBAdapter, query_embedding: List[float], limit: int = 5, min_score: float = 0.7) -> List[Dict[str, Any]]:
    """
    Perform vector similarity search using the query embedding.

    Args:
        adapter: FalkorDB adapter
        query_embedding: Query embedding vector
        limit: Maximum number of results to return
        min_score: Minimum similarity score

    Returns:
        List of search results
    """
    logger.info(f"Performing vector similarity search (limit: {limit}, min_score: {min_score})")

    try:
        # FalkorDB doesn't support vector similarity search yet, so we'll implement a manual approach
        # First, get all facts with embeddings
        facts_query = """
        MATCH (f:Fact)
        WHERE f.embedding IS NOT NULL
        MATCH (e:Episode)-[:CONTAINS]->(f)
        RETURN f.uuid AS uuid,
               f.body AS body,
               f.embedding AS embedding,
               e.title AS document,
               e.uuid AS document_id
        """

        result = adapter.execute_cypher(facts_query)

        # Calculate similarity scores manually
        results = []
        if result and len(result) > 1:
            for row in result[1]:
                try:
                    fact_uuid = row[0]
                    fact_body = row[1]
                    fact_embedding_json = row[2]
                    document = row[3]
                    document_id = row[4]

                    # Parse the embedding JSON
                    fact_embedding = json.loads(fact_embedding_json)

                    # Calculate cosine similarity
                    score = cosine_similarity(query_embedding, fact_embedding)

                    # Only include results above the minimum score
                    if score >= min_score:
                        results.append({
                            "uuid": fact_uuid,
                            "body": fact_body,
                            "score": score,
                            "document": document,
                            "document_id": document_id
                        })
                except Exception as e:
                    logger.warning(f"Error processing fact {row[0]}: {e}")
                    continue

        # Sort results by score in descending order
        results.sort(key=lambda x: x["score"], reverse=True)

        # Limit the number of results
        results = results[:limit]

        logger.info(f"Found {len(results)} results")
        return results
    except Exception as e:
        logger.error(f"Error performing vector similarity search: {e}")
        return []

def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    Calculate cosine similarity between two vectors.

    Args:
        vec1: First vector
        vec2: Second vector

    Returns:
        Cosine similarity score
    """
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    magnitude1 = math.sqrt(sum(a * a for a in vec1))
    magnitude2 = math.sqrt(sum(b * b for b in vec2))

    if magnitude1 * magnitude2 == 0:
        return 0

    return dot_product / (magnitude1 * magnitude2)

async def get_related_entities(adapter: GraphitiFalkorDBAdapter, fact_uuids: List[str]) -> List[Dict[str, Any]]:
    """
    Get entities related to the given facts.

    Args:
        adapter: FalkorDB adapter
        fact_uuids: List of fact UUIDs

    Returns:
        List of entities
    """
    logger.info(f"Getting entities related to {len(fact_uuids)} facts")

    try:
        # Convert fact UUIDs to a string for the Cypher query
        fact_uuids_str = "', '".join(fact_uuids)

        query = f"""
        MATCH (f:Fact)-[:MENTIONS]->(e:Entity)
        WHERE f.uuid IN ['{fact_uuids_str}']
        RETURN e.name AS name, e.type AS type, e.description AS description, count(f) AS mentions
        ORDER BY mentions DESC
        """

        result = adapter.execute_cypher(query)

        entities = []
        if result and len(result) > 1:
            for row in result[1]:
                entities.append({
                    "name": row[0],
                    "type": row[1],
                    "description": row[2],
                    "mentions": row[3]
                })

        logger.info(f"Found {len(entities)} related entities")
        return entities
    except Exception as e:
        logger.error(f"Error getting related entities: {e}")
        return []

async def hybrid_search(adapter: GraphitiFalkorDBAdapter, api_key: str, query: str, limit: int = 5, min_score: float = 0.7) -> Dict[str, Any]:
    """
    Perform hybrid search using vector similarity and graph relationships.

    Args:
        adapter: FalkorDB adapter
        api_key: OpenAI API key
        query: Search query
        limit: Maximum number of results to return
        min_score: Minimum similarity score

    Returns:
        Search results with facts and related entities
    """
    logger.info(f"Performing hybrid search for query: '{query}'")

    try:
        # Generate embedding for the query
        query_embedding = await generate_query_embedding(api_key, query)

        # Perform vector similarity search
        vector_results = await vector_similarity_search(adapter, query_embedding, limit, min_score)

        if not vector_results:
            logger.warning("No vector search results found")
            return {"facts": [], "entities": []}

        # Get fact UUIDs from the results
        fact_uuids = [result["uuid"] for result in vector_results]

        # Get related entities
        entities = await get_related_entities(adapter, fact_uuids)

        # Return combined results
        return {
            "facts": vector_results,
            "entities": entities
        }
    except Exception as e:
        logger.error(f"Error performing hybrid search: {e}")
        return {"facts": [], "entities": []}

async def display_search_results(results: Dict[str, Any]):
    """
    Display search results in a readable format.

    Args:
        results: Search results
    """
    facts = results.get("facts", [])
    entities = results.get("entities", [])

    print("\n===== SEARCH RESULTS =====\n")

    if facts:
        print(f"Found {len(facts)} relevant facts:\n")
        for i, fact in enumerate(facts):
            print(f"Result {i+1} (Score: {fact['score']:.4f}):")
            print(f"Document: {fact['document']}")
            print(f"Content: {fact['body'][:200]}...")
            print()
    else:
        print("No relevant facts found.")

    if entities:
        print(f"\nFound {len(entities)} related entities:\n")

        # Group entities by type
        entities_by_type = {}
        for entity in entities:
            entity_type = entity["type"]
            if entity_type not in entities_by_type:
                entities_by_type[entity_type] = []
            entities_by_type[entity_type].append(entity)

        # Display entities by type
        for entity_type, type_entities in entities_by_type.items():
            print(f"{entity_type} ({len(type_entities)}):")
            for entity in type_entities:
                print(f"  - {entity['name']} ({entity['mentions']} mentions)")
                if entity.get("description"):
                    print(f"    {entity['description']}")
            print()
    else:
        print("\nNo related entities found.")

async def main():
    """Main function."""
    # Load environment variables
    load_dotenv()

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Perform vector similarity search on the FalkorDB knowledge graph.')
    parser.add_argument('query', help='Search query')
    parser.add_argument('--limit', type=int, default=5, help='Maximum number of results to return')
    parser.add_argument('--min-score', type=float, default=0.7, help='Minimum similarity score')

    args = parser.parse_args()

    try:
        # Connect to FalkorDB
        adapter = GraphitiFalkorDBAdapter()

        # Perform hybrid search
        results = await hybrid_search(adapter, openai_api_key, args.query, args.limit, args.min_score)

        # Display results
        await display_search_results(results)

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close FalkorDB connection if needed
        if 'adapter' in locals():
            adapter.close()

if __name__ == "__main__":
    asyncio.run(main())
