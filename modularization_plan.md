# Modularization Plan for Long Files

## 1. entity_extraction.py (39,240 bytes)

Create a new package structure:
```
entity_extraction/
├── __init__.py
├── base.py
├── extractors/
│   ├── __init__.py
│   ├── llm_extractor.py
│   ├── rule_based_extractor.py
│   └── hybrid_extractor.py
├── processors/
│   ├── __init__.py
│   ├── text_processor.py
│   └── entity_processor.py
├── models/
│   ├── __init__.py
│   ├── entity.py
│   └── relationship.py
└── utils/
    ├── __init__.py
    ├── text_utils.py
    └── validation.py
```

## 2. services/document_processing_service.py (32,553 bytes)

Split into specialized service modules:
```
services/
├── document_processing/
│   ├── __init__.py
│   ├── base_processor.py
│   ├── pdf_processor.py
│   ├── text_processor.py
│   ├── docx_processor.py
│   ├── html_processor.py
│   └── metadata_processor.py
└── document_service.py (main interface)
```

## 3. graphiti_core/graphiti.py (30,507 bytes)

Break into smaller modules:
```
graphiti_core/
├── __init__.py
├── core.py (main interface)
├── document/
│   ├── __init__.py
│   ├── document_processor.py
│   └── document_manager.py
├── entity/
│   ├── __init__.py
│   ├── entity_processor.py
│   └── entity_manager.py
├── knowledge_graph/
│   ├── __init__.py
│   ├── graph_builder.py
│   └── graph_manager.py
└── search/
    ├── __init__.py
    ├── search_engine.py
    └── query_builder.py
```

## 4. reference_extraction.py (24,338 bytes)

Split into specialized modules:
```
reference_extraction/
├── __init__.py
├── extractors/
│   ├── __init__.py
│   ├── citation_extractor.py
│   ├── bibliography_extractor.py
│   └── doi_extractor.py
├── processors/
│   ├── __init__.py
│   ├── text_processor.py
│   └── reference_processor.py
├── models/
│   ├── __init__.py
│   └── reference.py
└── utils/
    ├── __init__.py
    ├── formatting.py
    └── validation.py
```

## 5. workers/worker_manager.py (23,059 bytes)

Break into smaller modules:
```
workers/
├── __init__.py
├── base.py
├── manager.py (simplified manager)
├── queue/
│   ├── __init__.py
│   ├── task_queue.py
│   └── priority_queue.py
├── tasks/
│   ├── __init__.py
│   ├── task_base.py
│   ├── document_tasks.py
│   ├── entity_tasks.py
│   └── reference_tasks.py
└── monitoring/
    ├── __init__.py
    ├── status_tracker.py
    └── performance_monitor.py
```

## Implementation Strategy

1. For each file:
   - Create the directory structure
   - Extract common utilities and base classes first
   - Extract specialized functionality into appropriate modules
   - Update imports in all affected files
   - Create proper __init__.py files to maintain the public API

2. Testing:
   - After each file is modularized, run tests to ensure functionality is preserved
   - Update any imports in test files

3. Documentation:
   - Update docstrings in all new files
   - Update README.md with the new project structure
