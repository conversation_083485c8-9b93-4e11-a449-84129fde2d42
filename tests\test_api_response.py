"""
Test script to check the API response format
"""

import requests
import json

def test_api_response():
    """Test the API response format"""
    # Make a request to the API
    response = requests.get("http://localhost:9753/api/answer?q=What%20is%20ThymoDream?")

    # Check if the response is successful
    if response.status_code == 200:
        # Parse the response
        data = response.json()

        # Print the response
        print("API Response:")
        print(json.dumps(data, indent=2))

        # Check if the response has the expected fields
        if "answer" in data:
            print("\nAnswer field found!")
            print(f"Answer: {data['answer'][:100]}...")
        else:
            print("\nAnswer field not found!")

        if "sources" in data:
            print("\nSources field found!")
            print(f"Number of sources: {len(data['sources'])}")
            if data['sources']:
                print(f"First source: {data['sources'][0]}")
        else:
            print("\nSources field not found!")
    else:
        print(f"API call failed with status code {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    test_api_response()
