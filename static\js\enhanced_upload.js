/**
 * Enhanced Document Upload with Real-time Progress Tracking
 * Supports multiple document types with unified processing pipeline
 */

class EnhancedDocumentUploader {
    constructor() {
        this.activeOperations = new Map();
        this.supportedTypes = [];
        this.maxFileSize = 100 * 1024 * 1024; // 100MB default
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadSupportedTypes();
    }
    
    initializeElements() {
        // Main elements
        this.dropZone = document.getElementById('enhanced-dropzone');
        this.fileInput = document.getElementById('enhanced-file-input');
        this.fileList = document.getElementById('enhanced-file-list');
        this.uploadButton = document.getElementById('enhanced-upload-button');
        this.progressContainer = document.getElementById('enhanced-progress-container');
        this.previewContainer = document.getElementById('enhanced-preview-container');
        
        // Settings
        this.chunkSizeInput = document.getElementById('chunk-size');
        this.overlapInput = document.getElementById('overlap');
        this.extractEntitiesCheck = document.getElementById('extract-entities');
        this.extractReferencesCheck = document.getElementById('extract-references');
        this.extractMetadataCheck = document.getElementById('extract-metadata');
        this.generateEmbeddingsCheck = document.getElementById('generate-embeddings');
        
        // Create elements if they don't exist
        this.createMissingElements();
    }
    
    createMissingElements() {
        if (!this.dropZone) {
            this.dropZone = this.createElement('div', 'enhanced-dropzone', 'dropzone enhanced-dropzone');
            this.dropZone.innerHTML = `
                <div class="dropzone-content">
                    <i class="bi bi-cloud-upload fs-1 text-primary"></i>
                    <h5>Drag & Drop Documents Here</h5>
                    <p class="text-muted">Supports PDF, Word, Excel, PowerPoint, Text, HTML, and Images</p>
                    <button type="button" class="btn btn-primary" onclick="document.getElementById('enhanced-file-input').click()">
                        <i class="bi bi-file-earmark-plus"></i> Select Files
                    </button>
                </div>
            `;
            document.body.appendChild(this.dropZone);
        }
        
        if (!this.fileInput) {
            this.fileInput = this.createElement('input', 'enhanced-file-input', 'd-none');
            this.fileInput.type = 'file';
            this.fileInput.multiple = true;
            this.fileInput.accept = '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.md,.html,.htm,.xml,.jpg,.jpeg,.png,.gif,.tiff,.bmp';
            document.body.appendChild(this.fileInput);
        }
        
        if (!this.fileList) {
            this.fileList = this.createElement('div', 'enhanced-file-list', 'file-list mt-3');
            this.dropZone.after(this.fileList);
        }
        
        if (!this.progressContainer) {
            this.progressContainer = this.createElement('div', 'enhanced-progress-container', 'progress-container mt-3');
            this.fileList.after(this.progressContainer);
        }
        
        if (!this.previewContainer) {
            this.previewContainer = this.createElement('div', 'enhanced-preview-container', 'preview-container mt-3');
            this.progressContainer.after(this.previewContainer);
        }
    }
    
    createElement(tag, id, className) {
        const element = document.createElement(tag);
        element.id = id;
        element.className = className;
        return element;
    }
    
    setupEventListeners() {
        // Drag and drop
        this.dropZone.addEventListener('dragover', this.handleDragOver.bind(this));
        this.dropZone.addEventListener('dragleave', this.handleDragLeave.bind(this));
        this.dropZone.addEventListener('drop', this.handleDrop.bind(this));
        
        // File input
        this.fileInput.addEventListener('change', this.handleFileSelect.bind(this));
        
        // Upload button
        if (this.uploadButton) {
            this.uploadButton.addEventListener('click', this.handleUpload.bind(this));
        }
    }
    
    async loadSupportedTypes() {
        try {
            const response = await fetch('/api/enhanced/supported-types');
            const data = await response.json();
            this.supportedTypes = data.supported_extensions;
            this.updateDropZoneText();
        } catch (error) {
            console.error('Error loading supported types:', error);
        }
    }
    
    updateDropZoneText() {
        const contentDiv = this.dropZone.querySelector('.dropzone-content p');
        if (contentDiv && this.supportedTypes.length > 0) {
            const typeGroups = {
                'Documents': ['.pdf', '.doc', '.docx', '.txt', '.md', '.html', '.htm'],
                'Spreadsheets': ['.xls', '.xlsx', '.csv'],
                'Presentations': ['.ppt', '.pptx'],
                'Images': ['.jpg', '.jpeg', '.png', '.gif', '.tiff', '.bmp']
            };
            
            const supportedGroups = [];
            for (const [group, extensions] of Object.entries(typeGroups)) {
                if (extensions.some(ext => this.supportedTypes.includes(ext))) {
                    supportedGroups.push(group);
                }
            }
            
            contentDiv.textContent = `Supports ${supportedGroups.join(', ')}`;
        }
    }
    
    handleDragOver(e) {
        e.preventDefault();
        this.dropZone.classList.add('drag-over');
    }
    
    handleDragLeave(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
    }
    
    handleDrop(e) {
        e.preventDefault();
        this.dropZone.classList.remove('drag-over');
        
        const files = Array.from(e.dataTransfer.files);
        this.handleFiles(files);
    }
    
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.handleFiles(files);
    }
    
    handleFiles(files) {
        // Validate files
        const validFiles = [];
        const invalidFiles = [];
        
        for (const file of files) {
            if (this.validateFile(file)) {
                validFiles.push(file);
            } else {
                invalidFiles.push(file);
            }
        }
        
        // Show validation results
        if (invalidFiles.length > 0) {
            this.showValidationErrors(invalidFiles);
        }
        
        if (validFiles.length > 0) {
            this.displayFiles(validFiles);
            this.showUploadButton();
        }
    }
    
    validateFile(file) {
        // Check file type
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        if (!this.supportedTypes.includes(extension)) {
            return false;
        }
        
        // Check file size
        if (file.size > this.maxFileSize) {
            return false;
        }
        
        return true;
    }
    
    showValidationErrors(invalidFiles) {
        const errorMessages = invalidFiles.map(file => {
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            if (!this.supportedTypes.includes(extension)) {
                return `${file.name}: Unsupported file type`;
            }
            if (file.size > this.maxFileSize) {
                return `${file.name}: File too large (max ${this.formatFileSize(this.maxFileSize)})`;
            }
            return `${file.name}: Invalid file`;
        });
        
        this.showAlert(errorMessages.join('<br>'), 'danger');
    }
    
    displayFiles(files) {
        this.fileList.innerHTML = '';
        
        files.forEach((file, index) => {
            const fileItem = this.createFileItem(file, index);
            this.fileList.appendChild(fileItem);
        });
        
        // Store files for upload
        this.selectedFiles = files;
    }
    
    createFileItem(file, index) {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item card mb-2';
        fileItem.innerHTML = `
            <div class="card-body p-3">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="file-info">
                        <h6 class="mb-1">${file.name}</h6>
                        <small class="text-muted">
                            ${this.getFileTypeIcon(file)} ${this.formatFileSize(file.size)}
                        </small>
                    </div>
                    <div class="file-actions">
                        <button class="btn btn-sm btn-outline-primary me-2" onclick="enhancedUploader.previewFile(${index})">
                            <i class="bi bi-eye"></i> Preview
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="enhancedUploader.removeFile(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return fileItem;
    }
    
    getFileTypeIcon(file) {
        const extension = '.' + file.name.split('.').pop().toLowerCase();
        const iconMap = {
            '.pdf': '<i class="bi bi-file-earmark-pdf text-danger"></i>',
            '.doc': '<i class="bi bi-file-earmark-word text-primary"></i>',
            '.docx': '<i class="bi bi-file-earmark-word text-primary"></i>',
            '.xls': '<i class="bi bi-file-earmark-excel text-success"></i>',
            '.xlsx': '<i class="bi bi-file-earmark-excel text-success"></i>',
            '.ppt': '<i class="bi bi-file-earmark-ppt text-warning"></i>',
            '.pptx': '<i class="bi bi-file-earmark-ppt text-warning"></i>',
            '.txt': '<i class="bi bi-file-earmark-text text-secondary"></i>',
            '.md': '<i class="bi bi-file-earmark-text text-secondary"></i>',
            '.html': '<i class="bi bi-file-earmark-code text-info"></i>',
            '.htm': '<i class="bi bi-file-earmark-code text-info"></i>',
            '.jpg': '<i class="bi bi-file-earmark-image text-purple"></i>',
            '.jpeg': '<i class="bi bi-file-earmark-image text-purple"></i>',
            '.png': '<i class="bi bi-file-earmark-image text-purple"></i>'
        };
        
        return iconMap[extension] || '<i class="bi bi-file-earmark text-secondary"></i>';
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showUploadButton() {
        if (!this.uploadButton) {
            this.uploadButton = document.createElement('button');
            this.uploadButton.id = 'enhanced-upload-button';
            this.uploadButton.className = 'btn btn-primary btn-lg mt-3';
            this.uploadButton.innerHTML = '<i class="bi bi-upload"></i> Process Documents';
            this.uploadButton.addEventListener('click', this.handleUpload.bind(this));
            this.fileList.after(this.uploadButton);
        }
        this.uploadButton.style.display = 'block';
    }
    
    async previewFile(index) {
        const file = this.selectedFiles[index];
        if (!file) return;
        
        try {
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch('/api/enhanced/preview', {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const previewData = await response.json();
            this.showPreview(previewData);
            
        } catch (error) {
            console.error('Error generating preview:', error);
            this.showAlert('Error generating preview: ' + error.message, 'danger');
        }
    }
    
    showPreview(previewData) {
        this.previewContainer.innerHTML = `
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-eye"></i> Document Preview: ${previewData.filename}</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>Content Preview:</h6>
                            <div class="preview-text border p-3 bg-light" style="max-height: 300px; overflow-y: auto;">
                                <pre class="mb-0">${previewData.preview_text}</pre>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>Metadata:</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Type:</strong></td><td>${previewData.file_type}</td></tr>
                                <tr><td><strong>Method:</strong></td><td>${previewData.extraction_method}</td></tr>
                                ${Object.entries(previewData.metadata).slice(0, 5).map(([key, value]) => 
                                    `<tr><td><strong>${key}:</strong></td><td>${value}</td></tr>`
                                ).join('')}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    removeFile(index) {
        this.selectedFiles.splice(index, 1);
        
        if (this.selectedFiles.length === 0) {
            this.fileList.innerHTML = '';
            if (this.uploadButton) {
                this.uploadButton.style.display = 'none';
            }
        } else {
            this.displayFiles(this.selectedFiles);
        }
    }
    
    async handleUpload() {
        if (!this.selectedFiles || this.selectedFiles.length === 0) {
            this.showAlert('No files selected', 'warning');
            return;
        }
        
        // Disable upload button
        this.uploadButton.disabled = true;
        this.uploadButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
        
        // Clear previous progress
        this.progressContainer.innerHTML = '';
        
        try {
            // Upload files one by one or in batch
            if (this.selectedFiles.length === 1) {
                await this.uploadSingleFile(this.selectedFiles[0]);
            } else {
                await this.uploadMultipleFiles(this.selectedFiles);
            }
        } catch (error) {
            console.error('Upload error:', error);
            this.showAlert('Upload failed: ' + error.message, 'danger');
        } finally {
            // Re-enable upload button
            this.uploadButton.disabled = false;
            this.uploadButton.innerHTML = '<i class="bi bi-upload"></i> Process Documents';
        }
    }
    
    async uploadSingleFile(file) {
        const formData = this.createFormData(file);
        
        const response = await fetch('/api/enhanced/enhanced-upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        this.startProgressTracking(result.operation_id, file.name);
    }
    
    async uploadMultipleFiles(files) {
        const formData = new FormData();
        
        files.forEach(file => {
            formData.append('files', file);
        });
        
        // Add settings
        formData.append('chunk_size', this.getChunkSize());
        formData.append('overlap', this.getOverlap());
        formData.append('extract_entities', this.getExtractEntities());
        formData.append('extract_references', this.getExtractReferences());
        formData.append('extract_metadata', this.getExtractMetadata());
        formData.append('generate_embeddings', this.getGenerateEmbeddings());
        
        const response = await fetch('/api/enhanced/batch-enhanced-upload', {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Start progress tracking for each successful upload
        result.results.forEach(fileResult => {
            if (fileResult.operation_id) {
                this.startProgressTracking(fileResult.operation_id, fileResult.filename);
            }
        });
    }
    
    createFormData(file) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('chunk_size', this.getChunkSize());
        formData.append('overlap', this.getOverlap());
        formData.append('extract_entities', this.getExtractEntities());
        formData.append('extract_references', this.getExtractReferences());
        formData.append('extract_metadata', this.getExtractMetadata());
        formData.append('generate_embeddings', this.getGenerateEmbeddings());
        
        return formData;
    }
    
    getChunkSize() {
        return this.chunkSizeInput ? parseInt(this.chunkSizeInput.value) || 1200 : 1200;
    }
    
    getOverlap() {
        return this.overlapInput ? parseInt(this.overlapInput.value) || 0 : 0;
    }
    
    getExtractEntities() {
        return this.extractEntitiesCheck ? this.extractEntitiesCheck.checked : true;
    }
    
    getExtractReferences() {
        return this.extractReferencesCheck ? this.extractReferencesCheck.checked : true;
    }
    
    getExtractMetadata() {
        return this.extractMetadataCheck ? this.extractMetadataCheck.checked : true;
    }
    
    getGenerateEmbeddings() {
        return this.generateEmbeddingsCheck ? this.generateEmbeddingsCheck.checked : true;
    }
    
    startProgressTracking(operationId, filename) {
        // Create progress card
        const progressCard = this.createProgressCard(operationId, filename);
        this.progressContainer.appendChild(progressCard);
        
        // Start polling for progress
        const interval = setInterval(async () => {
            try {
                const response = await fetch(`/api/enhanced/progress/${operationId}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const progressData = await response.json();
                this.updateProgressCard(operationId, progressData);
                
                // Stop polling if completed or failed
                if (progressData.status === 'completed' || progressData.status === 'failed') {
                    clearInterval(interval);
                    this.activeOperations.delete(operationId);
                    
                    if (progressData.status === 'completed') {
                        this.showAlert(`Successfully processed ${filename}`, 'success');
                    } else {
                        this.showAlert(`Failed to process ${filename}: ${progressData.error_message}`, 'danger');
                    }
                }
                
            } catch (error) {
                console.error('Error checking progress:', error);
                clearInterval(interval);
                this.activeOperations.delete(operationId);
            }
        }, 2000); // Check every 2 seconds
        
        this.activeOperations.set(operationId, interval);
    }
    
    createProgressCard(operationId, filename) {
        const card = document.createElement('div');
        card.className = 'card mb-3 shadow-sm';
        card.id = `progress-${operationId}`;
        card.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="bi bi-file-earmark"></i> ${filename}</h6>
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" id="step-badge-${operationId}">Step 0/7</span>
                    <span class="text-muted small" id="eta-${operationId}"></span>
                </div>
            </div>
            <div class="card-body">
                <!-- Overall Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small fw-bold">Overall Progress</span>
                        <span class="small fw-bold" id="overall-percentage-${operationId}">0%</span>
                    </div>
                    <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar progress-bar-striped progress-bar-animated"
                             id="overall-progress-${operationId}" role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Current Step Progress -->
                <div class="mb-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span class="small text-muted" id="current-step-${operationId}">Initializing...</span>
                        <span class="small text-muted" id="step-percentage-${operationId}">0%</span>
                    </div>
                    <div class="progress" style="height: 6px;">
                        <div class="progress-bar bg-info" id="step-progress-${operationId}"
                             role="progressbar" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Processing Statistics -->
                <div class="row text-center" id="stats-${operationId}">
                    <div class="col-3">
                        <div class="small text-muted">Facts</div>
                        <div class="fw-bold text-primary" id="facts-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">Entities</div>
                        <div class="fw-bold text-success" id="entities-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">References</div>
                        <div class="fw-bold text-warning" id="references-${operationId}">-</div>
                    </div>
                    <div class="col-3">
                        <div class="small text-muted">Embeddings</div>
                        <div class="fw-bold text-info" id="embeddings-${operationId}">-</div>
                    </div>
                </div>

                <!-- Expandable Details -->
                <div class="mt-3">
                    <button class="btn btn-sm btn-outline-secondary w-100" type="button"
                            data-bs-toggle="collapse" data-bs-target="#details-${operationId}">
                        <i class="bi bi-chevron-down"></i> View Details
                    </button>
                    <div class="collapse mt-2" id="details-${operationId}">
                        <div class="card card-body bg-light">
                            <div class="small">
                                <div><strong>Operation ID:</strong> ${operationId}</div>
                                <div><strong>Start Time:</strong> <span id="start-time-${operationId}">-</span></div>
                                <div><strong>Elapsed Time:</strong> <span id="elapsed-time-${operationId}">-</span></div>
                                <div><strong>Processing Speed:</strong> <span id="speed-${operationId}">-</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return card;
    }
    
    updateProgressCard(operationId, progressData) {
        const card = document.getElementById(`progress-${operationId}`);
        if (!card) return;

        // Update step badge
        const stepBadge = document.getElementById(`step-badge-${operationId}`);
        if (stepBadge) {
            stepBadge.textContent = `Step ${progressData.current_step || 0}/${progressData.total_steps || 7}`;

            // Update badge color based on status
            stepBadge.className = 'badge me-2 ' + (
                progressData.status === 'completed' ? 'bg-success' :
                progressData.status === 'failed' ? 'bg-danger' : 'bg-primary'
            );
        }

        // Update ETA
        const eta = document.getElementById(`eta-${operationId}`);
        if (eta && progressData.estimated_remaining_time > 0) {
            const minutes = Math.floor(progressData.estimated_remaining_time / 60);
            const seconds = Math.round(progressData.estimated_remaining_time % 60);
            eta.textContent = minutes > 0 ? `ETA: ${minutes}m ${seconds}s` : `ETA: ${seconds}s`;
        }

        // Update overall progress
        const overallProgress = document.getElementById(`overall-progress-${operationId}`);
        const overallPercentage = document.getElementById(`overall-percentage-${operationId}`);
        if (overallProgress && overallPercentage) {
            overallProgress.style.width = `${progressData.progress_percentage}%`;
            overallProgress.setAttribute('aria-valuenow', progressData.progress_percentage);
            overallPercentage.textContent = `${progressData.progress_percentage}%`;

            // Update progress bar color based on status
            overallProgress.className = 'progress-bar progress-bar-striped progress-bar-animated ' + (
                progressData.status === 'completed' ? 'bg-success' :
                progressData.status === 'failed' ? 'bg-danger' : ''
            );
        }

        // Update current step
        const currentStep = document.getElementById(`current-step-${operationId}`);
        const stepPercentage = document.getElementById(`step-percentage-${operationId}`);
        const stepProgress = document.getElementById(`step-progress-${operationId}`);

        if (currentStep) {
            currentStep.textContent = progressData.current_step_name || 'Processing...';
        }

        if (stepPercentage && stepProgress) {
            // Calculate step-specific progress (assuming each step is roughly equal)
            const stepProgressPercent = progressData.current_step > 0 ?
                ((progressData.progress_percentage % (100 / progressData.total_steps)) / (100 / progressData.total_steps)) * 100 : 0;

            stepProgress.style.width = `${stepProgressPercent}%`;
            stepPercentage.textContent = `${Math.round(stepProgressPercent)}%`;
        }

        // Update statistics
        this.updateStatistics(operationId, progressData);

        // Update detailed information
        this.updateDetailedInfo(operationId, progressData);
    }

    updateStatistics(operationId, progressData) {
        // Handle both nested details and direct properties (for completed operations)
        const details = progressData.details || progressData;

        // Update facts count
        const factsElement = document.getElementById(`facts-${operationId}`);
        if (factsElement) {
            const factsCount = details.facts_count || progressData.facts_count || '-';
            factsElement.textContent = factsCount;
        }

        // Update entities count
        const entitiesElement = document.getElementById(`entities-${operationId}`);
        if (entitiesElement) {
            const entitiesCount = details.entities_count || progressData.entities_count || '-';
            entitiesElement.textContent = entitiesCount;
        }

        // Update references count
        const referencesElement = document.getElementById(`references-${operationId}`);
        if (referencesElement) {
            const referencesCount = details.references_count || progressData.references_count || '-';
            referencesElement.textContent = referencesCount;
        }

        // Update embeddings count
        const embeddingsElement = document.getElementById(`embeddings-${operationId}`);
        if (embeddingsElement) {
            const embeddingsCount = details.embeddings_count || details.embeddings_in_redis_count ||
                                   progressData.embeddings_count || progressData.embeddings_in_redis_count || '-';
            embeddingsElement.textContent = embeddingsCount;
        }
    }

    updateDetailedInfo(operationId, progressData) {
        const statistics = progressData.statistics || {};

        // Update start time - handle both nested and direct properties
        const startTimeElement = document.getElementById(`start-time-${operationId}`);
        const startTimeValue = statistics.start_time || progressData.start_time;
        if (startTimeElement && startTimeValue) {
            const startTime = new Date(startTimeValue);
            startTimeElement.textContent = startTime.toLocaleTimeString();
        }

        // Update elapsed time - handle both nested and direct properties
        const elapsedTimeElement = document.getElementById(`elapsed-time-${operationId}`);
        const elapsedTimeValue = statistics.elapsed_time || progressData.processing_time;
        if (elapsedTimeElement && elapsedTimeValue) {
            let elapsedSeconds;

            // Handle different time formats
            if (typeof elapsedTimeValue === 'string') {
                // If it's an ISO string, calculate elapsed time
                const startTime = new Date(progressData.start_time);
                const endTime = progressData.completion_time ? new Date(progressData.completion_time) : new Date();
                elapsedSeconds = (endTime - startTime) / 1000;
            } else {
                elapsedSeconds = elapsedTimeValue;
            }

            const minutes = Math.floor(elapsedSeconds / 60);
            const seconds = Math.round(elapsedSeconds % 60);
            elapsedTimeElement.textContent = minutes > 0 ? `${minutes}m ${seconds}s` : `${seconds}s`;
        }

        // Update processing speed
        const speedElement = document.getElementById(`speed-${operationId}`);
        const elapsedForSpeed = statistics.elapsed_time ||
                              (progressData.processing_time && typeof progressData.processing_time === 'number' ? progressData.processing_time : null);

        if (speedElement && elapsedForSpeed > 0) {
            const details = progressData.details || progressData;
            const totalItems = (details.facts_count || 0) + (details.entities_count || 0);
            if (totalItems > 0) {
                const itemsPerSecond = totalItems / elapsedForSpeed;
                speedElement.textContent = `${itemsPerSecond.toFixed(1)} items/sec`;
            } else {
                speedElement.textContent = 'Processing complete';
            }
        }
    }

    async loadExistingOperation(operationId) {
        /**
         * Load and display progress for an existing operation.
         * This is useful for showing completed operations or resuming progress tracking.
         */
        try {
            console.log(`Loading existing operation: ${operationId}`);

            // Try to get detailed progress first
            const response = await fetch(`/api/enhanced/progress/${operationId}/detailed`);

            if (response.ok) {
                const progressData = await response.json();
                console.log('Loaded progress data:', progressData);

                // Create progress card with the loaded data
                const filename = progressData.document_name || 'Unknown Document';
                const progressCard = this.createProgressCard(operationId, filename);
                this.progressContainer.appendChild(progressCard);

                // Update the card with the loaded data
                this.updateProgressCard(operationId, progressData);

                // If still processing, start polling
                if (progressData.status === 'processing') {
                    this.startProgressTracking(operationId, filename);
                } else {
                    // Show completion message
                    const statusMessage = progressData.status === 'completed' ?
                        `Document "${filename}" processed successfully` :
                        `Document "${filename}" processing failed`;

                    this.showAlert(statusMessage, progressData.status === 'completed' ? 'success' : 'danger');
                }

                // Store in localStorage for future reference
                localStorage.setItem('lastOperationId', operationId);

            } else {
                console.warn(`Operation ${operationId} not found or expired`);
                // Clean up localStorage if operation not found
                if (localStorage.getItem('lastOperationId') === operationId) {
                    localStorage.removeItem('lastOperationId');
                }
            }

        } catch (error) {
            console.error('Error loading existing operation:', error);
        }
    }

    showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container') || document.body;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alert.parentNode) {
                alert.remove();
            }
        }, 5000);
    }
}

// Initialize the enhanced uploader when the page loads
let enhancedUploader;
document.addEventListener('DOMContentLoaded', () => {
    enhancedUploader = new EnhancedDocumentUploader();

    // Check for any operation ID in the URL or localStorage to display progress
    const urlParams = new URLSearchParams(window.location.search);
    const operationId = urlParams.get('operation_id') || localStorage.getItem('lastOperationId');

    if (operationId) {
        enhancedUploader.loadExistingOperation(operationId);
    }
});
