# Using Graphiti with Google Gemini

This example demonstrates how to use Graphiti with Google's Gemini models for both LLM inference and embeddings.

## Prerequisites

- Neo4j database (running locally or remotely)
- Google API key with access to Gemini models
- Python 3.9+
- Graphiti installed (`pip install graphiti-core`)

## Setup Instructions

1. Make sure your Neo4j database is running and accessible.

2. Set up your environment variables:
   - Copy the `.env.example` file to `.env` in the root directory (if not already done)
   - Add your Google API key to the `.env` file:
     ```
     GOOGLE_API_KEY=your-google-api-key-here
     ```
   - Verify your Neo4j connection details are correct in the `.env` file

3. Run the example:
   ```bash
   python examples/gemini_example.py
   ```

## What This Example Does

1. Connects to your Neo4j database
2. Initializes Graphiti with Google Gemini for both LLM and embeddings
3. Sets up necessary indices and constraints in Neo4j
4. Adds a sample episode to the knowledge graph
5. Performs a search query using the Gemini embeddings
6. Displays the search results

## Available Gemini Models

- For LLM inference: `models/gemini-2.0-flash`, `models/gemini-1.5-pro`, etc.
- For embeddings: `models/embedding-001`, `models/text-embedding-004`, `models/gemini-embedding-exp-03-07`

**Important**: When using the Google Generative AI API, model names must be prefixed with `models/` as shown above.

## Troubleshooting

- If you encounter authentication errors, verify your Google API key is correct and has access to Gemini models
- For Neo4j connection issues, check that your database is running and the connection details in `.env` are correct
- Make sure you have the latest version of the `google-generativeai` Python package installed
- If you get an error about invalid model names, ensure you're using the full model name with the `models/` prefix
- For embedding errors, try using a different embedding model like `models/text-embedding-004` or `models/embedding-001`
- If you encounter import errors with `from google import genai`, use `import google.generativeai as genai` instead

## Additional Resources

- [Google AI Gemini API Documentation](https://ai.google.dev/docs)
- [Gemini Embeddings Documentation](https://ai.google.dev/gemini-api/docs/embeddings)
- [Graphiti Documentation](https://help.getzep.com/graphiti)
- [Google AI Studio](https://aistudio.google.com/) - For testing Gemini models and getting an API key
