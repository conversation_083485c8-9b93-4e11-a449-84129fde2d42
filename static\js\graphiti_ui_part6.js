/**
 * Graphiti UI Part 6 - Additional functionality for the Graphiti Knowledge Graph UI
 * 
 * This file contains functions for the Settings tab and remaining Enhancements tab functions.
 */

/**
 * Build citation network
 */
function buildCitationNetwork() {
    // Show progress
    const progressContainer = document.getElementById('enhancements-progress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        
        const progressMessage = progressContainer.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = 'Building citation network...';
        }
        
        const progressBar = progressContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
        }
    }
    
    // Clear previous results
    const resultsContainer = document.getElementById('enhancements-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // Build citation network
    fetch('/api/references/build-network', {
        method: 'POST'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show results
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="alert alert-success">
                    <h5>Citation Network Built</h5>
                    <p>Created ${data.nodes} nodes and ${data.edges} edges in the citation network.</p>
                </div>
            `;
        }
        
        // Show network visualization
        showNetworkVisualization(data.network);
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    })
    .catch(error => {
        console.error('Error building citation network:', error);
        
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show error message
        if (resultsContainer) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Error building citation network: ${error.message}</div>`;
        }
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}

/**
 * Show network visualization
 * 
 * @param {Object} network - Network data
 */
function showNetworkVisualization(network) {
    const networkVisualization = document.getElementById('network-visualization');
    if (!networkVisualization) {
        console.error("Network visualization element not found");
        return;
    }
    
    // Clear existing content
    networkVisualization.innerHTML = '';
    
    // Create visualization placeholder
    const visualization = document.createElement('div');
    visualization.className = 'network-visualization-placeholder';
    visualization.style.height = '400px';
    visualization.style.border = '1px solid #ccc';
    visualization.style.padding = '20px';
    visualization.style.display = 'flex';
    visualization.style.alignItems = 'center';
    visualization.style.justifyContent = 'center';
    
    // Add placeholder text
    const placeholderText = document.createElement('p');
    placeholderText.textContent = 'Network Visualization Placeholder';
    visualization.appendChild(placeholderText);
    
    networkVisualization.appendChild(visualization);
}

/**
 * Enrich references
 */
function enrichReferences() {
    // Show progress
    const progressContainer = document.getElementById('enhancements-progress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        
        const progressMessage = progressContainer.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = 'Enriching references...';
        }
        
        const progressBar = progressContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
        }
    }
    
    // Clear previous results
    const resultsContainer = document.getElementById('enhancements-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // Enrich references
    fetch('/api/references/enrich', {
        method: 'POST'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show results
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="alert alert-success">
                    <h5>References Enriched</h5>
                    <p>Enriched ${data.enriched_count} references with additional metadata.</p>
                </div>
            `;
        }
        
        // Show enriched references
        showEnrichedReferences(data.enriched_references);
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    })
    .catch(error => {
        console.error('Error enriching references:', error);
        
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show error message
        if (resultsContainer) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Error enriching references: ${error.message}</div>`;
        }
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}

/**
 * Show enriched references
 * 
 * @param {Array} references - Enriched references
 */
function showEnrichedReferences(references) {
    const enrichedReferencesList = document.getElementById('enriched-references-list');
    if (!enrichedReferencesList) {
        console.error("Enriched references list element not found");
        return;
    }
    
    // Clear existing content
    enrichedReferencesList.innerHTML = '';
    
    // If no references, show message
    if (!references || references.length === 0) {
        enrichedReferencesList.innerHTML = '<div class="alert alert-info">No references were enriched.</div>';
        return;
    }
    
    // Create references container
    const referencesContainer = document.createElement('div');
    referencesContainer.className = 'mt-4';
    
    // Add heading
    const heading = document.createElement('h4');
    heading.textContent = 'Enriched References';
    referencesContainer.appendChild(heading);
    
    // Create references table
    const table = document.createElement('table');
    table.className = 'table table-striped table-hover';
    
    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Title</th>
            <th>Authors</th>
            <th>Year</th>
            <th>Journal</th>
            <th>DOI</th>
            <th>Document</th>
        </tr>
    `;
    table.appendChild(thead);
    
    // Create table body
    const tbody = document.createElement('tbody');
    
    // Add reference rows
    references.forEach(ref => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${ref.title || 'Unknown'}</td>
            <td>${ref.authors?.join(', ') || 'Unknown'}</td>
            <td>${ref.year || 'Unknown'}</td>
            <td>${ref.journal || 'Unknown'}</td>
            <td>${ref.doi ? `<a href="https://doi.org/${ref.doi}" target="_blank">${ref.doi}</a>` : 'None'}</td>
            <td>${ref.document_id ? `<a href="/documents/${ref.document_id}">${ref.document_title || 'View'}</a>` : 'None'}</td>
        `;
        
        tbody.appendChild(row);
    });
    
    table.appendChild(tbody);
    referencesContainer.appendChild(table);
    
    enrichedReferencesList.appendChild(referencesContainer);
}

/**
 * Run all enhancements
 */
function runAllEnhancements() {
    // Show progress
    const progressContainer = document.getElementById('enhancements-progress');
    if (progressContainer) {
        progressContainer.style.display = 'block';
        
        const progressMessage = progressContainer.querySelector('.progress-message');
        if (progressMessage) {
            progressMessage.textContent = 'Running all enhancements...';
        }
        
        const progressBar = progressContainer.querySelector('.progress-bar');
        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
        }
    }
    
    // Clear previous results
    const resultsContainer = document.getElementById('enhancements-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = '';
    }
    
    // Run all enhancements
    fetch('/api/references/enhance-all', {
        method: 'POST'
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show results
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="alert alert-success">
                    <h5>All Enhancements Complete</h5>
                    <p>Successfully ran all reference enhancements.</p>
                    <ul>
                        <li>Deduplication: Found ${data.deduplication.duplicate_groups} duplicate groups</li>
                        <li>Citation Network: Created ${data.network.nodes} nodes and ${data.network.edges} edges</li>
                        <li>Enrichment: Enriched ${data.enrichment.enriched_count} references</li>
                    </ul>
                </div>
            `;
        }
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    })
    .catch(error => {
        console.error('Error running all enhancements:', error);
        
        // Update progress
        if (progressBar) {
            progressBar.style.width = '100%';
            progressBar.setAttribute('aria-valuenow', '100');
        }
        
        // Show error message
        if (resultsContainer) {
            resultsContainer.innerHTML = `<div class="alert alert-danger">Error running all enhancements: ${error.message}</div>`;
        }
        
        // Hide progress after a delay
        setTimeout(() => {
            if (progressContainer) {
                progressContainer.style.display = 'none';
            }
        }, 2000);
    });
}
