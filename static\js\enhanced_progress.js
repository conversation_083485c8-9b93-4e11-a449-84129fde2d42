/**
 * Enhanced Progress Tracking for Document Processing
 * 
 * This script provides improved visibility into document processing with:
 * - Detailed step-by-step progress
 * - Visual step indicators
 * - Real-time processing log
 * - Detailed statistics panel
 */

// Global variables
let progressInterval = null;
let currentDocumentId = null;
let processingStartTime = null;
let processingSteps = {};
let processingLog = [];

// DOM elements
const enhancedProgressContainer = document.getElementById('enhanced-progress-container');
const overallProgressBar = document.getElementById('overall-progress-bar');
const overallProgressPercentage = document.getElementById('overall-progress-percentage');
const overallStatus = document.getElementById('overall-status');
const currentStepName = document.getElementById('current-step-name');
const currentStepPercentage = document.getElementById('current-step-percentage');
const currentStepProgressBar = document.getElementById('current-step-progress-bar');
const processingTimeElement = document.getElementById('processing-time');
const documentIdElement = document.getElementById('document-id');
const documentFilenameElement = document.getElementById('document-filename');
const processingStartElement = document.getElementById('processing-start');
const factsCountElement = document.getElementById('facts-count');
const entitiesCountElement = document.getElementById('entities-count');
const referencesCountElement = document.getElementById('references-count');
const embeddingsCountElement = document.getElementById('embeddings-count');
const processingStepsElement = document.getElementById('processing-steps');
const processingLogElement = document.getElementById('processing-log');

// Step names and descriptions
const PROCESSING_STEPS = {
    1: { name: "Text Extraction", description: "Extracting text from document" },
    2: { name: "Entity Extraction", description: "Extracting entities from text" },
    3: { name: "Reference Extraction", description: "Extracting references from document" },
    4: { name: "Metadata Extraction", description: "Extracting metadata from document" },
    5: { name: "Embedding Generation", description: "Generating embeddings for facts" }
};

/**
 * Initialize the enhanced progress tracking
 * 
 * @param {string} documentId - The ID of the document being processed
 * @param {string} filename - The filename of the document
 */
function initEnhancedProgressTracking(documentId, filename) {
    // Reset state
    currentDocumentId = documentId;
    processingStartTime = new Date();
    processingSteps = {};
    processingLog = [];
    
    // Update UI elements
    if (enhancedProgressContainer) {
        enhancedProgressContainer.style.display = 'block';
    }
    
    // Initialize step indicators
    for (let i = 1; i <= 5; i++) {
        const stepIndicator = document.getElementById(`step-indicator-${i}`);
        if (stepIndicator) {
            stepIndicator.className = 'step-indicator';
        }
    }
    
    // Initialize processing steps
    if (processingStepsElement) {
        processingStepsElement.innerHTML = '';
        
        for (let i = 1; i <= 5; i++) {
            const stepInfo = PROCESSING_STEPS[i];
            
            // Create step progress element
            const stepDiv = document.createElement('div');
            stepDiv.className = 'step-progress';
            
            const stepHeader = document.createElement('div');
            stepHeader.className = 'step-progress-header';
            
            const stepNameSpan = document.createElement('span');
            stepNameSpan.textContent = stepInfo.name;
            
            const stepPercentageSpan = document.createElement('span');
            stepPercentageSpan.id = `step-${i}-percentage`;
            stepPercentageSpan.textContent = 'Pending';
            
            stepHeader.appendChild(stepNameSpan);
            stepHeader.appendChild(stepPercentageSpan);
            
            const progressDiv = document.createElement('div');
            progressDiv.className = 'progress';
            progressDiv.style.height = '10px';
            
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar step-progress-bar';
            progressBar.id = `step-${i}-progress-bar`;
            progressBar.role = 'progressbar';
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', '0');
            progressBar.setAttribute('aria-valuemin', '0');
            progressBar.setAttribute('aria-valuemax', '100');
            
            progressDiv.appendChild(progressBar);
            stepDiv.appendChild(stepHeader);
            stepDiv.appendChild(progressDiv);
            
            processingStepsElement.appendChild(stepDiv);
        }
    }
    
    // Initialize document info
    if (documentIdElement) documentIdElement.textContent = documentId;
    if (documentFilenameElement) documentFilenameElement.textContent = filename;
    if (processingStartElement) processingStartElement.textContent = formatDateTime(processingStartTime);
    
    // Initialize statistics
    if (factsCountElement) factsCountElement.textContent = '0';
    if (entitiesCountElement) entitiesCountElement.textContent = '0';
    if (referencesCountElement) referencesCountElement.textContent = '0';
    if (embeddingsCountElement) embeddingsCountElement.textContent = '0';
    
    // Initialize processing log
    if (processingLogElement) {
        addToProcessingLog(`Processing started for document: ${filename} (${documentId})`);
        addToProcessingLog(`Start time: ${formatDateTime(processingStartTime)}`);
    }
    
    // Start progress tracking
    startEnhancedProgressTracking(documentId);
}

/**
 * Start tracking the progress of document processing
 * 
 * @param {string} documentId - The ID of the document being processed
 */
function startEnhancedProgressTracking(documentId) {
    // Clear any existing interval
    if (progressInterval) {
        clearInterval(progressInterval);
    }
    
    // Set up the interval to check progress
    progressInterval = setInterval(() => {
        checkEnhancedDocumentProgress(documentId);
        updateProcessingTime();
    }, 1000); // Check every second
}

/**
 * Check the progress of document processing
 *
 * @param {string} operationId - The operation ID of the document being processed
 */
function checkEnhancedDocumentProgress(operationId) {
    fetch(`/api/enhanced/progress/${operationId}/detailed`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Update the progress UI
            updateEnhancedProgressUI(data);
            
            // If processing is complete or failed, stop tracking
            if (data.status === 'completed' || data.status === 'failed') {
                stopEnhancedProgressTracking();
                
                // Add final log entry
                if (data.status === 'completed') {
                    addToProcessingLog('Processing completed successfully!');
                } else {
                    addToProcessingLog(`Processing failed: ${data.details.error || 'Unknown error'}`);
                }
            }
        })
        .catch(error => {
            console.error('Error checking document progress:', error);
            addToProcessingLog(`Error checking progress: ${error.message}`);
        });
}

/**
 * Update the progress UI with the latest progress data
 * 
 * @param {Object} progressData - The progress data from the API
 */
function updateEnhancedProgressUI(progressData) {
    // Extract data
    const status = progressData.status || 'processing';
    const percentage = progressData.progress_percentage || 0;
    const stepName = progressData.step_name || 'Processing...';
    const currentStep = progressData.current_step || 0;
    const totalSteps = progressData.total_steps || 5;
    const details = progressData.details || {};
    
    // Update overall progress
    if (overallProgressBar) {
        overallProgressBar.style.width = `${percentage}%`;
        overallProgressBar.setAttribute('aria-valuenow', percentage);
        
        // Update color based on status
        overallProgressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
        if (status === 'completed') {
            overallProgressBar.classList.add('bg-success');
        } else if (status === 'failed') {
            overallProgressBar.classList.add('bg-danger');
        }
    }
    
    if (overallProgressPercentage) {
        overallProgressPercentage.textContent = `${percentage}%`;
    }
    
    // Update overall status
    if (overallStatus) {
        if (status === 'processing') {
            overallStatus.textContent = `Processing document... Step ${currentStep}/${totalSteps}`;
        } else if (status === 'completed') {
            overallStatus.textContent = 'Processing complete!';
        } else if (status === 'failed') {
            overallStatus.textContent = `Processing failed: ${details.error || 'Unknown error'}`;
        } else if (status === 'queued') {
            overallStatus.textContent = 'Waiting in queue...';
        }
    }
    
    // Update current step
    if (currentStepName && currentStep > 0 && currentStep <= 5) {
        currentStepName.textContent = PROCESSING_STEPS[currentStep].name;
    }
    
    if (currentStepPercentage) {
        currentStepPercentage.textContent = `${percentage}%`;
    }
    
    if (currentStepProgressBar) {
        currentStepProgressBar.style.width = `${percentage}%`;
        currentStepProgressBar.setAttribute('aria-valuenow', percentage);
    }
    
    // Update step indicators
    for (let i = 1; i <= 5; i++) {
        const stepIndicator = document.getElementById(`step-indicator-${i}`);
        if (stepIndicator) {
            stepIndicator.className = 'step-indicator';
            
            if (i < currentStep) {
                stepIndicator.classList.add('completed');
            } else if (i === currentStep) {
                stepIndicator.classList.add('active');
            }
        }
    }
    
    // Update step progress bars
    for (let i = 1; i <= 5; i++) {
        const stepProgressBar = document.getElementById(`step-${i}-progress-bar`);
        const stepPercentage = document.getElementById(`step-${i}-percentage`);
        
        if (stepProgressBar && stepPercentage) {
            if (i < currentStep) {
                // Previous steps are complete
                stepProgressBar.style.width = '100%';
                stepProgressBar.setAttribute('aria-valuenow', 100);
                stepProgressBar.classList.add('bg-success');
                stepPercentage.textContent = 'Complete';
            } else if (i === currentStep) {
                // Current step is in progress
                stepProgressBar.style.width = `${percentage}%`;
                stepProgressBar.setAttribute('aria-valuenow', percentage);
                stepPercentage.textContent = `${percentage}%`;
            } else {
                // Future steps are pending
                stepProgressBar.style.width = '0%';
                stepProgressBar.setAttribute('aria-valuenow', 0);
                stepPercentage.textContent = 'Pending';
            }
        }
    }
    
    // Update statistics if available
    if (details) {
        if (factsCountElement && details.facts_count !== undefined) {
            factsCountElement.textContent = details.facts_count;
        }

        if (entitiesCountElement && details.entities_count !== undefined) {
            entitiesCountElement.textContent = details.entities_count;
        }

        if (referencesCountElement && details.references_count !== undefined) {
            referencesCountElement.textContent = details.references_count;
        }

        if (embeddingsCountElement && details.embeddings_count !== undefined) {
            embeddingsCountElement.textContent = details.embeddings_count;
        }
    }

    // Update document information
    const documentIdElement = document.getElementById('document-id');
    const documentFilenameElement = document.getElementById('document-filename');
    const processingStartElement = document.getElementById('processing-start');

    if (documentIdElement && progressData.operation_id) {
        documentIdElement.textContent = progressData.operation_id;
    }

    if (documentFilenameElement && progressData.document_name) {
        documentFilenameElement.textContent = progressData.document_name;
    }

    if (processingStartElement && progressData.statistics && progressData.statistics.start_time) {
        const startTime = new Date(progressData.statistics.start_time);
        processingStartElement.textContent = startTime.toLocaleString();
    }
    
    // Add to processing log if step changed
    if (stepName && (!processingSteps[currentStep] || processingSteps[currentStep] !== stepName)) {
        processingSteps[currentStep] = stepName;
        addToProcessingLog(`Step ${currentStep}: ${stepName}`);
    }
}

/**
 * Stop tracking the progress of document processing
 */
function stopEnhancedProgressTracking() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }
}

/**
 * Update the processing time display
 */
function updateProcessingTime() {
    if (processingTimeElement && processingStartTime) {
        const currentTime = new Date();
        const elapsedSeconds = Math.floor((currentTime - processingStartTime) / 1000);
        const minutes = Math.floor(elapsedSeconds / 60);
        const seconds = elapsedSeconds % 60;
        
        processingTimeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

/**
 * Add an entry to the processing log
 * 
 * @param {string} message - The message to add to the log
 */
function addToProcessingLog(message) {
    if (processingLogElement) {
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = `[${timestamp}] ${message}`;
        
        processingLog.push(logEntry);
        processingLogElement.textContent = processingLog.join('\n');
        
        // Auto-scroll to bottom
        processingLogElement.scrollTop = processingLogElement.scrollHeight;
    }
}

/**
 * Format a date as a readable string
 * 
 * @param {Date} date - The date to format
 * @returns {string} The formatted date string
 */
function formatDateTime(date) {
    return date.toLocaleString();
}
