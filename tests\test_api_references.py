"""
Integration tests for the reference API endpoints.
"""

import os
import sys
import pytest
import io
from pathlib import Path
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

@patch("services.reference_service.get_all_references")
def test_get_references_endpoint(mock_get_all_references, test_client):
    """
    Test the get references endpoint.
    
    Args:
        mock_get_all_references: Mocked get_all_references function
        test_client: FastAPI test client
    """
    # Mock the get_all_references function
    mock_get_all_references.return_value = {
        "references": [
            {
                "source_document": "doc1.pdf",
                "extraction_method": "llm",
                "authors": "Author 1",
                "title": "Title 1",
                "year": "2023",
                "journal": "Journal 1"
            },
            {
                "source_document": "doc2.pdf",
                "extraction_method": "regex",
                "reference_text": "Reference text 2"
            }
        ]
    }
    
    # Make a request to the references endpoint
    response = test_client.get("/api/references")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "references" in data
    assert len(data["references"]) == 2
    assert data["references"][0]["source_document"] == "doc1.pdf"
    assert data["references"][1]["source_document"] == "doc2.pdf"

@patch("services.reference_service.get_all_references")
def test_get_references_endpoint_with_filters(mock_get_all_references, test_client):
    """
    Test the get references endpoint with filters.
    
    Args:
        mock_get_all_references: Mocked get_all_references function
        test_client: FastAPI test client
    """
    # Mock the get_all_references function
    mock_get_all_references.return_value = {
        "references": [
            {
                "source_document": "doc1.pdf",
                "extraction_method": "llm",
                "authors": "Author 1",
                "title": "Title 1",
                "year": "2023",
                "journal": "Journal 1"
            }
        ]
    }
    
    # Make a request to the references endpoint with filters
    response = test_client.get(
        "/api/references",
        params={
            "source_document": "doc1.pdf",
            "authors": "Author 1",
            "year": "2023",
            "journal": "Journal 1",
            "extraction_method": "llm",
            "search_query": "Title"
        }
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "references" in data
    assert len(data["references"]) == 1
    assert data["references"][0]["source_document"] == "doc1.pdf"
    
    # Check that the filter was passed to the get_all_references function
    mock_get_all_references.assert_called_once()
    filter_arg = mock_get_all_references.call_args[0][0]
    assert filter_arg.source_document == "doc1.pdf"
    assert filter_arg.authors == "Author 1"
    assert filter_arg.year == "2023"
    assert filter_arg.journal == "Journal 1"
    assert filter_arg.extraction_method == "llm"
    assert filter_arg.search_query == "Title"

@patch("services.reference_service.get_all_references")
def test_get_references_endpoint_error(mock_get_all_references, test_client):
    """
    Test the get references endpoint with an error.
    
    Args:
        mock_get_all_references: Mocked get_all_references function
        test_client: FastAPI test client
    """
    # Mock the get_all_references function to raise an exception
    mock_get_all_references.side_effect = Exception("Test error")
    
    # Make a request to the references endpoint
    response = test_client.get("/api/references")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting references" in response.json()["detail"]

@patch("os.path.exists")
@patch("fastapi.responses.FileResponse")
def test_get_references_csv_endpoint(mock_file_response, mock_exists, test_client):
    """
    Test the get references CSV endpoint.
    
    Args:
        mock_file_response: Mocked FileResponse
        mock_exists: Mocked os.path.exists
        test_client: FastAPI test client
    """
    # Mock os.path.exists to return True
    mock_exists.return_value = True
    
    # Mock FileResponse
    mock_file_response.return_value = "CSV file content"
    
    # Make a request to the references CSV endpoint
    response = test_client.get("/api/references/csv")
    
    # Check the response
    assert response.status_code == 200

@patch("os.path.exists")
def test_get_references_csv_endpoint_not_found(mock_exists, test_client):
    """
    Test the get references CSV endpoint when the file doesn't exist.
    
    Args:
        mock_exists: Mocked os.path.exists
        test_client: FastAPI test client
    """
    # Mock os.path.exists to return False
    mock_exists.return_value = False
    
    # Make a request to the references CSV endpoint
    response = test_client.get("/api/references/csv")
    
    # Check the response
    assert response.status_code == 404
    
    # Check the error message
    assert "No references found" in response.json()["detail"]

@patch("services.reference_service.extract_references_from_document")
def test_extract_references_endpoint(mock_extract_references, test_client):
    """
    Test the extract references endpoint.
    
    Args:
        mock_extract_references: Mocked extract_references_from_document function
        test_client: FastAPI test client
    """
    # Mock the extract_references_from_document function
    mock_extract_references.return_value = {
        "filename": "test.pdf",
        "file_path": "/path/to/test.pdf",
        "extraction_date": "2023-01-01T00:00:00",
        "success": True,
        "llm_references": [
            {
                "authors": "Author 1",
                "title": "Title 1",
                "year": "2023",
                "journal": "Journal 1"
            }
        ],
        "regex_references": [
            "Reference text 1"
        ]
    }
    
    # Create a test file
    file_content = b"Test file content"
    file = io.BytesIO(file_content)
    
    # Make a request to the extract references endpoint
    response = test_client.post(
        "/api/references/extract",
        files={"file": ("test.pdf", file, "application/pdf")}
    )
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert data["success"] == True
    assert data["filename"] == "test.pdf"
    assert len(data["llm_references"]) == 1
    assert len(data["regex_references"]) == 1

@patch("services.reference_service.extract_references_from_document")
def test_extract_references_endpoint_error(mock_extract_references, test_client):
    """
    Test the extract references endpoint with an error.
    
    Args:
        mock_extract_references: Mocked extract_references_from_document function
        test_client: FastAPI test client
    """
    # Mock the extract_references_from_document function to raise an exception
    mock_extract_references.side_effect = Exception("Test error")
    
    # Create a test file
    file_content = b"Test file content"
    file = io.BytesIO(file_content)
    
    # Make a request to the extract references endpoint
    response = test_client.post(
        "/api/references/extract",
        files={"file": ("test.pdf", file, "application/pdf")}
    )
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error extracting references" in response.json()["detail"]
