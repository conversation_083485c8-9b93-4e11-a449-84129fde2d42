"""
Episode UUID validator.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class EpisodeValidator(BaseValidator):
    """Validator for Episode node UUIDs."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "episodes_total": 0,
            "episodes_missing_uuid": 0,
            "episodes_fixed": 0
        }

    async def validate(self):
        """Check Episode nodes for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all Episode nodes
        count_query = """
        MATCH (e:Episode)
        RETURN count(e) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["episodes_total"] = count_result[1][0][0]

        # Find Episode nodes without UUIDs
        missing_query = """
        MATCH (e:Episode)
        WHERE e.uuid IS NULL
        RETURN id(e) as id, e.name as name
        """

        missing_result = adapter.execute_cypher(missing_query)
        episodes = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                episodes.append({
                    "id": row[0],
                    "name": row[1]
                })

            self.stats["episodes_missing_uuid"] = len(episodes)
            self.log_info(f"Found {len(episodes)} Episode nodes without UUIDs")

            if self.fix:
                fixed_count = await self._fix_episodes(episodes)
                self.stats["episodes_fixed"] = fixed_count

    async def _fix_episodes(self, episodes: List[Dict[str, Any]]) -> int:
        """
        Fix Episode nodes missing UUIDs.

        Args:
            episodes: List of Episode nodes to fix

        Returns:
            Number of nodes fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for episode in episodes:
            episode_uuid = self.generate_new_uuid()
            timestamp = self.get_timestamp()

            update_query = f"""
            MATCH (e:Episode)
            WHERE id(e) = {episode['id']}
            SET e.uuid = '{episode_uuid}',
                e.updated_at = '{timestamp}'
            RETURN e.name as name, e.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                self.log_info(f"Fixed Episode node: {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

        return fixed_count
