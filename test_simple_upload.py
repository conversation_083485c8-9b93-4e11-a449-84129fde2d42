#!/usr/bin/env python3
"""
Test script to upload a simple text file.
"""

import requests
import time
import tempfile
import os

def test_simple_upload():
    """Test uploading a simple text file"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Simple File Upload")
    print("=" * 30)
    
    # Create a simple test file
    test_content = """
    Test Document for Upload
    
    This is a simple test document to verify that the upload and processing system is working correctly.
    
    Key Points:
    - This document contains basic text
    - It should be processed quickly
    - Entity extraction should find some basic entities
    - References should be minimal
    
    Health Information:
    Vitamin C is an important nutrient for immune system function.
    Echinacea is an herb commonly used for immune support.
    """
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write(test_content)
        temp_file_path = f.name
    
    try:
        print("1. Uploading test file...")
        
        # Upload the file
        with open(temp_file_path, 'rb') as f:
            files = {'file': ('test_document.txt', f, 'text/plain')}
            data = {
                'chunk_size': 500,
                'overlap': 0,
                'extract_entities': True,
                'extract_references': True,
                'extract_metadata': True,
                'generate_embeddings': False  # Skip embeddings for faster processing
            }
            
            response = requests.post(
                f"{base_url}/api/enhanced/enhanced-upload",
                files=files,
                data=data,
                timeout=30
            )
            
            print(f"Upload status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                operation_id = result.get('operation_id')
                print(f"✅ Upload successful!")
                print(f"Operation ID: {operation_id}")
                print(f"Status: {result.get('status')}")
                print(f"Message: {result.get('message')}")
                
                if operation_id:
                    # Monitor progress
                    print(f"\n2. Monitoring progress...")
                    for i in range(10):  # Check for up to 30 seconds
                        try:
                            progress_response = requests.get(
                                f"{base_url}/api/enhanced/progress/{operation_id}",
                                timeout=5
                            )
                            
                            if progress_response.status_code == 200:
                                progress_data = progress_response.json()
                                print(f"  Step {progress_data.get('current_step', 0)}/{progress_data.get('total_steps', 0)}: {progress_data.get('step_name', 'N/A')}")
                                print(f"  Progress: {progress_data.get('progress_percentage', 0)}%")
                                print(f"  Status: {progress_data.get('status', 'N/A')}")
                                
                                if progress_data.get('status') == 'completed':
                                    print("✅ Processing completed!")
                                    break
                                elif progress_data.get('status') == 'failed':
                                    print("❌ Processing failed!")
                                    break
                            else:
                                print(f"  Progress check failed: {progress_response.status_code}")
                                
                        except Exception as e:
                            print(f"  Progress check error: {e}")
                        
                        time.sleep(3)
                else:
                    print("❌ No operation ID returned")
            else:
                print(f"❌ Upload failed: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    
    finally:
        # Clean up temp file
        if os.path.exists(temp_file_path):
            os.unlink(temp_file_path)

if __name__ == "__main__":
    test_simple_upload()
