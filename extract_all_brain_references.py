#!/usr/bin/env python3
"""
Comprehensive reference extraction from Brain.one file.
This script will extract ALL references from the actual Brain.one content.
"""

import asyncio
import json
import re
from pathlib import Path
from typing import List, Dict, Any
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def extract_all_brain_references():
    """Extract ALL references from Brain.one file using multiple methods."""
    
    print("🧠 COMPREHENSIVE BRAIN.ONE REFERENCE EXTRACTION")
    print("=" * 80)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    
    try:
        # Method 1: Use OneNote processor to extract raw content
        from processors.onenote_processor import OneNoteProcessor
        
        processor = OneNoteProcessor()
        print("\n🔄 Step 1: Extracting raw content from OneNote...")
        
        # Extract content using OneNote processor
        result = await processor.process_document(str(brain_file))
        
        if result and 'content' in result:
            raw_content = result['content']
            print(f"✅ Extracted {len(raw_content)} characters of content")
            
            # Save raw content for analysis
            with open("brain_raw_content.txt", "w", encoding="utf-8") as f:
                f.write(raw_content)
            print("💾 Saved raw content to brain_raw_content.txt")
            
            # Method 2: Use enhanced reference extraction
            print("\n🔄 Step 2: Enhanced reference extraction...")
            
            references = await extract_references_comprehensive(raw_content)
            
            if references:
                print(f"✅ Found {len(references)} references!")
                
                # Save references to CSV
                await save_references_to_csv(references, brain_file.name)
                
                # Display first 10 references
                print("\n📚 FIRST 10 REFERENCES FOUND:")
                print("-" * 60)
                for i, ref in enumerate(references[:10], 1):
                    print(f"{i:2d}. {ref.get('title', 'No title')}")
                    print(f"    Authors: <AUTHORS>
                    print(f"    Year: {ref.get('year', 'Unknown')}")
                    print(f"    Journal: {ref.get('journal', 'Unknown')}")
                    print()
                
                if len(references) > 10:
                    print(f"... and {len(references) - 10} more references")
                
            else:
                print("❌ No references found in content")
                
        else:
            print("❌ Failed to extract content from OneNote file")
            
    except Exception as e:
        logger.error(f"Error processing Brain.one file: {e}")
        print(f"❌ Error: {e}")

async def extract_references_comprehensive(content: str) -> List[Dict[str, Any]]:
    """Extract references using multiple comprehensive patterns."""
    
    references = []
    
    # Pattern 1: Numbered references (1. Author...)
    numbered_pattern = r'(\d+)\.\s*([^.]+\..*?)(?=\d+\.|$)'
    numbered_matches = re.findall(numbered_pattern, content, re.DOTALL | re.MULTILINE)
    
    for num, ref_text in numbered_matches:
        ref = parse_reference_text(ref_text.strip(), f"numbered_{num}")
        if ref:
            references.append(ref)
    
    # Pattern 2: Author-year format (Author, A. (Year))
    author_year_pattern = r'([A-Z][a-z]+,?\s+[A-Z]\.?(?:\s+[A-Z]\.?)*)\s*\((\d{4})\)\.\s*([^.]+\..*?)(?=[A-Z][a-z]+,?\s+[A-Z]\.?\s*\(\d{4}\)|$)'
    author_year_matches = re.findall(author_year_pattern, content, re.DOTALL)
    
    for author, year, ref_text in author_year_matches:
        ref = parse_reference_text(f"{author} ({year}). {ref_text}", "author_year")
        if ref:
            references.append(ref)
    
    # Pattern 3: Journal citations (Title. Journal. Year;Volume:Pages)
    journal_pattern = r'([^.]+)\.\s*([A-Z][^.]+)\.\s*(\d{4});?(\d+)?:?(\d+-?\d*)?'
    journal_matches = re.findall(journal_pattern, content)
    
    for title, journal, year, volume, pages in journal_matches:
        if len(title) > 10 and len(journal) > 3:  # Filter out false matches
            ref = {
                'title': title.strip(),
                'journal': journal.strip(),
                'year': year,
                'volume': volume if volume else '',
                'pages': pages if pages else '',
                'extraction_method': 'journal_pattern'
            }
            references.append(ref)
    
    # Pattern 4: DOI references
    doi_pattern = r'(doi:\s*10\.\d+/[^\s]+)'
    doi_matches = re.findall(doi_pattern, content, re.IGNORECASE)
    
    for doi in doi_matches:
        ref = {
            'doi': doi.strip(),
            'extraction_method': 'doi_pattern'
        }
        references.append(ref)
    
    # Pattern 5: PubMed ID references
    pmid_pattern = r'(PMID:\s*\d+)'
    pmid_matches = re.findall(pmid_pattern, content, re.IGNORECASE)
    
    for pmid in pmid_matches:
        ref = {
            'pmid': pmid.strip(),
            'extraction_method': 'pmid_pattern'
        }
        references.append(ref)
    
    # Remove duplicates based on title or DOI
    unique_refs = []
    seen_titles = set()
    seen_dois = set()
    
    for ref in references:
        title = ref.get('title', '').lower()
        doi = ref.get('doi', '')
        
        if title and title not in seen_titles:
            seen_titles.add(title)
            unique_refs.append(ref)
        elif doi and doi not in seen_dois:
            seen_dois.add(doi)
            unique_refs.append(ref)
    
    return unique_refs

def parse_reference_text(ref_text: str, method: str) -> Dict[str, Any]:
    """Parse a reference text into structured components."""
    
    ref = {'raw_text': ref_text, 'extraction_method': method}
    
    # Extract year
    year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
    if year_match:
        ref['year'] = year_match.group()
    
    # Extract title (usually the first sentence or before journal name)
    title_match = re.search(r'^([^.]+\.)', ref_text)
    if title_match:
        ref['title'] = title_match.group(1).strip('.')
    
    # Extract journal (look for italicized or capitalized journal names)
    journal_patterns = [
        r'\.\s*([A-Z][^.]+)\.\s*\d{4}',  # Journal before year
        r'\d{4};\s*([^;]+);',  # Journal between year and volume
        r'In:\s*([^.]+)\.',  # Book chapters
    ]
    
    for pattern in journal_patterns:
        journal_match = re.search(pattern, ref_text)
        if journal_match:
            ref['journal'] = journal_match.group(1).strip()
            break
    
    # Extract authors (beginning of reference)
    author_match = re.search(r'^([^.]+(?:\set\sal\.?)?)', ref_text)
    if author_match:
        ref['authors'] = author_match.group(1).strip()
    
    # Extract volume and pages
    vol_page_match = re.search(r'(\d+):(\d+-?\d*)', ref_text)
    if vol_page_match:
        ref['volume'] = vol_page_match.group(1)
        ref['pages'] = vol_page_match.group(2)
    
    # Extract DOI
    doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', ref_text, re.IGNORECASE)
    if doi_match:
        ref['doi'] = doi_match.group(1)
    
    return ref

async def save_references_to_csv(references: List[Dict[str, Any]], source_file: str):
    """Save references to CSV file."""
    
    import csv
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"references/brain_all_references_{timestamp}.csv"
    
    # Ensure references directory exists
    Path("references").mkdir(exist_ok=True)
    
    fieldnames = [
        'source_document', 'extraction_method', 'reference_text', 'authors', 
        'title', 'year', 'journal', 'volume', 'pages', 'doi', 'pmid',
        'extraction_date', 'confidence_score'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for ref in references:
            row = {
                'source_document': source_file,
                'extraction_method': ref.get('extraction_method', 'comprehensive'),
                'reference_text': ref.get('raw_text', ''),
                'authors': ref.get('authors', ''),
                'title': ref.get('title', ''),
                'year': ref.get('year', ''),
                'journal': ref.get('journal', ''),
                'volume': ref.get('volume', ''),
                'pages': ref.get('pages', ''),
                'doi': ref.get('doi', ''),
                'pmid': ref.get('pmid', ''),
                'extraction_date': datetime.now().isoformat(),
                'confidence_score': 0.8
            }
            writer.writerow(row)
    
    print(f"💾 Saved {len(references)} references to {filename}")

if __name__ == "__main__":
    asyncio.run(extract_all_brain_references())
