"""
Entity management service for the Graphiti application.

This service provides a unified interface for entity operations by delegating
to specialized services for extraction, querying, and relationships.
"""

from typing import List, Dict, Any, Optional

from utils.logging_utils import get_logger
from models.entity import Entity, EntitySummary, EntityTypeCount, EntityTypeCounts

# Import modular services
from services.entity_extraction_service import (
    extract_entities_from_text as _extract_entities_from_text,
    extract_entities_from_document as _extract_entities_from_document
)
from services.entity_query_service import (
    get_entity_count as _get_entity_count, 
    get_entity_types as _get_entity_types, 
    get_entity_counts as _get_entity_counts,
    get_entities_by_type as _get_entities_by_type, 
    get_entity_by_uuid as _get_entity_by_uuid
)
from services.entity_relationship_service import (
    get_entity_relationships as _get_entity_relationships,
    create_entity_relationship as _create_entity_relationship,
    delete_entity_relationship as _delete_entity_relationship,
    get_entity_neighbors as _get_entity_neighbors
)

# Set up logger
logger = get_logger(__name__)


# Delegate functions to modular services
async def extract_entities_from_text(text: str, document_id: str = None, fact_id: str = None, llm_provider: str = 'openai') -> List[Dict[str, Any]]:
    """Extract entities from text."""
    return await _extract_entities_from_text(text, document_id, fact_id, llm_provider)


async def extract_entities_from_document(document_id: str, llm_provider: str = 'openai') -> Dict[str, Any]:
    """Extract entities from a document."""
    return await _extract_entities_from_document(document_id, llm_provider)


async def get_entity_count() -> int:
    """Get the total number of entities."""
    return await _get_entity_count()


async def get_entity_types() -> List[str]:
    """Get all entity types."""
    return await _get_entity_types()


async def get_entity_counts() -> EntityTypeCounts:
    """Get counts of entities by type."""
    return await _get_entity_counts()


async def get_entities_by_type(entity_type: str, limit: int = 100, offset: int = 0) -> List[Entity]:
    """Get entities by type."""
    return await _get_entities_by_type(entity_type, limit, offset)


async def get_entity_by_uuid(entity_uuid: str) -> Optional[Entity]:
    """Get an entity by UUID."""
    return await _get_entity_by_uuid(entity_uuid)


async def get_entity_relationships(entity_uuid: str) -> List[Dict[str, Any]]:
    """Get relationships for an entity."""
    return await _get_entity_relationships(entity_uuid)


async def create_entity_relationship(source_uuid: str, target_uuid: str, relationship_type: str, properties: Dict[str, Any] = None) -> bool:
    """Create a relationship between two entities."""
    return await _create_entity_relationship(source_uuid, target_uuid, relationship_type, properties)


async def delete_entity_relationship(source_uuid: str, target_uuid: str, relationship_type: str = None) -> bool:
    """Delete a relationship between two entities."""
    return await _delete_entity_relationship(source_uuid, target_uuid, relationship_type)


async def get_entity_neighbors(entity_uuid: str, max_depth: int = 1) -> List[Dict[str, Any]]:
    """Get neighboring entities within a specified depth."""
    return await _get_entity_neighbors(entity_uuid, max_depth)


# Additional utility functions
async def get_entity_summary() -> EntitySummary:
    """
    Get a summary of entities in the database.

    Returns:
        EntitySummary with counts and types
    """
    try:
        total_count = await get_entity_count()
        entity_counts = await get_entity_counts()
        
        return EntitySummary(
            total_entities=total_count,
            entity_types=entity_counts.counts
        )
    except Exception as e:
        logger.error(f"Error getting entity summary: {e}")
        return EntitySummary(total_entities=0, entity_types=[])


async def get_entities_paginated(entity_type: str = None, page: int = 1, page_size: int = 50) -> Dict[str, Any]:
    """
    Get entities with pagination.

    Args:
        entity_type: Optional entity type filter
        page: Page number (1-based)
        page_size: Number of entities per page

    Returns:
        Dictionary with entities and pagination info
    """
    try:
        offset = (page - 1) * page_size
        
        if entity_type:
            entities = await get_entities_by_type(entity_type, limit=page_size, offset=offset)
            # For simplicity, we'll estimate total count
            total_count = len(entities) + offset
        else:
            # Get all entities (this would need a new function in entity_query_service)
            entities = []
            total_count = await get_entity_count()
        
        total_pages = (total_count + page_size - 1) // page_size
        
        return {
            "entities": entities,
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": page < total_pages,
                "has_prev": page > 1
            }
        }
    except Exception as e:
        logger.error(f"Error getting paginated entities: {e}")
        return {
            "entities": [],
            "pagination": {
                "page": page,
                "page_size": page_size,
                "total_count": 0,
                "total_pages": 0,
                "has_next": False,
                "has_prev": False
            }
        }
