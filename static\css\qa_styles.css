/* Q&A Interface Styles */

/* Message Styles */
.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    max-width: 100%;
}

.user-message {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    margin-left: 2rem;
}

.assistant-message {
    background-color: #f8f9fa;
    border-left: 4px solid #28a745;
    margin-right: 2rem;
}

.message-content {
    line-height: 1.6;
}

/* Sources Styles */
.sources-list {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
}

.source-item {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: white;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.source-item:last-child {
    margin-bottom: 0;
}

.source-header {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #495057;
}

.source-number {
    color: #007bff;
    font-weight: bold;
}

.source-id {
    color: #6c757d;
    font-size: 0.875rem;
}

.source-metadata {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
    font-style: italic;
}

.source-preview {
    font-size: 0.9rem;
    line-height: 1.5;
    color: #495057;
}

.citation {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
    color: #856404;
}

.math-notation {
    font-family: 'Times New Roman', serif;
    font-style: italic;
}

/* LLM Settings Sidebar */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.form-range {
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.75rem;
    color: #6c757d;
}

#current-model-display {
    background-color: #e9ecef;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

/* Loading Animation */
.loading {
    text-align: center;
    padding: 2rem;
}

.loading p {
    margin-top: 1rem;
    color: #6c757d;
}

/* Conversation Container */
#conversation-container {
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #ffffff;
}

#conversation-container:empty::before {
    content: "No conversation yet. Ask a question to get started!";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 2rem;
}

/* Settings Status */
.alert-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 991.98px) {
    .col-lg-3 {
        margin-top: 2rem;
    }
    
    .user-message {
        margin-left: 0;
    }
    
    .assistant-message {
        margin-right: 0;
    }
}

/* Enhanced Reference Styling */
.source-preview h4 {
    color: #495057;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
}

.source-preview em {
    color: #007bff;
    font-style: italic;
}

.source-preview strong {
    color: #28a745;
    font-weight: 600;
}

/* Citation highlighting in answers */
.message-content [1], .message-content [2], .message-content [3], 
.message-content [4], .message-content [5] {
    background-color: #fff3cd;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-weight: 600;
    color: #856404;
    cursor: pointer;
    transition: background-color 0.2s;
}

.message-content [1]:hover, .message-content [2]:hover, .message-content [3]:hover,
.message-content [4]:hover, .message-content [5]:hover {
    background-color: #ffeaa7;
}

/* Improved scrollbar for conversation container */
#conversation-container::-webkit-scrollbar {
    width: 8px;
}

#conversation-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

#conversation-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

#conversation-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Settings panel animations */
.card-body {
    transition: all 0.3s ease;
}

.form-range:focus {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Advanced settings toggle */
#ollama-advanced-settings {
    transition: all 0.3s ease;
}

/* Button hover effects */
#apply-qa-settings:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

#question-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* Status indicators */
.settings-applied {
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0% { opacity: 0; transform: translateY(-10px); }
    20% { opacity: 1; transform: translateY(0); }
    80% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-10px); }
}
