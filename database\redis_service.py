"""
Redis service for the Graphiti application.
"""

import redis.asyncio as redis
from typing import Optional

from utils.logging_utils import get_logger
from utils.config import get_settings

# Set up logger
logger = get_logger(__name__)

# Redis connection pool
_redis_pool = None

async def get_redis_connection() -> redis.Redis:
    """
    Get a Redis connection from the pool.
    
    Returns:
        Redis connection
    """
    global _redis_pool
    
    if _redis_pool is None:
        # Get Redis settings
        settings = get_settings()
        redis_host = settings.get("database_settings", {}).get("redis_host", "localhost")
        redis_port = settings.get("database_settings", {}).get("redis_port", 6380)
        redis_password = settings.get("database_settings", {}).get("redis_password", None)
        
        # Create connection pool
        try:
            if redis_password:
                _redis_pool = redis.ConnectionPool(
                    host=redis_host,
                    port=redis_port,
                    password=redis_password,
                    decode_responses=True
                )
            else:
                _redis_pool = redis.ConnectionPool(
                    host=redis_host,
                    port=redis_port,
                    decode_responses=True
                )
                
            logger.info(f"Created Redis connection pool at {redis_host}:{redis_port}")
        except Exception as e:
            logger.error(f"Error creating Redis connection pool: {str(e)}")
            raise
    
    # Get connection from pool
    try:
        return redis.Redis(connection_pool=_redis_pool)
    except Exception as e:
        logger.error(f"Error getting Redis connection: {str(e)}")
        raise
