#!/usr/bin/env python3
"""
Test script for the complete Echinacea document ingestion pipeline.
Tests: Mistral OCR → Entities → Relationships → Embeddings → References → CSV
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from typing import Dict, Any

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent))

from services.document_processing_service import get_document_processing_service
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = get_logger(__name__)

async def test_complete_pipeline(file_path: str) -> Dict[str, Any]:
    """
    Test the complete document ingestion pipeline.
    
    Args:
        file_path: Path to the Echinacea document
        
    Returns:
        Complete processing results
    """
    logger.info("🚀 Starting complete Echinacea document ingestion pipeline test")
    logger.info(f"📄 Processing file: {file_path}")
    
    if not os.path.exists(file_path):
        logger.error(f"❌ File not found: {file_path}")
        return {"success": False, "error": "File not found"}
    
    try:
        # Get the document processing service
        service = await get_document_processing_service()
        
        # Process the document with all features enabled
        logger.info("🔄 Starting document processing with all features enabled...")
        result = await service.process_document(
            file_path=file_path,
            chunk_size=1200,
            overlap=0,
            extract_entities=True,
            extract_references=True,
            extract_metadata=True,
            generate_embeddings=True
        )
        
        # Log detailed results
        if result.get("success", False):
            logger.info("✅ Document processing completed successfully!")
            logger.info(f"📊 Processing Summary:")
            logger.info(f"   📝 Episode ID: {result.get('episode_id', 'N/A')}")
            logger.info(f"   📄 Text chunks: {result.get('chunks', 0)}")
            logger.info(f"   🏷️  Entities extracted: {result.get('entities_extracted', 0)}")
            logger.info(f"   🔗 Relationships found: {result.get('duplicates_found', 0)} duplicates, {result.get('entities_merged', 0)} merged")
            logger.info(f"   📚 References extracted: {result.get('references_extracted', 0)}")
            logger.info(f"   🧠 Embeddings generated: {result.get('embeddings_generated', 0)}")
            logger.info(f"   🔧 Embedding model: {result.get('embedding_model', 'N/A')}")
            logger.info(f"   📋 Metadata extracted: {result.get('metadata_extracted', False)}")
            
            # Test specific components
            await test_individual_components(result.get('episode_id'))
            
        else:
            logger.error(f"❌ Document processing failed: {result.get('error', 'Unknown error')}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error in pipeline test: {e}", exc_info=True)
        return {"success": False, "error": str(e)}

async def test_individual_components(episode_id: str):
    """Test individual components of the pipeline."""
    logger.info("🔍 Testing individual pipeline components...")
    
    try:
        # Test entity extraction
        from services.entity_extraction_service import extract_entities_from_document
        logger.info("🏷️  Testing entity extraction...")
        entity_result = await extract_entities_from_document(episode_id, 'openrouter')
        logger.info(f"   Entities: {entity_result.get('entities_extracted', 0)}")
        
        # Test embeddings
        from services.embedding_processor import EmbeddingProcessor
        logger.info("🧠 Testing embedding generation...")
        embedding_processor = EmbeddingProcessor()
        embedding_result = await embedding_processor.generate_embeddings_for_document(episode_id)
        logger.info(f"   Embeddings: {embedding_result.get('embeddings_generated', 0)} using {embedding_result.get('embedding_model', 'N/A')}")
        
        # Test database queries
        from database.database_service import get_falkordb_adapter
        logger.info("🗄️  Testing database queries...")
        adapter = await get_falkordb_adapter()
        
        # Query facts
        facts_query = f"MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact) RETURN count(f) as fact_count"
        facts_result = adapter.execute_cypher(facts_query)
        fact_count = facts_result[1][0][0] if facts_result and len(facts_result) > 1 and facts_result[1] else 0
        logger.info(f"   Facts in database: {fact_count}")
        
        # Query entities
        entities_query = f"MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(ent:Entity) RETURN count(DISTINCT ent) as entity_count"
        entities_result = adapter.execute_cypher(entities_query)
        entity_count = entities_result[1][0][0] if entities_result and len(entities_result) > 1 and entities_result[1] else 0
        logger.info(f"   Entities in database: {entity_count}")
        
        logger.info("✅ Individual component testing completed")
        
    except Exception as e:
        logger.error(f"❌ Error testing individual components: {e}")

async def main():
    """Main function to run the pipeline test."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test complete Echinacea document ingestion pipeline")
    parser.add_argument("file_path", help="Path to the Echinacea document (PDF)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose logging")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Test the complete pipeline
    result = await test_complete_pipeline(args.file_path)
    
    # Final summary
    if result.get("success", False):
        logger.info("🎉 PIPELINE TEST COMPLETED SUCCESSFULLY!")
        logger.info("📋 Summary:")
        logger.info(f"   ✅ Mistral OCR: Text extracted")
        logger.info(f"   ✅ Entities: {result.get('entities_extracted', 0)} extracted")
        logger.info(f"   ✅ Relationships: {result.get('entities_merged', 0)} merged")
        logger.info(f"   ✅ Embeddings: {result.get('embeddings_generated', 0)} generated")
        logger.info(f"   ✅ References: {result.get('references_extracted', 0)} extracted")
        logger.info(f"   ✅ Database: Episode {result.get('episode_id', 'N/A')} created")
    else:
        logger.error("❌ PIPELINE TEST FAILED!")
        logger.error(f"Error: {result.get('error', 'Unknown error')}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
