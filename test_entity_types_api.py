#!/usr/bin/env python3
"""
Test script to check entity types API.
"""

import requests

def test_entity_types_api():
    """Test the entity types API endpoint"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Entity Types API")
    print("=" * 50)
    
    try:
        response = requests.get(f"{base_url}/api/entity-types", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response format: {type(data)}")
            print(f"Keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if 'entity_types' in data:
                entity_types = data['entity_types']
                print(f"Entity types count: {len(entity_types)}")
                print(f"Sample types: {entity_types[:5] if entity_types else 'None'}")
                print(f"Type of first item: {type(entity_types[0]) if entity_types else 'None'}")
            
            print(f"Full response: {data}")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entity_types_api()
