"""
Test script to directly test the API and display the results
"""

import requests
import json
import webbrowser
import os
import time
from datetime import datetime

def test_api_and_display():
    """Test the API and display the results in a browser"""
    # Make a request to the API
    print("Making request to API...")
    response = requests.get("http://localhost:9753/api/answer?q=What%20is%20ThymoDream%3F")

    # Check if the response is successful
    if response.status_code == 200:
        # Parse the response
        data = response.json()

        # Print the response
        print("API Response:")
        print(json.dumps(data, indent=2))

        # Create an HTML file to display the results
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>API Test Results</title>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    max-width: 800px;
                    margin: 0 auto;
                    padding: 20px;
                }}
                .message {{
                    margin-bottom: 15px;
                    padding: 15px;
                    border-radius: 10px;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }}
                .user-message {{
                    background-color: #e9ecef;
                    margin-left: auto;
                    border-top-right-radius: 0;
                }}
                .assistant-message {{
                    background-color: #f0f7ff;
                    margin-right: auto;
                    border-top-left-radius: 0;
                }}
                .source-item {{
                    margin-top: 10px;
                    padding: 10px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    border: 1px solid #dee2e6;
                }}
                .source-header {{
                    font-weight: bold;
                    margin-bottom: 5px;
                }}
                .source-preview {{
                    margin-top: 5px;
                }}
                pre {{
                    background-color: #f8f9fa;
                    padding: 10px;
                    border-radius: 5px;
                    overflow-x: auto;
                }}
            </style>
        </head>
        <body>
            <h1>API Test Results</h1>
            <p>Test run at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

            <h2>Question</h2>
            <div class="message user-message">
                <p>What is ThymoDream?</p>
            </div>

            <h2>Answer</h2>
            <div class="message assistant-message">
                <p>{data.get('answer', 'No answer provided')}</p>
            </div>

            <h2>Sources</h2>
            <div id="sources">
        """

        # Add sources if available
        if 'sources' in data and data['sources']:
            for source in data['sources']:
                html_content += f"""
                <div class="source-item">
                    <div class="source-header">
                        <span class="source-document">{source.get('document', 'Unknown document')}</span>
                        <span class="source-score">Score: {float(source.get('score', 0)):.2f}</span>
                    </div>
                    <div class="source-preview">{source.get('preview', 'No preview available')}</div>
                </div>
                """
        else:
            html_content += "<p>No sources available</p>"

        # Add raw JSON response
        html_content += f"""
            </div>

            <h2>Raw JSON Response</h2>
            <pre>{json.dumps(data, indent=2)}</pre>
        </body>
        </html>
        """

        # Write the HTML file
        with open("api_test_results.html", "w", encoding="utf-8") as f:
            f.write(html_content)

        # Open the HTML file in a browser
        print("Opening results in browser...")
        webbrowser.open("file://" + os.path.abspath("api_test_results.html"))
    else:
        print(f"API call failed with status code {response.status_code}")
        print(f"Response: {response.text}")

if __name__ == "__main__":
    test_api_and_display()
