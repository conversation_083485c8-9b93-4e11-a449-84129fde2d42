"""
Embedding Sync Service for synchronizing embeddings between FalkorDB and Redis Vector Search.

This service provides functions to sync embeddings from FalkorDB to Redis Vector Search,
enabling efficient vector similarity search while maintaining the graph structure in FalkorDB.
"""

import json
import logging
import asyncio
from typing import List, Dict, Any, Optional, Union

# Set up logger
logger = logging.getLogger(__name__)

class EmbeddingSyncService:
    """Service for synchronizing embeddings between FalkorDB and Redis Vector Search."""

    def __init__(self):
        """Initialize the embedding sync service."""
        self.is_syncing = False

    async def sync_embeddings_for_episode(self, episode_id: str) -> Dict[str, Any]:
        """
        Sync embeddings for a specific episode from FalkorDB to Redis Vector Search.

        Args:
            episode_id: UUID of the episode

        Returns:
            Result dictionary with sync details
        """
        try:
            # Import here to avoid circular imports
            from database.falkordb_adapter import GraphitiFalkorDBAdapter
            from utils.redis_vector_search import get_redis_vector_search_client, create_vector_index, store_embedding

            # Get FalkorDB adapter
            falkordb_adapter = GraphitiFalkorDBAdapter()

            # Create vector index if it doesn't exist
            create_vector_index()

            # This method is now primarily for migration purposes
            # Get facts with embedding_id for this episode
            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
            WHERE f.embedding_id IS NOT NULL
            RETURN f.uuid AS uuid, f.body AS body, f.embedding_id AS embedding_id,
                   f.embedding AS embedding, f.embedding_in_redis AS in_redis
            """

            result = falkordb_adapter.execute_cypher(query)

            if not result or len(result) < 2:
                logger.warning(f"No facts with embedding metadata found for episode {episode_id}")
                return {
                    "success": False,
                    "error": "No facts with embedding metadata found",
                    "episode_id": episode_id,
                    "facts_synced": 0
                }

            # Skip header row
            facts = result[1:]

            logger.info(f"Found {len(facts)} facts with embedding metadata for episode {episode_id}")

            # Sync embeddings to Redis Vector Search
            facts_synced = 0

            for fact in facts:
                fact_uuid = fact[0]
                body = fact[1]
                embedding_id = fact[2]
                embedding_json = fact[3]
                in_redis = fact[4] if len(fact) > 4 else False

                # Skip if already in Redis
                if in_redis:
                    logger.debug(f"Embedding for fact {fact_uuid} already in Redis, skipping")
                    facts_synced += 1
                    continue

                # For migration: if we have the embedding in FalkorDB, use it
                success = False
                if embedding_json:
                    try:
                        # Parse embedding from JSON string
                        embedding = json.loads(embedding_json)

                        # Store in Redis Vector Search
                        success = store_embedding(
                            fact_uuid=fact_uuid,
                            episode_uuid=episode_id,
                            body=body,
                            embedding=embedding,
                            embedding_id=embedding_id
                        )

                        if success:
                            # Update FalkorDB to mark as in Redis
                            update_query = f"""
                            MATCH (f:Fact {{uuid: '{fact_uuid}'}})
                            SET f.embedding_in_redis = true
                            RETURN f.uuid
                            """
                            falkordb_adapter.execute_cypher(update_query)
                    except Exception as e:
                        logger.error(f"Error migrating embedding for fact {fact_uuid}: {e}")
                        success = False

                if success:
                    facts_synced += 1

            logger.info(f"Synced {facts_synced} facts with embeddings for episode {episode_id}")

            return {
                "success": True,
                "episode_id": episode_id,
                "facts_synced": facts_synced
            }

        except Exception as e:
            logger.error(f"Error syncing embeddings for episode {episode_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "episode_id": episode_id,
                "facts_synced": 0
            }

    async def sync_all_embeddings(self) -> Dict[str, Any]:
        """
        Sync all embeddings from FalkorDB to Redis Vector Search.

        Returns:
            Result dictionary with sync details
        """
        if self.is_syncing:
            logger.warning("Embedding sync already in progress")
            return {
                "success": False,
                "error": "Sync already in progress",
                "episodes_synced": 0,
                "facts_synced": 0
            }

        self.is_syncing = True

        try:
            # Import here to avoid circular imports
            from database.falkordb_adapter import get_falkordb_adapter

            # Get FalkorDB adapter
            falkordb_adapter = await get_falkordb_adapter()

            # Get all episodes with facts that have embeddings
            query = """
            MATCH (e:Episode)-[:CONTAINS]->(f:Fact)
            WHERE f.embedding IS NOT NULL
            RETURN DISTINCT e.uuid AS uuid
            """

            result = falkordb_adapter.execute_cypher(query)

            if not result or len(result) < 2:
                logger.warning("No episodes with embeddings found")
                self.is_syncing = False
                return {
                    "success": False,
                    "error": "No episodes with embeddings found",
                    "episodes_synced": 0,
                    "facts_synced": 0
                }

            # Skip header row
            episodes = [row[0] for row in result[1:]]

            logger.info(f"Found {len(episodes)} episodes with embeddings")

            # Sync embeddings for each episode
            episodes_synced = 0
            facts_synced = 0

            for episode_id in episodes:
                result = await self.sync_embeddings_for_episode(episode_id)

                if result["success"]:
                    episodes_synced += 1
                    facts_synced += result["facts_synced"]

            logger.info(f"Synced embeddings for {episodes_synced} episodes ({facts_synced} facts)")

            self.is_syncing = False
            return {
                "success": True,
                "episodes_synced": episodes_synced,
                "facts_synced": facts_synced
            }

        except Exception as e:
            logger.error(f"Error syncing all embeddings: {e}")
            self.is_syncing = False
            return {
                "success": False,
                "error": str(e),
                "episodes_synced": 0,
                "facts_synced": 0
            }

# Singleton instance
_embedding_sync_service = None

async def get_embedding_sync_service() -> EmbeddingSyncService:
    """
    Get the embedding sync service instance.

    Returns:
        EmbeddingSyncService instance
    """
    global _embedding_sync_service

    if _embedding_sync_service is None:
        _embedding_sync_service = EmbeddingSyncService()

    return _embedding_sync_service
