"""
Dynamic threshold calculation for entity deduplication.

This module implements adaptive similarity thresholds that adjust based on:
- Entity type and domain context
- Historical accuracy and performance metrics
- Confidence scores and uncertainty measures
"""

import logging
import math
import time
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from collections import defaultdict, deque
import json
import os

from entity_deduplication.models import EntityForDeduplication, EntityMatch
from entity_deduplication.config import (
    ENTITY_TYPE_THRESHOLDS,
    USE_DYNAMIC_THRESHOLDS,
    DYNAMIC_THRESHOLD_LEARNING_RATE,
    DYNAMIC_THRESHOLD_MIN_SAMPLES,
    NAME_SIMILARITY_THRESHOLD,
    MIN_OVERALL_SIMILARITY
)

logger = logging.getLogger(__name__)


@dataclass
class ThresholdMetrics:
    """Metrics for threshold performance tracking."""
    true_positives: int = 0
    false_positives: int = 0
    true_negatives: int = 0
    false_negatives: int = 0
    total_comparisons: int = 0
    
    @property
    def precision(self) -> float:
        """Calculate precision score."""
        if self.true_positives + self.false_positives == 0:
            return 0.0
        return self.true_positives / (self.true_positives + self.false_positives)
    
    @property
    def recall(self) -> float:
        """Calculate recall score."""
        if self.true_positives + self.false_negatives == 0:
            return 0.0
        return self.true_positives / (self.true_positives + self.false_negatives)
    
    @property
    def f1_score(self) -> float:
        """Calculate F1 score."""
        if self.precision + self.recall == 0:
            return 0.0
        return 2 * (self.precision * self.recall) / (self.precision + self.recall)


@dataclass
class DynamicThreshold:
    """Dynamic threshold configuration for an entity type."""
    entity_type: str
    name_similarity_threshold: float
    semantic_similarity_threshold: float
    confidence_weight: float
    metrics: ThresholdMetrics
    last_updated: float
    sample_history: deque  # Recent similarity scores and outcomes


class DynamicThresholdCalculator:
    """
    Calculates and maintains dynamic similarity thresholds for entity deduplication.
    
    This class adapts thresholds based on:
    - Entity type characteristics
    - Historical performance metrics
    - Domain-specific patterns
    - Confidence scores
    """
    
    def __init__(self, metrics_file: Optional[str] = None):
        """
        Initialize the dynamic threshold calculator.
        
        Args:
            metrics_file: Optional file path to persist threshold metrics
        """
        self.metrics_file = metrics_file or "entity_deduplication_metrics.json"
        self.thresholds: Dict[str, DynamicThreshold] = {}
        self.global_metrics = ThresholdMetrics()
        self._load_metrics()
        self._initialize_thresholds()
    
    def _initialize_thresholds(self):
        """Initialize thresholds for all entity types."""
        for entity_type, config in ENTITY_TYPE_THRESHOLDS.items():
            if entity_type not in self.thresholds:
                self.thresholds[entity_type] = DynamicThreshold(
                    entity_type=entity_type,
                    name_similarity_threshold=config['name_similarity'],
                    semantic_similarity_threshold=config['semantic_similarity'],
                    confidence_weight=config['confidence_weight'],
                    metrics=ThresholdMetrics(),
                    last_updated=0.0,
                    sample_history=deque(maxlen=100)  # Keep last 100 samples
                )
    
    def get_threshold(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        similarity_type: str = "name"
    ) -> float:
        """
        Get dynamic threshold for entity comparison.
        
        Args:
            entity1: First entity
            entity2: Second entity
            similarity_type: Type of similarity ("name" or "semantic")
            
        Returns:
            Dynamic threshold value
        """
        if not USE_DYNAMIC_THRESHOLDS:
            # Fall back to static thresholds
            if similarity_type == "name":
                return NAME_SIMILARITY_THRESHOLD
            else:
                return MIN_OVERALL_SIMILARITY
        
        # Determine entity type (use the more specific type if different)
        entity_type = self._get_entity_type_for_threshold(entity1, entity2)
        
        # Get base threshold for this entity type
        threshold_config = self.thresholds.get(entity_type, self.thresholds['default'])
        
        if similarity_type == "name":
            base_threshold = threshold_config.name_similarity_threshold
        else:
            base_threshold = threshold_config.semantic_similarity_threshold
        
        # Apply dynamic adjustments
        adjusted_threshold = self._apply_dynamic_adjustments(
            base_threshold, entity1, entity2, threshold_config
        )
        
        return max(0.1, min(0.99, adjusted_threshold))  # Clamp between 0.1 and 0.99
    
    def _get_entity_type_for_threshold(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> str:
        """Determine which entity type to use for threshold calculation."""
        # If types are the same, use that type
        if entity1.type == entity2.type:
            return entity1.type if entity1.type in ENTITY_TYPE_THRESHOLDS else 'default'
        
        # If types are different, use the more specific one (non-default)
        for entity_type in [entity1.type, entity2.type]:
            if entity_type in ENTITY_TYPE_THRESHOLDS and entity_type != 'default':
                return entity_type
        
        return 'default'
    
    def _apply_dynamic_adjustments(
        self,
        base_threshold: float,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        threshold_config: DynamicThreshold
    ) -> float:
        """Apply dynamic adjustments to base threshold."""
        adjusted_threshold = base_threshold
        
        # Confidence-based adjustment
        confidence_adjustment = self._calculate_confidence_adjustment(
            entity1, entity2, threshold_config.confidence_weight
        )
        adjusted_threshold += confidence_adjustment
        
        # Performance-based adjustment
        performance_adjustment = self._calculate_performance_adjustment(threshold_config)
        adjusted_threshold += performance_adjustment
        
        # Context-based adjustment
        context_adjustment = self._calculate_context_adjustment(entity1, entity2)
        adjusted_threshold += context_adjustment
        
        return adjusted_threshold
    
    def _calculate_confidence_adjustment(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        confidence_weight: float
    ) -> float:
        """Calculate threshold adjustment based on entity confidence scores."""
        # Get confidence scores (default to 0.5 if not available)
        conf1 = entity1.confidence or 0.5
        conf2 = entity2.confidence or 0.5
        
        # Lower confidence entities should have higher thresholds (be more conservative)
        avg_confidence = (conf1 + conf2) / 2
        confidence_factor = (0.5 - avg_confidence) * confidence_weight
        
        # Scale the adjustment (max ±0.1)
        return max(-0.1, min(0.1, confidence_factor * 0.2))
    
    def _calculate_performance_adjustment(self, threshold_config: DynamicThreshold) -> float:
        """Calculate threshold adjustment based on historical performance."""
        metrics = threshold_config.metrics
        
        if metrics.total_comparisons < DYNAMIC_THRESHOLD_MIN_SAMPLES:
            return 0.0  # Not enough data for adjustment
        
        # If precision is low (too many false positives), increase threshold
        # If recall is low (too many false negatives), decrease threshold
        precision = metrics.precision
        recall = metrics.recall
        
        if precision < 0.7:  # Too many false positives
            return 0.05 * DYNAMIC_THRESHOLD_LEARNING_RATE
        elif recall < 0.7:  # Too many false negatives
            return -0.05 * DYNAMIC_THRESHOLD_LEARNING_RATE
        
        return 0.0
    
    def _calculate_context_adjustment(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication
    ) -> float:
        """Calculate threshold adjustment based on context."""
        adjustment = 0.0
        
        # Same document context - entities from same document are more likely to be different
        if (entity1.source_document_id and entity2.source_document_id and
            entity1.source_document_id == entity2.source_document_id):
            adjustment += 0.02  # Slightly higher threshold
        
        # Description similarity - if descriptions are very different, be more conservative
        if entity1.description and entity2.description:
            desc_similarity = self._calculate_description_similarity(
                entity1.description, entity2.description
            )
            if desc_similarity < 0.3:  # Very different descriptions
                adjustment += 0.03
        
        return adjustment
    
    def _calculate_description_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate simple description similarity."""
        if not desc1 or not desc2:
            return 0.0
        
        # Simple word overlap similarity
        words1 = set(desc1.lower().split())
        words2 = set(desc2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    def update_metrics(
        self,
        entity1: EntityForDeduplication,
        entity2: EntityForDeduplication,
        similarity_score: float,
        predicted_match: bool,
        actual_match: Optional[bool] = None
    ):
        """
        Update threshold metrics based on comparison results.
        
        Args:
            entity1: First entity
            entity2: Second entity
            similarity_score: Calculated similarity score
            predicted_match: Whether algorithm predicted a match
            actual_match: Ground truth (if available)
        """
        entity_type = self._get_entity_type_for_threshold(entity1, entity2)
        threshold_config = self.thresholds.get(entity_type, self.thresholds['default'])
        
        # Add to sample history
        threshold_config.sample_history.append({
            'similarity_score': similarity_score,
            'predicted_match': predicted_match,
            'actual_match': actual_match,
            'timestamp': time.time()
        })
        
        # Update metrics if we have ground truth
        if actual_match is not None:
            metrics = threshold_config.metrics
            metrics.total_comparisons += 1
            
            if predicted_match and actual_match:
                metrics.true_positives += 1
            elif predicted_match and not actual_match:
                metrics.false_positives += 1
            elif not predicted_match and actual_match:
                metrics.false_negatives += 1
            else:
                metrics.true_negatives += 1
            
            # Update global metrics
            self.global_metrics.total_comparisons += 1
            if predicted_match and actual_match:
                self.global_metrics.true_positives += 1
            elif predicted_match and not actual_match:
                self.global_metrics.false_positives += 1
            elif not predicted_match and actual_match:
                self.global_metrics.false_negatives += 1
            else:
                self.global_metrics.true_negatives += 1
        
        # Periodically save metrics
        if threshold_config.metrics.total_comparisons % 50 == 0:
            self._save_metrics()
    
    def _load_metrics(self):
        """Load metrics from file if it exists."""
        if os.path.exists(self.metrics_file):
            try:
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    # Load global metrics
                    if 'global_metrics' in data:
                        gm = data['global_metrics']
                        self.global_metrics = ThresholdMetrics(**gm)
                    
                    # Load threshold-specific metrics
                    if 'thresholds' in data:
                        for entity_type, threshold_data in data['thresholds'].items():
                            metrics_data = threshold_data.get('metrics', {})
                            metrics = ThresholdMetrics(**metrics_data)
                            
                            self.thresholds[entity_type] = DynamicThreshold(
                                entity_type=entity_type,
                                name_similarity_threshold=threshold_data.get('name_similarity_threshold', 0.85),
                                semantic_similarity_threshold=threshold_data.get('semantic_similarity_threshold', 0.8),
                                confidence_weight=threshold_data.get('confidence_weight', 1.0),
                                metrics=metrics,
                                last_updated=threshold_data.get('last_updated', 0.0),
                                sample_history=deque(threshold_data.get('sample_history', []), maxlen=100)
                            )
            except Exception as e:
                logger.warning(f"Failed to load metrics from {self.metrics_file}: {e}")
    
    def _save_metrics(self):
        """Save metrics to file."""
        try:
            data = {
                'global_metrics': {
                    'true_positives': self.global_metrics.true_positives,
                    'false_positives': self.global_metrics.false_positives,
                    'true_negatives': self.global_metrics.true_negatives,
                    'false_negatives': self.global_metrics.false_negatives,
                    'total_comparisons': self.global_metrics.total_comparisons
                },
                'thresholds': {}
            }
            
            for entity_type, threshold_config in self.thresholds.items():
                data['thresholds'][entity_type] = {
                    'name_similarity_threshold': threshold_config.name_similarity_threshold,
                    'semantic_similarity_threshold': threshold_config.semantic_similarity_threshold,
                    'confidence_weight': threshold_config.confidence_weight,
                    'last_updated': threshold_config.last_updated,
                    'metrics': {
                        'true_positives': threshold_config.metrics.true_positives,
                        'false_positives': threshold_config.metrics.false_positives,
                        'true_negatives': threshold_config.metrics.true_negatives,
                        'false_negatives': threshold_config.metrics.false_negatives,
                        'total_comparisons': threshold_config.metrics.total_comparisons
                    },
                    'sample_history': list(threshold_config.sample_history)
                }
            
            with open(self.metrics_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save metrics to {self.metrics_file}: {e}")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get performance report for all entity types."""
        report = {
            'global_metrics': {
                'precision': self.global_metrics.precision,
                'recall': self.global_metrics.recall,
                'f1_score': self.global_metrics.f1_score,
                'total_comparisons': self.global_metrics.total_comparisons
            },
            'entity_types': {}
        }
        
        for entity_type, threshold_config in self.thresholds.items():
            metrics = threshold_config.metrics
            report['entity_types'][entity_type] = {
                'precision': metrics.precision,
                'recall': metrics.recall,
                'f1_score': metrics.f1_score,
                'total_comparisons': metrics.total_comparisons,
                'current_thresholds': {
                    'name_similarity': threshold_config.name_similarity_threshold,
                    'semantic_similarity': threshold_config.semantic_similarity_threshold
                }
            }
        
        return report


# Global instance
_threshold_calculator = None


def get_threshold_calculator() -> DynamicThresholdCalculator:
    """Get the global threshold calculator instance."""
    global _threshold_calculator
    if _threshold_calculator is None:
        _threshold_calculator = DynamicThresholdCalculator()
    return _threshold_calculator
