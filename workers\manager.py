"""
Worker manager for the Graphiti document processing pipeline.

This module provides a worker manager that coordinates different types of workers
for processing documents in parallel.
"""

import os
import asyncio
import logging
import multiprocessing
from typing import Dict, List, Any, Optional, Union, Callable
from pathlib import Path
import time
import json

# Import base classes and enums
from workers.base import WorkerType, WorkerStatus, WorkerStats
from workers.processors import PROCESSOR_MAP

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

class WorkerManager:
    """
    Manager for document processing workers.

    This class manages different types of workers for processing documents in parallel.
    It distributes tasks to workers and monitors their status.
    """

    def __init__(self, config: Dict[str, Any] = None):
        """
        Initialize the worker manager.

        Args:
            config: Configuration dictionary
        """
        self.config = config or {}

        # Default worker counts
        self.default_worker_counts = {
            WorkerType.DOCUMENT_PROCESSOR: 2,
            WorkerType.ENTITY_EXTRACTOR: 3,
            WorkerType.REFERENCE_EXTRACTOR: 2,
            WorkerType.EMBEDDING_GENERATOR: 2,
            WorkerType.DATABASE_WRITER: 2
        }

        # Worker counts from config or defaults
        self.worker_counts = {
            worker_type: self.config.get(f"{worker_type.value}_workers",
                                         self.default_worker_counts[worker_type])
            for worker_type in WorkerType
        }

        # Task queues for each worker type
        self.queues = {
            worker_type: asyncio.Queue()
            for worker_type in WorkerType
        }

        # Worker processes/tasks
        self.workers = {
            worker_type: []
            for worker_type in WorkerType
        }

        # Worker status
        self.worker_status = {
            worker_type: {}
            for worker_type in WorkerType
        }

        # Results queue
        self.results_queue = asyncio.Queue()

        # Processing statistics
        self.stats = WorkerStats()

        # Flag to indicate if the manager is running
        self.is_running = False

    async def start(self):
        """Start the worker manager and all workers."""
        if self.is_running:
            logger.warning("Worker manager is already running")
            return

        logger.info("Starting worker manager")
        self.is_running = True
        self.stats.start()

        # Start workers for each type
        for worker_type in WorkerType:
            await self._start_workers(worker_type)

        # Start the result collector
        asyncio.create_task(self._collect_results())

        logger.info("Worker manager started")

    async def stop(self):
        """Stop the worker manager and all workers."""
        if not self.is_running:
            logger.warning("Worker manager is not running")
            return

        logger.info("Stopping worker manager")
        self.is_running = False
        self.stats.stop()

        # Stop all workers
        for worker_type in WorkerType:
            await self._stop_workers(worker_type)

        logger.info("Worker manager stopped")

    async def _start_workers(self, worker_type: WorkerType):
        """
        Start workers of a specific type.

        Args:
            worker_type: Type of workers to start
        """
        worker_count = self.worker_counts[worker_type]
        logger.info(f"Starting {worker_count} {worker_type.value} workers")

        for i in range(worker_count):
            worker_id = f"{worker_type.value}_{i}"
            worker_task = asyncio.create_task(
                self._worker_loop(worker_type, worker_id)
            )
            self.workers[worker_type].append(worker_task)
            self.worker_status[worker_type][worker_id] = WorkerStatus.IDLE

        logger.info(f"Started {worker_count} {worker_type.value} workers")

    async def _stop_workers(self, worker_type: WorkerType):
        """
        Stop workers of a specific type.

        Args:
            worker_type: Type of workers to stop
        """
        logger.info(f"Stopping {worker_type.value} workers")

        # Cancel all worker tasks
        for worker_task in self.workers[worker_type]:
            worker_task.cancel()

        # Wait for all worker tasks to complete
        if self.workers[worker_type]:
            await asyncio.gather(*self.workers[worker_type], return_exceptions=True)

        # Clear the worker list
        self.workers[worker_type] = []

        # Update worker status
        for worker_id in self.worker_status[worker_type]:
            self.worker_status[worker_type][worker_id] = WorkerStatus.TERMINATED

        logger.info(f"Stopped {worker_type.value} workers")

    async def _worker_loop(self, worker_type: WorkerType, worker_id: str):
        """
        Main loop for a worker.

        Args:
            worker_type: Type of worker
            worker_id: Unique ID for the worker
        """
        logger.info(f"Worker {worker_id} started")

        try:
            while self.is_running:
                # Update status to idle
                self.worker_status[worker_type][worker_id] = WorkerStatus.IDLE

                # Get a task from the queue
                try:
                    task = await asyncio.wait_for(
                        self.queues[worker_type].get(),
                        timeout=5.0  # Check is_running every 5 seconds
                    )
                except asyncio.TimeoutError:
                    # No task available, check if we should continue running
                    continue

                # Update status to busy
                self.worker_status[worker_type][worker_id] = WorkerStatus.BUSY

                # Process the task
                try:
                    logger.info(f"Worker {worker_id} processing task: {task.get('id', 'unknown')}")
                    result = await self._process_task(worker_type, task)

                    # Put the result in the results queue
                    await self.results_queue.put({
                        "worker_id": worker_id,
                        "worker_type": worker_type.value,
                        "task_id": task.get("id"),
                        "success": True,
                        "result": result
                    })

                except Exception as e:
                    logger.error(f"Worker {worker_id} error processing task: {e}")

                    # Update status to error
                    self.worker_status[worker_type][worker_id] = WorkerStatus.ERROR

                    # Put the error in the results queue
                    await self.results_queue.put({
                        "worker_id": worker_id,
                        "worker_type": worker_type.value,
                        "task_id": task.get("id"),
                        "success": False,
                        "error": str(e)
                    })

                    # Increment error count
                    self.stats.increment_errors()

                finally:
                    # Mark the task as done
                    self.queues[worker_type].task_done()

        except asyncio.CancelledError:
            logger.info(f"Worker {worker_id} cancelled")

        except Exception as e:
            logger.error(f"Worker {worker_id} unexpected error: {e}")

        finally:
            # Update status to terminated
            self.worker_status[worker_type][worker_id] = WorkerStatus.TERMINATED
            logger.info(f"Worker {worker_id} stopped")

    async def _process_task(self, worker_type: WorkerType, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a task based on the worker type.

        Args:
            worker_type: Type of worker
            task: Task to process

        Returns:
            Result of the task
        """
        # Get the processor function for this worker type
        processor_func = PROCESSOR_MAP.get(worker_type)

        if not processor_func:
            raise ValueError(f"No processor function found for worker type: {worker_type}")

        # Process the task using the processor function
        return await processor_func(task, self.add_task)

    async def _collect_results(self):
        """Collect and process results from workers."""
        logger.info("Result collector started")

        try:
            while self.is_running:
                try:
                    # Get a result from the queue with timeout
                    result = await asyncio.wait_for(
                        self.results_queue.get(),
                        timeout=5.0  # Check is_running every 5 seconds
                    )

                    # Process the result
                    await self._process_result(result)

                    # Mark the result as processed
                    self.results_queue.task_done()

                except asyncio.TimeoutError:
                    # No result available, check if we should continue running
                    continue

        except asyncio.CancelledError:
            logger.info("Result collector cancelled")

        except Exception as e:
            logger.error(f"Result collector error: {e}")

        finally:
            logger.info("Result collector stopped")

    async def _process_result(self, result: Dict[str, Any]):
        """
        Process a result from a worker.

        Args:
            result: Result from a worker
        """
        # Update statistics based on the worker type and result
        worker_type = result.get("worker_type")

        if result.get("success", False):
            if worker_type == WorkerType.DOCUMENT_PROCESSOR.value:
                self.stats.increment_documents()
            elif worker_type == WorkerType.ENTITY_EXTRACTOR.value:
                entities_count = len(result.get("result", {}).get("entities", []))
                if entities_count > 0:
                    self.stats.increment_entities(entities_count)
            elif worker_type == WorkerType.REFERENCE_EXTRACTOR.value:
                references_count = len(result.get("result", {}).get("references", []))
                if references_count > 0:
                    self.stats.increment_references(references_count)
            elif worker_type == WorkerType.EMBEDDING_GENERATOR.value:
                embeddings_count = len(result.get("result", {}).get("embeddings", []))
                if embeddings_count > 0:
                    self.stats.increment_embeddings(embeddings_count)
        else:
            # Log the error
            logger.error(f"Worker {result.get('worker_id')} error: {result.get('error')}")

            # Increment error count
            self.stats.increment_errors()

    # Task addition methods

    async def add_task(self, worker_type: WorkerType, task: Dict[str, Any]):
        """
        Add a task to the queue.

        Args:
            worker_type: Type of worker to process the task
            task: Task to add to the queue
        """
        await self.queues[worker_type].put(task)

    async def add_document_processing_task(self, task: Dict[str, Any]):
        """
        Add a document processing task to the queue.

        Args:
            task: Document processing task
        """
        await self.add_task(WorkerType.DOCUMENT_PROCESSOR, task)

    # Status and monitoring methods

    def get_status(self) -> Dict[str, Any]:
        """
        Get the status of the worker manager.

        Returns:
            Status dictionary
        """
        return {
            "is_running": self.is_running,
            "worker_counts": {k.value: v for k, v in self.worker_counts.items()},
            "queue_sizes": {k.value: v.qsize() for k, v in self.queues.items()},
            "worker_status": {k.value: {wid: s.value for wid, s in v.items()} for k, v in self.worker_status.items()},
            "stats": self.stats.to_dict()
        }

    def get_queue_sizes(self) -> Dict[str, int]:
        """
        Get the sizes of all queues.

        Returns:
            Dictionary of queue sizes
        """
        return {k.value: v.qsize() for k, v in self.queues.items()}
