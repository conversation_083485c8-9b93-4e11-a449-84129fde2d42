"""
Database processor for the worker system.

This module provides the database processor implementation for the worker system.
"""

from typing import Dict, List, Any, Optional, Union
import asyncio

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

async def process_database_task(task: Dict[str, Any], add_task_callback=None) -> Dict[str, Any]:
    """
    Process a database write task.

    Args:
        task: Database write task to process
        add_task_callback: Callback function to add new tasks to the queue

    Returns:
        Result of the database write
    """
    from database.database_service import get_falkordb_adapter

    task_type = task.get("type")
    document_id = task.get("document_id")

    logger.info(f"Writing {task_type} to database for document {document_id}")

    # Get the database adapter
    adapter = await get_falkordb_adapter()

    if task_type == "entities":
        # Write entities to the database
        fact_id = task.get("fact_id")
        entities = task.get("entities", [])

        # Create entity nodes and link to fact
        for entity in entities:
            # Create entity node
            entity_uuid = await adapter.create_entity_node(
                name=entity.get("name", "Unknown"),
                entity_type=entity.get("type", "Other"),
                properties={
                    "document_id": document_id,
                    "confidence": entity.get("confidence", 1.0)
                }
            )

            # Link entity to fact
            if entity_uuid and fact_id:
                await adapter.link_entity_to_fact(entity_uuid, fact_id, {
                    "confidence": entity.get("confidence", 1.0)
                })

        return {
            "document_id": document_id,
            "entities_written": len(entities)
        }

    elif task_type == "references":
        # Write references to the database
        references = task.get("references", [])

        # Import the reference adapter
        from database.reference_adapter import store_references_in_database

        # Store references in database
        references_stored = await store_references_in_database(document_id, references)

        return {
            "document_id": document_id,
            "references_written": references_stored
        }

    elif task_type == "embedding":
        # Write embedding to the database
        fact_id = task.get("fact_id")
        embedding = task.get("embedding")

        if fact_id and embedding:
            # Update fact with embedding
            await adapter.update_fact_embedding(fact_id, embedding)

        return {
            "document_id": document_id,
            "embedding_written": bool(embedding)
        }

    else:
        raise ValueError(f"Unknown database task type: {task_type}")
