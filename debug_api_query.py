#!/usr/bin/env python3
"""
Debug script to test the exact API query.
"""

import asyncio
import requests

async def debug_api_query():
    """Debug the exact API query used by the documents endpoint"""
    print("🔍 Debugging API Query")
    print("=" * 30)
    
    # Test 1: Test the exact query used by the API
    print("1. Testing exact API query...")
    try:
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        # This is the exact query from the API
        query = """
        MATCH (d:Episode)
        RETURN d.uuid as uuid, d.name as name, d.created_at as created_at
        LIMIT 20
        """
        
        result = adapter.execute_cypher(query)
        
        print(f"Query result structure: {len(result) if result else 0} parts")
        if result:
            print(f"Headers: {result[0] if len(result) > 0 else 'None'}")
            print(f"Data rows: {len(result[1]) if len(result) > 1 else 0}")
            
            if len(result) > 1 and result[1]:
                print("Sample rows:")
                for i, row in enumerate(result[1][:3]):
                    print(f"  Row {i+1}: {row}")
            else:
                print("No data rows found")
        else:
            print("No result returned")
            
    except Exception as e:
        print(f"❌ Query error: {e}")
    
    # Test 2: Test API endpoint directly
    print("\n2. Testing API endpoint directly...")
    try:
        response = requests.get("http://127.0.0.1:9753/api/fast/documents?limit=5", timeout=10)
        print(f"API Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"API returned {len(documents)} documents")
            
            for i, doc in enumerate(documents):
                print(f"  {i+1}. {doc.get('name', 'N/A')} - {doc.get('uuid', 'N/A')}")
        else:
            print(f"API Error: {response.text}")
            
    except Exception as e:
        print(f"❌ API error: {e}")
    
    # Test 3: Compare with different query
    print("\n3. Testing alternative query...")
    try:
        # Try a simpler query
        query = "MATCH (d:Episode) RETURN d LIMIT 5"
        result = adapter.execute_cypher(query)
        
        print(f"Alternative query result: {len(result[1]) if result and len(result) > 1 else 0} documents")
        
        if result and len(result) > 1:
            for i, row in enumerate(result[1][:3]):
                print(f"  Document {i+1}: {type(row[0])} - {str(row[0])[:100]}...")
                
    except Exception as e:
        print(f"❌ Alternative query error: {e}")
    
    # Test 4: Check if there's a filtering issue
    print("\n4. Testing with different filters...")
    try:
        # Test with name filter
        query = """
        MATCH (d:Episode)
        WHERE d.name IS NOT NULL AND d.name <> ''
        RETURN d.uuid as uuid, d.name as name, d.created_at as created_at
        LIMIT 10
        """
        
        result = adapter.execute_cypher(query)
        
        print(f"Filtered query result: {len(result[1]) if result and len(result) > 1 else 0} documents")
        
        if result and len(result) > 1:
            for i, row in enumerate(result[1][:3]):
                uuid_val = row[0] if row[0] else "N/A"
                name_val = row[1] if row[1] else "N/A"
                created_at_val = row[2] if row[2] else "N/A"
                print(f"  {i+1}. {name_val} ({uuid_val[:8]}...) - {created_at_val}")
                
    except Exception as e:
        print(f"❌ Filtered query error: {e}")

if __name__ == "__main__":
    asyncio.run(debug_api_query())
