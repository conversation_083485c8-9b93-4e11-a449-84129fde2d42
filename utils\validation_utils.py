"""
Validation utilities for the Graphiti application.

This module provides functions for validating data before returning it to the API.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime
import logging
from models.document_simplified import DocumentType

# Set up logger
logger = logging.getLogger(__name__)

def validate_document_summary(doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize a document summary.

    Args:
        doc: Document summary data

    Returns:
        Validated and normalized document summary
    """
    # Create a new dictionary with validated data
    validated_doc = {}

    # Validate required fields
    validated_doc["id"] = doc.get("id") or doc.get("uuid", "")
    validated_doc["filename"] = doc.get("filename") or doc.get("name", "Unknown document")

    # Validate file_type (must be a valid enum value)
    file_type = doc.get("file_type", "other")
    if file_type not in [e.value for e in DocumentType]:
        logger.warning(f"Invalid file_type: {file_type}, defaulting to 'other'")
        file_type = "other"
    validated_doc["file_type"] = file_type

    # Validate upload_date
    upload_date = doc.get("upload_date") or doc.get("processed_at")
    if upload_date:
        if isinstance(upload_date, str):
            try:
                upload_date = datetime.fromisoformat(upload_date)
            except ValueError:
                logger.warning(f"Invalid upload_date format: {upload_date}, using current time")
                upload_date = datetime.now()
    else:
        upload_date = datetime.now()
    validated_doc["upload_date"] = upload_date

    # Validate numeric fields
    validated_doc["chunks"] = max(0, int(doc.get("chunks", 0)))
    validated_doc["entities"] = max(0, int(doc.get("entities", 0)))
    validated_doc["references"] = max(0, int(doc.get("references", 0)))

    # Include metadata if available
    validated_doc["metadata"] = doc.get("metadata")

    return validated_doc

def validate_document_list(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize a document list.

    Args:
        data: Document list data

    Returns:
        Validated and normalized document list
    """
    # Create a new dictionary with validated data
    validated_data = {}

    # Validate documents
    documents = []
    for doc in data.get("documents", []):
        validated_doc = validate_document_summary(doc)
        documents.append(validated_doc)
    validated_data["documents"] = documents

    # Validate pagination
    validated_data["total"] = max(0, int(data.get("total", 0)))
    validated_data["page"] = max(1, int(data.get("page", 1)))
    validated_data["page_size"] = max(1, int(data.get("page_size", 10)))

    return validated_data

def validate_document_details(doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Validate and normalize document details.

    Args:
        doc: Document details data

    Returns:
        Validated and normalized document details
    """
    # Create a new dictionary with validated data
    validated_doc = {}

    # Validate required fields
    validated_doc["uuid"] = doc.get("uuid", "")
    validated_doc["name"] = doc.get("name", "Unknown document")
    validated_doc["file_path"] = doc.get("file_path", "")

    # Validate processed_at
    processed_at = doc.get("processed_at", "")
    if processed_at and isinstance(processed_at, str):
        try:
            # Try to parse the date to validate it
            datetime.fromisoformat(processed_at)
        except ValueError:
            logger.warning(f"Invalid processed_at format: {processed_at}, using empty string")
            processed_at = ""
    validated_doc["processed_at"] = processed_at

    # Validate numeric fields
    validated_doc["chunks"] = max(0, int(doc.get("chunks", 0)))
    validated_doc["entities"] = max(0, int(doc.get("entities", 0)))
    validated_doc["references"] = max(0, int(doc.get("references", 0)))

    # Validate file_type (must be a valid enum value)
    file_type = doc.get("file_type", "other")
    if file_type not in [e.value for e in DocumentType]:
        logger.warning(f"Invalid file_type: {file_type}, defaulting to 'other'")
        file_type = "other"
    validated_doc["file_type"] = file_type

    return validated_doc
