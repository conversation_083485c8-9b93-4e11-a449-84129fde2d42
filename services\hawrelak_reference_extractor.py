"""
Hawrelak-style Reference Extractor
Specialized for academic documents with bullet-pointed, author-year references
"""

import re
import uuid
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class HawrelakReferenceExtractor:
    """
    Specialized extractor for Hawrelak-style academic references.
    Handles bullet points, complex author formats, and academic citation styles.
    """
    
    def __init__(self, mistral_ocr_processor):
        self.mistral_ocr = mistral_ocr_processor
        
    async def extract_references(self, file_path: str) -> Dict[str, Any]:
        """
        Extract references from an academic document using Mistral OCR.
        
        Args:
            file_path: Path to the document file
            
        Returns:
            Dictionary with extraction results
        """
        try:
            logger.info(f"📚 Starting Hawrelak-style reference extraction for: {Path(file_path).name}")
            
            # Extract text using Mistral OCR
            text = await self.mistral_ocr.extract_text_from_pdf(file_path)
            if not text:
                logger.error("❌ No text extracted from document")
                return self._empty_result("No text extracted")
            
            logger.info(f"📝 Extracted {len(text):,} characters from academic document")
            
            # Extract references using academic-specific patterns
            references = self._extract_academic_references(text)
            
            logger.info(f"📚 Found {len(references)} references in academic document")
            
            # Save references to CSV
            csv_path = await self._save_references_to_csv(references, file_path)
            
            return {
                "success": True,
                "filename": Path(file_path).name,
                "file_path": file_path,
                "extraction_method": "hawrelak_academic_mistral_ocr",
                "total_reference_count": len(references),
                "references": references,
                "csv_path": csv_path,
                "extracted_text_length": len(text)
            }
            
        except Exception as e:
            logger.error(f"❌ Error extracting Hawrelak-style references: {e}", exc_info=True)
            return self._empty_result(str(e))
    
    def _extract_academic_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references from academic text using specialized patterns."""
        
        references = []
        
        # Strategy 1: Bullet-pointed references
        bullet_refs = self._extract_bullet_references(text)
        references.extend(bullet_refs)
        
        # Strategy 2: Author-year academic format
        author_year_refs = self._extract_academic_author_year(text)
        references.extend(author_year_refs)
        
        # Strategy 3: DOI-based references
        doi_refs = self._extract_doi_references(text)
        references.extend(doi_refs)
        
        # Strategy 4: Journal-specific patterns
        journal_refs = self._extract_journal_patterns(text)
        references.extend(journal_refs)
        
        # Strategy 5: Multi-line references
        multiline_refs = self._extract_multiline_references(text)
        references.extend(multiline_refs)

        # Strategy 6: Hyphen-separated references (for OCR issues)
        hyphen_refs = self._extract_hyphen_separated_references(text)
        references.extend(hyphen_refs)
        
        # Clean and deduplicate
        cleaned_refs = self._clean_and_deduplicate(references)
        
        logger.info(f"📋 Hawrelak extraction breakdown:")
        logger.info(f"   Bullet references: {len(bullet_refs)}")
        logger.info(f"   Author-year format: {len(author_year_refs)}")
        logger.info(f"   DOI references: {len(doi_refs)}")
        logger.info(f"   Journal patterns: {len(journal_refs)}")
        logger.info(f"   Multi-line references: {len(multiline_refs)}")
        logger.info(f"   Hyphen-separated references: {len(hyphen_refs)}")
        logger.info(f"   After deduplication: {len(cleaned_refs)}")
        
        return cleaned_refs
    
    def _extract_bullet_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract bullet-pointed references (handles both • and - formats)."""
        references = []

        # Try multiple bullet patterns (OCR often converts • to -)
        bullet_patterns = [
            r'•\s*',  # Original bullet
            r'-\s*(?=[A-Z][a-z]+\s*,\s*[A-Z]\.)',  # Hyphen followed by author pattern
            r'-\s*(?=[A-Z][a-z]+\s+[A-Z]{1,3}\.)',  # Hyphen followed by author initial
        ]

        for pattern in bullet_patterns:
            parts = re.split(pattern, text)

            for part in parts[1:]:  # Skip first part (before first bullet)
                # Take content until next bullet/hyphen or reference pattern
                ref_text = self._extract_single_reference_from_part(part)

                if ref_text and self._is_valid_academic_reference(ref_text):
                    references.append(self._create_reference_dict(ref_text, 'bullet_point'))

        return references

    def _extract_single_reference_from_part(self, part: str) -> str:
        """Extract a single reference from a text part."""
        # Look for the end of this reference (start of next reference)
        end_patterns = [
            r'\s+-\s+[A-Z][a-z]+\s*,',  # Next hyphen + author
            r'\s+•\s+[A-Z][a-z]+\s*,',  # Next bullet + author
            r'\s+[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.\s*\(',  # Next author pattern with year
        ]

        ref_text = part
        for pattern in end_patterns:
            match = re.search(pattern, part)
            if match:
                ref_text = part[:match.start()]
                break

        # Clean up the reference text
        ref_text = self._clean_reference_text(ref_text)
        return ref_text
    
    def _extract_academic_author_year(self, text: str) -> List[Dict[str, Any]]:
        """Extract academic author-year format references."""
        references = []
        
        patterns = [
            # Pattern 1: Charalambous, B. M., Stephens, R. C., ... (2007). Title. Journal.
            r'[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.(?:,\s*[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.)*(?:,\s*&\s*[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.)?\s*\(\d{4}\)\.\s*[^.]{10,300}\.\s*[A-Z][^.]{5,100}',
            
            # Pattern 2: Chen, H. L., Cheng, H. C., ... (2008). Title. Journal, volume(issue), pages.
            r'[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.(?:,\s*[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.)*\s*\(\d{4}\)\.\s*[^.]{10,300}\.\s*[A-Z][^,]{5,100},\s*\d+\s*\(\d+\),\s*\d+[-–]\d+',
            
            # Pattern 3: Dao, M. C., A. Everard, ... (2015). "Title." Journal
            r'[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.(?:,\s*[A-Z]\.\s*[A-Z][a-z]+)*\s*\(\d{4}\)\.\s*"[^"]{10,300}"\s*[A-Z][^.]{5,100}',
            
            # Pattern 4: Simplified author format with year
            r'[A-Z][a-z]+(?:\s*,\s*[A-Z]\.)*(?:\s*,\s*[A-Z][a-z]+)*\s*\(\d{4}\)[^.]{10,300}\.[^.]{5,100}',
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.MULTILINE | re.DOTALL)
            for match in matches:
                if self._is_valid_academic_reference(match):
                    references.append(self._create_reference_dict(match, 'academic_author_year'))
        
        return references
    
    def _extract_doi_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references that contain DOI information."""
        references = []
        
        # Find DOI patterns and extract surrounding context
        doi_pattern = r'doi:\s*(?:https?://(?:dx\.)?doi\.org/)?10\.\d+/[^\s\n]+'
        
        for match in re.finditer(doi_pattern, text, re.IGNORECASE):
            # Extract context around DOI (up to 500 characters before)
            start = max(0, match.start() - 500)
            end = min(len(text), match.end() + 100)
            context = text[start:end]
            
            # Look for the start of the reference (author name or bullet)
            ref_start_patterns = [
                r'•\s*[A-Z][a-z]+',  # Bullet point with author
                r'\n[A-Z][a-z]+\s*,',  # New line with author
                r'^[A-Z][a-z]+\s*,',  # Start of line with author
            ]
            
            ref_start = start
            for pattern in ref_start_patterns:
                ref_match = re.search(pattern, context)
                if ref_match:
                    ref_start = start + ref_match.start()
                    break
            
            ref_text = text[ref_start:end].strip()
            ref_text = self._clean_reference_text(ref_text)
            
            if self._is_valid_academic_reference(ref_text):
                references.append(self._create_reference_dict(ref_text, 'doi_based'))
        
        return references
    
    def _extract_journal_patterns(self, text: str) -> List[Dict[str, Any]]:
        """Extract references based on journal name patterns."""
        references = []
        
        # Common journal patterns
        journal_patterns = [
            r'[^.]{20,300}\.\s*(?:Nature|Science|Cell|Lancet|NEJM|JAMA|PLoS\s+One|Gut|Diabetes|Microbiome)\s*[^.]{0,100}\.\s*\d{4}',
            r'[^.]{20,300}\.\s*[A-Z][^.]{5,50}\s+(?:Rev|J|Journal|Microbiol|Clin|Med|Sci)\s*[^.]{0,100}\.\s*\d{4}',
            r'[^.]{20,300}\.\s*[A-Z][^.]{5,50}\s*,\s*\d+\s*\(\d+\)\s*:\s*\d+[-–]\d+',
        ]
        
        for pattern in journal_patterns:
            matches = re.findall(pattern, text, re.MULTILINE)
            for match in matches:
                if self._is_valid_academic_reference(match):
                    references.append(self._create_reference_dict(match, 'journal_pattern'))
        
        return references
    
    def _extract_multiline_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references that span multiple lines."""
        references = []
        
        # Split text into lines and look for reference patterns
        lines = text.split('\n')
        
        i = 0
        while i < len(lines):
            line = lines[i].strip()
            
            # Check if line starts a reference
            if self._line_starts_reference(line):
                # Collect multiple lines for this reference
                ref_lines = [line]
                j = i + 1
                
                # Continue collecting lines until we hit another reference or empty line
                while j < len(lines) and j < i + 5:  # Max 5 lines per reference
                    next_line = lines[j].strip()
                    if not next_line or self._line_starts_reference(next_line):
                        break
                    ref_lines.append(next_line)
                    j += 1
                
                # Combine lines into single reference
                ref_text = ' '.join(ref_lines)
                ref_text = self._clean_reference_text(ref_text)
                
                if self._is_valid_academic_reference(ref_text):
                    references.append(self._create_reference_dict(ref_text, 'multiline'))
                
                i = j
            else:
                i += 1
        
        return references
    
    def _line_starts_reference(self, line: str) -> bool:
        """Check if a line starts a new reference."""
        if len(line) < 10:
            return False
        
        # Check for common reference starting patterns
        patterns = [
            r'^•\s*[A-Z][a-z]+',  # Bullet with author
            r'^[A-Z][a-z]+\s*,\s*[A-Z]\.',  # Author, Initial.
            r'^[A-Z][a-z]+\s*,\s*[A-Z][a-z]+',  # Author, Author
        ]
        
        return any(re.match(pattern, line) for pattern in patterns)

    def _extract_hyphen_separated_references(self, text: str) -> List[Dict[str, Any]]:
        """Extract references separated by hyphens (OCR conversion of bullets)."""
        references = []

        # Split text by hyphens that precede author patterns
        # This handles the case where OCR converts bullets to hyphens
        hyphen_pattern = r'\s+-\s+(?=[A-Z][a-z]+\s*,\s*[A-Z]\.)'

        parts = re.split(hyphen_pattern, text)

        for part in parts[1:]:  # Skip first part
            # Extract reference until next author pattern or end
            ref_text = self._extract_reference_until_next_author(part)

            if ref_text and self._is_valid_academic_reference(ref_text):
                references.append(self._create_reference_dict(ref_text, 'hyphen_separated'))

        return references

    def _extract_reference_until_next_author(self, text: str) -> str:
        """Extract reference text until the next author pattern."""
        # Look for the start of the next reference
        next_ref_patterns = [
            r'\s+[A-Z][a-z]+\s*,\s*[A-Z]\.\s*[A-Z]\.',  # Next author pattern
            r'\s+[A-Z][a-z]+\s*,\s*[A-Z][a-z]+\s*,',  # Alternative author pattern
        ]

        ref_text = text
        for pattern in next_ref_patterns:
            match = re.search(pattern, text)
            if match:
                ref_text = text[:match.start()]
                break

        return self._clean_reference_text(ref_text)

    def _clean_reference_text(self, text: str) -> str:
        """Clean up reference text."""
        # Remove bullet points
        text = re.sub(r'^•\s*', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove trailing periods if multiple
        text = re.sub(r'\.+$', '.', text)
        
        return text.strip()
    
    def _is_valid_academic_reference(self, text: str) -> bool:
        """Check if text looks like a valid academic reference."""
        if not text or len(text) < 40:  # Minimum length for academic reference
            return False
        
        # Must contain some academic indicators
        indicators = [
            r'\d{4}',  # Year (required)
            r'[A-Z][a-z]+\s*,\s*[A-Z]\.',  # Author format
            r'doi:',  # DOI
            r'[Jj]ournal|[Rr]ev|[Cc]lin|[Mm]ed|[Ss]ci',  # Journal indicators
            r'\(\d+\)',  # Volume/issue numbers
            r'\d+[-–]\d+',  # Page ranges
            r'[Pp]ublished|[Aa]rticle',  # Publication indicators
        ]
        
        indicator_count = sum(1 for pattern in indicators if re.search(pattern, text))
        
        # Must have year + at least 2 other indicators
        has_year = bool(re.search(r'\d{4}', text))
        
        return has_year and indicator_count >= 3
    
    def _create_reference_dict(self, text: str, extraction_method: str) -> Dict[str, Any]:
        """Create a standardized reference dictionary."""
        return {
            "text": text.strip(),
            "extraction_method": extraction_method,
            "source": "hawrelak_academic_mistral_ocr",
            "uuid": str(uuid.uuid4()),
            "metadata": self._extract_academic_metadata(text)
        }
    
    def _extract_academic_metadata(self, text: str) -> Dict[str, Any]:
        """Extract metadata from academic reference text."""
        metadata = {}
        
        # Extract year
        year_match = re.search(r'\b(19|20)\d{2}\b', text)
        if year_match:
            metadata['year'] = year_match.group()
        
        # Extract DOI
        doi_match = re.search(r'doi:\s*(?:https?://(?:dx\.)?doi\.org/)?(10\.\d+/[^\s]+)', text, re.IGNORECASE)
        if doi_match:
            metadata['doi'] = doi_match.group(1)
        
        # Extract first author
        author_patterns = [
            r'^([A-Z][a-z]+)\s*,',  # Last name first
            r'•\s*([A-Z][a-z]+)\s*,',  # After bullet
        ]
        
        for pattern in author_patterns:
            match = re.search(pattern, text)
            if match:
                metadata['first_author'] = match.group(1)
                break
        
        # Extract journal name (simplified)
        journal_patterns = [
            r'\.\s*([A-Z][^.]{5,50})\s*[,.]?\s*\d{4}',  # Journal before year
            r'\.\s*([A-Z][^,]{5,50}),\s*\d+',  # Journal before volume
        ]
        
        for pattern in journal_patterns:
            match = re.search(pattern, text)
            if match:
                journal = match.group(1).strip()
                if len(journal) > 5 and not re.search(r'\d', journal):  # Avoid capturing years/numbers
                    metadata['journal'] = journal
                    break
        
        return metadata
    
    def _clean_and_deduplicate(self, references: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Clean and remove duplicate references."""
        seen_texts = set()
        unique_refs = []
        
        for ref in references:
            # Normalize text for comparison
            normalized = re.sub(r'\s+', ' ', ref['text'].lower().strip())
            normalized = re.sub(r'[^\w\s]', '', normalized)  # Remove punctuation for comparison
            
            # Remove very short references
            if len(normalized) < 30:
                continue
            
            # Check for duplicates (allow some variation)
            is_duplicate = False
            for seen in seen_texts:
                # Calculate similarity (simple approach)
                if len(set(normalized.split()) & set(seen.split())) / max(len(normalized.split()), len(seen.split())) > 0.8:
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                seen_texts.add(normalized)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def _save_references_to_csv(self, references: List[Dict[str, Any]], file_path: str) -> Optional[str]:
        """Save references to CSV file."""
        try:
            import csv
            from datetime import datetime
            
            # Create CSV filename
            base_name = Path(file_path).stem
            csv_filename = f"hawrelak_refs_{base_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            csv_path = Path("data/references") / csv_filename
            csv_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['uuid', 'text', 'extraction_method', 'year', 'doi', 'journal', 'first_author', 'source_document']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for ref in references:
                    metadata = ref.get('metadata', {})
                    writer.writerow({
                        'uuid': ref['uuid'],
                        'text': ref['text'],
                        'extraction_method': ref['extraction_method'],
                        'year': metadata.get('year', ''),
                        'doi': metadata.get('doi', ''),
                        'journal': metadata.get('journal', ''),
                        'first_author': metadata.get('first_author', ''),
                        'source_document': Path(file_path).name
                    })
            
            logger.info(f"💾 Saved {len(references)} Hawrelak-style references to {csv_path}")
            return str(csv_path)
            
        except Exception as e:
            logger.error(f"❌ Error saving Hawrelak references to CSV: {e}")
            return None
    
    def _empty_result(self, error_msg: str) -> Dict[str, Any]:
        """Return empty result with error."""
        return {
            "success": False,
            "error": error_msg,
            "total_reference_count": 0,
            "references": []
        }
