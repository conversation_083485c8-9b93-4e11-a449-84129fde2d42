"""
<PERSON><PERSON><PERSON> to process a single PDF file and add it to Graphiti
"""

import os
import sys
import asyncio
import logging
from datetime import datetime, timezone
import PyPDF2

from dotenv import load_dotenv

from graphiti_core import Graphiti
from graphiti_core.llm_client.openai_client import OpenAIClient
from graphiti_core.llm_client.config import LLMConfig
from graphiti_core.embedder.openai import OpenAIEmbedder, OpenAIEmbedderConfig
from graphiti_core.nodes import EpisodeType

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file using PyPDF2."""
    logger.info(f"Extracting text from {pdf_path}")

    text = ""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page_num in range(len(reader.pages)):
                page = reader.pages[page_num]
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n\n"

        logger.info(f"Extracted {len(text)} characters from {pdf_path}")
        return text
    except Exception as e:
        logger.error(f"Error extracting text from {pdf_path}: {e}")
        return ""

def chunk_text(text, chunk_size=800, overlap=20):
    """Split text into chunks with overlap."""
    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = min(start + chunk_size, len(text))

        # Try to find a good breaking point (newline or period)
        if end < len(text):
            # Look for a newline
            newline_pos = text.rfind('\n', start, end)
            period_pos = text.rfind('. ', start, end)

            if newline_pos > start + chunk_size // 2:
                end = newline_pos + 1
            elif period_pos > start + chunk_size // 2:
                end = period_pos + 2

        chunks.append(text[start:end])
        start = end - overlap

    logger.info(f"Split text into {len(chunks)} chunks")
    return chunks

async def process_pdf_and_add_to_graphiti(file_path, graphiti):
    """Process a PDF file and add it to Graphiti."""
    logger.info(f"Processing PDF: {file_path}")

    # Extract text from PDF
    text = extract_text_from_pdf(file_path)
    if not text:
        logger.warning(f"No text extracted from {file_path}, skipping")
        return

    # Get document title from filename
    document_title = os.path.basename(file_path)

    # Split into chunks if the text is long
    chunks = chunk_text(text)

    # Add the document as an episode
    document_episode_id = await graphiti.add_episode(
        name=document_title,
        episode_body=f"Document: {document_title}\nSource: {file_path}",
        source=EpisodeType.text,
        reference_time=datetime.now(timezone.utc),
        source_description=f"PDF Document: {file_path}",
        metadata={
            "file_name": os.path.basename(file_path),
            "document_title": document_title,
            "total_chunks": len(chunks)
        }
    )

    logger.info(f"Added document episode with ID: {document_episode_id}")

    # Add each chunk as a fact connected to the document episode
    for i, chunk in enumerate(chunks):
        chunk_name = f"Chunk {i+1} of {document_title}"

        logger.info(f"Adding fact: {chunk_name}")
        fact_id = await graphiti.add_fact(
            fact_body=chunk,
            episode_id=document_episode_id,
            metadata={
                "chunk_index": i,
                "total_chunks": len(chunks)
            }
        )

        logger.info(f"Added fact with ID: {fact_id}")

    logger.info(f"Successfully processed document: {file_path}")
    return document_episode_id

async def main():
    """Main function to process a single PDF and add it to Graphiti."""
    # Load environment variables
    load_dotenv()

    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')

    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')

    if not openai_api_key:
        logger.error("No OpenAI API key found in environment variables. Please add OPENAI_API_KEY to your .env file.")
        return

    # Check if a PDF file was specified
    if len(sys.argv) < 2:
        logger.error("Please specify a PDF file to process")
        logger.info("Usage: python process_single_pdf.py <pdf_file>")
        return

    pdf_file = sys.argv[1]
    if not os.path.exists(pdf_file):
        logger.error(f"PDF file not found: {pdf_file}")
        return

    try:
        # Initialize Graphiti with OpenAI
        graphiti = Graphiti(
            neo4j_uri,
            neo4j_user,
            neo4j_password,
            llm_client=OpenAIClient(
                config=LLMConfig(
                    api_key=openai_api_key,
                    model="gpt-4o"
                )
            ),
            embedder=OpenAIEmbedder(
                config=OpenAIEmbedderConfig(
                    api_key=openai_api_key,
                    embedding_model="text-embedding-3-small"
                )
            )
        )

        logger.info("Setting up indices and constraints")
        await graphiti.build_indices_and_constraints()

        # Process the PDF
        document_id = await process_pdf_and_add_to_graphiti(pdf_file, graphiti)

        if document_id:
            logger.info(f"PDF processing completed successfully! Document ID: {document_id}")

    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'graphiti' in locals():
            await graphiti.driver.close()

if __name__ == "__main__":
    asyncio.run(main())
