"""
Test script to check the API endpoints
"""

import requests
import json

def test_api():
    """Test the API endpoints"""
    # Test the entities endpoint
    entities_url = "http://localhost:9753/api/entities"
    print(f"Testing {entities_url}")
    try:
        response = requests.get(entities_url)
        print(f"Status code: {response.status_code}")
        print(f"Response content: {response.content}")

        if response.status_code == 200:
            try:
                data = response.json()
                print("\nParsed JSON response:")
                print(json.dumps(data, indent=2))
            except json.JSONDecodeError:
                print("\nResponse is not valid JSON")
    except Exception as e:
        print(f"Error: {e}")

    # Test the test-entities endpoint
    test_entities_url = "http://localhost:9753/api/test-entities"
    print(f"\nTesting {test_entities_url}")
    try:
        response = requests.get(test_entities_url)
        print(f"Status code: {response.status_code}")
        print(f"Response content: {response.content}")

        if response.status_code == 200:
            try:
                data = response.json()
                print("\nParsed JSON response:")
                print(json.dumps(data, indent=2))
            except json.JSONDecodeError:
                print("\nResponse is not valid JSON")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
