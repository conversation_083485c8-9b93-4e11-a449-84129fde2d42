# Reference System To-Do List

## Completed Tasks
- ✅ Fix reference extraction code to handle different return formats
- ✅ Fix CSV and JSON saving methods to handle Path objects
- ✅ Fix web interface to properly handle references
- ✅ Create script to process documents for references
- ✅ Fix issues with BOM (Byte Order Mark) in CSV files
- ✅ Fix issues with empty references
- ✅ Create deduplication script for references

## Pending Tasks

### UI Improvements
- [ ] Implement a UI component to display references in a more user-friendly way
  - [ ] Create a dedicated "References" tab in the UI
  - [ ] Display references with proper formatting
  - [ ] Group references by document
  - [ ] Add pagination for large reference lists

### Search and Filter
- [ ] Add functionality to filter or search references by various criteria
  - [ ] Filter by author
  - [ ] Filter by year
  - [ ] Filter by journal
  - [ ] Filter by document source
  - [ ] Implement full-text search across all reference fields

### Reference Extraction Enhancements
- [ ] Enhance reference extraction to capture more metadata
  - [ ] Improve DOI extraction
  - [ ] Add URL extraction and validation
  - [ ] Extract more detailed publication information
  - [ ] Improve author name parsing and normalization

### Citation Network
- [ ] Create visualization of citation networks between references
  - [ ] Identify citations between documents
  - [ ] Create graph visualization of citation relationships
  - [ ] Add interactive features to explore the citation network
  - [ ] Implement citation metrics (citation count, etc.)

### Knowledge Graph Integration
- [ ] Integrate references with the knowledge graph
  - [ ] Create Reference nodes in the knowledge graph
  - [ ] Link Reference nodes to Document nodes
  - [ ] Link Reference nodes to Entity nodes mentioned in the reference
  - [ ] Enable traversal of the knowledge graph through references

### Workflow Integration
- [ ] Improve PDF processing workflow to automatically extract references during document upload
  - [ ] Ensure reference extraction is part of the standard ingestion pipeline
  - [ ] Add progress indicators for reference extraction
  - [ ] Implement background processing for large documents
  - [ ] Add error handling and recovery for failed reference extraction

### Export Features
- [ ] Add feature to export references in different formats
  - [ ] BibTeX export
  - [ ] EndNote export
  - [ ] RIS format export
  - [ ] JSON export
  - [ ] Excel export

### Reference Management
- [ ] Implement reference management system to organize references
  - [ ] Create collections/folders for references
  - [ ] Add tagging system for references
  - [ ] Implement sorting options
  - [ ] Add ability to manually edit references
  - [ ] Add ability to manually add references

## Priority Order (Suggested)
1. Workflow Integration - Ensure reference extraction is fully integrated into the document upload process
2. UI Improvements - Make references visible and usable in the interface
3. Search and Filter - Enable users to find specific references
4. Export Features - Allow users to use references in other systems
5. Reference Extraction Enhancements - Improve the quality of extracted references
6. Knowledge Graph Integration - Connect references to the knowledge graph
7. Citation Network - Visualize relationships between references
8. Reference Management - Add advanced organization features
