/**
 * Batch document upload functionality for Graphiti
 *
 * This script provides functionality for uploading multiple documents at once
 * and processing them in parallel.
 */

console.log("Batch upload script loaded successfully!");

document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const uploadForm = document.getElementById('upload-form');
    const fileInput = document.getElementById('file-input');
    const folderInput = document.getElementById('folder-input');
    const selectFilesButton = document.getElementById('select-files-button');
    const selectFolderButton = document.getElementById('select-folder-button');
    const fileList = document.getElementById('file-list');
    const uploadButton = document.getElementById('upload-button');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const progressText = document.getElementById('progress-text');
    const alertContainer = document.getElementById('alert-container');
    const chunkSizeInput = document.getElementById('chunk-size');
    const overlapInput = document.getElementById('overlap');
    const extractEntitiesCheckbox = document.getElementById('extract-entities');
    const extractReferencesCheckbox = document.getElementById('extract-references');
    const extractMetadataCheckbox = document.getElementById('extract-metadata');
    const maxParallelProcessesInput = document.getElementById('max-parallel-processes');

    // Array to store files to upload
    let filesToUpload = [];

    // Map to track file progress
    let fileProgressMap = {};

    // Add event listeners
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
    }

    if (folderInput) {
        folderInput.addEventListener('change', function() {
            handleFiles(this.files);
        });
    }

    if (selectFilesButton) {
        selectFilesButton.addEventListener('click', function() {
            fileInput.click();
        });
    }

    if (selectFolderButton) {
        selectFolderButton.addEventListener('click', function() {
            folderInput.click();
        });
    }

    if (uploadForm) {
        uploadForm.addEventListener('submit', handleFormSubmit);

        // Add drag and drop functionality
        uploadForm.addEventListener('dragover', handleDragOver);
        uploadForm.addEventListener('dragleave', handleDragLeave);
        uploadForm.addEventListener('drop', handleDrop);
    }

    /**
     * Handle form submit
     * @param {Event} e - Form submit event
     */
    function handleFormSubmit(e) {
        e.preventDefault();
        uploadFiles();
    }

    /**
     * Handle drag over
     * @param {Event} e - Drag over event
     */
    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadForm.classList.add('drag-over');
    }

    /**
     * Handle drag leave
     * @param {Event} e - Drag leave event
     */
    function handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadForm.classList.remove('drag-over');
    }

    /**
     * Handle drop
     * @param {Event} e - Drop event
     */
    function handleDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadForm.classList.remove('drag-over');

        const dt = e.dataTransfer;

        // Check if items are available (for folder support)
        if (dt.items) {
            // Use DataTransferItemList interface to access the files
            handleDropItems(dt.items);
        } else {
            // Use DataTransfer interface to access the files
            handleFiles(dt.files);
        }
    }

    /**
     * Handle dropped items (supports folders)
     * @param {DataTransferItemList} items - Dropped items
     */
    function handleDropItems(items) {
        const allFiles = [];
        let pendingFolders = 0;

        // Function to process all entries
        function processEntries() {
            if (pendingFolders === 0 && allFiles.length > 0) {
                // Convert FileList to Array
                handleFiles(allFiles);
            }
        }

        // Process each item
        for (let i = 0; i < items.length; i++) {
            const item = items[i];

            // Skip non-file entries
            if (item.kind !== 'file') {
                continue;
            }

            // Get the entry
            const entry = item.webkitGetAsEntry ? item.webkitGetAsEntry() : item.getAsEntry();

            if (entry) {
                if (entry.isFile) {
                    // Get file directly
                    const file = item.getAsFile();
                    if (file) {
                        allFiles.push(file);
                    }
                } else if (entry.isDirectory) {
                    // Process directory
                    pendingFolders++;
                    processDirectory(entry, allFiles, () => {
                        pendingFolders--;
                        processEntries();
                    });
                }
            } else {
                // Fallback for browsers that don't support webkitGetAsEntry
                const file = item.getAsFile();
                if (file) {
                    allFiles.push(file);
                }
            }
        }

        // If no folders to process, handle files immediately
        if (pendingFolders === 0) {
            processEntries();
        }
    }

    /**
     * Process a directory recursively
     * @param {DirectoryEntry} directoryEntry - The directory entry
     * @param {Array} files - Array to collect files
     * @param {Function} callback - Callback when done
     */
    function processDirectory(directoryEntry, files, callback) {
        const dirReader = directoryEntry.createReader();
        let entriesChunk = [];

        // Read directory entries in chunks
        function readEntries() {
            dirReader.readEntries((entries) => {
                if (entries.length === 0) {
                    // No more entries, process the collected entries
                    processEntryChunk(entriesChunk, files, callback);
                } else {
                    // Add entries to the chunk and continue reading
                    entriesChunk = entriesChunk.concat(Array.from(entries));
                    readEntries();
                }
            }, (error) => {
                console.error('Error reading directory:', error);
                callback();
            });
        }

        // Start reading entries
        readEntries();
    }

    /**
     * Process a chunk of directory entries
     * @param {Array} entries - Directory entries
     * @param {Array} files - Array to collect files
     * @param {Function} callback - Callback when done
     */
    function processEntryChunk(entries, files, callback) {
        let pendingEntries = entries.length;

        if (pendingEntries === 0) {
            callback();
            return;
        }

        // Process each entry
        entries.forEach((entry) => {
            if (entry.isFile) {
                // Get file
                entry.file((file) => {
                    files.push(file);
                    pendingEntries--;
                    if (pendingEntries === 0) {
                        callback();
                    }
                }, (error) => {
                    console.error('Error getting file:', error);
                    pendingEntries--;
                    if (pendingEntries === 0) {
                        callback();
                    }
                });
            } else if (entry.isDirectory) {
                // Process subdirectory
                processDirectory(entry, files, () => {
                    pendingEntries--;
                    if (pendingEntries === 0) {
                        callback();
                    }
                });
            } else {
                pendingEntries--;
                if (pendingEntries === 0) {
                    callback();
                }
            }
        });
    }

    /**
     * Handle files
     * @param {FileList} files - Files to handle
     */
    function handleFiles(files) {
        if (files.length > 0) {
            // Filter for supported file types
            const supportedFiles = Array.from(files).filter(file => {
                const fileType = file.type.toLowerCase();
                const fileName = file.name.toLowerCase();

                return (
                    fileType === 'application/pdf' ||
                    fileName.endsWith('.pdf') ||
                    fileType === 'text/plain' ||
                    fileName.endsWith('.txt') ||
                    fileType === 'application/msword' ||
                    fileType === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||
                    fileName.endsWith('.doc') ||
                    fileName.endsWith('.docx')
                );
            });

            if (supportedFiles.length === 0) {
                showAlert('Please select supported file types (PDF, TXT, DOC, DOCX).', 'danger');
                return;
            }

            // Add files to the list
            filesToUpload = [...filesToUpload, ...supportedFiles];
            updateFileList();
        }
    }

    /**
     * Update file list
     */
    function updateFileList() {
        if (!fileList) return;

        fileList.innerHTML = '';

        filesToUpload.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            const fileName = document.createElement('span');
            fileName.className = 'file-name';
            fileName.textContent = file.name;

            const fileSize = document.createElement('span');
            fileSize.className = 'file-size';
            fileSize.textContent = formatFileSize(file.size);

            const removeButton = document.createElement('button');
            removeButton.className = 'btn btn-sm btn-danger remove-file';
            removeButton.textContent = 'Remove';
            removeButton.addEventListener('click', () => {
                filesToUpload.splice(index, 1);
                updateFileList();
            });

            fileItem.appendChild(fileName);
            fileItem.appendChild(fileSize);
            fileItem.appendChild(removeButton);

            fileList.appendChild(fileItem);
        });

        // Update upload button state
        if (uploadButton) {
            uploadButton.disabled = filesToUpload.length === 0;
        }
    }

    /**
     * Format file size
     * @param {number} bytes - File size in bytes
     * @returns {string} - Formatted file size
     */
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Initialize file progress tracking
     */
    function initializeFileProgress() {
        // Clear the file progress map
        fileProgressMap = {};

        // Clear the file progress list
        const fileProgressList = document.getElementById('file-progress-list');
        if (fileProgressList) {
            fileProgressList.innerHTML = '';
        }

        // Initialize progress for each file
        filesToUpload.forEach(file => {
            // Create a unique ID for the file
            const fileId = `file-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

            // Add to the progress map
            fileProgressMap[fileId] = {
                id: fileId,
                name: file.name,
                size: file.size,
                status: 'queued',
                progress: 0,
                documentId: null
            };

            // Add to the progress list
            if (fileProgressList) {
                const fileItem = document.createElement('div');
                fileItem.className = 'list-group-item';
                fileItem.id = `progress-${fileId}`;
                fileItem.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div>
                            <span class="fw-bold">${file.name}</span>
                            <span class="text-muted">(${formatFileSize(file.size)})</span>
                        </div>
                        <span class="badge bg-secondary">Queued</span>
                    </div>
                    <div class="progress" style="height: 5px;">
                        <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <div class="text-muted small mt-1 status-text">Waiting to be processed...</div>
                `;
                fileProgressList.appendChild(fileItem);
            }
        });
    }

    /**
     * Update file progress
     *
     * @param {string} fileId - The file ID
     * @param {string} status - The file status (queued, uploading, processing, completed, failed)
     * @param {number} progress - The progress percentage (0-100)
     * @param {string} statusText - The status text
     * @param {string} documentId - The document ID (if available)
     */
    function updateFileProgress(fileId, status, progress, statusText, documentId = null) {
        // Update the progress map
        if (fileProgressMap[fileId]) {
            fileProgressMap[fileId].status = status;
            fileProgressMap[fileId].progress = progress;
            if (documentId) {
                fileProgressMap[fileId].documentId = documentId;
            }
        }

        // Update the progress UI
        const fileItem = document.getElementById(`progress-${fileId}`);
        if (fileItem) {
            // Update the badge
            const badge = fileItem.querySelector('.badge');
            if (badge) {
                badge.className = `badge bg-${getBadgeColorForStatus(status)}`;
                badge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }

            // Update the progress bar
            const progressBar = fileItem.querySelector('.progress-bar');
            if (progressBar) {
                progressBar.style.width = `${progress}%`;
                progressBar.setAttribute('aria-valuenow', progress);
            }

            // Update the status text
            const statusTextElement = fileItem.querySelector('.status-text');
            if (statusTextElement) {
                statusTextElement.textContent = statusText;
            }
        }
    }

    /**
     * Get badge color for status
     *
     * @param {string} status - The file status
     * @returns {string} - The badge color class
     */
    function getBadgeColorForStatus(status) {
        switch (status) {
            case 'queued':
                return 'secondary';
            case 'uploading':
                return 'primary';
            case 'processing':
                return 'info';
            case 'completed':
                return 'success';
            case 'failed':
                return 'danger';
            default:
                return 'secondary';
        }
    }

    /**
     * Upload files
     */
    function uploadFiles() {
        if (filesToUpload.length === 0) {
            showAlert('Please select at least one file to upload.', 'warning');
            return;
        }

        // Validate inputs
        const chunkSize = parseInt(chunkSizeInput?.value || 1200);
        const overlap = parseInt(overlapInput?.value || 0);
        const extractEntities = extractEntitiesCheckbox?.checked !== false;
        const extractReferences = extractReferencesCheckbox?.checked !== false;
        const extractMetadata = extractMetadataCheckbox?.checked !== false;
        const maxParallelProcesses = parseInt(maxParallelProcessesInput?.value || 4);

        if (isNaN(chunkSize) || chunkSize < 100 || chunkSize > 10000) {
            showAlert('Chunk size must be between 100 and 10000 characters.', 'danger');
            return;
        }

        if (isNaN(overlap) || overlap < 0 || overlap > 500) {
            showAlert('Overlap must be between 0 and 500 characters.', 'danger');
            return;
        }

        if (isNaN(maxParallelProcesses) || maxParallelProcesses < 1 || maxParallelProcesses > 10) {
            showAlert('Max parallel processes must be between 1 and 10.', 'danger');
            return;
        }

        // Initialize file progress tracking
        initializeFileProgress();

        // Show progress
        if (progressContainer) {
            progressContainer.classList.remove('d-none');
        }

        if (progressBar) {
            progressBar.style.width = '0%';
            progressBar.setAttribute('aria-valuenow', 0);
        }

        if (progressText) {
            progressText.textContent = 'Uploading files...';
        }

        // Disable upload button
        if (uploadButton) {
            uploadButton.disabled = true;
        }

        // Create form data
        const formData = new FormData();

        // Add files and update their status to uploading
        filesToUpload.forEach((file, index) => {
            formData.append('files', file);

            // Get the file ID
            const fileId = Object.keys(fileProgressMap).find(id => fileProgressMap[id].name === file.name);
            if (fileId) {
                updateFileProgress(fileId, 'uploading', 10, 'Uploading file...');
            }
        });

        // Add other form data
        formData.append('chunk_size', chunkSize);
        formData.append('overlap', overlap);
        formData.append('extract_entities', extractEntities);
        formData.append('extract_references', extractReferences);
        formData.append('extract_metadata', extractMetadata);
        formData.append('max_parallel_processes', maxParallelProcesses);

        // Upload files
        fetch('/api/batch-upload', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Update progress
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.setAttribute('aria-valuenow', 100);
            }

            if (progressText) {
                progressText.textContent = 'Upload complete! Processing files...';
            }

            // Show success message
            showAlert(`Successfully uploaded ${data.successful_uploads} of ${data.total_files} files.`, 'success');

            // Update individual file progress
            if (data.documents && data.documents.length > 0) {
                data.documents.forEach(doc => {
                    // Find the file ID by name
                    const fileId = Object.keys(fileProgressMap).find(id => fileProgressMap[id].name === doc.filename);

                    if (fileId) {
                        if (doc.success) {
                            // Update to processing status
                            updateFileProgress(
                                fileId,
                                'processing',
                                30,
                                'Processing document in background...',
                                doc.uuid
                            );

                            // Start tracking progress for this document
                            startDocumentProgressTracking(fileId, doc.uuid);
                        } else {
                            // Update to failed status
                            updateFileProgress(
                                fileId,
                                'failed',
                                0,
                                `Failed: ${doc.error || 'Unknown error'}`,
                                null
                            );
                        }
                    }
                });
            }

            // Clear file list but keep the progress tracking
            filesToUpload = [];
            updateFileList();

            // Refresh document list if available
            if (typeof refreshDocumentList === 'function') {
                refreshDocumentList();
            }
        })
        .catch(error => {
            console.error('Error uploading files:', error);

            // Show error message
            showAlert(`Error uploading files: ${error.message}`, 'danger');

            // Update progress
            if (progressBar) {
                progressBar.style.width = '100%';
                progressBar.classList.remove('bg-primary');
                progressBar.classList.add('bg-danger');
            }

            if (progressText) {
                progressText.textContent = 'Upload failed!';
            }
        })
        .finally(() => {
            // Enable upload button
            if (uploadButton) {
                uploadButton.disabled = false;
            }
        });
    }

    /**
     * Start tracking progress for a specific document
     *
     * @param {string} fileId - The file ID
     * @param {string} documentId - The document ID
     */
    function startDocumentProgressTracking(fileId, documentId) {
        // Set up an interval to check progress
        const intervalId = setInterval(() => {
            checkDocumentProgress(fileId, documentId, intervalId);
        }, 3000); // Check every 3 seconds
    }

    /**
     * Check document progress
     *
     * @param {string} fileId - The file ID
     * @param {string} documentId - The document ID
     * @param {number} intervalId - The interval ID for clearing
     */
    function checkDocumentProgress(fileId, documentId, intervalId) {
        fetch(`/api/document-progress/${documentId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Calculate progress based on the step
                let progress = 30; // Start at 30% (after upload)
                let statusText = 'Processing document...';

                if (data.status === 'processing') {
                    // Calculate progress based on the step and percentage
                    const stepProgress = data.progress_percentage || 0;
                    const currentStep = data.current_step || 0;
                    const totalSteps = data.total_steps || 5;

                    // Calculate overall progress (30% for upload, 70% for processing)
                    progress = 30 + Math.round((currentStep / totalSteps) * 70 * (stepProgress / 100));
                    statusText = data.step_name || 'Processing document...';
                } else if (data.status === 'completed') {
                    progress = 100;
                    statusText = 'Processing complete!';

                    // Update file progress
                    updateFileProgress(fileId, 'completed', progress, statusText);

                    // Clear the interval
                    clearInterval(intervalId);
                    return;
                } else if (data.status === 'failed') {
                    progress = 0;
                    statusText = `Failed: ${data.details?.error || 'Unknown error'}`;

                    // Update file progress
                    updateFileProgress(fileId, 'failed', progress, statusText);

                    // Clear the interval
                    clearInterval(intervalId);
                    return;
                } else if (data.status === 'queued') {
                    progress = 30;
                    statusText = 'Waiting in queue...';
                }

                // Update file progress
                updateFileProgress(fileId, 'processing', progress, statusText);
            })
            .catch(error => {
                console.error(`Error checking progress for document ${documentId}:`, error);

                // After several retries, mark as failed
                const fileProgress = fileProgressMap[fileId];
                if (fileProgress && fileProgress.retries >= 5) {
                    updateFileProgress(fileId, 'failed', 0, 'Failed to check progress');
                    clearInterval(intervalId);
                } else {
                    // Increment retry count
                    if (fileProgressMap[fileId]) {
                        fileProgressMap[fileId].retries = (fileProgressMap[fileId].retries || 0) + 1;
                    }
                }
            });
    }

    /**
     * Show alert
     * @param {string} message - Alert message
     * @param {string} type - Alert type (success, danger, warning, info)
     */
    function showAlert(message, type = 'info') {
        if (!alertContainer) return;

        const alert = document.createElement('div');
        alert.className = `alert alert-${type} alert-dismissible fade show`;
        alert.role = 'alert';

        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        alertContainer.appendChild(alert);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            alert.classList.remove('show');
            setTimeout(() => {
                alertContainer.removeChild(alert);
            }, 150);
        }, 5000);
    }
});
