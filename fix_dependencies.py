#!/usr/bin/env python3
"""
Fix dependency conflicts in the Graphiti environment.

This script resolves the major dependency conflicts that are preventing
the frontend from starting properly.
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Error: {e.stderr}")
        return False

def main():
    """Fix dependency conflicts."""
    print("🚀 FIXING GRAPHITI DEPENDENCY CONFLICTS")
    print("=" * 60)
    
    # Step 1: Upgrade core dependencies that are causing conflicts
    core_upgrades = [
        "pip install --upgrade typing-extensions>=4.12.0",
        "pip install --upgrade pydantic>=2.8.2",
        "pip install --upgrade fastapi>=0.115.9",
        "pip install --upgrade httpx>=0.28.1",
        "pip install --upgrade uvicorn>=0.34.0",
        "pip install --upgrade anyio>=4.8.0",
        "pip install --upgrade openai>=1.68.2",
        "pip install --upgrade python-dotenv>=1.0.1",
        "pip install --upgrade jinja2>=3.1.3"
    ]
    
    print("📦 Upgrading core dependencies...")
    for command in core_upgrades:
        if not run_command(command, f"Upgrading {command.split()[-1]}"):
            print(f"⚠️ Failed to upgrade {command.split()[-1]}, continuing...")
    
    # Step 2: Reinstall problematic packages
    print("\n🔄 Reinstalling problematic packages...")
    reinstall_packages = [
        "pip install --upgrade --force-reinstall chromadb",
        "pip install --upgrade --force-reinstall google-genai",
        "pip install --upgrade --force-reinstall graphiti-core",
        "pip install --upgrade --force-reinstall langchain",
        "pip install --upgrade --force-reinstall mistralai",
        "pip install --upgrade --force-reinstall ollama",
        "pip install --upgrade --force-reinstall mcp"
    ]
    
    for command in reinstall_packages:
        package_name = command.split()[-1]
        if not run_command(command, f"Reinstalling {package_name}"):
            print(f"⚠️ Failed to reinstall {package_name}, continuing...")
    
    # Step 3: Install any missing requirements
    print("\n📋 Installing requirements from requirements.txt...")
    if Path("requirements.txt").exists():
        run_command("pip install -r requirements.txt", "Installing requirements.txt")
    
    # Step 4: Check for conflicts
    print("\n🔍 Checking for remaining conflicts...")
    run_command("pip check", "Checking for dependency conflicts")
    
    print("\n✅ DEPENDENCY FIX COMPLETE!")
    print("=" * 60)
    print("🚀 Try starting the frontend now with:")
    print("   python app.py")
    print("   or")
    print("   python start.py ui")
    print("\n📍 Frontend should be available at: http://localhost:9753")

if __name__ == "__main__":
    main()
