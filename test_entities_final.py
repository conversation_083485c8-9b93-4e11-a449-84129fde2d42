#!/usr/bin/env python3
"""
Final test for entities API.
"""

import requests
import time

def test_entities_final():
    """Final test for entities API"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Final Entities Test")
    print("=" * 30)
    
    try:
        response = requests.get(f"{base_url}/api/entities?limit=5", timeout=5)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            
            print(f"Entities returned: {len(entities)}")
            print(f"Total count: {data.get('total_count', 0)}")
            
            if entities:
                print("\nValid entities:")
                for i, entity in enumerate(entities):
                    name = entity.get('name', 'N/A')
                    entity_type = entity.get('type', 'N/A')
                    mentions = entity.get('mention_count', 0)
                    
                    # Check if entity has valid name and type
                    if name and name.strip() and entity_type and entity_type.strip():
                        print(f"  ✅ {i+1}. {name} ({entity_type}) - {mentions} mentions")
                    else:
                        print(f"  ❌ {i+1}. Invalid entity: name='{name}', type='{entity_type}'")
            else:
                print("❌ No entities returned")
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_entities_final()
