"""
Advanced Reference Processor

A comprehensive, high-performance reference extraction system designed to handle
academic documents of any size with various reference formats and structures.
"""

import re
import os
import uuid
import logging
from typing import Dict, List, Any, Optional, Tuple, Set
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import asyncio

# Set up logging
logger = logging.getLogger(__name__)

class ReferenceFormat(Enum):
    """Enumeration of reference formats"""
    NUMBERED_PERIOD = "numbered_period"      # 1. Author...
    NUMBERED_BRACKET = "numbered_bracket"    # [1] Author...
    DASH_BRACKET = "dash_bracket"           # - [1] Author...
    AUTHOR_YEAR = "author_year"             # Author (Year)...
    BULLET_POINT = "bullet_point"           # • Author...
    MIXED = "mixed"                         # Multiple formats
    UNKNOWN = "unknown"                     # Unrecognized format

@dataclass
class ReferenceSection:
    """Data class for reference section information"""
    start_pos: int
    end_pos: int
    format_type: ReferenceFormat
    header_text: str
    confidence: float
    reference_count: int

@dataclass
class ExtractedReference:
    """Data class for extracted reference"""
    number: Optional[int]
    text: str
    authors: List[str]
    title: Optional[str]
    year: Optional[int]
    journal: Optional[str]
    doi: Optional[str]
    url: Optional[str]
    confidence: float
    format_type: ReferenceFormat

class AdvancedReferenceProcessor:
    """
    Advanced reference processor with comprehensive pattern matching,
    batch processing capabilities, and high accuracy extraction.
    """
    
    def __init__(self):
        """Initialize the advanced reference processor."""
        self.logger = logging.getLogger(__name__)
        self.reference_patterns = self._initialize_patterns()
        self.header_patterns = self._initialize_header_patterns()
        self.validation_patterns = self._initialize_validation_patterns()
        
    def _initialize_patterns(self) -> Dict[ReferenceFormat, List[str]]:
        """Initialize comprehensive reference patterns."""
        return {
            ReferenceFormat.NUMBERED_PERIOD: [
                r'(\d+)\.\s+([^0-9]+?)(?=\s*\d+\.\s+|$)',  # Split by next number
                r'(?:^|\n)\s*(\d+)\.\s+([A-Z].*?)(?=\n\s*\d+\.|$)',  # More flexible: any text after number
                r'(\d+)\.\s+([A-Z][^.\n]*(?:\.[^.\n]*)*\.)',  # Original strict pattern
                r'\n\s*(\d+)\.\s+(.+?)(?=\n\s*\d+\.|$)',  # Newline-based
            ],
            ReferenceFormat.NUMBERED_BRACKET: [
                r'(?:^|\n)\s*\[(\d+)\]\s+(.+?)(?=\n\s*\[\d+\]|$)',
                r'\[(\d+)\]\s+([A-Z].*?)(?=\[\d+\]|$)',
                r'(?:^|\n)\s*\[(\d+)\]\s+(.+?)(?=\n|$)',
            ],
            ReferenceFormat.DASH_BRACKET: [
                r'(?:^|\n)\s*-\s*\[(\d+)\]\s+(.+?)(?=\n\s*-\s*\[\d+\]|$)',
                r'-\s*\[(\d+)\]\s+([A-Z].*?)(?=-\s*\[\d+\]|$)',
                r'(?:^|\n)\s*-\s*\[(\d+)\]\s+(.+?)(?=\n|$)',
            ],
            ReferenceFormat.AUTHOR_YEAR: [
                r'(?:^|\n)\s*([A-Z][a-z]+(?:,\s*[A-Z]\.)*)\s*\((\d{4})\)\.\s*(.+?)(?=\n[A-Z]|$)',
                r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s*\((\d{4})\)\.\s*(.+?)(?=\n|$)',
            ],
            ReferenceFormat.BULLET_POINT: [
                r'(?:^|\n)\s*[•·▪▫]\s+(.+?)(?=\n\s*[•·▪▫]|$)',
                r'(?:^|\n)\s*\*\s+(.+?)(?=\n\s*\*|$)',
            ]
        }
    
    def _initialize_header_patterns(self) -> List[str]:
        """Initialize reference section header patterns."""
        return [
            # Markdown headers with various formatting (more flexible)
            r'(?:^|\n)\s*#{1,6}\s*\*?\*?\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited|LITERATURE\s+REFERENCES?)\s*\*?\*?\s*(?:\n|$)',

            # Standard headers (case insensitive)
            r'(?:^|\n)\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited|LITERATURE\s+REFERENCES?|REFERENCE\s+LIST|CITED\s+LITERATURE)\s*(?:\n|$)',
            
            # Numbered section headers
            r'(?:^|\n)\s*\d+\.\s*(?:REFERENCES?|Bibliography|LITERATURE|Citations?)\s*(?:\n|$)',
            
            # Bold/emphasized headers
            r'(?:^|\n)\s*\*\*\s*(?:REFERENCES?|Bibliography|Citations?)\s*\*\*\s*(?:\n|$)',
            r'(?:^|\n)\s*__\s*(?:REFERENCES?|Bibliography|Citations?)\s*__\s*(?:\n|$)',
            
            # Headers with special characters
            r'(?:^|\n)\s*=+\s*(?:REFERENCES?|Bibliography)\s*=+\s*(?:\n|$)',
            r'(?:^|\n)\s*-+\s*(?:REFERENCES?|Bibliography)\s*-+\s*(?:\n|$)',
            
            # Case variations
            r'(?:^|\n)\s*(?:References?|REFERENCES?|bibliography|BIBLIOGRAPHY|citations?|CITATIONS?)\s*:?\s*(?:\n|$)',
        ]
    
    def _initialize_validation_patterns(self) -> Dict[str, str]:
        """Initialize validation patterns for reference quality."""
        return {
            'author': r'[A-Z][a-z]+(?:,\s*[A-Z]\.?)*|[A-Z][a-z]+\s+[A-Z][a-z]+',
            'year': r'\b(19|20)\d{2}\b',
            'journal': r'[A-Z][a-z\s]+(?:Journal|Review|Proceedings|Magazine|Quarterly)',
            'doi': r'doi[:\s]*10\.\d+/[^\s,;]+',
            'url': r'https?://[^\s,;]+',
            'volume_page': r'vol\.\s*\d+|volume\s*\d+|pp?\.\s*\d+',
            'academic_indicators': [
                r'\bjournal\b', r'\bproceedings\b', r'\bconference\b', r'\breview\b',
                r'\bvol\.\b', r'\bpp\.\b', r'\bdoi\b', r'\bissn\b', r'\bisbn\b'
            ]
        }

    async def extract_references_from_text(self, text: str, document_name: str = "unknown") -> Dict[str, Any]:
        """
        Main entry point for reference extraction.
        
        Args:
            text: Full document text
            document_name: Name of the document for logging
            
        Returns:
            Dictionary containing extracted references and metadata
        """
        try:
            self.logger.info(f"🔍 Starting advanced reference extraction for {document_name}")
            self.logger.info(f"📄 Document length: {len(text):,} characters")
            
            # Step 1: Find reference sections
            sections = await self._find_reference_sections(text)
            self.logger.info(f"📚 Found {len(sections)} potential reference sections")
            
            # Step 2: Extract references from each section
            all_references = []
            section_stats = []
            
            for i, section in enumerate(sections):
                self.logger.info(f"🔍 Processing section {i+1}: {section.format_type.value} format")
                section_text = text[section.start_pos:section.end_pos]
                
                references = await self._extract_references_from_section(section_text, section.format_type)
                all_references.extend(references)
                
                section_stats.append({
                    'section_number': i + 1,
                    'format': section.format_type.value,
                    'references_found': len(references),
                    'confidence': section.confidence,
                    'header': section.header_text[:100] + "..." if len(section.header_text) > 100 else section.header_text
                })
                
                self.logger.info(f"✅ Extracted {len(references)} references from section {i+1}")
            
            # Step 3: Fallback extraction if no sections found OR if very few references found
            if not all_references or len(all_references) < 5:
                self.logger.info("🔄 No structured sections found or insufficient references, attempting general pattern extraction")
                fallback_references = await self._extract_with_general_patterns(text)
                if len(fallback_references) > len(all_references):
                    all_references = fallback_references
            
            # Step 4: Deduplicate and validate
            unique_references = await self._deduplicate_references(all_references)
            validated_references = await self._validate_references(unique_references)
            
            # Step 5: Prepare results
            result = {
                'references': [self._reference_to_dict(ref) for ref in validated_references],
                'total_found': len(validated_references),
                'sections_analyzed': len(sections),
                'section_details': section_stats,
                'document_name': document_name,
                'extraction_method': 'structured' if sections else 'general_patterns',
                'confidence_score': self._calculate_overall_confidence(validated_references, sections)
            }
            
            self.logger.info(f"🎉 Reference extraction completed: {len(validated_references)} high-quality references found")
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Error in reference extraction for {document_name}: {e}")
            return {
                'references': [],
                'total_found': 0,
                'sections_analyzed': 0,
                'section_details': [],
                'document_name': document_name,
                'extraction_method': 'failed',
                'confidence_score': 0.0,
                'error': str(e)
            }

    async def _find_reference_sections(self, text: str) -> List[ReferenceSection]:
        """Find and analyze reference sections in the document."""
        sections = []
        
        # Try to find sections using header patterns
        for pattern in self.header_patterns:
            try:
                matches = list(re.finditer(pattern, text, re.IGNORECASE | re.MULTILINE))
                for match in matches:
                    section = await self._analyze_potential_section(text, match)
                    if section and section.confidence > 0.2:  # Reduced confidence threshold
                        sections.append(section)
            except re.error as e:
                self.logger.warning(f"Regex error in header pattern: {e}")
                continue
        
        # If no header-based sections found, look for high-density reference areas
        if not sections:
            sections = await self._find_reference_dense_areas(text)
        
        # Sort by confidence and remove overlapping sections
        sections = await self._resolve_overlapping_sections(sections)
        
        return sections

    async def _analyze_potential_section(self, text: str, header_match: re.Match) -> Optional[ReferenceSection]:
        """Analyze a potential reference section starting from a header match."""
        start_pos = header_match.start()
        header_text = header_match.group(0).strip()

        # Find the end of the section
        end_pos = await self._find_section_end(text, header_match.end())

        if end_pos - start_pos < 100:  # Too short to be a meaningful reference section
            return None

        section_text = text[start_pos:end_pos]

        # Analyze the format and quality of this section
        format_analysis = await self._analyze_section_format(section_text)

        if format_analysis['confidence'] < 0.2:  # Reduced threshold
            return None

        return ReferenceSection(
            start_pos=start_pos,
            end_pos=end_pos,
            format_type=format_analysis['format'],
            header_text=header_text,
            confidence=format_analysis['confidence'],
            reference_count=format_analysis['estimated_count']
        )

    async def _find_section_end(self, text: str, start_pos: int) -> int:
        """Find the end of a reference section."""
        # Look for common section endings
        end_patterns = [
            r'\n\s*(?:APPENDIX|ACKNOWLEDGMENTS?|ACKNOWLEDGEMENTS?|AUTHOR\s+INFORMATION|CONFLICT\s+OF\s+INTEREST|FUNDING|SUPPLEMENTARY|ADDITIONAL\s+INFORMATION|FIGURE\s+LEGENDS?|TABLE\s+LEGENDS?)\s*\n',
            r'\n\s*\d+\.\s*(?:APPENDIX|ACKNOWLEDGMENTS?|ACKNOWLEDGEMENTS?|DISCUSSION|CONCLUSION|FIGURES?|TABLES?)\s*\n',
            r'\n\s*(?:Figure\s+\d+|Table\s+\d+|Supplementary|Appendix)',
            r'\n\s*#{1,6}\s*(?:APPENDIX|ACKNOWLEDGMENTS?|FIGURES?|TABLES?)',  # Markdown headers
        ]

        end_pos = len(text)
        search_text = text[start_pos:]

        for pattern in end_patterns:
            try:
                match = re.search(pattern, search_text, re.IGNORECASE | re.MULTILINE)
                if match:
                    potential_end = start_pos + match.start()
                    if potential_end < end_pos:
                        end_pos = potential_end
            except re.error:
                continue

        return end_pos

    async def _analyze_section_format(self, section_text: str) -> Dict[str, Any]:
        """Analyze the format and quality of a potential reference section."""
        format_scores = {}

        # Test each reference format
        for ref_format, patterns in self.reference_patterns.items():
            score = 0
            matches = 0

            for pattern in patterns:
                try:
                    pattern_matches = list(re.finditer(pattern, section_text, re.MULTILINE | re.DOTALL))
                    matches += len(pattern_matches)

                    # Score based on number of matches and their quality
                    for match in pattern_matches[:10]:  # Limit to first 10 for performance
                        ref_text = match.group(0) if match.groups() == () else match.group(-1)
                        quality_score = await self._assess_reference_quality(ref_text)
                        score += quality_score

                except re.error:
                    continue

            if matches > 0:
                format_scores[ref_format] = {
                    'score': score / max(matches, 1),
                    'matches': matches,
                    'total_score': score
                }

        # Determine the best format
        if not format_scores:
            return {'format': ReferenceFormat.UNKNOWN, 'confidence': 0.0, 'estimated_count': 0}

        best_format = max(format_scores.keys(), key=lambda f: format_scores[f]['total_score'])
        best_score = format_scores[best_format]

        # Calculate confidence based on multiple factors
        confidence = min(1.0, (
            (best_score['score'] * 0.4) +  # Quality of individual references
            (min(best_score['matches'] / 10, 1.0) * 0.3) +  # Number of matches (capped at 10)
            (0.3 if best_score['matches'] >= 5 else 0.1)  # Minimum threshold bonus
        ))

        return {
            'format': best_format,
            'confidence': confidence,
            'estimated_count': best_score['matches'],
            'all_scores': format_scores
        }

    async def _assess_reference_quality(self, ref_text: str) -> float:
        """Assess the quality of a potential reference."""
        if len(ref_text) < 20:
            return 0.0

        score = 0.0

        # Check for academic indicators
        for indicator in self.validation_patterns['academic_indicators']:
            if re.search(indicator, ref_text, re.IGNORECASE):
                score += 0.15

        # Check for author patterns
        if re.search(self.validation_patterns['author'], ref_text):
            score += 0.25

        # Check for year
        if re.search(self.validation_patterns['year'], ref_text):
            score += 0.20

        # Check for journal/publication
        if re.search(self.validation_patterns['journal'], ref_text, re.IGNORECASE):
            score += 0.20

        # Check for DOI or URL
        if re.search(self.validation_patterns['doi'], ref_text, re.IGNORECASE):
            score += 0.15
        elif re.search(self.validation_patterns['url'], ref_text):
            score += 0.10

        # Check for volume/page information
        if re.search(self.validation_patterns['volume_page'], ref_text, re.IGNORECASE):
            score += 0.10

        # Length bonus (longer references are often more complete)
        if len(ref_text) > 100:
            score += 0.05

        return min(score, 1.0)

    async def _find_reference_dense_areas(self, text: str) -> List[ReferenceSection]:
        """Find areas with high density of reference-like patterns."""
        sections = []

        # Look for areas with multiple numbered items that could be references
        patterns_to_try = [
            (ReferenceFormat.DASH_BRACKET, r'-\s*\[(\d+)\]\s+.*?(?=-\s*\[\d+\]|$)'),
            (ReferenceFormat.NUMBERED_BRACKET, r'\[(\d+)\]\s+.*?(?=\[\d+\]|$)'),
            (ReferenceFormat.NUMBERED_PERIOD, r'(?:^|\n)\s*(\d+)\.\s+.*?(?=\n\s*\d+\.|$)'),
            (ReferenceFormat.NUMBERED_PERIOD, r'(\d+)\.\s+[A-Z].*?(?=\d+\.|$)'),  # More aggressive pattern
            (ReferenceFormat.NUMBERED_PERIOD, r'(?:^|\s)(\d+)\.\s+[A-Z][^.]*\.'),  # Simple period-ended references
        ]

        for ref_format, pattern in patterns_to_try:
            try:
                matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

                if len(matches) >= 5:  # Minimum threshold for a reference section
                    # Group consecutive matches
                    groups = await self._group_consecutive_matches(matches)

                    for group in groups:
                        if len(group) >= 5:  # At least 5 references in a group
                            start_pos = group[0].start()
                            end_pos = group[-1].end()

                            section_text = text[start_pos:end_pos]
                            # Calculate average quality
                            quality_scores = []
                            for match in group:
                                quality = await self._assess_reference_quality(match.group(0))
                                quality_scores.append(quality)
                            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0

                            if avg_quality > 0.3:  # Quality threshold
                                sections.append(ReferenceSection(
                                    start_pos=start_pos,
                                    end_pos=end_pos,
                                    format_type=ref_format,
                                    header_text=f"Auto-detected {ref_format.value} section",
                                    confidence=avg_quality,
                                    reference_count=len(group)
                                ))
            except re.error:
                continue

        return sections

    async def _group_consecutive_matches(self, matches: List[re.Match]) -> List[List[re.Match]]:
        """Group consecutive matches that are likely part of the same reference section."""
        if not matches:
            return []

        groups = []
        current_group = [matches[0]]

        for i in range(1, len(matches)):
            # If the gap between matches is reasonable (less than 1000 characters)
            # and they appear to be consecutive numbers, group them together
            gap = matches[i].start() - matches[i-1].end()

            if gap < 1000:  # Reasonable gap
                current_group.append(matches[i])
            else:
                if len(current_group) >= 3:  # Minimum group size
                    groups.append(current_group)
                current_group = [matches[i]]

        # Don't forget the last group
        if len(current_group) >= 3:
            groups.append(current_group)

        return groups

    async def _resolve_overlapping_sections(self, sections: List[ReferenceSection]) -> List[ReferenceSection]:
        """Resolve overlapping sections by keeping the highest confidence ones."""
        if len(sections) <= 1:
            return sections

        # Sort by confidence (highest first)
        sections.sort(key=lambda s: s.confidence, reverse=True)

        resolved = []
        for section in sections:
            # Check if this section overlaps with any already resolved section
            overlaps = False
            for resolved_section in resolved:
                if (section.start_pos < resolved_section.end_pos and
                    section.end_pos > resolved_section.start_pos):
                    overlaps = True
                    break

            if not overlaps:
                resolved.append(section)

        # Sort by position for processing
        resolved.sort(key=lambda s: s.start_pos)
        return resolved

    async def _extract_references_from_section(self, section_text: str, format_type: ReferenceFormat) -> List[ExtractedReference]:
        """Extract individual references from a section based on its format."""
        references = []

        if format_type in self.reference_patterns:
            patterns = self.reference_patterns[format_type]

            for pattern in patterns:
                try:
                    matches = list(re.finditer(pattern, section_text, re.MULTILINE | re.DOTALL))

                    for match in matches:
                        ref = await self._parse_reference_match(match, format_type)
                        if ref and ref.confidence > 0.2:  # Reduced quality threshold
                            references.append(ref)

                    # If we found good references with this pattern, use them
                    if references:
                        break

                except re.error as e:
                    self.logger.warning(f"Regex error in reference extraction: {e}")
                    continue

        return references

    async def _parse_reference_match(self, match: re.Match, format_type: ReferenceFormat) -> Optional[ExtractedReference]:
        """Parse a regex match into a structured reference."""
        try:
            groups = match.groups()

            if format_type in [ReferenceFormat.NUMBERED_PERIOD, ReferenceFormat.NUMBERED_BRACKET, ReferenceFormat.DASH_BRACKET]:
                if len(groups) >= 2:
                    number = int(groups[0]) if groups[0].isdigit() else None
                    ref_text = groups[1].strip()
                else:
                    number = None
                    ref_text = match.group(0).strip()
            elif format_type == ReferenceFormat.AUTHOR_YEAR:
                if len(groups) >= 3:
                    ref_text = f"{groups[0]} ({groups[1]}). {groups[2]}".strip()
                    number = None
                else:
                    ref_text = match.group(0).strip()
                    number = None
            else:
                ref_text = match.group(0).strip()
                number = None

            # Extract metadata from the reference text
            metadata = await self._extract_reference_metadata(ref_text)

            # Calculate confidence based on completeness and quality
            confidence = await self._calculate_reference_confidence(ref_text, metadata)

            return ExtractedReference(
                number=number,
                text=ref_text,
                authors=metadata.get('authors', []),
                title=metadata.get('title'),
                year=metadata.get('year'),
                journal=metadata.get('journal'),
                doi=metadata.get('doi'),
                url=metadata.get('url'),
                confidence=confidence,
                format_type=format_type
            )

        except Exception as e:
            self.logger.warning(f"Error parsing reference match: {e}")
            return None

    async def _extract_reference_metadata(self, ref_text: str) -> Dict[str, Any]:
        """Extract structured metadata from reference text."""
        metadata = {}

        # Extract authors
        author_patterns = [
            r'^([A-Z][a-z]+(?:,\s*[A-Z]\.?)*(?:\s+and\s+[A-Z][a-z]+(?:,\s*[A-Z]\.?)*)*)',
            r'^([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*(?:,\s*[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)*)',
        ]

        for pattern in author_patterns:
            try:
                match = re.search(pattern, ref_text)
                if match:
                    authors_text = match.group(1)
                    # Split authors by 'and' or commas
                    authors = [author.strip() for author in re.split(r'\s+and\s+|,\s*(?=[A-Z])', authors_text)]
                    metadata['authors'] = [author for author in authors if len(author) > 2]
                    break
            except re.error:
                continue

        # Extract year
        try:
            year_match = re.search(r'\b(19|20)\d{2}\b', ref_text)
            if year_match:
                metadata['year'] = int(year_match.group(0))
        except (re.error, ValueError):
            pass

        # Extract title (text in quotes or after authors before year)
        try:
            title_patterns = [
                r'"([^"]+)"',
                r'["""]([^"""]+)["""]',
                r'(?:\.|\))\s*([A-Z][^.]*?)\.\s*(?:[A-Z][a-z]+\s+|$)',
            ]

            for pattern in title_patterns:
                match = re.search(pattern, ref_text)
                if match:
                    metadata['title'] = match.group(1).strip()
                    break
        except re.error:
            pass

        # Extract journal
        try:
            journal_patterns = [
                r'\b([A-Z][a-z\s]+(?:Journal|Review|Proceedings|Magazine|Quarterly))\b',
                r'\.\s*([A-Z][a-z\s]+),\s*vol\.',
                r'\.\s*([A-Z][a-z\s]+),\s*\d+',
            ]

            for pattern in journal_patterns:
                match = re.search(pattern, ref_text, re.IGNORECASE)
                if match:
                    metadata['journal'] = match.group(1).strip()
                    break
        except re.error:
            pass

        # Extract DOI
        try:
            doi_match = re.search(r'doi[:\s]*(10\.\d+/[^\s,;]+)', ref_text, re.IGNORECASE)
            if doi_match:
                metadata['doi'] = doi_match.group(1)
        except re.error:
            pass

        # Extract URL
        try:
            url_match = re.search(r'(https?://[^\s,;]+)', ref_text)
            if url_match:
                metadata['url'] = url_match.group(1)
        except re.error:
            pass

        return metadata

    async def _calculate_reference_confidence(self, ref_text: str, metadata: Dict[str, Any]) -> float:
        """Calculate confidence score for a reference based on completeness and quality."""
        score = 0.0

        # Base score for having text
        if len(ref_text) > 20:
            score += 0.2

        # Author score
        if metadata.get('authors'):
            score += 0.25
            if len(metadata['authors']) > 1:
                score += 0.05

        # Year score
        if metadata.get('year'):
            score += 0.2

        # Title score
        if metadata.get('title'):
            score += 0.15

        # Journal score
        if metadata.get('journal'):
            score += 0.15

        # DOI/URL score
        if metadata.get('doi'):
            score += 0.1
        elif metadata.get('url'):
            score += 0.05

        # Length bonus
        if len(ref_text) > 100:
            score += 0.05

        return min(score, 1.0)

    async def _extract_with_general_patterns(self, text: str) -> List[ExtractedReference]:
        """Fallback extraction using general patterns when no structured sections are found."""
        references = []

        # First try to find and split references section manually
        ref_section_match = re.search(r'(?:##\s*)?REFERENCES?\s+(.*?)(?=##|$)', text, re.DOTALL | re.IGNORECASE)
        if ref_section_match:
            ref_section = ref_section_match.group(1).strip()
            self.logger.info(f"📚 Found references section ({len(ref_section):,} chars), attempting manual split")

            # Split by numbered references pattern
            parts = re.split(r'\s+(\d+)\.\s+', ref_section)

            if len(parts) > 3:  # Should have at least one reference
                for i in range(1, len(parts), 2):  # Skip first part, then take every other
                    if i + 1 < len(parts):
                        ref_num = parts[i]
                        ref_text = parts[i + 1].strip()

                        # Clean up the reference text (remove next number if present)
                        ref_text = re.sub(r'\s+\d+\.\s*$', '', ref_text)

                        if ref_text and len(ref_text) > 20:  # Minimum length check
                            # Create a mock match object for parsing
                            class MockMatch:
                                def __init__(self, num, text):
                                    self._groups = (num, text)
                                def groups(self):
                                    return self._groups
                                def group(self, n):
                                    return self._groups[n-1] if n > 0 else f"{self._groups[0]}. {self._groups[1]}"

                            mock_match = MockMatch(ref_num, ref_text)
                            ref = await self._parse_reference_match(mock_match, ReferenceFormat.NUMBERED_PERIOD)
                            if ref and ref.confidence > 0.2:  # Lower threshold for manual split
                                references.append(ref)

                if references:
                    self.logger.info(f"✅ Manual split extracted {len(references)} references")
                    return references

        # Fallback to pattern matching
        self.logger.info("🔄 Manual split failed, trying pattern matching")
        for format_type, patterns in self.reference_patterns.items():
            for pattern in patterns:
                try:
                    matches = list(re.finditer(pattern, text, re.MULTILINE | re.DOTALL))

                    for match in matches:
                        ref = await self._parse_reference_match(match, format_type)
                        if ref and ref.confidence > 0.4:  # Higher threshold for general extraction
                            references.append(ref)

                except re.error:
                    continue

        return references

    async def _deduplicate_references(self, references: List[ExtractedReference]) -> List[ExtractedReference]:
        """Remove duplicate references based on similarity."""
        if not references:
            return references

        unique_refs = []
        seen_texts = set()

        for ref in references:
            # Create a normalized version for comparison
            normalized = re.sub(r'\s+', ' ', ref.text.lower().strip())
            normalized = re.sub(r'[^\w\s]', '', normalized)  # Remove punctuation

            # Check for similarity with existing references
            is_duplicate = False
            for seen_text in seen_texts:
                similarity = await self._calculate_text_similarity(normalized, seen_text)
                if similarity > 0.85:  # 85% similarity threshold
                    is_duplicate = True
                    break

            if not is_duplicate:
                unique_refs.append(ref)
                seen_texts.add(normalized)

        return unique_refs

    async def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings."""
        if not text1 or not text2:
            return 0.0

        # Simple character-based similarity
        len1, len2 = len(text1), len(text2)
        if len1 == 0 and len2 == 0:
            return 1.0
        if len1 == 0 or len2 == 0:
            return 0.0

        # Use a simple approach: count common characters
        common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
        max_len = max(len1, len2)

        return common_chars / max_len

    async def _validate_references(self, references: List[ExtractedReference]) -> List[ExtractedReference]:
        """Validate and filter references based on quality criteria."""
        validated = []

        for ref in references:
            # Basic validation criteria
            if (len(ref.text) >= 30 and  # Minimum length
                ref.confidence >= 0.3 and  # Minimum confidence
                (ref.authors or ref.year or ref.journal)):  # Must have some metadata

                validated.append(ref)

        return validated

    def _calculate_overall_confidence(self, references: List[ExtractedReference], sections: List[ReferenceSection]) -> float:
        """Calculate overall confidence score for the extraction."""
        if not references:
            return 0.0

        # Average reference confidence
        avg_ref_confidence = sum(ref.confidence for ref in references) / len(references)

        # Section-based confidence
        section_confidence = 0.0
        if sections:
            section_confidence = sum(section.confidence for section in sections) / len(sections)

        # Number of references factor
        count_factor = min(len(references) / 20, 1.0)  # Normalize to 20 references

        # Combine factors
        overall = (avg_ref_confidence * 0.5 + section_confidence * 0.3 + count_factor * 0.2)

        return min(overall, 1.0)

    def _reference_to_dict(self, ref: ExtractedReference) -> Dict[str, Any]:
        """Convert ExtractedReference to dictionary format."""
        return {
            'number': ref.number,
            'text': ref.text,
            'authors': ref.authors,
            'title': ref.title,
            'year': ref.year,
            'journal': ref.journal,
            'doi': ref.doi,
            'url': ref.url,
            'confidence': ref.confidence,
            'format_type': ref.format_type.value
        }

    async def process_large_document_in_batches(self, text: str, document_name: str = "unknown", batch_size: int = 200000) -> Dict[str, Any]:
        """
        Process very large documents in batches to handle memory constraints.

        Args:
            text: Full document text
            document_name: Name of the document
            batch_size: Size of each batch in characters

        Returns:
            Combined results from all batches
        """
        if len(text) <= batch_size:
            return await self.extract_references_from_text(text, document_name)

        self.logger.info(f"📚 Processing large document ({len(text):,} chars) in batches of {batch_size:,}")

        all_references = []
        all_sections = []
        batch_count = 0

        # Process in overlapping batches to avoid cutting references in half
        overlap = 10000  # 10KB overlap
        start = 0

        while start < len(text):
            end = min(start + batch_size, len(text))
            batch_text = text[start:end]
            batch_count += 1

            self.logger.info(f"🔄 Processing batch {batch_count}: chars {start:,} to {end:,}")

            batch_result = await self.extract_references_from_text(batch_text, f"{document_name}_batch_{batch_count}")

            all_references.extend(batch_result['references'])
            all_sections.extend(batch_result['section_details'])

            # Move start position with overlap
            start = end - overlap
            if start >= len(text) - overlap:
                break

        # Deduplicate across batches
        unique_references = await self._deduplicate_references([
            ExtractedReference(**ref) for ref in all_references
        ])

        return {
            'references': [self._reference_to_dict(ref) for ref in unique_references],
            'total_found': len(unique_references),
            'sections_analyzed': len(all_sections),
            'section_details': all_sections,
            'document_name': document_name,
            'extraction_method': 'batch_processing',
            'batch_count': batch_count,
            'confidence_score': sum(ref.confidence for ref in unique_references) / len(unique_references) if unique_references else 0.0
        }
