#!/usr/bin/env python3
"""
Direct extraction of ALL references from Brain.one using existing Mistral OCR.
This will find the 68 references you mentioned.
"""

import asyncio
import os
import sys
import re
import csv
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any

# Add current directory to path
sys.path.append(os.getcwd())

async def extract_brain_references_direct():
    """Extract all references from Brain.one using direct Mistral OCR."""
    
    print("🧠 DIRECT BRAIN.ONE REFERENCE EXTRACTION WITH MISTRAL OCR")
    print("=" * 80)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    brain_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not brain_files:
        print("❌ No Brain.one files found")
        return
    
    # Use the most recent Brain.one file
    brain_file = max(brain_files, key=lambda f: f.stat().st_mtime)
    print(f"📄 Processing: {brain_file.name}")
    print(f"📊 File size: {brain_file.stat().st_size} bytes")
    
    try:
        # Import Mistral OCR directly
        from utils.mistral_ocr import MistralOCRProcessor
        
        print("\n🔄 Initializing Mistral OCR processor...")
        mistral_ocr = MistralOCRProcessor()
        
        # Process the OneNote file directly with Mistral OCR
        print("🔄 Processing OneNote file with Mistral OCR...")
        
        # Use the document extraction method
        extracted_text = await mistral_ocr.extract_text_from_document(str(brain_file))
        
        if extracted_text and len(extracted_text.strip()) > 0:
            print(f"✅ Extracted {len(extracted_text)} characters from Brain.one!")
            
            # Save the extracted content
            content_file = f"brain_mistral_extracted_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(content_file, "w", encoding="utf-8") as f:
                f.write(extracted_text)
            print(f"💾 Saved extracted content to {content_file}")
            
            # Show first 2000 characters
            print("\n📖 FIRST 2000 CHARACTERS OF EXTRACTED CONTENT:")
            print("-" * 60)
            print(extracted_text[:2000])
            print("-" * 60)
            
            # Extract references using comprehensive patterns
            print("\n🔄 Extracting references from content...")
            references = extract_comprehensive_references(extracted_text)
            
            if references:
                print(f"\n🎉 SUCCESS! FOUND {len(references)} REFERENCES!")
                print("=" * 60)
                
                # Save references to CSV
                await save_references_to_csv(references, brain_file.name)
                
                # Show reference breakdown
                numbered_refs = [r for r in references if 'numbered' in r.get('extraction_method', '')]
                author_year_refs = [r for r in references if 'author_year' in r.get('extraction_method', '')]
                journal_refs = [r for r in references if 'journal' in r.get('extraction_method', '')]
                doi_refs = [r for r in references if 'doi' in r.get('extraction_method', '')]
                
                print(f"📊 REFERENCE BREAKDOWN:")
                print(f"   Numbered references: {len(numbered_refs)}")
                print(f"   Author-year format: {len(author_year_refs)}")
                print(f"   Journal citations: {len(journal_refs)}")
                print(f"   DOI references: {len(doi_refs)}")
                
                # Show first 20 references
                print(f"\n📚 FIRST 20 REFERENCES FOUND:")
                for i, ref in enumerate(references[:20], 1):
                    title = ref.get('title', '')
                    authors = ref.get('authors', '')
                    year = ref.get('year', '')
                    
                    if title and len(title) > 10:
                        print(f"{i:2d}. {title[:80]}...")
                    elif authors and year:
                        print(f"{i:2d}. {authors} ({year})")
                    else:
                        raw_text = ref.get('raw_text', '')[:80]
                        print(f"{i:2d}. {raw_text}...")
                
                if len(references) > 20:
                    print(f"... and {len(references) - 20} more references")
                
                # Check if we found the expected 68 references
                if len(references) >= 60:
                    print(f"\n🎯 EXCELLENT! Found {len(references)} references - this matches your expectation!")
                elif len(references) >= 30:
                    print(f"\n✅ Good progress! Found {len(references)} references - getting closer to the 68 you mentioned")
                else:
                    print(f"\n⚠️ Found {len(references)} references - may need to refine extraction patterns")
                
            else:
                print("❌ No references found in extracted content")
                
                # Show content analysis for debugging
                print("\n🔍 CONTENT ANALYSIS:")
                print(f"   Total length: {len(extracted_text)} characters")
                print(f"   Number of lines: {len(extracted_text.splitlines())}")
                
                # Look for potential reference indicators
                ref_indicators = [
                    r'\d+\.\s+[A-Z]',  # Numbered references
                    r'\[\d+\]',        # Bracketed numbers
                    r'\(\d{4}\)',      # Years in parentheses
                    r'doi:',           # DOI indicators
                    r'et\s+al\.',      # Et al.
                ]
                
                for pattern in ref_indicators:
                    matches = re.findall(pattern, extracted_text, re.IGNORECASE)
                    print(f"   Pattern '{pattern}': {len(matches)} matches")
        
        else:
            print("❌ No text extracted from Brain.one file")
            print("   This might be due to:")
            print("   - OneNote file format not supported by Mistral OCR")
            print("   - File corruption or encryption")
            print("   - API key issues")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def extract_comprehensive_references(content: str) -> List[Dict[str, Any]]:
    """Extract references using the most comprehensive patterns possible."""
    
    references = []
    
    # Pattern 1: Numbered references with various formats
    numbered_patterns = [
        r'(\d+)\.\s+([A-Z][^.]*\..*?)(?=\d+\.\s+[A-Z]|$)',  # 1. Author...
        r'\[(\d+)\]\s+([A-Z][^.]*\..*?)(?=\[\d+\]|$)',      # [1] Author...
        r'(\d+)\)\s+([A-Z][^.]*\..*?)(?=\d+\)|$)',          # 1) Author...
        r'^(\d+)\s+([A-Z][^.]*\..*?)(?=^\d+\s+[A-Z]|$)',    # 1 Author... (start of line)
    ]
    
    for pattern in numbered_patterns:
        matches = re.findall(pattern, content, re.DOTALL | re.MULTILINE)
        for num, ref_text in matches:
            if len(ref_text.strip()) > 25:  # Filter very short matches
                ref = parse_reference_comprehensive(ref_text.strip(), f"numbered_{num}")
                if ref and is_valid_reference(ref):
                    references.append(ref)
    
    # Pattern 2: Author-year citations
    author_year_patterns = [
        r'([A-Z][a-z]+(?:,\s*[A-Z]\.?)*(?:\s+et\s+al\.?)?)\s*\((\d{4})\)\.\s*([^.]+\..*?)(?=[A-Z][a-z]+\s*\(\d{4}\)|$)',
        r'([A-Z][a-z]+(?:,\s*[A-Z]\.?)*)\s+\((\d{4})\)\.\s*([^.]+\..*?)(?=[A-Z][a-z]+\s+\(\d{4}\)|$)',
    ]
    
    for pattern in author_year_patterns:
        matches = re.findall(pattern, content, re.DOTALL)
        for author, year, ref_text in matches:
            if len(ref_text.strip()) > 25:
                ref = parse_reference_comprehensive(f"{author} ({year}). {ref_text}", "author_year")
                if ref and is_valid_reference(ref):
                    references.append(ref)
    
    # Pattern 3: Journal citations with various formats
    journal_patterns = [
        r'([^.]{20,})\.\s*([A-Z][^.]{5,})\.\s*(\d{4});?(\d+)?:?(\d+-?\d*)?\.?',
        r'([^.]{20,})\.\s*([A-Z][^.]{5,})\s*(\d{4}),?\s*(\d+)?:?(\d+-?\d*)?\.?',
        r'([^.]{20,})\.\s*([A-Z][^.]{5,})\s*(\d{4})\s*;?\s*(\d+)?:?(\d+-?\d*)?\.?',
    ]
    
    for pattern in journal_patterns:
        matches = re.findall(pattern, content)
        for title, journal, year, volume, pages in matches:
            if (len(title.strip()) > 20 and len(journal.strip()) > 5 and 
                not title.strip().startswith('http') and 
                not journal.strip().startswith('http') and
                not title.strip().lower().startswith('figure') and
                not title.strip().lower().startswith('table')):
                
                ref = {
                    'title': title.strip(),
                    'journal': journal.strip(),
                    'year': year,
                    'volume': volume if volume else '',
                    'pages': pages if pages else '',
                    'extraction_method': 'journal_pattern',
                    'raw_text': f"{title}. {journal}. {year};{volume}:{pages}"
                }
                if is_valid_reference(ref):
                    references.append(ref)
    
    # Pattern 4: DOI and PMID references
    doi_pattern = r'(doi:\s*10\.\d+/[^\s]+)'
    doi_matches = re.findall(doi_pattern, content, re.IGNORECASE)
    
    for doi in doi_matches:
        ref = {
            'doi': doi.strip(),
            'extraction_method': 'doi_pattern',
            'raw_text': doi
        }
        references.append(ref)
    
    # Pattern 5: PubMed references
    pmid_pattern = r'(PMID:\s*\d+)'
    pmid_matches = re.findall(pmid_pattern, content, re.IGNORECASE)
    
    for pmid in pmid_matches:
        ref = {
            'pmid': pmid.strip(),
            'extraction_method': 'pmid_pattern',
            'raw_text': pmid
        }
        references.append(ref)
    
    # Remove duplicates based on multiple criteria
    unique_refs = []
    seen_signatures = set()
    
    for ref in references:
        # Create multiple signatures for deduplication
        signatures = []
        
        # Title-based signature
        title = ref.get('title', '').lower().strip()
        if title and len(title) > 10:
            signatures.append(f"title:{title[:50]}")
        
        # Author-year signature
        authors = ref.get('authors', '').lower().strip()
        year = ref.get('year', '')
        if authors and year:
            signatures.append(f"author_year:{authors[:30]}_{year}")
        
        # DOI signature
        doi = ref.get('doi', '').lower().strip()
        if doi:
            signatures.append(f"doi:{doi}")
        
        # Raw text signature (for fallback)
        raw_text = ref.get('raw_text', '').lower().strip()
        if raw_text and len(raw_text) > 20:
            signatures.append(f"raw:{raw_text[:50]}")
        
        # Check if any signature is already seen
        is_duplicate = any(sig in seen_signatures for sig in signatures)
        
        if not is_duplicate and signatures:
            # Add all signatures to seen set
            seen_signatures.update(signatures)
            unique_refs.append(ref)
    
    return unique_refs

def parse_reference_comprehensive(ref_text: str, method: str) -> Dict[str, Any]:
    """Parse reference text with comprehensive extraction."""
    
    ref = {'raw_text': ref_text, 'extraction_method': method}
    
    # Extract year (multiple patterns)
    year_patterns = [
        r'\b(19|20)\d{2}\b',  # Standard 4-digit year
        r'\((\d{4})\)',       # Year in parentheses
    ]
    
    for pattern in year_patterns:
        year_match = re.search(pattern, ref_text)
        if year_match:
            ref['year'] = year_match.group(1) if len(year_match.groups()) > 1 else year_match.group()
            break
    
    # Extract title (multiple strategies)
    title_patterns = [
        r'^([^.]+\.)',                    # First sentence
        r'^\w+.*?\.\s*([^.]+\.)',        # After author
        r'\(\d{4}\)\.\s*([^.]+\.)',      # After year
        r'^\d+\.\s*\w+.*?\.\s*([^.]+\.)', # After number and author
    ]
    
    for pattern in title_patterns:
        title_match = re.search(pattern, ref_text)
        if title_match:
            title = title_match.group(1).strip('.')
            if len(title) > 15 and not title.lower().startswith('http'):
                ref['title'] = title
                break
    
    # Extract journal (multiple patterns)
    journal_patterns = [
        r'\.\s*([A-Z][^.]+)\.\s*\d{4}',  # Journal before year
        r'\d{4};\s*([^;]+);',            # Journal after year with semicolon
        r'\d{4},?\s*([A-Z][^,]+),',      # Journal after year with comma
        r'In:\s*([^.]+)\.',              # Book chapters
    ]
    
    for pattern in journal_patterns:
        journal_match = re.search(pattern, ref_text)
        if journal_match:
            journal = journal_match.group(1).strip()
            if len(journal) > 3 and not journal.lower().startswith('http'):
                ref['journal'] = journal
                break
    
    # Extract authors (multiple patterns)
    author_patterns = [
        r'^([^.]+(?:\set\sal\.?)?)',      # Standard author format
        r'^([A-Z][a-z]+(?:,\s*[A-Z]\.?)*)', # Last, F.M. format
        r'^\d+\.\s*([^.]+(?:\set\sal\.?)?)', # After number
    ]
    
    for pattern in author_patterns:
        author_match = re.search(pattern, ref_text)
        if author_match:
            authors = author_match.group(1).strip()
            if len(authors) > 3 and not authors.isdigit():
                ref['authors'] = authors
                break
    
    # Extract volume and pages
    vol_page_patterns = [
        r'(\d+):(\d+-?\d*)',     # Volume:Pages
        r'(\d+)\((\d+)\):(\d+-?\d*)', # Volume(Issue):Pages
        r';(\d+):(\d+-?\d*)',    # ;Volume:Pages
    ]
    
    for pattern in vol_page_patterns:
        vol_page_match = re.search(pattern, ref_text)
        if vol_page_match:
            groups = vol_page_match.groups()
            if len(groups) >= 2:
                ref['volume'] = groups[0]
                ref['pages'] = groups[-1]  # Last group is always pages
            break
    
    # Extract DOI
    doi_match = re.search(r'doi:\s*(10\.\d+/[^\s]+)', ref_text, re.IGNORECASE)
    if doi_match:
        ref['doi'] = doi_match.group(1)
    
    # Extract PMID
    pmid_match = re.search(r'PMID:\s*(\d+)', ref_text, re.IGNORECASE)
    if pmid_match:
        ref['pmid'] = pmid_match.group(1)
    
    return ref

def is_valid_reference(ref: Dict[str, Any]) -> bool:
    """Check if a reference is valid and not a false positive."""
    
    # Must have some meaningful content
    has_content = (
        ref.get('title') or 
        ref.get('authors') or 
        ref.get('doi') or 
        ref.get('pmid') or
        (ref.get('raw_text') and len(ref.get('raw_text', '')) > 20)
    )
    
    if not has_content:
        return False
    
    # Filter out common false positives
    raw_text = ref.get('raw_text', '').lower()
    title = ref.get('title', '').lower()
    
    false_positive_indicators = [
        'figure', 'table', 'appendix', 'supplementary',
        'page', 'chapter', 'section', 'http://', 'https://',
        'www.', '.com', '.org', '.edu', 'email', '@'
    ]
    
    for indicator in false_positive_indicators:
        if indicator in raw_text or indicator in title:
            return False
    
    return True

async def save_references_to_csv(references: List[Dict[str, Any]], source_file: str):
    """Save references to CSV file."""
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"references/brain_direct_mistral_references_{timestamp}.csv"
    
    # Ensure references directory exists
    Path("references").mkdir(exist_ok=True)
    
    fieldnames = [
        'source_document', 'extraction_method', 'reference_text', 'authors', 
        'title', 'year', 'journal', 'volume', 'pages', 'doi', 'pmid',
        'extraction_date', 'confidence_score'
    ]
    
    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        
        for ref in references:
            row = {
                'source_document': source_file,
                'extraction_method': ref.get('extraction_method', 'mistral_direct'),
                'reference_text': ref.get('raw_text', ''),
                'authors': ref.get('authors', ''),
                'title': ref.get('title', ''),
                'year': ref.get('year', ''),
                'journal': ref.get('journal', ''),
                'volume': ref.get('volume', ''),
                'pages': ref.get('pages', ''),
                'doi': ref.get('doi', ''),
                'pmid': ref.get('pmid', ''),
                'extraction_date': datetime.now().isoformat(),
                'confidence_score': 0.85
            }
            writer.writerow(row)
    
    print(f"💾 Saved {len(references)} references to {filename}")

if __name__ == "__main__":
    asyncio.run(extract_brain_references_direct())
