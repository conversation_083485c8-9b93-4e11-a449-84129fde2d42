"""
Setup FalkorDB environment for Graphiti
"""

import os
import logging
import argparse
import redis
from dotenv import load_dotenv

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

def setup_falkordb():
    """
    Set up FalkorDB environment for Graphiti
    """
    logger.info("Setting up FalkorDB environment for Graphiti")
    
    # Get FalkorDB connection details from environment variables
    host = os.environ.get('FALKORDB_HOST', 'localhost')
    port = int(os.environ.get('FALKORDB_PORT', '6379'))
    password = os.environ.get('FALKORDB_PASSWORD', None)
    
    # Connect to FalkorDB
    redis_client = redis.Redis(
        host=host,
        port=port,
        password=password,
        decode_responses=True
    )
    
    try:
        # Check if FalkorDB is running
        info = redis_client.info()
        logger.info(f"Connected to FalkorDB: {info.get('redis_version', 'unknown')}")
        
        # Create graphiti graph if it doesn't exist
        try:
            # Check if graph exists
            result = redis_client.execute_command("GRAPH.LIST")
            if "graphiti" not in result:
                logger.info("Creating graphiti graph")
                redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE (n:Root {name: 'Graphiti Root'})")
                logger.info("Created graphiti graph")
            else:
                logger.info("graphiti graph already exists")
        except redis.exceptions.ResponseError as e:
            if "unknown command" in str(e).lower():
                logger.error("FalkorDB module not loaded. Make sure FalkorDB is installed correctly.")
            else:
                logger.error(f"Error creating graph: {e}")
        
        # Create indexes
        try:
            # Create indexes for Episode nodes
            logger.info("Creating indexes for Episode nodes")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Episode(uuid)")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Episode(name)")
            
            # Create indexes for Fact nodes
            logger.info("Creating indexes for Fact nodes")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Fact(uuid)")
            
            # Create indexes for Entity nodes
            logger.info("Creating indexes for Entity nodes")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Entity(name)")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Entity(type)")
            
            # Create indexes for Reference nodes
            logger.info("Creating indexes for Reference nodes")
            redis_client.execute_command("GRAPH.QUERY", "graphiti", "CREATE INDEX ON :Reference(uuid)")
            
            logger.info("Created indexes")
        except redis.exceptions.ResponseError as e:
            if "already exists" in str(e).lower():
                logger.info("Indexes already exist")
            else:
                logger.error(f"Error creating indexes: {e}")
        
        logger.info("FalkorDB environment setup complete")
    
    except Exception as e:
        logger.error(f"Error setting up FalkorDB environment: {e}")
    finally:
        redis_client.close()

def create_env_file():
    """
    Create .env file with FalkorDB configuration
    """
    logger.info("Creating .env file with FalkorDB configuration")
    
    # Check if .env file exists
    if os.path.exists(".env"):
        logger.info(".env file already exists")
        
        # Update .env file with FalkorDB configuration
        with open(".env", "r") as f:
            lines = f.readlines()
        
        # Check if FalkorDB configuration exists
        falkordb_config_exists = any(line.startswith("FALKORDB_") for line in lines)
        
        if not falkordb_config_exists:
            logger.info("Adding FalkorDB configuration to .env file")
            
            with open(".env", "a") as f:
                f.write("\n# FalkorDB configuration\n")
                f.write("FALKORDB_HOST=localhost\n")
                f.write("FALKORDB_PORT=6379\n")
                f.write("FALKORDB_PASSWORD=\n")
            
            logger.info("Added FalkorDB configuration to .env file")
        else:
            logger.info("FalkorDB configuration already exists in .env file")
    else:
        logger.info("Creating new .env file")
        
        with open(".env", "w") as f:
            f.write("# FalkorDB configuration\n")
            f.write("FALKORDB_HOST=localhost\n")
            f.write("FALKORDB_PORT=6379\n")
            f.write("FALKORDB_PASSWORD=\n")
            
            # Add other configuration
            f.write("\n# OpenAI configuration\n")
            f.write("OPENAI_API_KEY=\n")
            
            # Add LLM configuration
            f.write("\n# LLM configuration\n")
            f.write("QA_LLM_PROVIDER=openai\n")
            f.write("QA_LLM_MODEL=gpt-4.1-mini\n")
            f.write("ENTITY_EXTRACTION_MODEL=gpt-4.1-mini\n")
        
        logger.info("Created new .env file")

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Setup FalkorDB environment for Graphiti")
    parser.add_argument("--env-only", action="store_true", help="Only create .env file")
    
    args = parser.parse_args()
    
    # Create .env file
    create_env_file()
    
    if not args.env_only:
        # Set up FalkorDB environment
        setup_falkordb()

if __name__ == "__main__":
    main()
