#!/usr/bin/env python3
"""
Test reference extraction for Co Q10 document
"""

import asyncio
from services.reference_processor import ReferenceProcessor

async def test_reference_extraction():
    processor = ReferenceProcessor()
    file_path = 'uploads/4e8bb988-26e9-4e75-a50d-53e7bcb4835d_Co Q10 - Athletes.pdf'
    
    print(f'Testing reference extraction for: {file_path}')
    
    # Test the reference extraction
    result = await processor._extract_references_with_mistral_ocr(file_path)
    
    print(f'Reference extraction result:')
    print(f'Success: {result.get("success", False)}')
    print(f'Total references: {result.get("total_reference_count", 0)}')
    print(f'Extraction method: {result.get("extraction_method", "unknown")}')
    
    if result.get('regex_references'):
        print(f'Regex references found: {len(result["regex_references"])}')
        for i, ref in enumerate(result['regex_references'][:3], 1):
            print(f'  {i}. {ref.get("text", "No text")[:200]}...')
    
    if result.get('mistral_references'):
        print(f'Mistral references found: {len(result["mistral_references"])}')
        for i, ref in enumerate(result['mistral_references'][:3], 1):
            print(f'  {i}. {ref[:200]}...')
    
    # Also test the raw text to see what we're working with
    print(f'\n--- Testing raw text extraction ---')
    from processors.pdf_processor import PDFProcessor
    from pathlib import Path
    
    pdf_processor = PDFProcessor()
    text_result = await pdf_processor.extract_text(Path(file_path))
    
    if text_result.get('success'):
        text = text_result.get('text', '')
        print(f'Raw text length: {len(text)}')
        
        # Look for the reference in the text
        if 'Journal of the International Society' in text:
            print('✅ Reference found in raw text!')
            # Find the reference and show context
            start = text.find('Journal of the International Society')
            context_start = max(0, start - 100)
            context_end = min(len(text), start + 300)
            print(f'Reference context: ...{text[context_start:context_end]}...')
        else:
            print('❌ Reference NOT found in raw text')
            print(f'Text sample: {text[-500:]}')  # Show end of text where reference might be

if __name__ == "__main__":
    asyncio.run(test_reference_extraction())
