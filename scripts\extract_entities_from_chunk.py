"""
Script to extract entities from a specific chunk in the database.
"""

import os
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from entity_extraction import extract_entities
from utils.logging_utils import get_logger

# Try to import dotenv
try:
    from dotenv import load_dotenv
except ImportError:
    # If dotenv is not installed, define a simple function
    def load_dotenv():
        pass

# Set up logger
logger = get_logger(__name__)

# Load environment variables
load_dotenv()

async def main():
    """Main function."""
    # Connect to FalkorDB
    adapter = GraphitiFalkorDBAdapter()

    # Check if connected
    if not adapter.is_connected():
        logger.error("Failed to connect to FalkorDB")
        return

    logger.info("Connected to FalkorDB")

    # Get the first fact from the database
    facts_query = """
    MATCH (f:Fact)
    RETURN f.uuid as uuid, f.body as body
    LIMIT 1
    """

    facts_result = adapter.execute_cypher(facts_query)

    if not facts_result or len(facts_result) < 2 or not facts_result[1]:
        logger.error("No facts found in the database")
        return

    fact_uuid = facts_result[1][0][0]
    fact_body = facts_result[1][0][1]

    logger.info(f"Found fact with UUID: {fact_uuid}")
    logger.info(f"Fact body sample: {fact_body[:100]}...")

    # Extract entities from the fact
    logger.info("Extracting entities from fact...")
    api_key = os.environ.get('OPENAI_API_KEY')
    entities = extract_entities(fact_body, api_key)

    logger.info(f"Extracted {len(entities)} entities:")
    for entity in entities:
        logger.info(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")

        # Escape special characters in entity name and description
        entity_name = entity["name"].replace("'", "\\'")
        entity_type = entity["type"]
        entity_description = entity.get("description", "").replace("'", "\\'")

        # Get current timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Create entity node and link to fact
        create_entity_query = f"""
        MERGE (e:Entity {{name: '{entity_name}', type: '{entity_type}'}})
        ON CREATE SET
            e.description = '{entity_description}',
            e.created_at = '{timestamp}'
        WITH e
        MATCH (f:Fact {{uuid: '{fact_uuid}'}})
        MERGE (f)-[r:MENTIONS]->(e)
        RETURN e.name as name
        """

        result = adapter.execute_cypher(create_entity_query)
        if result and len(result) > 1 and len(result[1]) > 0:
            logger.info(f"  Created entity node and relationship for {entity['name']}")
        else:
            logger.error(f"  Failed to create entity node for {entity['name']}")

    # Close the connection
    adapter.close()

if __name__ == "__main__":
    asyncio.run(main())
