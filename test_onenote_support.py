#!/usr/bin/env python3
"""
Test script for OneNote (.one) file support in the document ingestion pipeline.

This script tests the complete OneNote processing workflow including:
- File type detection
- OneNote processor functionality
- Integration with enhanced document processor
- Upload route validation
"""

import sys
import os
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_file_type_detection():
    """Test OneNote file type detection in file utilities."""
    logger.info("=== Testing OneNote File Type Detection ===")
    
    from utils.file_utils import is_supported_file, get_file_type, get_file_category
    
    # Test file extension detection
    test_files = [
        "test_document.one",
        "MyNotes.ONE",
        "project_notes.one",
        "TEST.One"
    ]
    
    for filename in test_files:
        logger.info(f"Testing file: {filename}")
        
        # Test if file is supported
        is_supported = is_supported_file(filename)
        logger.info(f"  Supported: {is_supported}")
        
        # Test MIME type detection
        mime_type = get_file_type(filename)
        logger.info(f"  MIME type: {mime_type}")
        
        # Test category detection
        category = get_file_category(filename)
        logger.info(f"  Category: {category}")
        
        # Verify results
        assert is_supported, f"OneNote file {filename} should be supported"
        assert mime_type == "application/onenote", f"Expected 'application/onenote', got '{mime_type}'"
        assert category == "onenote", f"Expected 'onenote', got '{category}'"
        
        logger.info(f"  ✅ {filename} passed all tests")
    
    logger.info("✅ File type detection tests passed!")


def test_onenote_processor():
    """Test OneNote processor functionality."""
    logger.info("\n=== Testing OneNote Processor ===")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        
        # Initialize processor
        processor = OneNoteProcessor()
        logger.info(f"Processor initialized: {processor.processor_name}")
        
        # Test processor info
        info = processor.get_processor_info()
        logger.info(f"Processor info: {info}")
        
        # Test supported extensions
        extensions = processor.get_supported_extensions()
        logger.info(f"Supported extensions: {extensions}")
        
        assert '.one' in extensions, "OneNote processor should support .one files"
        assert info['library_available'], "one-extract library should be available"
        
        logger.info("✅ OneNote processor tests passed!")
        
    except ImportError as e:
        logger.error(f"❌ OneNote processor import failed: {e}")
        logger.error("Make sure 'one-extract' library is installed: pip install one-extract")
        return False
    
    return True


def test_enhanced_document_processor_integration():
    """Test OneNote integration with enhanced document processor."""
    logger.info("\n=== Testing Enhanced Document Processor Integration ===")
    
    from processors.enhanced_document_processor import EnhancedDocumentProcessor
    
    # Initialize enhanced processor
    processor = EnhancedDocumentProcessor()
    
    # Test supported extensions
    extensions = processor.get_supported_extensions()
    logger.info(f"Enhanced processor supported extensions: {extensions}")
    
    assert '.one' in extensions, "Enhanced processor should support .one files"
    
    # Test file category mapping
    test_file = "test.one"
    category = processor._get_file_category(Path(test_file))
    logger.info(f"File category for {test_file}: {category}")
    
    assert category == 'onenote', f"Expected 'onenote', got '{category}'"
    
    # Test processor availability
    onenote_processor = processor.processors.get('onenote')
    assert onenote_processor is not None, "OneNote processor should be available in enhanced processor"
    
    logger.info("✅ Enhanced document processor integration tests passed!")


def test_mock_onenote_processing():
    """Test OneNote processing with a mock file."""
    logger.info("\n=== Testing Mock OneNote Processing ===")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        import asyncio
        
        processor = OneNoteProcessor()
        
        # Create a mock OneNote file (this won't be a real .one file, just for testing the error handling)
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            # Write some dummy data
            temp_file.write(b"This is not a real OneNote file, just for testing")
            temp_file_path = temp_file.name
        
        try:
            # Test extraction (should fail gracefully with mock data)
            result = asyncio.run(processor.extract_text(temp_file_path))
            
            logger.info(f"Processing result: {result}")
            
            # Should fail but handle error gracefully
            assert not result['success'], "Mock file should fail processing"
            assert 'error' in result, "Result should contain error message"
            assert result['text'] == '', "Text should be empty on failure"
            
            logger.info("✅ Mock OneNote processing test passed (graceful failure)!")
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except ImportError as e:
        logger.warning(f"⚠️ OneNote processor not available: {e}")
        return False
    
    return True


def test_upload_route_validation():
    """Test that upload routes accept OneNote files."""
    logger.info("\n=== Testing Upload Route Validation ===")
    
    from utils.file_utils import is_supported_file
    
    # Test various OneNote file names
    test_files = [
        "meeting_notes.one",
        "project_documentation.ONE",
        "research_notes.One",
        "personal_journal.one"
    ]
    
    for filename in test_files:
        is_supported = is_supported_file(filename)
        logger.info(f"Upload validation for {filename}: {'✅ Accepted' if is_supported else '❌ Rejected'}")
        assert is_supported, f"Upload should accept OneNote file: {filename}"
    
    logger.info("✅ Upload route validation tests passed!")


def test_ui_file_input_acceptance():
    """Test that UI file input accepts OneNote files."""
    logger.info("\n=== Testing UI File Input Acceptance ===")
    
    # Read the enhanced upload template
    template_path = Path("templates/enhanced_upload.html")
    
    if template_path.exists():
        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()
        
        # Check if .one is in the accept attribute
        if '.one' in template_content:
            logger.info("✅ UI file input accepts .one files")
        else:
            logger.error("❌ UI file input does not accept .one files")
            return False
        
        # Check if OneNote is mentioned in the description
        if 'OneNote' in template_content:
            logger.info("✅ UI mentions OneNote support")
        else:
            logger.warning("⚠️ UI does not mention OneNote support in description")
    else:
        logger.warning("⚠️ Enhanced upload template not found")
        return False
    
    return True


def test_complete_workflow_simulation():
    """Simulate the complete OneNote processing workflow."""
    logger.info("\n=== Testing Complete Workflow Simulation ===")
    
    try:
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        from utils.file_utils import is_supported_file, get_file_category
        
        # Simulate workflow steps
        filename = "test_notebook.one"
        
        # Step 1: File upload validation
        logger.info(f"Step 1: Validating file upload for {filename}")
        assert is_supported_file(filename), "File should pass upload validation"
        
        # Step 2: File category detection
        logger.info(f"Step 2: Detecting file category for {filename}")
        category = get_file_category(filename)
        assert category == 'onenote', f"Expected 'onenote', got '{category}'"
        
        # Step 3: Processor selection
        logger.info(f"Step 3: Selecting processor for category '{category}'")
        enhanced_processor = EnhancedDocumentProcessor()
        processor = enhanced_processor.processors.get(category)
        assert processor is not None, f"No processor found for category '{category}'"
        
        # Step 4: Processor capability check
        logger.info(f"Step 4: Checking processor capabilities")
        extensions = processor.get_supported_extensions()
        assert '.one' in extensions, "Processor should support .one files"
        
        logger.info("✅ Complete workflow simulation passed!")
        
    except Exception as e:
        logger.error(f"❌ Workflow simulation failed: {e}")
        return False
    
    return True


def main():
    """Run all OneNote support tests."""
    logger.info("Starting OneNote Support Tests")
    logger.info("=" * 60)
    
    tests = [
        ("File Type Detection", test_file_type_detection),
        ("OneNote Processor", test_onenote_processor),
        ("Enhanced Processor Integration", test_enhanced_document_processor_integration),
        ("Mock OneNote Processing", test_mock_onenote_processing),
        ("Upload Route Validation", test_upload_route_validation),
        ("UI File Input Acceptance", test_ui_file_input_acceptance),
        ("Complete Workflow Simulation", test_complete_workflow_simulation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            if result is None:
                result = True  # Assume success if no explicit return
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("ONENOTE SUPPORT TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! OneNote support is fully integrated.")
        logger.info("\nOneNote Integration Summary:")
        logger.info("✅ File type detection and validation")
        logger.info("✅ OneNote processor with one-extract library")
        logger.info("✅ Enhanced document processor integration")
        logger.info("✅ Upload route acceptance")
        logger.info("✅ UI file input support")
        logger.info("✅ Complete processing workflow")
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
