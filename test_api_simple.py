#!/usr/bin/env python3
"""
Simple test script to check API endpoints.
"""

import requests
import time

def test_api():
    """Test the API endpoints"""
    base_url = "http://127.0.0.1:9753"
    
    # Wait for server to be ready
    print("Waiting for server...")
    for i in range(10):
        try:
            response = requests.get(f"{base_url}/health", timeout=2)
            if response.status_code == 200:
                print("✅ Server is ready!")
                break
        except:
            pass
        time.sleep(1)
        print(f"  Attempt {i+1}/10...")
    
    print("\n🔍 Testing Fast API Endpoints")
    print("=" * 50)
    
    # Test entities endpoint
    print("\n1. Testing /api/fast/entities:")
    try:
        response = requests.get(f"{base_url}/api/fast/entities?limit=3", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Count: {data.get('count', 0)}")
            entities = data.get('entities', [])
            for i, entity in enumerate(entities):
                print(f"  Entity {i+1}: '{entity.get('name', 'N/A')}' ({entity.get('type', 'N/A')})")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test documents endpoint
    print("\n2. Testing /api/fast/documents:")
    try:
        response = requests.get(f"{base_url}/api/fast/documents?limit=3", timeout=10)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Count: {data.get('count', 0)}")
            documents = data.get('documents', [])
            for i, doc in enumerate(documents):
                print(f"  Document {i+1}: '{doc.get('name', 'N/A')}'")
        else:
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_api()
