"""
Simple test for Google Generative AI
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

def main():
    try:
        # Load environment variables
        load_dotenv()
        
        # Get Google API key
        api_key = os.environ.get('GOOGLE_API_KEY')
        if not api_key:
            print("No Google API key found in environment variables")
            return
            
        print(f"API key found: {api_key[:5]}...{api_key[-5:]}")
        
        # Configure the API
        genai.configure(api_key=api_key)
        
        # Test a simple generation
        model = genai.GenerativeModel('gemini-1.5-flash')
        response = model.generate_content("Hello, how are you?")
        print(f"Response: {response.text}")
        
        print("Test completed successfully!")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
