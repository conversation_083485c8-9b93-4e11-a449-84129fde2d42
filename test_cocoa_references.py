#!/usr/bin/env python3
"""
Test reference extraction for Cocoa benefits document
"""

import asyncio
import csv
from pathlib import Path
from services.reference_processor import ReferenceProcessor

async def test_cocoa_references():
    print("🍫 Testing Cocoa Benefits Reference Extraction")
    print("=" * 60)
    
    # Check if the Cocoa benefits file exists
    cocoa_files = list(Path("uploads").glob("*Cocoa*benefits*.pdf"))
    
    if not cocoa_files:
        print("❌ No Cocoa benefits PDF found in uploads directory")
        return
    
    cocoa_file = cocoa_files[0]
    print(f"📄 Found Cocoa file: {cocoa_file}")
    
    # Test with the improved reference processor
    processor = ReferenceProcessor()
    
    print(f"\n🔍 Testing reference extraction...")
    result = await processor.extract_references_from_document(str(cocoa_file))
    
    print(f"\n📊 Results:")
    print(f"Success: {result.get('success', False)}")
    print(f"Total references: {result.get('total_reference_count', 0)}")
    print(f"Extraction method: {result.get('extraction_method', 'unknown')}")
    
    if result.get('regex_references'):
        print(f"\n📚 Regex references found: {len(result['regex_references'])}")
        for i, ref in enumerate(result['regex_references'][:5], 1):
            ref_text = ref.get('text', 'No text') if isinstance(ref, dict) else str(ref)
            print(f"  {i}. {ref_text[:100]}...")
    
    if result.get('mistral_references'):
        print(f"\n🤖 Mistral references found: {len(result['mistral_references'])}")
        for i, ref in enumerate(result['mistral_references'][:5], 1):
            print(f"  {i}. {ref[:100]}...")
    
    # Check the master CSV file
    print(f"\n📋 Checking master CSV file...")
    csv_file = Path("references/all_references.csv")
    
    if csv_file.exists():
        cocoa_refs_in_csv = []
        with open(csv_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if 'Cocoa' in row.get('source_document', ''):
                    cocoa_refs_in_csv.append(row)
        
        print(f"📈 Found {len(cocoa_refs_in_csv)} Cocoa references in master CSV")
        
        if cocoa_refs_in_csv:
            print(f"\n📝 Sample references from CSV:")
            for i, ref in enumerate(cocoa_refs_in_csv[:3], 1):
                ref_text = ref.get('reference_text', 'No text')
                if ref_text and len(ref_text) > 10:
                    print(f"  {i}. {ref_text[:100]}...")
    else:
        print("❌ Master CSV file not found")
    
    print(f"\n✅ Test completed!")

if __name__ == "__main__":
    asyncio.run(test_cocoa_references())
