"""
<PERSON><PERSON><PERSON> to fix the Andrographis entity in the FalkorDB database.

This script:
1. Checks if the Andrographis entity exists
2. If it doesn't exist, creates it with the correct UUID, type, and name
3. If it exists but has issues, updates it with the correct properties
"""

import asyncio
import sys
import os
import uuid

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.database_service import get_falkordb_adapter, create_entity_node
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def fix_andrographis_entity():
    """
    Fix the Andrographis entity in the database.
    """
    logger.info("Starting to fix Andrographis entity...")
    
    # Get database adapter
    adapter = await get_falkordb_adapter()
    
    # Check if the adapter is connected
    if not adapter.is_connected():
        logger.error("Database adapter is not connected")
        return False
    
    # Check if Andrographis entity exists
    query = """
    MATCH (e:Entity)
    WHERE e.name = 'Andrographis' OR e.name CONTAINS 'Andrographis'
    RETURN e
    """
    
    result = adapter.execute_cypher(query)
    
    if result and len(result) > 1 and len(result[1]) > 0:
        # Entity exists, check if it needs to be fixed
        entity_data = result[1][0][0]
        
        # Create a dictionary to hold entity properties
        entity_dict = {}
        
        # Check if entity_data is a dictionary
        if isinstance(entity_data, dict):
            entity_dict = entity_data
        # Check if entity_data is a list (FalkorDB format)
        elif isinstance(entity_data, list):
            # Convert the list to a dictionary
            for i in range(0, len(entity_data), 2):
                if i + 1 < len(entity_data):
                    key = entity_data[i]
                    value = entity_data[i + 1]
                    
                    # Handle nested properties
                    if key == "properties" and isinstance(value, list):
                        properties = {}
                        for j in range(0, len(value), 2):
                            if j + 1 < len(value):
                                prop_key = value[j]
                                prop_value = value[j + 1]
                                properties[prop_key] = prop_value
                        entity_dict.update(properties)
                    else:
                        entity_dict[key] = value
        
        # Check if the entity has the correct properties
        has_uuid = entity_dict.get("uuid") and entity_dict.get("uuid") != ""
        has_type = entity_dict.get("type") and entity_dict.get("type") == "Herb"
        has_name = entity_dict.get("name") and entity_dict.get("name") == "Andrographis"
        
        if has_uuid and has_type and has_name:
            logger.info(f"Andrographis entity exists and has correct properties: {entity_dict}")
            return True
        
        # Entity exists but needs to be fixed
        logger.info(f"Andrographis entity exists but needs to be fixed: {entity_dict}")
        
        # Get the UUID if it exists, otherwise generate a new one
        entity_uuid = entity_dict.get("uuid", str(uuid.uuid4()))
        
        # Update the entity
        update_query = f"""
        MATCH (e:Entity)
        WHERE e.name = 'Andrographis' OR e.name CONTAINS 'Andrographis'
        SET e.uuid = '{entity_uuid}',
            e.type = 'Herb',
            e.name = 'Andrographis',
            e.confidence = 1.0,
            e.mention_count = 16
        RETURN e
        """
        
        update_result = adapter.execute_cypher(update_query)
        
        if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
            logger.info(f"Successfully updated Andrographis entity: {update_result[1][0][0]}")
            return True
        else:
            logger.error(f"Failed to update Andrographis entity: {update_result}")
            return False
    
    else:
        # Entity doesn't exist, create it
        logger.info("Andrographis entity doesn't exist, creating it...")
        
        # Generate a UUID
        entity_uuid = str(uuid.uuid4())
        
        # Create the entity
        create_query = f"""
        CREATE (e:Entity {{
            uuid: '{entity_uuid}',
            type: 'Herb',
            name: 'Andrographis',
            confidence: 1.0,
            mention_count: 16
        }})
        RETURN e
        """
        
        create_result = adapter.execute_cypher(create_query)
        
        if create_result and len(create_result) > 1 and len(create_result[1]) > 0:
            logger.info(f"Successfully created Andrographis entity: {create_result[1][0][0]}")
            return True
        else:
            logger.error(f"Failed to create Andrographis entity: {create_result}")
            return False

async def main():
    """
    Main function.
    """
    success = await fix_andrographis_entity()
    
    if success:
        logger.info("Successfully fixed Andrographis entity")
    else:
        logger.error("Failed to fix Andrographis entity")

if __name__ == "__main__":
    asyncio.run(main())
