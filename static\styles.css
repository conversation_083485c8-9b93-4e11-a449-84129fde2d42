/**
 * Graphiti Knowledge Graph - Main Stylesheet
 */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    margin-bottom: 1rem;
}

a {
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* Loading Spinner */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
}

.loading p {
    margin-top: 1rem;
    color: #6c757d;
}

/* Cards */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 0.25rem;
    font-weight: 500;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

.btn-outline-primary {
    color: #0d6efd;
    border-color: #0d6efd;
}

.btn-outline-primary:hover {
    background-color: #0d6efd;
    color: #fff;
}

/* Forms */
.form-control {
    border-radius: 0.25rem;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Tables */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid #dee2e6;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #dee2e6;
    background-color: #f8f9fa;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.075);
}

/* Navbar */
.navbar {
    padding: 0.5rem 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
}

.nav-link.active {
    font-weight: 700;
}

/* Breadcrumb */
.breadcrumb {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 0.75rem 1rem;
    margin-bottom: 1rem;
}

/* Alerts */
.alert {
    border-radius: 0.25rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

/* Entity Types */
.entity-type {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

.entity-type-Person {
    background-color: #cfe2ff;
    color: #084298;
}

.entity-type-Organization {
    background-color: #d1e7dd;
    color: #0f5132;
}

.entity-type-Location {
    background-color: #f8d7da;
    color: #842029;
}

.entity-type-Disease {
    background-color: #fff3cd;
    color: #664d03;
}

.entity-type-Symptom {
    background-color: #e2e3e5;
    color: #41464b;
}

.entity-type-Treatment {
    background-color: #d1e7dd;
    color: #0f5132;
}

.entity-type-Medication {
    background-color: #cff4fc;
    color: #055160;
}

.entity-type-Food {
    background-color: #f8d7da;
    color: #842029;
}

.entity-type-Herb {
    background-color: #d1e7dd;
    color: #0f5132;
}

.entity-type-Nutrient {
    background-color: #cfe2ff;
    color: #084298;
}

.entity-type-Process {
    background-color: #e2e3e5;
    color: #41464b;
}

.entity-type-Research {
    background-color: #cff4fc;
    color: #055160;
}

/* Entity Detail */
.entity-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.entity-detail-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.entity-detail-type {
    font-size: 1.25rem;
    color: #6c757d;
}

.entity-detail-section {
    margin-bottom: 2rem;
}

.entity-detail-section-header {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

/* Knowledge Graph */
.graph-container {
    width: 100%;
    height: 600px;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Q&A Interface */
.conversation-container {
    max-height: 500px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.message {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
}

.user-message {
    background-color: #f8f9fa;
    border-left: 4px solid #0d6efd;
}

.assistant-message {
    background-color: #f0f7ff;
    border-left: 4px solid #20c997;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.message-sender {
    font-weight: 600;
}

.message-time {
    font-size: 0.875rem;
    color: #6c757d;
}

.message-content {
    white-space: pre-wrap;
}

/* References */
.reference-item {
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.5rem;
}

.reference-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.reference-authors {
    font-style: italic;
    margin-bottom: 0.5rem;
}

.reference-journal {
    color: #6c757d;
}

/* Settings */
.settings-section {
    margin-bottom: 2rem;
}

.settings-section-header {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dee2e6;
}

/* Sources */
.sources-list {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 4px solid #6c757d;
}

.sources-list h5 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #495057;
    font-weight: 600;
}

.source-item {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.source-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.source-header {
    margin-bottom: 5px;
    font-size: 1.05em;
}

.source-id {
    color: #6c757d;
    font-size: 0.9em;
}

.source-metadata {
    margin-bottom: 8px;
    font-size: 0.85em;
    color: #6c757d;
    font-style: italic;
}

.source-preview {
    font-size: 0.9em;
    color: #495057;
    white-space: pre-line;
    line-height: 1.5;
    background-color: #ffffff;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #adb5bd;
}

.source-preview h4 {
    margin-top: 10px;
    margin-bottom: 8px;
    color: #343a40;
    font-weight: 600;
    font-size: 1.1em;
}

.source-preview .citation {
    color: #0d6efd;
    font-weight: 500;
}

.source-preview em {
    color: #6c757d;
}

.source-preview strong {
    color: #495057;
}

.source-preview .math-notation {
    font-family: 'Cambria Math', Georgia, serif;
    font-style: italic;
    color: #0d6efd;
    background-color: #f8f9fa;
    padding: 0 3px;
    border-radius: 2px;
}

.source-number {
    font-weight: bold;
    color: #0d6efd;
    margin-right: 5px;
    font-size: 1.1em;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .entity-detail-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .graph-container {
        height: 400px;
    }
}
