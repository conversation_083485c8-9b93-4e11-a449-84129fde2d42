#!/usr/bin/env python3
"""
Extract text from OneNote images using Ollama vision model (qwen2.5vl:3b).

This script uses Ollama's vision capabilities to process the extracted OneNote images
and extract the actual text content that should be in your research notes.
"""

import sys
import os
import logging
import asyncio
import base64
from pathlib import Path
import json

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def process_image_with_ollama_vision(image_path: Path, model: str = "qwen2.5-vl:3b") -> str:
    """
    Process an image with Ollama vision model to extract text content.
    
    Args:
        image_path: Path to the image file
        model: Ollama vision model to use
        
    Returns:
        Extracted text content
    """
    try:
        import httpx
        
        # Read and encode the image
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # Encode image as base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # Prepare the request for Ollama
        payload = {
            "model": model,
            "prompt": """Please extract ALL text content from this image. This appears to be from a OneNote document containing research information about brain health, neuroprotection, and bioactive compounds.

Please provide:
1. All visible text, including titles, headings, bullet points, and body text
2. Any research information, chemical names, or scientific terms
3. References, URLs, or citations if visible
4. Table data or structured information if present

Extract the text exactly as it appears, maintaining the structure and organization. If there are multiple sections or columns, please indicate the organization clearly.""",
            "images": [image_base64],
            "stream": False
        }
        
        # Make request to Ollama
        async with httpx.AsyncClient(timeout=120.0) as client:
            logger.info(f"Processing {image_path.name} with Ollama {model}...")
            
            response = await client.post(
                "http://localhost:11434/api/generate",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                extracted_text = result.get('response', '').strip()
                
                if extracted_text:
                    logger.info(f"✅ Extracted {len(extracted_text)} characters from {image_path.name}")
                    return extracted_text
                else:
                    logger.warning(f"⚠️ No text extracted from {image_path.name}")
                    return ""
            else:
                logger.error(f"❌ Ollama request failed: {response.status_code} - {response.text}")
                return ""
                
    except Exception as e:
        logger.error(f"❌ Error processing {image_path.name} with Ollama: {e}")
        return ""


async def check_ollama_availability(model: str = "qwen2.5-vl:3b") -> bool:
    """
    Check if Ollama is running and the vision model is available.
    
    Args:
        model: Model name to check
        
    Returns:
        True if Ollama and model are available
    """
    try:
        import httpx
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            # Check if Ollama is running
            try:
                response = await client.get("http://localhost:11434/api/tags")
                if response.status_code != 200:
                    logger.error("❌ Ollama is not running or not accessible")
                    return False
            except Exception as e:
                logger.error(f"❌ Cannot connect to Ollama: {e}")
                logger.error("Make sure Ollama is running: ollama serve")
                return False
            
            # Check if the vision model is available
            models = response.json().get('models', [])
            model_names = [m.get('name', '') for m in models]
            
            if any(model in name for name in model_names):
                logger.info(f"✅ Found vision model: {model}")
                return True
            else:
                logger.warning(f"⚠️ Vision model {model} not found")
                logger.info("Available models:")
                for name in model_names:
                    logger.info(f"  - {name}")
                logger.info(f"To install the vision model: ollama pull {model}")
                return False
                
    except ImportError:
        logger.error("❌ httpx library not available. Install with: pip install httpx")
        return False
    except Exception as e:
        logger.error(f"❌ Error checking Ollama availability: {e}")
        return False


async def process_all_onenote_images_with_ollama():
    """Process all OneNote images with Ollama vision model."""
    
    print("🤖 PROCESSING ONENOTE IMAGES WITH OLLAMA VISION")
    print("=" * 60)
    
    # Check if extracted images exist
    image_dir = Path("extracted_onenote_images")
    if not image_dir.exists():
        print("❌ No extracted images found. Run extract_and_save_onenote_images.py first")
        return False
    
    # Find image files
    image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
    if not image_files:
        print("❌ No image files found in extracted directory")
        return False
    
    print(f"📎 Found {len(image_files)} images to process")
    
    # Check Ollama availability
    model = "qwen2.5-vl:3b"
    if not await check_ollama_availability(model):
        print("❌ Ollama vision model not available")
        print("Setup instructions:")
        print("1. Start Ollama: ollama serve")
        print(f"2. Install vision model: ollama pull {model}")
        return False
    
    print(f"✅ Using Ollama vision model: {model}")
    
    # Process each image
    total_extracted_text = ""
    successful_extractions = 0
    
    for i, image_file in enumerate(sorted(image_files)):
        print(f"\n📷 Processing {image_file.name}:")
        print(f"   Size: {image_file.stat().st_size:,} bytes")
        
        try:
            extracted_text = await process_image_with_ollama_vision(image_file, model)
            
            if extracted_text and len(extracted_text.strip()) > 20:
                print(f"   ✅ SUCCESS: Extracted {len(extracted_text)} characters")
                print(f"   📝 Preview: {extracted_text[:200]}...")
                
                total_extracted_text += f"\n\n=== {image_file.name.upper()} ===\n"
                total_extracted_text += extracted_text
                successful_extractions += 1
            else:
                print(f"   ❌ No meaningful text extracted")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary and save results
    print(f"\n📊 OLLAMA VISION PROCESSING SUMMARY:")
    print("=" * 60)
    print(f"Images processed: {len(image_files)}")
    print(f"Successful extractions: {successful_extractions}")
    print(f"Total text extracted: {len(total_extracted_text):,} characters")
    
    if total_extracted_text:
        # Save comprehensive results
        output_file = Path("onenote_ollama_vision_results.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== ONENOTE OLLAMA VISION OCR RESULTS ===\n\n")
            f.write(f"Vision Model: {model}\n")
            f.write(f"Images processed: {len(image_files)}\n")
            f.write(f"Successful extractions: {successful_extractions}\n")
            f.write(f"Total text: {len(total_extracted_text):,} characters\n\n")
            f.write("=== EXTRACTED TEXT FROM ALL IMAGES ===\n")
            f.write(total_extracted_text)
        
        print(f"💾 Saved comprehensive results to: {output_file}")
        
        # Show preview
        print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
        print("=" * 60)
        print(total_extracted_text[:1000])
        if len(total_extracted_text) > 1000:
            print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
        print("=" * 60)
        
        # Compare with previous attempts
        print(f"\n🎉 FINAL COMPARISON:")
        print(f"Original OneNote extraction: ~1,880 characters (metadata only)")
        print(f"Ollama Vision extraction: {len(total_extracted_text):,} characters (ACTUAL CONTENT)")
        
        if len(total_extracted_text) > 1880:
            improvement = len(total_extracted_text) / 1880
            print(f"Improvement factor: {improvement:.1f}x MORE REAL CONTENT!")
            print("🎉 THIS IS THE ACTUAL ONENOTE RESEARCH CONTENT!")
        
        return True
    else:
        print("❌ No text was extracted from any images")
        return False


async def test_single_image_with_ollama():
    """Test Ollama vision with a single image first."""
    
    print("🧪 TESTING OLLAMA VISION WITH SINGLE IMAGE")
    print("=" * 60)
    
    # Find the most promising image (Image 4 had 85% light pixels)
    image_dir = Path("extracted_onenote_images")
    test_image = image_dir / "onenote_image_4_jpeg.jpg"
    
    if not test_image.exists():
        # Fallback to any available image
        image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        if image_files:
            test_image = image_files[0]
        else:
            print("❌ No test images available")
            return False
    
    print(f"🖼️ Testing with: {test_image.name}")
    print(f"📁 File size: {test_image.stat().st_size:,} bytes")
    
    # Check Ollama first
    model = "qwen2.5-vl:3b"
    if not await check_ollama_availability(model):
        return False
    
    # Process the test image
    extracted_text = await process_image_with_ollama_vision(test_image, model)
    
    if extracted_text and len(extracted_text.strip()) > 20:
        print(f"✅ TEST SUCCESS: Extracted {len(extracted_text)} characters")
        print(f"📝 Full extracted text:")
        print("-" * 40)
        print(extracted_text)
        print("-" * 40)
        
        # Save test result
        with open("ollama_vision_test_result.txt", 'w', encoding='utf-8') as f:
            f.write(f"=== OLLAMA VISION TEST RESULT ===\n\n")
            f.write(f"Model: {model}\n")
            f.write(f"Image: {test_image.name}\n")
            f.write(f"Text length: {len(extracted_text)} characters\n\n")
            f.write("=== EXTRACTED TEXT ===\n\n")
            f.write(extracted_text)
        
        print(f"💾 Saved test result to: ollama_vision_test_result.txt")
        return True
    else:
        print("❌ TEST FAILED: No meaningful text extracted")
        return False


def main():
    """Run Ollama vision OCR processing on OneNote images."""
    print("🤖 OLLAMA VISION ONENOTE OCR PROCESSING")
    print("=" * 60)
    
    async def run_processing():
        # Test with single image first
        test_success = await test_single_image_with_ollama()
        
        if test_success:
            print("\n✅ Single image test successful, processing all images...")
            # Process all images
            success = await process_all_onenote_images_with_ollama()
            return success
        else:
            print("\n❌ Single image test failed, check Ollama setup")
            return False
    
    success = asyncio.run(run_processing())
    
    print("\n" + "=" * 60)
    print("🎯 OLLAMA VISION OCR SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Ollama vision extracted actual OneNote content!")
        print("✅ Real research content extracted from OneNote images")
        print("✅ This is the actual content you were looking for")
        print("✅ Local processing with Ollama vision model")
        print("\nNext steps:")
        print("1. Integrate Ollama vision into OneNote processor")
        print("2. Use extracted content for entity extraction")
        print("3. Process references from actual content")
        print("4. Update knowledge graph with real research data")
    else:
        print("❌ Ollama vision OCR needs setup")
        print("Required steps:")
        print("1. Start Ollama: ollama serve")
        print("2. Install vision model: ollama pull qwen2.5-vl:3b")
        print("3. Install httpx: pip install httpx")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
