/* Enhanced Progress Tracking UI Styles */

/* Step Indicators */
.step-indicators {
    margin-bottom: 20px;
}

.step-indicator {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #e9ecef;
    color: #6c757d;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 10px;
    transition: all 0.3s ease;
}

.step-indicator.active {
    background-color: #007bff;
    color: white;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.step-indicator.completed {
    background-color: #28a745;
    color: white;
}

.step-label {
    font-size: 0.8rem;
    color: #6c757d;
    text-align: center;
    max-width: 80px;
    margin: 0 auto;
}

/* Progress Bars */
.progress {
    background-color: #e9ecef;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: #007bff;
    transition: width 0.6s ease;
}

.progress-bar.bg-success {
    background-color: #28a745 !important;
}

.progress-bar.bg-danger {
    background-color: #dc3545 !important;
}

.progress-bar.bg-warning {
    background-color: #ffc107 !important;
    color: #212529;
}

/* Processing Log */
#processing-log {
    font-family: 'Courier New', monospace;
    font-size: 0.85rem;
    margin: 0;
    white-space: pre-wrap;
}

/* Processing Steps */
.step-progress {
    margin-bottom: 10px;
}

.step-progress-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.step-progress-bar {
    height: 10px !important;
}

/* Processing Time */
#processing-time {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Collapse Buttons */
.card-header .btn-link {
    color: #212529;
    text-decoration: none;
    font-weight: 500;
    font-size: 1rem;
    width: 100%;
    text-align: left;
}

.card-header .btn-link:hover {
    color: #007bff;
    text-decoration: none;
}

/* List Group Items */
.list-group-item {
    padding: 0.5rem 1rem;
    background-color: transparent;
    border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.list-group-item:last-child {
    border-bottom: none;
}

/* Animation for active step */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.step-indicator.active {
    animation: pulse 2s infinite;
}

/* Enhanced Progress Card Styles */
.card.shadow-sm {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.card.shadow-sm:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
    transform: translateY(-2px);
}

.progress-bar-striped.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* Statistics Grid */
.row.text-center > .col-3 {
    padding: 0.5rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
}

.row.text-center > .col-3:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Badge Animations */
.badge {
    transition: all 0.3s ease;
}

.badge.bg-success {
    animation: pulse-success 2s infinite;
}

.badge.bg-danger {
    animation: pulse-danger 2s infinite;
}

@keyframes pulse-success {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

@keyframes pulse-danger {
    0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

/* Collapsible Details */
.collapse .card-body {
    font-size: 0.875rem;
    line-height: 1.5;
}

.btn-outline-secondary {
    transition: all 0.2s ease;
}

.btn-outline-secondary:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* ETA Display */
.eta-display {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 0.9rem;
    color: #856404;
    animation: fade-in 0.5s ease;
}

@keyframes fade-in {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .row.text-center > .col-3 {
        margin-bottom: 0.5rem;
    }

    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start;
    }

    .card-header .d-flex > div {
        margin-top: 0.5rem;
    }
}
