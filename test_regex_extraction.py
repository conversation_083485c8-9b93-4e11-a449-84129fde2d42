#!/usr/bin/env python3
"""
Test the regex extraction directly
"""

import asyncio
from services.reference_processor import ReferenceProcessor
from processors.pdf_processor import PDFProcessor
from pathlib import Path

async def test_regex_extraction():
    # Get the text first
    processor = PDFProcessor()
    file_path = Path('uploads/4e8bb988-26e9-4e75-a50d-53e7bcb4835d_Co Q10 - Athletes.pdf')
    
    result = await processor.extract_text(file_path)
    if not result.get('success'):
        print("Failed to extract text")
        return
    
    text = result.get('text', '')
    print(f"Text length: {len(text)}")
    
    # Test the reference processor regex extraction directly
    ref_processor = ReferenceProcessor()
    
    print("\n=== Testing _extract_references_with_regex ===")
    regex_refs = ref_processor._extract_references_with_regex(text)
    print(f"Regex extraction found: {len(regex_refs)} references")
    for i, ref in enumerate(regex_refs, 1):
        print(f"  {i}. {ref}")
    
    print("\n=== Testing _extract_with_general_patterns ===")
    general_refs = ref_processor._extract_with_general_patterns(text)
    print(f"General patterns found: {len(general_refs)} references")
    for i, ref in enumerate(general_refs, 1):
        print(f"  {i}. {ref}")
    
    print("\n=== Testing _find_reference_sections ===")
    ref_sections = ref_processor._find_reference_sections(text)
    print(f"Reference sections found: {len(ref_sections)}")
    for i, section in enumerate(ref_sections, 1):
        print(f"  Section {i}: {section[:100]}...")
    
    print("\n=== Testing validation ===")
    test_refs = [
        "Journal of the International Society of Sports Nutrition",
        "*Journal of the International Society of Sports Nutrition*",
    ]
    
    for ref in test_refs:
        is_valid = ref_processor._is_valid_reference(ref)
        print(f"'{ref}' -> Valid: {is_valid}")

if __name__ == "__main__":
    asyncio.run(test_regex_extraction())
