"""
Worker processors package for the Graphiti application.

This package provides worker processor implementations for different types of tasks.
"""

# Import processor functions
from workers.processors.document_processor import process_document_task
from workers.processors.entity_processor import process_entity_task
from workers.processors.entity_deduplicator import process_entity_deduplication_task
from workers.processors.reference_processor import process_reference_task
from workers.processors.embedding_processor import process_embedding_task
from workers.processors.database_processor import process_database_task

# Map of worker types to processor functions
from workers.base import WorkerType

PROCESSOR_MAP = {
    WorkerType.DOCUMENT_PROCESSOR: process_document_task,
    WorkerType.ENTITY_EXTRACTOR: process_entity_task,
    WorkerType.ENTITY_DEDUPLICATOR: process_entity_deduplication_task,
    WorkerType.REFERENCE_EXTRACTOR: process_reference_task,
    WorkerType.EMBEDDING_GENERATOR: process_embedding_task,
    WorkerType.DATABASE_WRITER: process_database_task
}
