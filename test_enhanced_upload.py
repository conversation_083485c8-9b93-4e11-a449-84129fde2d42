#!/usr/bin/env python3
"""
Test script to check enhanced upload endpoints.
"""

import requests

def test_enhanced_upload():
    """Test enhanced upload endpoints"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Enhanced Upload Endpoints")
    print("=" * 40)
    
    # Test 1: Check if enhanced upload endpoint exists
    print("1. Testing enhanced upload endpoint availability...")
    try:
        # Try a GET request to see if the endpoint exists (should return 405 Method Not Allowed)
        response = requests.get(f"{base_url}/api/enhanced/enhanced-upload", timeout=5)
        print(f"GET Status: {response.status_code}")
        
        if response.status_code == 405:
            print("✅ Endpoint exists (Method Not Allowed is expected for GET)")
        elif response.status_code == 404:
            print("❌ Endpoint not found")
        else:
            print(f"Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Check progress endpoint
    print("\n2. Testing progress endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/progress", timeout=5)
        print(f"Progress endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Progress endpoint working")
            print(f"Active operations: {len(data.get('active_operations', {}))}")
        else:
            print(f"❌ Progress endpoint error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Check supported types endpoint
    print("\n3. Testing supported types endpoint...")
    try:
        response = requests.get(f"{base_url}/api/enhanced/supported-types", timeout=5)
        print(f"Supported types status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Supported types endpoint working")
            print(f"Supported extensions: {data.get('supported_extensions', [])}")
        else:
            print(f"❌ Supported types error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Check if regular upload works
    print("\n4. Testing regular upload endpoint...")
    try:
        response = requests.get(f"{base_url}/api/upload", timeout=5)
        print(f"Regular upload status: {response.status_code}")
        
        if response.status_code == 405:
            print("✅ Regular upload endpoint exists")
        elif response.status_code == 404:
            print("❌ Regular upload endpoint not found")
        else:
            print(f"Regular upload status: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_enhanced_upload()
