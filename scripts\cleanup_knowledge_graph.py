"""
<PERSON>ript to clean up the knowledge graph
"""

import os
import sys
import asyncio
import logging

from dotenv import load_dotenv
from neo4j import AsyncGraphDatabase

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def delete_all_nodes(driver):
    """Delete all nodes and relationships in the knowledge graph."""
    logger.info("Deleting all nodes and relationships")
    
    try:
        async with driver.session() as session:
            # Delete all relationships first
            result = await session.run("MATCH ()-[r]->() DELETE r")
            await result.consume()
            logger.info("Deleted all relationships")
            
            # Delete all nodes
            result = await session.run("MATCH (n) DELETE n")
            await result.consume()
            logger.info("Deleted all nodes")
    except Exception as e:
        logger.error(f"Error deleting nodes and relationships: {e}")

async def delete_document(driver, document_name):
    """Delete a specific document and its chunks from the knowledge graph."""
    logger.info(f"Deleting document: {document_name}")
    
    try:
        async with driver.session() as session:
            # Find the Episode node for the document
            result = await session.run(
                "MATCH (e:Episode) WHERE e.name CONTAINS $document_name RETURN e.uuid AS uuid, e.name AS name",
                {"document_name": document_name}
            )
            
            episodes = []
            async for record in result:
                episodes.append({
                    "uuid": record["uuid"],
                    "name": record["name"]
                })
            
            if not episodes:
                logger.warning(f"No documents found matching: {document_name}")
                return
            
            logger.info(f"Found {len(episodes)} matching documents")
            
            # Delete each matching document and its chunks
            for episode in episodes:
                # Delete the relationships to Facts
                result = await session.run(
                    "MATCH (e:Episode {uuid: $uuid})-[r:CONTAINS]->(f:Fact) DELETE r",
                    {"uuid": episode["uuid"]}
                )
                await result.consume()
                
                # Delete the Facts
                result = await session.run(
                    "MATCH (e:Episode {uuid: $uuid})-[:CONTAINS]->(f:Fact) DELETE f",
                    {"uuid": episode["uuid"]}
                )
                await result.consume()
                
                # Delete the Episode
                result = await session.run(
                    "MATCH (e:Episode {uuid: $uuid}) DELETE e",
                    {"uuid": episode["uuid"]}
                )
                await result.consume()
                
                logger.info(f"Deleted document: {episode['name']}")
    except Exception as e:
        logger.error(f"Error deleting document: {e}")

async def main():
    """Main function to clean up the knowledge graph."""
    # Load environment variables
    load_dotenv()
    
    # Get Neo4j connection details
    neo4j_uri = os.environ.get('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.environ.get('NEO4J_USER', 'neo4j')
    neo4j_password = os.environ.get('NEO4J_PASSWORD', 'Triathlon16!')
    
    # Check command line arguments
    if len(sys.argv) < 2:
        logger.error("Please specify a command: delete-all or delete-document")
        logger.info("Usage:")
        logger.info("  python cleanup_knowledge_graph.py delete-all")
        logger.info("  python cleanup_knowledge_graph.py delete-document <document_name>")
        return
    
    command = sys.argv[1].lower()
    
    try:
        # Connect to Neo4j
        driver = AsyncGraphDatabase.driver(
            neo4j_uri, 
            auth=(neo4j_user, neo4j_password)
        )
        
        if command == "delete-all":
            # Delete all nodes and relationships
            await delete_all_nodes(driver)
        
        elif command == "delete-document":
            # Delete a specific document
            if len(sys.argv) < 3:
                logger.error("Please specify a document name")
                logger.info("Usage: python cleanup_knowledge_graph.py delete-document <document_name>")
                return
            
            document_name = " ".join(sys.argv[2:])
            await delete_document(driver, document_name)
        
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: delete-all, delete-document")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close the driver
        if 'driver' in locals():
            await driver.close()

if __name__ == "__main__":
    asyncio.run(main())
