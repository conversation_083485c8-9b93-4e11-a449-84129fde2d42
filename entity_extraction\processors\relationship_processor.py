"""
Relationship processor for creating and managing relationships between entities.

This module provides functionality for extracting and managing relationships between
entities in the knowledge graph. It uses LLMs to analyze text and identify meaningful
relationships between entities mentioned in the same context, then creates these
relationships in the knowledge graph database.

The RelationshipProcessor class encapsulates all the logic for relationship extraction
and database operations, providing a clean interface for other components to interact
with relationship data in the knowledge graph.

Example:
    ```python
    import asyncio
    from entity_extraction.processors.relationship_processor import RelationshipProcessor

    async def extract_relationships(driver, api_key):
        # Create a relationship processor
        processor = RelationshipProcessor(driver, api_key)

        # Extract relationships between entities in the same facts
        total_processed, total_relationships = await processor.extract_relationships_between_entities(
            batch_size=5,
            max_batches=10
        )

        print(f"Processed {total_processed} facts, extracted {total_relationships} relationships")

    # Run the async function
    asyncio.run(extract_relationships(driver, api_key))
    ```
"""

import os
import json
import logging
import asyncio
from datetime import datetime, timezone
from typing import List, Dict, Any, Tuple

import openai

# Set up logging
logger = logging.getLogger(__name__)

# Import clients
try:
    from utils.local_llm_client import LocalLLMClient
except ImportError:
    logger.warning("LocalLLMClient not found, will use OpenAI if available")
    LocalLLMClient = None


class RelationshipProcessor:
    """
    Processor for creating and managing relationships between entities.

    This class provides methods for extracting and managing relationships between entities
    in the knowledge graph. It uses LLMs to analyze text and identify meaningful relationships
    between entities mentioned in the same context, then creates these relationships in the
    knowledge graph database.

    The processor supports different types of relationships, including:
    - Mechanistic relationships (e.g., "inhibits", "activates")
    - Therapeutic relationships (e.g., "treats", "prevents")
    - Compositional relationships (e.g., "contains", "yields")
    - Physiological relationships
    - Evidence relationships
    - Causal relationships (e.g., "causes", "leads to")
    - Comparative relationships (e.g., "more effective than")
    - Taxonomic relationships (e.g., "IS_A", "PART_OF")

    Attributes:
        driver: Database driver for connecting to the graph database
        api_key (Optional[str]): API key for the LLM service
        logger (Logger): Logger instance for this class
    """

    def __init__(self, driver, api_key=None):
        """
        Initialize the relationship processor.

        Args:
            driver: Database driver
            api_key: API key for the LLM service
        """
        self.driver = driver
        self.api_key = api_key
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    async def extract_relationships_between_entities(self, batch_size=5, max_batches=None):
        """
        Extract relationships between entities that are mentioned in the same fact.

        This method identifies facts that mention multiple entities but don't have
        relationships between those entities, then uses LLMs to extract meaningful
        relationships from the text. It creates RELATED_TO relationships in the database
        with properties that describe the nature of the relationship.

        The method processes facts in batches to manage memory usage and processing load.
        It can be limited to a maximum number of batches for testing or controlled processing.

        The extraction process:
        1. Finds facts with multiple entities but no relationships between them
        2. For each fact, retrieves the entities mentioned in it
        3. Uses LLM to analyze the text and identify relationships between entities
        4. Creates RELATED_TO relationships in the database with appropriate properties

        The relationships include properties such as:
        - type: The specific relationship type (e.g., "INHIBITS", "CONTAINS")
        - description: A detailed description of the relationship
        - directionality: Whether the effect is positive, negative, or neutral
        - magnitude: Quantitative measures of effect size when available
        - conditions: Contextual factors affecting the relationship
        - confidence: Assessment of the relationship's certainty (high, medium, low)

        Args:
            batch_size (int, optional): Number of facts to process in each batch.
                Defaults to 5.
            max_batches (Optional[int], optional): Maximum number of batches to process.
                If None, processes all available facts. Defaults to None.

        Returns:
            Tuple[int, int]: A tuple containing:
                - total_processed (int): The total number of facts processed
                - total_relationships (int): The total number of relationships created

        Raises:
            Exception: If there is an error extracting relationships
        """
        logger.info("Extracting relationships between entities")

        batch_count = 0
        total_processed = 0
        total_relationships = 0

        try:
            async with self.driver.session() as session:
                while True:
                    # Check if we've reached the maximum number of batches
                    if max_batches is not None and batch_count >= max_batches:
                        logger.info(f"Reached maximum number of batches ({max_batches})")
                        break

                    # Get facts that mention multiple entities but don't have RELATED_TO relationships between them
                    result = await session.run(
                        """
                        MATCH (f:Fact)-[:MENTIONS]->(e1:Entity)
                        MATCH (f)-[:MENTIONS]->(e2:Entity)
                        WHERE e1 <> e2
                        AND NOT EXISTS((e1)-[:RELATED_TO]->(e2))
                        AND NOT EXISTS((e2)-[:RELATED_TO]->(e1))
                        WITH f, collect(DISTINCT e1) AS entities
                        WHERE size(entities) > 1
                        RETURN f.uuid AS uuid, f.body AS body
                        LIMIT $batch_size
                        """,
                        {"batch_size": batch_size}
                    )

                    facts = []
                    async for record in result:
                        facts.append({
                            "uuid": record["uuid"],
                            "body": record["body"]
                        })

                    if not facts:
                        logger.info("No more facts with multiple entities without relationships")
                        break

                    logger.info(f"Found {len(facts)} facts with multiple entities without relationships")

                    # Process each fact
                    for fact in facts:
                        # Get entities mentioned in this fact
                        result = await session.run(
                            """
                            MATCH (f:Fact {uuid: $fact_uuid})-[:MENTIONS]->(e:Entity)
                            RETURN e.name AS name, e.type AS type, e.description AS description
                            """,
                            {"fact_uuid": fact["uuid"]}
                        )

                        entities = []
                        async for record in result:
                            entities.append({
                                "name": record["name"],
                                "type": record["type"],
                                "description": record["description"]
                            })

                        if len(entities) < 2:
                            continue

                        # Extract relationships between entities
                        relationships = await self._extract_relationships(fact["body"], entities)
                        logger.info(f"Extracted {len(relationships)} relationships from fact {fact['uuid']}")

                        # Create relationships in the database
                        entity_names = [e["name"] for e in entities]
                        entity_types = {e["name"]: e["type"] for e in entities}

                        # Create relationships in Neo4j
                        for rel in relationships:
                            source = rel["source"]
                            target = rel["target"]
                            relationship_type = rel["relationship"].upper()
                            description = rel.get("description", "")
                            directionality = rel.get("directionality", "neutral")
                            magnitude = rel.get("magnitude", "")
                            conditions = rel.get("conditions", "")
                            confidence = rel.get("confidence", "medium")

                            # Validate that source and target entities exist
                            if source not in entity_names or target not in entity_names:
                                logger.warning(f"Source or target entity not found: {source} -> {target}")
                                continue

                            # Create the relationship
                            result = await session.run(
                                """
                                MATCH (e1:Entity {name: $source, type: $source_type})
                                MATCH (e2:Entity {name: $target, type: $target_type})
                                MERGE (e1)-[r:RELATED_TO {type: $relationship_type}]->(e2)
                                ON CREATE SET
                                    r.description = $description,
                                    r.directionality = $directionality,
                                    r.magnitude = $magnitude,
                                    r.conditions = $conditions,
                                    r.confidence = $confidence,
                                    r.created_at = datetime($timestamp)
                                ON MATCH SET
                                    r.description = $description,
                                    r.directionality = $directionality,
                                    r.magnitude = $magnitude,
                                    r.conditions = $conditions,
                                    r.confidence = $confidence
                                RETURN type(r) AS type
                                """,
                                {
                                    "source": source,
                                    "source_type": entity_types[source],
                                    "target": target,
                                    "target_type": entity_types[target],
                                    "relationship_type": relationship_type,
                                    "description": description,
                                    "directionality": directionality,
                                    "magnitude": magnitude,
                                    "conditions": conditions,
                                    "confidence": confidence,
                                    "timestamp": datetime.now(timezone.utc).isoformat()
                                }
                            )
                            await result.consume()
                            total_relationships += 1

                    batch_count += 1
                    total_processed += len(facts)

                    logger.info(f"Processed batch {batch_count} ({total_processed} facts, {total_relationships} relationships total)")

                    # Sleep briefly to avoid rate limits
                    await asyncio.sleep(1)

                logger.info(f"Finished extracting relationships from {total_processed} facts ({total_relationships} relationships total)")
                return total_processed, total_relationships
        except Exception as e:
            logger.error(f"Error extracting relationships: {e}")
            return 0, 0

    async def _extract_relationships(self, text: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities in text.

        This method uses LLMs to analyze text and identify relationships between the
        specified entities. It constructs a detailed system prompt that guides the LLM
        to extract specific types of relationships with their attributes.

        The method can use either a local LLM via Ollama or OpenAI, depending on the
        configuration. It prioritizes using a local LLM if available and configured.

        The extracted relationships include detailed information such as:
        - Source and target entities
        - Relationship type
        - Description of the relationship
        - Directionality (positive, negative, neutral)
        - Magnitude (quantitative measures when available)
        - Confidence level (high, medium, low)

        This method is called internally by extract_relationships_between_entities.

        Args:
            text (str): Text to extract relationships from
            entities (List[Dict[str, Any]]): List of entities to extract relationships between,
                where each entity is a dictionary with at least the following keys:
                - name (str): The name of the entity
                - type (str): The type of the entity
                - description (Optional[str]): A description of the entity

        Returns:
            List[Dict[str, Any]]: A list of extracted relationships, where each relationship
                is a dictionary with the following keys:
                - source (str): The name of the source entity
                - target (str): The name of the target entity
                - relationship (str): The type of relationship
                - description (Optional[str]): A description of the relationship
                - directionality (Optional[str]): The directionality of the relationship
                - magnitude (Optional[str]): Quantitative measures of effect size
                - conditions (Optional[str]): Contextual factors affecting the relationship
                - confidence (Optional[str]): Assessment of the relationship's certainty
        """
        # Create a system prompt that specifies the relationship extraction task
        entity_names = [e["name"] for e in entities]

        system_prompt = self._get_relationship_system_prompt(entity_names)

        # Check if we should use local LLM
        use_local_llm = os.environ.get('USE_LOCAL_LLM', 'true').lower() == 'true'
        local_llm_model = os.environ.get('ENTITY_EXTRACTION_MODEL', 'medllama3-v20')
        ollama_base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')

        if use_local_llm and LocalLLMClient is not None:
            # Use local LLM
            logger.info(f"Using local LLM model {local_llm_model} for relationship extraction")
            local_client = LocalLLMClient(base_url=ollama_base_url, model=local_llm_model)

            response = await local_client.generate_completion(
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text}
                ],
                temperature=0.3,
                max_tokens=1000,
                response_format={"type": "json_object"}
            )

            # Parse the response
            content = response["choices"][0]["message"]["content"]
        else:
            # Use OpenAI
            logger.info("Using OpenAI for relationship extraction")
            client = openai.OpenAI(api_key=self.api_key)

            response = client.chat.completions.create(
                model=os.environ.get('ENTITY_EXTRACTION_MODEL', 'gpt-4.1-mini'),
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": text}
                ],
                response_format={"type": "json_object"}
            )

            # Parse the response
            content = response.choices[0].message.content

        # Process the content
        try:
            data = json.loads(content)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response content: {content}")
            data = {"relationships": []}

        return data.get("relationships", [])

    def _get_relationship_system_prompt(self, entity_names: List[str]) -> str:
        """
        Get the system prompt for relationship extraction.

        This method generates a detailed system prompt that instructs the LLM on how to
        extract relationships between the specified entities. The prompt includes information
        about the types of relationships to look for, the attributes to extract for each
        relationship, and formatting instructions for the output.

        The prompt is designed to work with modern LLMs like GPT-4, Claude, and Llama 2/3,
        instructing them to return structured JSON that can be easily parsed by the system.

        The prompt focuses on extracting various types of relationships, including:
        - Mechanistic relationships (e.g., compound-target interactions)
        - Therapeutic relationships (e.g., intervention-condition effects)
        - Compositional relationships (e.g., botanical-compound composition)
        - Physiological relationships (e.g., nutrient-process interactions)
        - Evidence relationships (e.g., study-finding connections)
        - Causal relationships (e.g., cause-effect links)
        - Comparative relationships (e.g., intervention comparisons)
        - Taxonomic relationships (e.g., hierarchical classifications)

        Args:
            entity_names (List[str]): List of entity names to extract relationships between.
                These names are included in the prompt to focus the LLM on finding
                relationships specifically between these entities.

        Returns:
            str: A detailed system prompt for relationship extraction
        """
        return f"""
        You are an advanced relationship extraction system specialized for scientific health literature. Your purpose is to identify precise relationships between entities from research documents for construction of a knowledge graph. Focus on scientific accuracy and capturing the semantic relationships that exist between concepts.

        Analyze the relationships between these entities mentioned in the text:
        {', '.join(entity_names)}

        Extract the following types of relationships:

        1. Mechanistic Relationships:
           - Compound → Target (e.g., "inhibits", "activates", "modulates")
           - Gene → Protein → Function
           - Pathway → Disease (e.g., "contributes to", "protects against")

        2. Therapeutic Relationships:
           - Intervention → Condition (e.g., "treats", "prevents", "manages")
           - Compound → Biomarker (e.g., "reduces", "increases", "normalizes")
           - Botanical → Therapeutic Application

        3. Compositional Relationships:
           - Botanical → Compound (e.g., "contains", "yields", "standardized for")
           - Food → Nutrient → Bioavailability

        4. Physiological Relationships:
           - Nutrient → Physiological Process
           - Lifestyle Factor → Health Outcome
           - Genetic Variant → Disease Risk

        5. Evidence Relationships:
           - Study → Finding → Evidence Level
           - Intervention → Outcome → Statistical Significance

        6. Causal Relationships:
           - Cause → Effect (e.g., "causes", "leads to", "results in")
           - Risk Factor → Disease (e.g., "increases risk of", "predisposes to")

        7. Comparative Relationships:
           - Intervention A vs Intervention B (e.g., "more effective than", "similar efficacy to")
           - Dose-Response Relationships (e.g., "higher doses produce greater effects")

        8. Taxonomic Relationships:
           - IS_A (hierarchical classification)
           - PART_OF (compositional hierarchy)

        For each pair of related entities, provide:
        1. source: The name of the source entity
        2. target: The name of the target entity
        3. relationship: A specific relationship type from the categories above
        4. description: A precise, scientifically accurate description of the relationship
        5. directionality: Whether the effect is positive, negative, or neutral
        6. magnitude: Quantitative measures of effect size when available
        7. confidence: Your assessment of the relationship's certainty based on the text (high, medium, low)

        Attribute Extraction for Relationships:
        - Directionality: Positive/negative/neutral effects
        - Magnitude: Quantitative measures of effect size
        - Statistical Significance: p-values, confidence intervals when available
        - Conditions: Contextual factors affecting the relationship
        - Temporal Aspects: Acute vs. chronic effects, time-dependency
        - Dose-Dependency: Threshold effects, dose-response relationships
        - Population Specificity: Demographics where the relationship applies

        Processing Instructions:
        1. Prioritize precision over recall - extract relationships with high confidence
        2. Preserve quantitative data and statistical information when available
        3. Distinguish between observed facts and hypothesized relationships
        4. Extract nested and complex relationships that span multiple sentences
        5. Pay special attention to statements that establish causal or mechanistic links
        6. Capture information about study populations and contextual factors
        7. Identify contradictory findings within the same document

        Return a JSON object with a "relationships" array containing the extracted relationships.
        If no relationships are found, return an empty array.

        Example output format:
        {{
            "relationships": [
                {{
                    "source": "Thymoquinone",
                    "target": "NF-κB pathway",
                    "relationship": "inhibits",
                    "description": "Thymoquinone inhibits the NF-κB signaling pathway, reducing inflammatory cytokine production",
                    "directionality": "negative",
                    "magnitude": "50% reduction at 10μM concentration",
                    "conditions": "In vitro study using human macrophage cells",
                    "confidence": "high"
                }},
                {{
                    "source": "Nigella sativa",
                    "target": "Thymoquinone",
                    "relationship": "contains",
                    "description": "Nigella sativa seeds contain thymoquinone as a primary bioactive constituent",
                    "directionality": "neutral",
                    "magnitude": "0.4-2.5% concentration in cold-pressed oil",
                    "conditions": "Varies by extraction method and seed source",
                    "confidence": "high"
                }},
                {{
                    "source": "Vitamin D",
                    "target": "Immune function",
                    "relationship": "enhances",
                    "description": "Vitamin D enhances immune function through multiple mechanisms including T-cell regulation",
                    "directionality": "positive",
                    "magnitude": "Significant improvement with serum levels >30 ng/mL",
                    "conditions": "Most pronounced in vitamin D deficient individuals",
                    "confidence": "medium"
                }}
            ]
        }}
        """
