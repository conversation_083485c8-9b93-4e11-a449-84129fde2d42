"""
Entity model for the knowledge graph.

This module defines the Entity data model used in the knowledge graph. The Entity class
represents an entity node in the graph, with properties such as name, type, description,
and additional attributes.

The model uses Pydantic for data validation and serialization, making it easy to convert
between different representations (e.g., Python objects, dictionaries, JSON) while
ensuring data integrity.

Example:
    ```python
    from entity_extraction.models.entity import Entity

    # Create an entity from a dictionary
    entity_data = {
        "name": "Vitamin C",
        "type": "Nutrient",
        "description": "An essential vitamin with antioxidant properties",
        "uuid": "123e4567-e89b-12d3-a456-************"
    }
    entity = Entity.from_dict(entity_data)

    # Access entity properties
    print(f"Entity: {entity.name} ({entity.type})")

    # Convert entity to dictionary
    entity_dict = entity.to_dict()
    ```
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class Entity(BaseModel):
    """
    Entity model for the knowledge graph.

    This class represents an entity node in the knowledge graph. It includes properties
    such as name, type, description, and additional attributes. The model uses Pydantic
    for data validation and serialization.

    Entities are the fundamental building blocks of the knowledge graph, representing
    concepts, objects, or things of interest in the domain. Each entity has a type that
    categorizes it (e.g., Person, Organization, Nutrient, Disease).

    Attributes:
        name (str): The name of the entity
        type (str): The type of the entity (one of the supported entity types)
        uuid (Optional[str]): The UUID of the entity
        description (Optional[str]): A description of the entity
        source_document_id (Optional[str]): The ID of the source document where this entity was found
        source_document_title (Optional[str]): The title of the source document
        source_fact_id (Optional[str]): The ID of the fact where this entity was found
        confidence (Optional[float]): Confidence score for the entity extraction (0.0-1.0)
        created_at (Optional[datetime]): The creation timestamp
        updated_at (Optional[datetime]): The last update timestamp
        attributes (Optional[Dict[str, Any]]): Additional attributes of the entity
    """

    name: str = Field(..., description="The name of the entity")
    type: str = Field(..., description="The type of the entity")
    uuid: Optional[str] = Field(None, description="The UUID of the entity")
    description: Optional[str] = Field("", description="The description of the entity")
    source_document_id: Optional[str] = Field(None, description="The ID of the source document where this entity was found")
    source_document_title: Optional[str] = Field(None, description="The title of the source document")
    source_fact_id: Optional[str] = Field(None, description="The ID of the fact where this entity was found")
    confidence: Optional[float] = Field(None, description="Confidence score for the entity extraction (0.0-1.0)")
    created_at: Optional[datetime] = Field(None, description="The creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="The last update timestamp")
    attributes: Optional[Dict[str, Any]] = Field({}, description="Additional attributes of the entity")

    class Config:
        """Pydantic configuration."""

        arbitrary_types_allowed = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    def to_dict(self) -> Dict[str, Any]:
        """
        Convert the entity to a dictionary.

        This method converts the Entity object to a dictionary representation, which can
        be used for serialization to JSON or other formats. It handles the conversion of
        datetime objects to ISO format strings.

        The resulting dictionary includes all the entity's properties, with datetime
        objects converted to ISO format strings for easier serialization.

        Returns:
            Dict[str, Any]: Dictionary representation of the entity with the following keys:
                - name (str): The name of the entity
                - type (str): The type of the entity
                - uuid (Optional[str]): The UUID of the entity
                - description (Optional[str]): A description of the entity
                - source_document_id (Optional[str]): The ID of the source document
                - source_document_title (Optional[str]): The title of the source document
                - source_fact_id (Optional[str]): The ID of the source fact
                - confidence (Optional[float]): Confidence score for the entity extraction
                - created_at (Optional[str]): The creation timestamp as ISO format string
                - updated_at (Optional[str]): The last update timestamp as ISO format string
                - attributes (Optional[Dict[str, Any]]): Additional attributes of the entity
        """
        return {
            "name": self.name,
            "type": self.type,
            "uuid": self.uuid,
            "description": self.description,
            "source_document_id": self.source_document_id,
            "source_document_title": self.source_document_title,
            "source_fact_id": self.source_fact_id,
            "confidence": self.confidence,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "attributes": self.attributes
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Entity":
        """
        Create an entity from a dictionary.

        This class method creates an Entity object from a dictionary representation.
        It's useful for deserializing entities from JSON or other dictionary-based
        formats.

        The method handles missing optional fields by providing default values.

        Example:
            ```python
            entity_data = {
                "name": "Vitamin C",
                "type": "Nutrient",
                "description": "An essential vitamin",
                "source_document_id": "doc-123",
                "source_document_title": "Nutritional Benefits of Vitamin C"
            }
            entity = Entity.from_dict(entity_data)
            ```

        Args:
            data (Dict[str, Any]): Dictionary representation of the entity with at least
                the following required keys:
                - name (str): The name of the entity
                - type (str): The type of the entity

                And these optional keys:
                - uuid (Optional[str]): The UUID of the entity
                - description (Optional[str]): A description of the entity
                - source_document_id (Optional[str]): The ID of the source document
                - source_document_title (Optional[str]): The title of the source document
                - source_fact_id (Optional[str]): The ID of the source fact
                - confidence (Optional[float]): Confidence score for the entity extraction
                - created_at (Optional[str/datetime]): The creation timestamp
                - updated_at (Optional[str/datetime]): The last update timestamp
                - attributes (Optional[Dict[str, Any]]): Additional attributes

        Returns:
            Entity: A new Entity instance created from the dictionary data
        """
        return cls(
            name=data["name"],
            type=data["type"],
            uuid=data.get("uuid"),
            description=data.get("description", ""),
            source_document_id=data.get("source_document_id"),
            source_document_title=data.get("source_document_title"),
            source_fact_id=data.get("source_fact_id"),
            confidence=data.get("confidence"),
            created_at=data.get("created_at"),
            updated_at=data.get("updated_at"),
            attributes=data.get("attributes", {})
        )

    @classmethod
    def from_record(cls, record: Dict[str, Any]) -> "Entity":
        """
        Create an entity from a database record.

        This class method creates an Entity object from a database record. It's specifically
        designed to handle records returned from database queries, which may have a slightly
        different structure than the dictionaries used in from_dict.

        The method is particularly useful when retrieving entities from the database and
        converting them to Entity objects for further processing.

        Example:
            ```python
            # Assuming 'record' is a record from a database query
            entity = Entity.from_record(record)
            ```

        Args:
            record (Dict[str, Any]): Database record containing entity data with at least
                the following required keys:
                - name (str): The name of the entity
                - type (str): The type of the entity

                And potentially these optional keys:
                - uuid (Optional[str]): The UUID of the entity
                - description (Optional[str]): A description of the entity
                - source_document_id (Optional[str]): The ID of the source document
                - source_document_title (Optional[str]): The title of the source document
                - source_fact_id (Optional[str]): The ID of the source fact
                - confidence (Optional[float]): Confidence score for the entity extraction
                - created_at (Optional[datetime]): The creation timestamp
                - updated_at (Optional[datetime]): The last update timestamp
                - attributes (Optional[Dict[str, Any]]): Additional attributes

        Returns:
            Entity: A new Entity instance created from the database record
        """
        return cls(
            name=record["name"],
            type=record["type"],
            uuid=record.get("uuid"),
            description=record.get("description", ""),
            source_document_id=record.get("source_document_id"),
            source_document_title=record.get("source_document_title"),
            source_fact_id=record.get("source_fact_id"),
            confidence=record.get("confidence"),
            created_at=record.get("created_at"),
            updated_at=record.get("updated_at"),
            attributes=record.get("attributes", {})
        )
