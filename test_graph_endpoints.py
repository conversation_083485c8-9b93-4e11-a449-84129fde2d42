#!/usr/bin/env python3
"""
Test script to check graph API endpoints
"""

import requests
import json

def test_graph_endpoints():
    """Test graph-related API endpoints"""
    base_url = 'http://localhost:9753'
    
    print('Testing Graph API Endpoints...')
    
    # Test 1: Check graph stats
    print('\n1. Testing /api/graph-stats:')
    try:
        response = requests.get(f'{base_url}/api/graph-stats', timeout=10)
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'Response: {json.dumps(data, indent=2)}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Exception: {e}')
    
    # Test 2: Check knowledge graph endpoint
    print('\n2. Testing /api/knowledge-graph:')
    try:
        response = requests.get(f'{base_url}/api/knowledge-graph?limit=10', timeout=15)
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            nodes = data.get('nodes', [])
            edges = data.get('edges', [])
            print(f'Nodes: {len(nodes)}')
            print(f'Edges: {len(edges)}')
            print(f'Response keys: {list(data.keys())}')
            
            if nodes:
                print(f'Sample node: {nodes[0]}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Exception: {e}')
    
    # Test 3: Check entities endpoint
    print('\n3. Testing /api/entities:')
    try:
        response = requests.get(f'{base_url}/api/entities?limit=5', timeout=10)
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            print(f'Entities count: {len(entities)}')
            if entities:
                print(f'Sample entity: {entities[0]}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Exception: {e}')
    
    # Test 4: Check system status
    print('\n4. Testing /api/system-status:')
    try:
        response = requests.get(f'{base_url}/api/system-status', timeout=10)
        print(f'Status: {response.status_code}')
        if response.status_code == 200:
            data = response.json()
            print(f'System status: {json.dumps(data, indent=2)}')
        else:
            print(f'Error: {response.text}')
    except Exception as e:
        print(f'Exception: {e}')
    
    print('\nAPI endpoint testing completed!')

if __name__ == "__main__":
    test_graph_endpoints()
