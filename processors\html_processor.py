"""
HTML/XML Document Processor
Handles .html, .htm, .xml files with enhanced text extraction.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class HTMLProcessor:
    """
    Processor for HTML/XML documents (.html, .htm, .xml).
    """
    
    def __init__(self):
        """Initialize the HTML processor."""
        self.supported_extensions = ['.html', '.htm', '.xml']
        
        # Check for BeautifulSoup availability
        self.bs4_available = self._check_bs4_availability()
    
    def _check_bs4_availability(self) -> bool:
        """Check if BeautifulSoup is available."""
        try:
            from bs4 import BeautifulSoup
            return True
        except ImportError:
            logger.warning("BeautifulSoup not available. Install with: pip install beautifulsoup4")
            return False
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from an HTML/XML file.
        
        Args:
            file_path: Path to the HTML/XML file
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Read the file
            encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
            content = None
            encoding_used = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read()
                    encoding_used = encoding
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"Error reading file with encoding {encoding}: {e}")
                    continue
            
            if content is None:
                return {
                    'success': False,
                    'error': f"Could not read file {file_path} with any supported encoding",
                    'text': '',
                    'metadata': {}
                }
            
            # Extract text using BeautifulSoup if available
            if self.bs4_available:
                try:
                    result = await self._extract_with_bs4(content, file_path, encoding_used)
                    if result['success']:
                        return result
                except Exception as e:
                    logger.warning(f"BeautifulSoup extraction failed: {e}")
            
            # Fallback to simple text extraction
            return await self._extract_simple(content, file_path, encoding_used)
            
        except Exception as e:
            logger.error(f"Error processing HTML/XML file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_bs4(self, content: str, file_path: Path, encoding: str) -> Dict[str, Any]:
        """Extract text using BeautifulSoup."""
        try:
            from bs4 import BeautifulSoup
            
            # Parse the HTML/XML
            soup = BeautifulSoup(content, 'html.parser')
            
            # Extract title
            title = file_path.stem
            title_tag = soup.find('title')
            if title_tag and title_tag.string:
                title = title_tag.string.strip()
            
            # Extract meta information
            meta_info = {}
            for meta in soup.find_all('meta'):
                name = meta.get('name') or meta.get('property')
                content_attr = meta.get('content')
                if name and content_attr:
                    meta_info[name] = content_attr
            
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()
            
            # Extract text content
            text = soup.get_text()
            
            # Clean up text
            lines = (line.strip() for line in text.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            
            # Extract metadata
            metadata = await self._extract_metadata_bs4(soup, file_path, encoding, meta_info, title)
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'beautifulsoup',
                'extraction_method': 'beautifulsoup',
                'encoding': encoding
            }
            
        except Exception as e:
            logger.error(f"Error extracting with BeautifulSoup: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_simple(self, content: str, file_path: Path, encoding: str) -> Dict[str, Any]:
        """Simple text extraction without BeautifulSoup."""
        try:
            import re
            
            # Remove HTML tags
            text = re.sub(r'<[^>]+>', '', content)
            
            # Clean up whitespace
            text = re.sub(r'\s+', ' ', text).strip()
            
            # Extract metadata
            metadata = await self._extract_metadata_simple(content, file_path, encoding)
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'regex',
                'extraction_method': 'regex',
                'encoding': encoding
            }
            
        except Exception as e:
            logger.error(f"Error with simple extraction: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata_bs4(self, soup, file_path: Path, encoding: str, meta_info: Dict, title: str) -> Dict[str, Any]:
        """Extract metadata using BeautifulSoup."""
        try:
            stat = file_path.stat()
            
            # Extract author from meta tags
            author = meta_info.get('author', 'Unknown')
            
            # Extract description
            description = meta_info.get('description', '')
            
            # Extract keywords
            keywords = meta_info.get('keywords', '')
            
            # Count elements
            paragraphs = len(soup.find_all('p'))
            headings = len(soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6']))
            links = len(soup.find_all('a'))
            images = len(soup.find_all('img'))
            
            metadata = {
                'title': title,
                'author': author,
                'description': description,
                'keywords': keywords,
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'encoding': encoding,
                'paragraphs_count': paragraphs,
                'headings_count': headings,
                'links_count': links,
                'images_count': images,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'beautifulsoup'
            }
            
            # Add meta information
            metadata['meta_tags'] = meta_info
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting BeautifulSoup metadata: {e}")
            return {
                'title': title,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    async def _extract_metadata_simple(self, content: str, file_path: Path, encoding: str) -> Dict[str, Any]:
        """Extract basic metadata without BeautifulSoup."""
        try:
            import re
            
            stat = file_path.stat()
            
            # Try to extract title from title tag
            title = file_path.stem
            title_match = re.search(r'<title[^>]*>([^<]+)</title>', content, re.IGNORECASE)
            if title_match:
                title = title_match.group(1).strip()
            
            # Count basic elements
            tag_counts = {
                'paragraphs': len(re.findall(r'<p[^>]*>', content, re.IGNORECASE)),
                'headings': len(re.findall(r'<h[1-6][^>]*>', content, re.IGNORECASE)),
                'links': len(re.findall(r'<a[^>]*>', content, re.IGNORECASE)),
                'images': len(re.findall(r'<img[^>]*>', content, re.IGNORECASE))
            }
            
            metadata = {
                'title': title,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'encoding': encoding,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'regex'
            }
            
            metadata.update(tag_counts)
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting simple metadata: {e}")
            return {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the HTML/XML content.
        
        Args:
            file_path: Path to the HTML/XML file
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
