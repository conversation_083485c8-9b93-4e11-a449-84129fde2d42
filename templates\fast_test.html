<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fast API Test - Graphiti</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Fast API Test Page</h1>
        <p>Testing fast API endpoints to verify UI data loading</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Graph Statistics</h5>
                    </div>
                    <div class="card-body">
                        <div id="stats-loading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div id="stats-content" style="display: none;">
                            <p><strong>Documents:</strong> <span id="doc-count">-</span></p>
                            <p><strong>Entities:</strong> <span id="entity-count">-</span></p>
                            <p><strong>Status:</strong> <span id="stats-status">-</span></p>
                        </div>
                        <div id="stats-error" class="alert alert-danger" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Sample Entities</h5>
                    </div>
                    <div class="card-body">
                        <div id="entities-loading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div id="entities-content" style="display: none;">
                            <ul id="entities-list" class="list-group"></ul>
                        </div>
                        <div id="entities-error" class="alert alert-danger" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Sample Documents</h5>
                    </div>
                    <div class="card-body">
                        <div id="docs-loading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div id="docs-content" style="display: none;">
                            <ul id="docs-list" class="list-group"></ul>
                        </div>
                        <div id="docs-error" class="alert alert-danger" style="display: none;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>References</h5>
                    </div>
                    <div class="card-body">
                        <div id="refs-loading" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                        <div id="refs-content" style="display: none;">
                            <p><strong>Reference Count:</strong> <span id="ref-count">-</span></p>
                            <p><strong>Status:</strong> <span id="refs-status">-</span></p>
                        </div>
                        <div id="refs-error" class="alert alert-danger" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>API Test Results</h5>
                    </div>
                    <div class="card-body">
                        <div id="test-results"></div>
                        <button id="run-tests" class="btn btn-primary">Run All Tests</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Test functions
        async function testGraphStats() {
            try {
                const response = await fetch('/api/fast/graph-stats');
                const data = await response.json();
                
                document.getElementById('stats-loading').style.display = 'none';
                document.getElementById('stats-content').style.display = 'block';
                
                document.getElementById('doc-count').textContent = data.total_episodes || 0;
                document.getElementById('entity-count').textContent = data.total_entities || 0;
                document.getElementById('stats-status').textContent = data.status || 'unknown';
                
                return { success: true, data };
            } catch (error) {
                document.getElementById('stats-loading').style.display = 'none';
                document.getElementById('stats-error').style.display = 'block';
                document.getElementById('stats-error').textContent = 'Error: ' + error.message;
                return { success: false, error: error.message };
            }
        }
        
        async function testEntities() {
            try {
                const response = await fetch('/api/fast/entities?limit=10');
                const data = await response.json();
                
                document.getElementById('entities-loading').style.display = 'none';
                document.getElementById('entities-content').style.display = 'block';
                
                const list = document.getElementById('entities-list');
                list.innerHTML = '';
                
                if (data.entities && data.entities.length > 0) {
                    data.entities.forEach(entity => {
                        const item = document.createElement('li');
                        item.className = 'list-group-item';
                        item.innerHTML = `<strong>${entity.name}</strong> <span class="badge bg-secondary">${entity.type}</span>`;
                        list.appendChild(item);
                    });
                } else {
                    list.innerHTML = '<li class="list-group-item">No entities found</li>';
                }
                
                return { success: true, data };
            } catch (error) {
                document.getElementById('entities-loading').style.display = 'none';
                document.getElementById('entities-error').style.display = 'block';
                document.getElementById('entities-error').textContent = 'Error: ' + error.message;
                return { success: false, error: error.message };
            }
        }
        
        async function testDocuments() {
            try {
                const response = await fetch('/api/fast/documents?limit=10');
                const data = await response.json();
                
                document.getElementById('docs-loading').style.display = 'none';
                document.getElementById('docs-content').style.display = 'block';
                
                const list = document.getElementById('docs-list');
                list.innerHTML = '';
                
                if (data.documents && data.documents.length > 0) {
                    data.documents.forEach(doc => {
                        const item = document.createElement('li');
                        item.className = 'list-group-item';
                        item.textContent = doc.name || 'Unnamed document';
                        list.appendChild(item);
                    });
                } else {
                    list.innerHTML = '<li class="list-group-item">No documents found</li>';
                }
                
                return { success: true, data };
            } catch (error) {
                document.getElementById('docs-loading').style.display = 'none';
                document.getElementById('docs-error').style.display = 'block';
                document.getElementById('docs-error').textContent = 'Error: ' + error.message;
                return { success: false, error: error.message };
            }
        }
        
        async function testReferences() {
            try {
                const response = await fetch('/api/fast/references');
                const data = await response.json();
                
                document.getElementById('refs-loading').style.display = 'none';
                document.getElementById('refs-content').style.display = 'block';
                
                document.getElementById('ref-count').textContent = data.count || 0;
                document.getElementById('refs-status').textContent = data.status || 'unknown';
                
                return { success: true, data };
            } catch (error) {
                document.getElementById('refs-loading').style.display = 'none';
                document.getElementById('refs-error').style.display = 'block';
                document.getElementById('refs-error').textContent = 'Error: ' + error.message;
                return { success: false, error: error.message };
            }
        }
        
        async function runAllTests() {
            const results = document.getElementById('test-results');
            results.innerHTML = '<div class="alert alert-info">Running tests...</div>';
            
            const tests = [
                { name: 'Graph Stats', func: testGraphStats },
                { name: 'Entities', func: testEntities },
                { name: 'Documents', func: testDocuments },
                { name: 'References', func: testReferences }
            ];
            
            let html = '<h6>Test Results:</h6><ul class="list-group">';
            
            for (const test of tests) {
                try {
                    const result = await test.func();
                    if (result.success) {
                        html += `<li class="list-group-item list-group-item-success">✅ ${test.name}: Success</li>`;
                    } else {
                        html += `<li class="list-group-item list-group-item-danger">❌ ${test.name}: ${result.error}</li>`;
                    }
                } catch (error) {
                    html += `<li class="list-group-item list-group-item-danger">❌ ${test.name}: ${error.message}</li>`;
                }
            }
            
            html += '</ul>';
            results.innerHTML = html;
        }
        
        // Auto-run tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            runAllTests();
            
            document.getElementById('run-tests').addEventListener('click', runAllTests);
        });
    </script>
</body>
</html>
