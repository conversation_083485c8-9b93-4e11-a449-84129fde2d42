"""
Automatic entity deduplication utilities.

This module provides functions for automatically deduplicating entities
after document processing.
"""

import asyncio
import logging
from typing import Op<PERSON>

from entity_deduplication import EntityDeduplicator

# Set up logging
logger = logging.getLogger(__name__)


async def deduplicate_document_entities(document_id: str) -> bool:
    """
    Deduplicate entities for a specific document.
    
    Args:
        document_id: ID of the document to deduplicate entities for
        
    Returns:
        True if deduplication was successful, False otherwise
    """
    logger.info(f"Starting automatic entity deduplication for document {document_id}")
    
    try:
        # Initialize deduplicator
        deduplicator = EntityDeduplicator()
        
        # Run deduplication for the document
        result = await deduplicator.deduplicate_entities(
            document_id=document_id,
            merge=True
        )
        
        # Log results
        logger.info(f"Automatic deduplication completed for document {document_id}")
        logger.info(f"Total entities processed: {result.total_entities}")
        logger.info(f"Duplicate groups found: {result.duplicate_groups}")
        logger.info(f"Total duplicate matches: {result.total_duplicates}")
        logger.info(f"Entities merged: {result.merged_entities}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during automatic deduplication for document {document_id}: {e}")
        return False


async def deduplicate_entity_type(entity_type: str) -> bool:
    """
    Deduplicate entities of a specific type.
    
    Args:
        entity_type: Type of entities to deduplicate
        
    Returns:
        True if deduplication was successful, False otherwise
    """
    logger.info(f"Starting automatic entity deduplication for type {entity_type}")
    
    try:
        # Initialize deduplicator
        deduplicator = EntityDeduplicator()
        
        # Run deduplication for the entity type
        result = await deduplicator.deduplicate_entities(
            entity_type=entity_type,
            merge=True
        )
        
        # Log results
        logger.info(f"Automatic deduplication completed for type {entity_type}")
        logger.info(f"Total entities processed: {result.total_entities}")
        logger.info(f"Duplicate groups found: {result.duplicate_groups}")
        logger.info(f"Total duplicate matches: {result.total_duplicates}")
        logger.info(f"Entities merged: {result.merged_entities}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during automatic deduplication for type {entity_type}: {e}")
        return False


async def deduplicate_all_entities() -> bool:
    """
    Deduplicate all entities in the knowledge graph.
    
    Returns:
        True if deduplication was successful, False otherwise
    """
    logger.info("Starting automatic entity deduplication for all entities")
    
    try:
        # Initialize deduplicator
        deduplicator = EntityDeduplicator()
        
        # Run deduplication for all entities
        result = await deduplicator.deduplicate_entities(
            merge=True
        )
        
        # Log results
        logger.info("Automatic deduplication completed for all entities")
        logger.info(f"Total entities processed: {result.total_entities}")
        logger.info(f"Duplicate groups found: {result.duplicate_groups}")
        logger.info(f"Total duplicate matches: {result.total_duplicates}")
        logger.info(f"Entities merged: {result.merged_entities}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during automatic deduplication for all entities: {e}")
        return False
