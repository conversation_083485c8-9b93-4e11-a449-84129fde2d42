"""
Metadata processor for extracting and enriching document metadata.
"""

from typing import Dict, Any, Optional
import logging
from pathlib import Path
import mimetypes
import hashlib
from datetime import datetime

logger = logging.getLogger(__name__)

class MetadataProcessor:
    """
    Processor for extracting and enriching document metadata.
    """
    
    def __init__(self):
        """Initialize the metadata processor."""
        self.processor_name = "MetadataProcessor"
    
    async def extract_metadata(self, file_path: str, extracted_text: str = "") -> Dict[str, Any]:
        """
        Extract comprehensive metadata from a document.
        
        Args:
            file_path: Path to the document file
            extracted_text: Extracted text content (optional)
            
        Returns:
            Comprehensive metadata dictionary
        """
        try:
            path = Path(file_path)
            
            # Basic file metadata
            metadata = await self._extract_file_metadata(path)
            
            # Content metadata
            if extracted_text:
                metadata.update(await self._extract_content_metadata(extracted_text))
            
            # Document type specific metadata
            metadata.update(await self._extract_type_specific_metadata(path))
            
            # Generate document fingerprint
            metadata["document_hash"] = await self._generate_document_hash(file_path)
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata from {file_path}: {e}")
            return {}
    
    async def _extract_file_metadata(self, path: Path) -> Dict[str, Any]:
        """Extract basic file system metadata."""
        try:
            stat = path.stat()
            
            # MIME type detection
            mime_type, encoding = mimetypes.guess_type(str(path))
            
            return {
                "filename": path.name,
                "file_path": str(path.absolute()),
                "file_size": stat.st_size,
                "file_extension": path.suffix.lower(),
                "mime_type": mime_type or "unknown",
                "encoding": encoding,
                "created_time": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "modified_time": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "accessed_time": datetime.fromtimestamp(stat.st_atime).isoformat(),
                "extraction_timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"Error extracting file metadata: {e}")
            return {}
    
    async def _extract_content_metadata(self, text: str) -> Dict[str, Any]:
        """Extract metadata from text content."""
        try:
            if not text:
                return {}
            
            # Basic text statistics
            words = text.split()
            lines = text.split('\n')
            
            metadata = {
                "character_count": len(text),
                "word_count": len(words),
                "line_count": len(lines),
                "paragraph_count": len([line for line in lines if line.strip()]),
                "average_word_length": sum(len(word) for word in words) / len(words) if words else 0,
                "average_sentence_length": await self._calculate_avg_sentence_length(text)
            }
            
            # Language detection
            language = await self._detect_language(text)
            if language:
                metadata["detected_language"] = language
            
            # Extract potential titles and headers
            titles = await self._extract_titles(text)
            if titles:
                metadata["extracted_titles"] = titles
            
            # Extract dates
            dates = await self._extract_dates(text)
            if dates:
                metadata["extracted_dates"] = dates
            
            # Extract email addresses
            emails = await self._extract_emails(text)
            if emails:
                metadata["extracted_emails"] = emails
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting content metadata: {e}")
            return {}
    
    async def _extract_type_specific_metadata(self, path: Path) -> Dict[str, Any]:
        """Extract metadata specific to document type."""
        extension = path.suffix.lower()
        metadata = {"document_type": self._get_document_type(extension)}
        
        try:
            if extension == '.pdf':
                metadata.update(await self._extract_pdf_metadata(path))
            elif extension in ['.docx', '.doc']:
                metadata.update(await self._extract_docx_metadata(path))
            elif extension in ['.html', '.htm']:
                metadata.update(await self._extract_html_metadata(path))
            
        except Exception as e:
            logger.warning(f"Error extracting type-specific metadata: {e}")
        
        return metadata
    
    def _get_document_type(self, extension: str) -> str:
        """Determine document type from extension."""
        type_mapping = {
            '.pdf': 'PDF Document',
            '.docx': 'Word Document',
            '.doc': 'Word Document (Legacy)',
            '.txt': 'Plain Text',
            '.md': 'Markdown',
            '.html': 'HTML Document',
            '.htm': 'HTML Document',
            '.rtf': 'Rich Text Format',
            '.csv': 'CSV Data',
            '.xlsx': 'Excel Spreadsheet',
            '.pptx': 'PowerPoint Presentation',
            '.xml': 'XML Document',
            '.json': 'JSON Data'
        }
        return type_mapping.get(extension, 'Unknown')
    
    async def _extract_pdf_metadata(self, path: Path) -> Dict[str, Any]:
        """Extract PDF-specific metadata."""
        try:
            import PyPDF2
            
            with open(path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                metadata = {"page_count": len(pdf_reader.pages)}
                
                if pdf_reader.metadata:
                    doc_info = pdf_reader.metadata
                    metadata.update({
                        "pdf_title": doc_info.get("/Title", ""),
                        "pdf_author": doc_info.get("/Author", ""),
                        "pdf_subject": doc_info.get("/Subject", ""),
                        "pdf_creator": doc_info.get("/Creator", ""),
                        "pdf_producer": doc_info.get("/Producer", ""),
                        "pdf_creation_date": str(doc_info.get("/CreationDate", "")),
                        "pdf_modification_date": str(doc_info.get("/ModDate", ""))
                    })
                
                return metadata
                
        except Exception as e:
            logger.warning(f"Could not extract PDF metadata: {e}")
            return {}
    
    async def _extract_docx_metadata(self, path: Path) -> Dict[str, Any]:
        """Extract DOCX-specific metadata."""
        try:
            from docx import Document
            
            doc = Document(path)
            metadata = {
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables)
            }
            
            if hasattr(doc, 'core_properties'):
                core_props = doc.core_properties
                metadata.update({
                    "docx_title": getattr(core_props, 'title', '') or '',
                    "docx_author": getattr(core_props, 'author', '') or '',
                    "docx_subject": getattr(core_props, 'subject', '') or '',
                    "docx_keywords": getattr(core_props, 'keywords', '') or '',
                    "docx_created": str(getattr(core_props, 'created', '') or ''),
                    "docx_modified": str(getattr(core_props, 'modified', '') or '')
                })
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Could not extract DOCX metadata: {e}")
            return {}
    
    async def _extract_html_metadata(self, path: Path) -> Dict[str, Any]:
        """Extract HTML-specific metadata."""
        try:
            with open(path, 'r', encoding='utf-8', errors='ignore') as file:
                content = file.read()
            
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(content, 'html.parser')
            
            metadata = {}
            
            # Title
            title_tag = soup.find('title')
            if title_tag:
                metadata['html_title'] = title_tag.get_text().strip()
            
            # Meta tags
            meta_tags = soup.find_all('meta')
            for meta in meta_tags:
                name = meta.get('name', '').lower()
                content = meta.get('content', '')
                
                if name in ['description', 'keywords', 'author']:
                    metadata[f'html_{name}'] = content
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Could not extract HTML metadata: {e}")
            return {}
    
    async def _generate_document_hash(self, file_path: str) -> str:
        """Generate SHA-256 hash of document content."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.warning(f"Could not generate document hash: {e}")
            return ""
    
    async def _calculate_avg_sentence_length(self, text: str) -> float:
        """Calculate average sentence length."""
        try:
            import re
            sentences = re.split(r'[.!?]+', text)
            sentences = [s.strip() for s in sentences if s.strip()]
            
            if not sentences:
                return 0.0
            
            total_words = sum(len(sentence.split()) for sentence in sentences)
            return total_words / len(sentences)
        except Exception:
            return 0.0
    
    async def _detect_language(self, text: str) -> Optional[str]:
        """Detect the language of the text."""
        try:
            from langdetect import detect
            return detect(text[:1000])  # Use first 1000 chars for detection
        except Exception:
            return None
    
    async def _extract_titles(self, text: str) -> list:
        """Extract potential titles from text."""
        try:
            import re
            lines = text.split('\n')
            titles = []
            
            for line in lines[:10]:  # Check first 10 lines
                line = line.strip()
                if line and len(line) < 100 and not line.endswith('.'):
                    # Potential title if it's short and doesn't end with period
                    titles.append(line)
            
            return titles[:3]  # Return top 3 potential titles
        except Exception:
            return []
    
    async def _extract_dates(self, text: str) -> list:
        """Extract dates from text."""
        try:
            import re
            date_patterns = [
                r'\b\d{1,2}[/-]\d{1,2}[/-]\d{4}\b',  # MM/DD/YYYY or MM-DD-YYYY
                r'\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b',  # YYYY/MM/DD or YYYY-MM-DD
                r'\b\d{1,2}\s+\w+\s+\d{4}\b',       # DD Month YYYY
                r'\b\w+\s+\d{1,2},?\s+\d{4}\b'      # Month DD, YYYY
            ]
            
            dates = []
            for pattern in date_patterns:
                matches = re.findall(pattern, text)
                dates.extend(matches)
            
            return list(set(dates))[:5]  # Return unique dates, max 5
        except Exception:
            return []
    
    async def _extract_emails(self, text: str) -> list:
        """Extract email addresses from text."""
        try:
            import re
            email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
            emails = re.findall(email_pattern, text)
            return list(set(emails))[:5]  # Return unique emails, max 5
        except Exception:
            return []
