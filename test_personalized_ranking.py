#!/usr/bin/env python3
"""
Test script for personalized ranking system.

This script demonstrates user-specific ranking adaptation.
"""

import sys
import os
import logging
import time
import random
from typing import List, Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ranking.personalized_ranking import (
    PersonalizedRanker, UserProfileManager, get_personalized_ranker
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_users_and_interactions():
    """Create mock users with different interaction patterns."""
    profile_manager = UserProfileManager()
    
    # User 1: Medical researcher (expert in healthcare)
    user1_interactions = [
        ("vitamin D deficiency", {"uuid": "r1", "name": "Vitamin D Deficiency", "type": "Disease", "summary": "Clinical analysis of vitamin D deficiency mechanisms"}, "feedback_positive", 45.0),
        ("calcium metabolism", {"uuid": "r2", "name": "Calcium Homeostasis", "type": "Process", "summary": "Molecular mechanisms of calcium regulation"}, "dwell", 60.0),
        ("bone health research", {"uuid": "r3", "name": "Osteoporosis Study", "type": "Research", "summary": "Systematic review of bone health interventions"}, "bookmark", None),
        ("vitamin D supplementation", {"uuid": "r4", "name": "Vitamin D3 Therapy", "type": "Treatment", "summary": "Clinical guidelines for vitamin D supplementation"}, "click", 30.0),
    ]
    
    # User 2: Nutrition enthusiast (novice in healthcare)
    user2_interactions = [
        ("healthy foods", {"uuid": "r5", "name": "Vitamin D Foods", "type": "Food", "summary": "Foods rich in vitamin D for daily nutrition"}, "click", 20.0),
        ("vitamin supplements", {"uuid": "r6", "name": "Daily Vitamins", "type": "Supplement", "summary": "Guide to choosing vitamin supplements"}, "feedback_positive", None),
        ("diet tips", {"uuid": "r7", "name": "Healthy Diet", "type": "Lifestyle", "summary": "Simple tips for better nutrition"}, "dwell", 40.0),
        ("sun exposure", {"uuid": "r8", "name": "Sunlight Benefits", "type": "Lifestyle", "summary": "How sunlight helps vitamin D production"}, "share", None),
    ]
    
    # User 3: General health seeker (intermediate)
    user3_interactions = [
        ("vitamin D benefits", {"uuid": "r9", "name": "Vitamin D Overview", "type": "Health", "summary": "General health benefits of vitamin D"}, "click", 25.0),
        ("bone strength", {"uuid": "r10", "name": "Strong Bones", "type": "Health", "summary": "How to maintain healthy bones"}, "dwell", 35.0),
        ("health supplements", {"uuid": "r11", "name": "Supplement Guide", "type": "Health", "summary": "Overview of health supplements"}, "feedback_positive", None),
    ]
    
    # Record interactions for each user
    for user_id, interactions in [("user1", user1_interactions), ("user2", user2_interactions), ("user3", user3_interactions)]:
        for query, result, interaction_type, dwell_time in interactions:
            profile_manager.update_profile_from_interaction(user_id, query, result, interaction_type, dwell_time)
    
    return profile_manager


def create_test_search_results() -> List[Dict[str, Any]]:
    """Create test search results with varying complexity and topics."""
    return [
        {
            "uuid": "test1",
            "name": "Vitamin D Deficiency Pathophysiology",
            "type": "Disease",
            "summary": "Comprehensive analysis of molecular mechanisms underlying vitamin D deficiency, including parathyroid hormone regulation, calcium homeostasis disruption, and bone mineralization pathways.",
            "confidence": 0.9
        },
        {
            "uuid": "test2", 
            "name": "Vitamin D Rich Foods",
            "type": "Food",
            "summary": "Simple guide to foods that contain vitamin D, including fish, eggs, and fortified dairy products for daily nutrition.",
            "confidence": 0.8
        },
        {
            "uuid": "test3",
            "name": "Vitamin D Supplementation Guidelines",
            "type": "Treatment",
            "summary": "Clinical recommendations for vitamin D supplementation dosing, monitoring, and safety considerations in various populations.",
            "confidence": 0.95
        },
        {
            "uuid": "test4",
            "name": "Sunlight and Vitamin D",
            "type": "Lifestyle",
            "summary": "How sun exposure helps your body make vitamin D naturally and tips for safe sun exposure.",
            "confidence": 0.7
        },
        {
            "uuid": "test5",
            "name": "Bone Health Research",
            "type": "Research", 
            "summary": "Latest research findings on vitamin D's role in bone health, including meta-analyses and clinical trial results.",
            "confidence": 0.85
        }
    ]


def test_user_profile_creation():
    """Test user profile creation and updates."""
    logger.info("=== Testing User Profile Creation ===")
    
    profile_manager = create_mock_users_and_interactions()
    
    # Check profiles for each user
    for user_id in ["user1", "user2", "user3"]:
        profile = profile_manager.get_or_create_profile(user_id)
        
        logger.info(f"\n{user_id.upper()} Profile:")
        logger.info(f"  Domain Expertise: {profile.domain_expertise}")
        logger.info(f"  Entity Preferences: {profile.entity_preferences}")
        logger.info(f"  Topic Interests: {profile.topic_interests}")
        logger.info(f"  Interactions: {len(profile.interaction_history)}")


def test_user_similarity():
    """Test user similarity calculation."""
    logger.info("\n=== Testing User Similarity ===")
    
    profile_manager = create_mock_users_and_interactions()
    
    # Calculate similarities between users
    users = ["user1", "user2", "user3"]
    
    for i, user1 in enumerate(users):
        for user2 in users[i+1:]:
            similarity = profile_manager.get_user_similarity(user1, user2)
            logger.info(f"{user1} vs {user2}: {similarity:.3f}")
    
    # Get similar users for each user
    for user_id in users:
        similar_users = profile_manager.get_similar_users(user_id, top_k=2)
        logger.info(f"\nSimilar users to {user_id}:")
        for similar_user, similarity in similar_users:
            logger.info(f"  {similar_user}: {similarity:.3f}")


def test_personalized_ranking():
    """Test personalized ranking for different users."""
    logger.info("\n=== Testing Personalized Ranking ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    results = create_test_search_results()
    base_scores = [0.8, 0.6, 0.9, 0.5, 0.7]  # Mock base relevance scores
    query = "vitamin D health benefits"
    
    # Test ranking for each user type
    users = {
        "user1": "Medical Researcher (Expert)",
        "user2": "Nutrition Enthusiast (Novice)", 
        "user3": "General Health Seeker (Intermediate)"
    }
    
    for user_id, description in users.items():
        logger.info(f"\n{description} ({user_id}):")
        
        personalized_results = ranker.personalize_ranking(user_id, query, results, base_scores)
        
        for i, (result, score) in enumerate(personalized_results):
            logger.info(f"  {i+1}. {result['name']} (score: {score:.3f})")


def test_ranking_explanations():
    """Test ranking explanations for users."""
    logger.info("\n=== Testing Ranking Explanations ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    results = create_test_search_results()
    query = "vitamin D health benefits"
    
    # Get explanations for top result for each user
    for user_id in ["user1", "user2", "user3"]:
        logger.info(f"\n{user_id.upper()} - Why '{results[0]['name']}' was ranked:")
        
        explanation = ranker.get_personalization_explanation(user_id, query, results[0])
        
        logger.info(f"  Entity Preference: {explanation['entity_preference']:.3f}")
        logger.info(f"  Topic Interest: {explanation['topic_interest']:.3f}")
        logger.info(f"  Domain Expertise: {explanation['domain_expertise']:.3f}")
        logger.info(f"  Novelty: {explanation['novelty']:.3f}")
        logger.info(f"  Collaborative: {explanation['collaborative']:.3f}")
        logger.info(f"  Explanation: {explanation['explanation']}")


def test_expertise_based_ranking():
    """Test how expertise level affects ranking."""
    logger.info("\n=== Testing Expertise-Based Ranking ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    # Create results with different complexity levels
    simple_result = {
        "uuid": "simple",
        "name": "Vitamin D Basics",
        "type": "Health",
        "summary": "Simple guide to vitamin D benefits for everyone."
    }
    
    complex_result = {
        "uuid": "complex", 
        "name": "Vitamin D Molecular Mechanisms",
        "type": "Research",
        "summary": "Detailed analysis of vitamin D receptor signaling pathways, genomic and non-genomic effects, and tissue-specific responses in calcium homeostasis regulation."
    }
    
    query = "vitamin D information"
    
    for user_id in ["user1", "user2", "user3"]:
        profile = profile_manager.get_or_create_profile(user_id)
        expertise = profile.domain_expertise.get('healthcare', 0.0)
        
        simple_factor = ranker._calculate_domain_expertise_factor(profile, query, simple_result)
        complex_factor = ranker._calculate_domain_expertise_factor(profile, query, complex_result)
        
        logger.info(f"\n{user_id} (expertise: {expertise:.2f}):")
        logger.info(f"  Simple result factor: {simple_factor:.3f}")
        logger.info(f"  Complex result factor: {complex_factor:.3f}")
        logger.info(f"  Prefers: {'Complex' if complex_factor > simple_factor else 'Simple'}")


def test_novelty_factor():
    """Test novelty factor calculation."""
    logger.info("\n=== Testing Novelty Factor ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    # Test with seen and unseen results
    seen_result = {"uuid": "r1", "name": "Seen Result"}  # user1 has interacted with r1
    new_result = {"uuid": "new", "name": "New Result"}
    
    for user_id in ["user1", "user2", "user3"]:
        seen_novelty = ranker._calculate_novelty_factor(
            profile_manager.get_or_create_profile(user_id), seen_result
        )
        new_novelty = ranker._calculate_novelty_factor(
            profile_manager.get_or_create_profile(user_id), new_result
        )
        
        logger.info(f"\n{user_id}:")
        logger.info(f"  Seen result novelty: {seen_novelty:.3f}")
        logger.info(f"  New result novelty: {new_novelty:.3f}")


def test_collaborative_filtering():
    """Test collaborative filtering effects."""
    logger.info("\n=== Testing Collaborative Filtering ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    # Test collaborative factor for a result that similar users have seen
    test_result = {"uuid": "r1", "name": "Test Result"}  # user1 has seen this
    
    for user_id in ["user1", "user2", "user3"]:
        collaborative_factor = ranker._calculate_collaborative_factor(user_id, test_result)
        similar_users = profile_manager.get_similar_users(user_id, top_k=2)
        
        logger.info(f"\n{user_id}:")
        logger.info(f"  Collaborative factor: {collaborative_factor:.3f}")
        logger.info(f"  Similar users: {[u for u, s in similar_users]}")


def test_ranking_comparison():
    """Compare personalized vs non-personalized ranking."""
    logger.info("\n=== Ranking Comparison ===")
    
    profile_manager = create_mock_users_and_interactions()
    ranker = PersonalizedRanker(profile_manager)
    
    results = create_test_search_results()
    base_scores = [0.8, 0.6, 0.9, 0.5, 0.7]
    query = "vitamin D health benefits"
    
    # Non-personalized ranking (just base scores)
    logger.info("Non-personalized ranking:")
    sorted_by_base = sorted(zip(results, base_scores), key=lambda x: x[1], reverse=True)
    for i, (result, score) in enumerate(sorted_by_base):
        logger.info(f"  {i+1}. {result['name']} ({score:.3f})")
    
    # Personalized ranking for medical researcher
    logger.info("\nPersonalized for medical researcher (user1):")
    personalized = ranker.personalize_ranking("user1", query, results, base_scores)
    for i, (result, score) in enumerate(personalized):
        logger.info(f"  {i+1}. {result['name']} ({score:.3f})")
    
    # Personalized ranking for nutrition enthusiast
    logger.info("\nPersonalized for nutrition enthusiast (user2):")
    personalized = ranker.personalize_ranking("user2", query, results, base_scores)
    for i, (result, score) in enumerate(personalized):
        logger.info(f"  {i+1}. {result['name']} ({score:.3f})")


def main():
    """Run all tests."""
    logger.info("Starting Personalized Ranking Tests")
    logger.info("=" * 60)
    
    try:
        test_user_profile_creation()
        test_user_similarity()
        test_personalized_ranking()
        test_ranking_explanations()
        test_expertise_based_ranking()
        test_novelty_factor()
        test_collaborative_filtering()
        test_ranking_comparison()
        
        logger.info("\n" + "=" * 60)
        logger.info("All personalized ranking tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
