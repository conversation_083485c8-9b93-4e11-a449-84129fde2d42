#!/usr/bin/env python3
"""
Test script to check relationships in FalkorDB
"""

import asyncio
from database.database_service import get_falkordb_adapter

async def test_relationships():
    """Test relationships in FalkorDB"""
    print('Testing relationships...')
    
    adapter = await get_falkordb_adapter()
    
    # Test 1: Check relationships between any entities with names
    print('\n1. Testing named entity relationships:')
    query = """
    MATCH (n:Entity)-[r]->(m:Entity) 
    WHERE n.name IS NOT NULL AND n.name <> '' 
    AND m.name IS NOT NULL AND m.name <> ''
    RETURN n.uuid, m.uuid, type(r) 
    LIMIT 10
    """
    result = adapter.execute_cypher(query)
    print(f'Named entity relationships: {result}')
    
    # Test 2: Check if there are any relationships at all
    print('\n2. Testing any relationships:')
    query = """
    MATCH (n:Entity)-[r]->(m:Entity) 
    RETURN n.uuid, m.uuid, type(r) 
    LIMIT 5
    """
    result = adapter.execute_cypher(query)
    print(f'Any relationships: {result}')
    
    # Test 3: Check specific entity relationships
    print('\n3. Testing specific entity relationships:')
    query = """
    MATCH (n:Entity)-[r]->(m:Entity) 
    WHERE n.uuid = 'test-herb-1'
    RETURN n.uuid, m.uuid, type(r) 
    LIMIT 5
    """
    result = adapter.execute_cypher(query)
    print(f'Test herb 1 relationships: {result}')

if __name__ == "__main__":
    asyncio.run(test_relationships())
