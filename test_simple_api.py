#!/usr/bin/env python3
"""
Simple API test without async.
"""

import requests

def test_simple():
    """Simple API test"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Simple API Test")
    print("=" * 20)
    
    try:
        # Test health endpoint
        print("1. Health check...")
        response = requests.get(f"{base_url}/health", timeout=3)
        print(f"Health: {response.status_code}")
        
        # Test entities endpoint
        print("2. Entities check...")
        response = requests.get(f"{base_url}/api/entities?limit=1", timeout=3)
        print(f"Entities: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            if entities:
                entity = entities[0]
                uuid = entity.get('uuid')
                name = entity.get('name')
                print(f"Sample entity: {name} ({uuid})")
                
                # Test entity detail
                print("3. Entity detail check...")
                detail_response = requests.get(f"{base_url}/api/entity/{uuid}", timeout=3)
                print(f"Entity detail: {detail_response.status_code}")
                
                if detail_response.status_code == 200:
                    detail_data = detail_response.json()
                    if 'entity' in detail_data:
                        entity_detail = detail_data['entity']
                        print(f"Detail name: {entity_detail.get('name', 'N/A')}")
                        print(f"Detail mentions: {entity_detail.get('mention_count', 'N/A')}")
                        print("✅ Entity detail working!")
                    else:
                        print("❌ No entity in detail response")
                else:
                    print(f"❌ Entity detail failed: {detail_response.text[:100]}")
            else:
                print("❌ No entities found")
        else:
            print(f"❌ Entities failed: {response.text[:100]}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_simple()
