"""
Create test entities in FalkorDB
"""

import asyncio
from falkordb_adapter import <PERSON>alkorD<PERSON>dapter

async def create_test_entities():
    """Create test entities in FalkorDB"""
    adapter = FalkorDBAdapter(graph_name="graphiti")
    
    try:
        # Create entity types
        entity_types = [
            "Herb", "Nutrient", "Disease", "Medication", "Concept", 
            "Person", "Organization", "Process", "Symptom", "Treatment"
        ]
        
        # Create test entities for each type
        for entity_type in entity_types:
            print(f"Creating test entities for type: {entity_type}")
            
            # Create 5 entities for each type
            for i in range(1, 6):
                entity_name = f"Test {entity_type} {i}"
                entity_uuid = f"test-{entity_type.lower()}-{i}"
                entity_description = f"This is a test {entity_type.lower()} entity for testing purposes"
                mention_count = i * 2  # Vary the mention count
                
                # Create the entity
                query = f"""
                CREATE (e:Entity {{
                    name: '{entity_name}',
                    type: '{entity_type}',
                    uuid: '{entity_uuid}',
                    description: '{entity_description}',
                    mention_count: {mention_count}
                }})
                RETURN e.name, e.type, e.uuid
                """
                
                result = adapter.execute_cypher(query)
                print(f"Created entity: {result}")
        
        print("All test entities created successfully!")
        
    finally:
        adapter.close()

if __name__ == "__main__":
    asyncio.run(create_test_entities())
