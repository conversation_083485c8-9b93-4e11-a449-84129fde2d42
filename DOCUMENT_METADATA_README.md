# Document Metadata and Reference Extraction

This module enhances Graphiti's document processing capabilities by adding metadata extraction and reference extraction from scientific documents.

## Features

### Document Metadata Extraction

- Extracts basic metadata from PDF files (title, author, creation date, etc.)
- Uses LLM to enhance metadata extraction with more detailed information
- Stores metadata in the knowledge graph for easy access
- Supports both automatic extraction during document upload and manual extraction

### Reference Extraction

- Extracts bibliographic references from scientific documents
- Uses both regex patterns and LLM for comprehensive reference extraction
- Stores references in the knowledge graph linked to their source documents
- Exports references to CSV for further analysis
- Provides a user interface for browsing and searching references

## Architecture

The implementation consists of several components:

1. **DocumentMetadataExtractor**: Extracts metadata from PDF documents
2. **ReferenceExtractor**: Extracts references from scientific documents
3. **EnhancedPDFProcessor**: Combines metadata extraction, reference extraction, and knowledge graph integration
4. **Web Interface**: Provides a user interface for viewing and managing document metadata and references

## Knowledge Graph Schema

The implementation extends the existing knowledge graph schema with new nodes and relationships:

- **Episode Node**: Enhanced with metadata fields (title, authors, publication date, etc.)
- **References Node**: Represents a collection of references for a document
- **Reference Node**: Represents an individual bibliographic reference
- **HAS_REFERENCES Relationship**: Links an Episode to its References node
- **CONTAINS Relationship**: Links a References node to individual Reference nodes

## Usage

### Command Line

#### Process a Single Document

```bash
python enhanced_pdf_processor.py path/to/document.pdf
```

#### Process a Directory of Documents

```bash
python batch_process_with_metadata.py path/to/directory
```

#### Extract Only Metadata

```bash
python enhanced_pdf_processor.py path/to/document.pdf --no-references
```

#### Extract Only References

```bash
python enhanced_pdf_processor.py path/to/document.pdf --no-metadata
```

### Web Interface

1. Navigate to the **Upload** tab
2. Drag and drop PDF files or click to select files
3. Configure processing options:
   - **Chunk Size**: 1200 characters (recommended)
   - **Overlap**: 50 characters (recommended)
   - **Extract metadata**: Enable to extract document metadata
   - **Extract references**: Enable to extract bibliographic references
4. Click "Upload & Process" to start processing

### Viewing Metadata

1. Navigate to the **Metadata** tab
2. Select a document from the dropdown menu
3. View the extracted metadata, including:
   - Document information (title, authors, publication date, etc.)
   - File information (filename, file size, pages, etc.)
   - Abstract

### Viewing References

1. Navigate to the **References** tab
2. Select a document from the dropdown menu
3. View the extracted references
4. Use the search box to find specific references
5. Filter references by extraction method (LLM or regex)
6. Download references as CSV for further analysis

## API Endpoints

The implementation adds several API endpoints to the web interface:

- `/api/upload`: Upload and process a document with metadata and reference extraction
- `/api/processing-status/{file_id}`: Get the processing status of a document
- `/api/metadata/{file_id}`: Get metadata for a processed document
- `/api/references/{file_id}`: Get references for a processed document
- `/api/references-csv/{file_id}`: Download references as CSV for a document
- `/api/all-references-csv`: Download all references as CSV
- `/api/documents`: Get a list of all documents with metadata information
- `/api/document/{document_id}`: Get details for a specific document
- `/api/document-references/{document_id}`: Get references for a document from the knowledge graph
- `/api/document-references-csv/{document_id}`: Download references as CSV for a document from the knowledge graph
- `/api/all-references`: Get all references in the knowledge graph

## Dependencies

- **PyMuPDF (fitz)**: For enhanced PDF text extraction and metadata access
- **OpenAI API** or **Mistral API**: For LLM-based metadata and reference extraction
- **Neo4j**: For storing metadata and references in the knowledge graph

## Configuration

Configuration options are available in the `.env` file:

```
# Metadata extraction configuration
METADATA_LLM_PROVIDER=openai  # Options: openai, mistral, none
```

## Limitations

- Reference extraction works best with well-structured scientific documents
- LLM-based extraction requires an API key for OpenAI or Mistral
- Processing large documents may take significant time and API resources
