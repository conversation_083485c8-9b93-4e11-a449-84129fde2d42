#!/usr/bin/env python3
"""
Process OneNote Files by Pages

Processes OneNote files by extracting individual pages with unique UUIDs
and running each through the complete document ingestion pipeline.

This ensures proper tracking, reference management, and entity linking
for each OneNote page/section.
"""

import asyncio
import logging
import sys
import argparse
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from processors.onenote_page_processor import OneNotePageProcessor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def process_onenote_file(file_path: str, 
                             extract_entities: bool = True,
                             extract_references: bool = True,
                             generate_embeddings: bool = True,
                             chunk_size: int = 1200,
                             overlap: int = 0):
    """
    Process a single OneNote file by pages.
    
    Args:
        file_path: Path to the OneNote file
        extract_entities: Whether to extract entities
        extract_references: Whether to extract references
        generate_embeddings: Whether to generate embeddings
        chunk_size: Text chunk size for processing
        overlap: Overlap between chunks
    """
    
    print(f"🧠 Processing OneNote file by pages: {file_path}")
    print("=" * 80)
    
    try:
        # Initialize processor
        processor = OneNotePageProcessor()
        
        # Process the file
        result = await processor.process_onenote_file_by_pages(
            file_path=file_path,
            extract_entities=extract_entities,
            extract_references=extract_references,
            generate_embeddings=generate_embeddings,
            chunk_size=chunk_size,
            overlap=overlap
        )
        
        # Display results
        print("\n📊 PROCESSING RESULTS:")
        print("=" * 60)
        
        if result.get('success', False):
            print(f"✅ Processing successful!")
            print(f"📄 Pages extracted: {result.get('pages_extracted', 0)}")
            print(f"📄 Pages processed: {result.get('pages_processed', 0)}")
            print(f"✅ Pages successful: {result.get('pages_successful', 0)}")
            print(f"🧠 Total entities: {result.get('total_entities_extracted', 0)}")
            print(f"📚 Total references: {result.get('total_references_extracted', 0)}")
            print(f"🔗 Total embeddings: {result.get('total_embeddings_generated', 0)}")
            
            # Show individual page results
            print(f"\n📄 INDIVIDUAL PAGE RESULTS:")
            print("-" * 40)
            
            for i, page_result in enumerate(result.get('processing_results', []), 1):
                page_title = page_result.get('page_title', f'Page {i}')
                page_uuid = page_result.get('page_uuid', 'Unknown')
                success = page_result.get('success', False)
                entities = page_result.get('entities_extracted', 0)
                references = page_result.get('references_extracted', 0)
                embeddings = page_result.get('embeddings_generated', 0)
                content_type = page_result.get('content_type', 'unknown')
                
                status = "✅" if success else "❌"
                print(f"{status} Page {i}: {page_title}")
                print(f"   UUID: {page_uuid}")
                print(f"   Type: {content_type}")
                print(f"   Entities: {entities}, References: {references}, Embeddings: {embeddings}")
                
                if not success:
                    error = page_result.get('error', 'Unknown error')
                    print(f"   Error: {error}")
                print()
            
            print("🎉 OneNote page processing completed successfully!")
            return True
        
        else:
            print(f"❌ Processing failed: {result.get('error', 'Unknown error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error processing OneNote file: {e}", exc_info=True)
        print(f"❌ Processing failed: {e}")
        return False

async def process_all_onenote_files(uploads_dir: str = "uploads",
                                  file_pattern: str = "*.one",
                                  extract_entities: bool = True,
                                  extract_references: bool = True,
                                  generate_embeddings: bool = True,
                                  chunk_size: int = 1200,
                                  overlap: int = 0):
    """
    Process all OneNote files in the uploads directory.
    
    Args:
        uploads_dir: Directory containing OneNote files
        file_pattern: File pattern to match
        extract_entities: Whether to extract entities
        extract_references: Whether to extract references
        generate_embeddings: Whether to generate embeddings
        chunk_size: Text chunk size for processing
        overlap: Overlap between chunks
    """
    
    print(f"🧠 Processing all OneNote files in: {uploads_dir}")
    print("=" * 80)
    
    try:
        # Initialize processor
        processor = OneNotePageProcessor()
        
        # Find OneNote files
        uploads_path = Path(uploads_dir)
        onenote_files = list(uploads_path.glob(file_pattern))
        
        if not onenote_files:
            print(f"❌ No OneNote files found matching pattern: {file_pattern}")
            return False
        
        print(f"📄 Found {len(onenote_files)} OneNote files:")
        for i, file_path in enumerate(onenote_files, 1):
            print(f"   {i}. {file_path.name}")
        print()
        
        # Process all files
        batch_result = await processor.process_all_onenote_files(
            uploads_dir=uploads_dir,
            file_pattern=file_pattern
        )
        
        # Display batch results
        print("\n📊 BATCH PROCESSING RESULTS:")
        print("=" * 60)
        
        if batch_result.get('success', False):
            print(f"✅ Batch processing successful!")
            print(f"📄 Files found: {batch_result.get('files_found', 0)}")
            print(f"📄 Files processed: {batch_result.get('files_processed', 0)}")
            print(f"✅ Files successful: {batch_result.get('files_successful', 0)}")
            print(f"📄 Total pages: {batch_result.get('total_pages_processed', 0)}")
            print(f"🧠 Total entities: {batch_result.get('total_entities_extracted', 0)}")
            print(f"📚 Total references: {batch_result.get('total_references_extracted', 0)}")
            
            # Show individual file results
            print(f"\n📄 INDIVIDUAL FILE RESULTS:")
            print("-" * 40)
            
            for i, file_result in enumerate(batch_result.get('processing_results', []), 1):
                file_path = Path(file_result.get('file_path', 'Unknown')).name
                success = file_result.get('success', False)
                pages_successful = file_result.get('pages_successful', 0)
                pages_extracted = file_result.get('pages_extracted', 0)
                entities = file_result.get('total_entities_extracted', 0)
                references = file_result.get('total_references_extracted', 0)
                
                status = "✅" if success else "❌"
                print(f"{status} File {i}: {file_path}")
                print(f"   Pages: {pages_successful}/{pages_extracted} successful")
                print(f"   Entities: {entities}, References: {references}")
                
                if not success:
                    error = file_result.get('error', 'Unknown error')
                    print(f"   Error: {error}")
                print()
            
            print("🎉 Batch OneNote processing completed successfully!")
            return True
        
        else:
            print(f"❌ Batch processing failed: {batch_result.get('error', 'Unknown error')}")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error in batch OneNote processing: {e}", exc_info=True)
        print(f"❌ Batch processing failed: {e}")
        return False

async def main():
    """Main function with command line argument parsing."""
    
    parser = argparse.ArgumentParser(description="Process OneNote files by pages")
    parser.add_argument("--file", type=str, help="Specific OneNote file to process")
    parser.add_argument("--uploads-dir", type=str, default="uploads", help="Directory containing OneNote files")
    parser.add_argument("--pattern", type=str, default="*.one", help="File pattern to match")
    parser.add_argument("--no-entities", action="store_true", help="Skip entity extraction")
    parser.add_argument("--no-references", action="store_true", help="Skip reference extraction")
    parser.add_argument("--no-embeddings", action="store_true", help="Skip embedding generation")
    parser.add_argument("--chunk-size", type=int, default=1200, help="Text chunk size")
    parser.add_argument("--overlap", type=int, default=0, help="Chunk overlap")
    
    args = parser.parse_args()
    
    # Configuration
    extract_entities = not args.no_entities
    extract_references = not args.no_references
    generate_embeddings = not args.no_embeddings
    
    print("🧠 ONENOTE PAGE-BY-PAGE PROCESSOR")
    print("=" * 80)
    print(f"📄 Extract entities: {extract_entities}")
    print(f"📚 Extract references: {extract_references}")
    print(f"🔗 Generate embeddings: {generate_embeddings}")
    print(f"📝 Chunk size: {args.chunk_size}")
    print(f"🔄 Overlap: {args.overlap}")
    print()
    
    try:
        if args.file:
            # Process specific file
            success = await process_onenote_file(
                file_path=args.file,
                extract_entities=extract_entities,
                extract_references=extract_references,
                generate_embeddings=generate_embeddings,
                chunk_size=args.chunk_size,
                overlap=args.overlap
            )
        else:
            # Process all files
            success = await process_all_onenote_files(
                uploads_dir=args.uploads_dir,
                file_pattern=args.pattern,
                extract_entities=extract_entities,
                extract_references=extract_references,
                generate_embeddings=generate_embeddings,
                chunk_size=args.chunk_size,
                overlap=args.overlap
            )
        
        if success:
            print("\n✅ OneNote processing completed successfully!")
            sys.exit(0)
        else:
            print("\n❌ OneNote processing failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Processing interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}", exc_info=True)
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
