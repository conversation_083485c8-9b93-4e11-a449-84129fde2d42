"""
Script to batch process documents using the document processing service.

This script processes all documents in a directory and its subdirectories.
"""

import os
import sys
import asyncio
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Any
import time

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.document_processing_service import DocumentProcessingService
from utils.config import get_config
from utils.logging_utils import get_logger
from utils.file_utils import is_supported_file

# Set up logger
logger = get_logger(__name__)

async def process_documents_in_directory(
    service: DocumentProcessingService,
    directory_path: Path,
    recursive: bool = False,
    chunk_size: int = 1200,
    overlap: int = 0,
    extract_entities: bool = True,
    extract_references: bool = True,
    extract_metadata: bool = True
) -> Dict[str, Any]:
    """
    Process all documents in a directory.
    
    Args:
        service: Document processing service
        directory_path: Path to the directory
        recursive: Whether to process subdirectories
        chunk_size: Size of text chunks in characters
        overlap: Overlap between chunks in characters
        extract_entities: Whether to extract entities
        extract_references: Whether to extract references
        extract_metadata: Whether to extract metadata
        
    Returns:
        Processing statistics
    """
    logger.info(f"Processing documents in directory: {directory_path}")
    
    # Get all supported files in the directory
    file_paths = []
    
    if recursive:
        # Walk through all subdirectories
        for root, _, files in os.walk(directory_path):
            for file in files:
                file_path = Path(root) / file
                if is_supported_file(file_path.name):
                    file_paths.append(file_path)
    else:
        # Only process files in the current directory
        for file_path in directory_path.iterdir():
            if file_path.is_file() and is_supported_file(file_path.name):
                file_paths.append(file_path)
    
    logger.info(f"Found {len(file_paths)} supported files in {directory_path}")
    
    # Add all documents to the processing queue
    for file_path in file_paths:
        await service.add_document_to_queue(file_path)
    
    # Wait for processing to complete
    start_time = time.time()
    processed_count = 0
    failed_count = 0
    
    while True:
        # Get the queue status
        status = await service.get_queue_status()
        
        # Update counts
        processed_count = status["processed_count"]
        failed_count = status["failed_count"]
        
        # Print status
        queue_size = status["queue_size"]
        is_processing = status["is_processing"]
        current_document = status["current_document"]
        
        logger.info(f"Queue status: {queue_size} remaining, {processed_count} processed, {failed_count} failed")
        if current_document:
            logger.info(f"Currently processing: {current_document}")
        
        # Check if processing is complete
        if queue_size == 0 and not is_processing:
            break
        
        # Wait before checking again
        await asyncio.sleep(5)
    
    # Calculate processing time
    processing_time = time.time() - start_time
    
    # Return statistics
    return {
        "total_files": len(file_paths),
        "processed_count": processed_count,
        "failed_count": failed_count,
        "processing_time": processing_time
    }

async def main():
    """Main function."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Batch process documents")
    parser.add_argument("directory", help="Directory containing documents to process")
    parser.add_argument("--recursive", action="store_true", help="Process subdirectories")
    parser.add_argument("--chunk-size", type=int, default=1200, help="Size of text chunks in characters")
    parser.add_argument("--overlap", type=int, default=0, help="Overlap between chunks in characters")
    parser.add_argument("--no-entities", dest="extract_entities", action="store_false", help="Skip entity extraction")
    parser.add_argument("--no-references", dest="extract_references", action="store_false", help="Skip reference extraction")
    parser.add_argument("--no-metadata", dest="extract_metadata", action="store_false", help="Skip metadata extraction")
    parser.set_defaults(extract_entities=True, extract_references=True, extract_metadata=True)
    
    args = parser.parse_args()
    
    # Create the document processing service
    service = DocumentProcessingService()
    
    # Process the directory
    directory_path = Path(args.directory)
    
    if not directory_path.exists() or not directory_path.is_dir():
        logger.error(f"Directory not found: {directory_path}")
        return
    
    # Process documents
    stats = await process_documents_in_directory(
        service,
        directory_path,
        recursive=args.recursive,
        chunk_size=args.chunk_size,
        overlap=args.overlap,
        extract_entities=args.extract_entities,
        extract_references=args.extract_references,
        extract_metadata=args.extract_metadata
    )
    
    # Print statistics
    print("\nProcessing complete!")
    print(f"Total files: {stats['total_files']}")
    print(f"Processed: {stats['processed_count']}")
    print(f"Failed: {stats['failed_count']}")
    print(f"Processing time: {stats['processing_time']:.2f} seconds")
    
    if stats['processing_time'] > 0 and stats['processed_count'] > 0:
        avg_time = stats['processing_time'] / stats['processed_count']
        print(f"Average processing time per document: {avg_time:.2f} seconds")

if __name__ == "__main__":
    asyncio.run(main())
