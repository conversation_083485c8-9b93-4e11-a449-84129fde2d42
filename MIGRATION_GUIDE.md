# Migration Guide: Transitioning to the Modular Structure

This guide will help you transition from the monolithic `web_interface_improved.py` structure to the new modular structure.

## Overview of Changes

The Graphiti application has been restructured from a monolithic file (`web_interface_improved.py`) to a modular structure with separate files for different components:

- Routes are now in the `routes/` directory
- Business logic is now in the `services/` directory
- Database access is now in the `database/` directory
- Data models are now in the `models/` directory
- Utility functions are now in the `utils/` directory

The main application is now in `app.py`.

## Step-by-Step Migration

### 1. Install Dependencies

Make sure you have all the required dependencies installed:

```bash
pip install -r requirements.txt
```

### 2. Run the Test Script

Run the test script to verify that the new structure works correctly:

```bash
python test_app.py
```

This will start the application, test various endpoints, and report any issues.

### 3. Update Your Environment

If you were previously running the application with:

```bash
python web_interface_improved.py
```

You should now run it with:

```bash
python app.py
```

Update any scripts, documentation, or deployment configurations accordingly.

### 4. Update Import Statements

If you have any custom code that imports from `web_interface_improved.py`, you'll need to update the import statements to use the new structure.

For example, if you had:

```python
from web_interface_improved import process_document
```

You should now use:

```python
from services.document_service import process_document
```

### 5. Access API Documentation

The new structure includes improved API documentation. You can access it at:

- Swagger UI: http://localhost:8023/docs
- ReDoc: http://localhost:8023/redoc
- OpenAPI Schema: http://localhost:8023/openapi.json

### 6. Gradual Transition

You can keep both the old and new structures side by side during the transition period. This allows you to gradually migrate your code and ensure everything works correctly before removing the old structure.

## File Mapping

Here's a mapping of functionality from the old structure to the new structure:

| Old Location (`web_interface_improved.py`) | New Location |
|-------------------------------------------|--------------|
| FastAPI app setup | `app.py` |
| Document upload endpoints | `routes/document_routes.py` |
| Reference endpoints | `routes/reference_routes.py` |
| Entity endpoints | `routes/entity_routes.py` |
| Knowledge graph endpoints | `routes/knowledge_graph_routes.py` |
| Search endpoints | `routes/search_routes.py` |
| Q&A endpoints | `routes/qa_routes.py` |
| Settings endpoints | `routes/settings_routes.py` |
| Document processing logic | `services/document_service.py` |
| Reference extraction logic | `services/reference_service.py` |
| Entity extraction logic | `services/entity_service.py` |
| Knowledge graph operations | `services/knowledge_graph_service.py` |
| Search functionality | `services/search_service.py` |
| Q&A functionality | `services/qa_service.py` |
| Database operations | `database/falkordb_adapter.py` and `database/database_service.py` |
| Configuration | `utils/config.py` |
| Logging | `utils/logging_utils.py` |
| File handling | `utils/file_utils.py` |
| Text processing | `utils/text_utils.py` |
| Data models | `models/` directory |

## Common Issues and Solutions

### Issue: Import Errors

If you see import errors when running the application, make sure you have installed all the required dependencies:

```bash
pip install -r requirements.txt
```

### Issue: Module Not Found

If you see "Module not found" errors, make sure you're running the application from the root directory of the project.

### Issue: Database Connection Errors

If you see database connection errors, make sure your FalkorDB instance is running and the connection details in your `.env` file are correct.

### Issue: Missing Static Files

If you see errors related to missing static files for the API documentation, make sure you have the required files in the `static/` directory.

## Getting Help

If you encounter any issues during the migration process, please:

1. Check the error messages for clues about what's wrong
2. Review this migration guide for solutions to common issues
3. Run the test script to identify specific issues
4. Check the application logs for more detailed error information

## Conclusion

The new modular structure makes the codebase more maintainable, testable, and extensible. While the migration process may require some effort, the benefits in terms of code quality and developer experience are substantial.

Once you've successfully migrated to the new structure, you can safely remove the old `web_interface_improved.py` file.
