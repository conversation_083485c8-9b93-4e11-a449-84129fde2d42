#!/usr/bin/env python3
"""
Debug Mistral OCR issue to understand why it's returning empty results.

This script will help us understand what's happening with Mistral OCR
and implement alternative OCR solutions.
"""

import sys
import os
import logging
import asyncio
import tempfile
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)


async def debug_mistral_ocr_response():
    """Debug Mistral OCR to see what's actually being returned."""
    
    print("🔍 DEBUGGING MISTRAL OCR RESPONSE")
    print("=" * 60)
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        
        # Initialize Mistral OCR
        mistral_ocr = MistralOCRProcessor()
        print("✅ Initialized Mistral OCR processor")
        
        # Test with one of the extracted OneNote images
        image_dir = Path("extracted_onenote_images")
        if not image_dir.exists():
            print("❌ No extracted images found. Run extract_and_save_onenote_images.py first")
            return False
        
        # Find the most promising image (Image 4 had 85% light pixels)
        test_image = image_dir / "onenote_image_4_jpeg.jpg"
        if not test_image.exists():
            # Fallback to any available image
            image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
            if image_files:
                test_image = image_files[0]
            else:
                print("❌ No image files found in extracted directory")
                return False
        
        print(f"🖼️ Testing with image: {test_image.name}")
        print(f"📁 File size: {test_image.stat().st_size:,} bytes")
        
        # Test direct image processing (should fail)
        print("\n1️⃣ Testing direct image processing:")
        try:
            result = await mistral_ocr.extract_text_from_document(str(test_image))
            print(f"   Result: '{result}' (length: {len(result) if result else 0})")
        except Exception as e:
            print(f"   Error: {e}")
        
        # Test image → PDF → OCR processing
        print("\n2️⃣ Testing image → PDF → OCR processing:")
        try:
            from reportlab.lib.pagesizes import letter
            from reportlab.platypus import SimpleDocTemplate
            from reportlab.platypus.flowables import Image as ReportLabImage
            
            # Convert image to PDF
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_pdf:
                pdf_path = temp_pdf.name
            
            doc = SimpleDocTemplate(pdf_path, pagesize=letter)
            
            # Calculate image size to fit page
            from PIL import Image
            with Image.open(test_image) as img:
                img_width, img_height = img.size
                
                # Scale to fit page
                page_width, page_height = letter
                margin = 50
                max_width = page_width - 2 * margin
                max_height = page_height - 2 * margin
                
                scale_x = max_width / img_width
                scale_y = max_height / img_height
                scale = min(scale_x, scale_y, 1.0)
                
                final_width = img_width * scale
                final_height = img_height * scale
            
            # Create PDF with image
            rl_image = ReportLabImage(str(test_image), width=final_width, height=final_height)
            doc.build([rl_image])
            
            print(f"   ✅ Created PDF: {Path(pdf_path).stat().st_size:,} bytes")
            
            # Process PDF with Mistral OCR
            result = await mistral_ocr.extract_text_from_document(pdf_path)
            print(f"   OCR Result: '{result}' (length: {len(result) if result else 0})")
            
            # Clean up
            Path(pdf_path).unlink()
            
        except Exception as e:
            print(f"   Error: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging Mistral OCR: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_alternative_ocr_solutions():
    """Test alternative OCR solutions."""
    
    print("\n🔧 TESTING ALTERNATIVE OCR SOLUTIONS")
    print("=" * 60)
    
    # Find test image
    image_dir = Path("extracted_onenote_images")
    test_image = image_dir / "onenote_image_4_jpeg.jpg"
    if not test_image.exists():
        image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        if image_files:
            test_image = image_files[0]
        else:
            print("❌ No test images available")
            return False
    
    print(f"🖼️ Testing with: {test_image.name}")
    
    # Test 1: Tesseract OCR (if available)
    print("\n1️⃣ Testing Tesseract OCR:")
    try:
        import pytesseract
        from PIL import Image
        
        with Image.open(test_image) as img:
            # Convert to RGB if needed
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Extract text with Tesseract
            extracted_text = pytesseract.image_to_string(img)
            
            if extracted_text and len(extracted_text.strip()) > 10:
                print(f"   ✅ Tesseract SUCCESS: {len(extracted_text)} characters")
                print(f"   📝 Preview: {extracted_text[:300]}...")
                
                # Save result
                with open("tesseract_ocr_result.txt", 'w', encoding='utf-8') as f:
                    f.write(f"=== TESSERACT OCR RESULT ===\n\n")
                    f.write(f"Image: {test_image.name}\n")
                    f.write(f"Text length: {len(extracted_text)} characters\n\n")
                    f.write("=== EXTRACTED TEXT ===\n\n")
                    f.write(extracted_text)
                
                print(f"   💾 Saved result to: tesseract_ocr_result.txt")
                return True
            else:
                print(f"   ❌ Tesseract returned no text")
                
    except ImportError:
        print("   ⚠️ Tesseract not available (pip install pytesseract)")
    except Exception as e:
        print(f"   ❌ Tesseract error: {e}")
    
    # Test 2: EasyOCR (if available)
    print("\n2️⃣ Testing EasyOCR:")
    try:
        import easyocr
        
        # Initialize EasyOCR reader
        reader = easyocr.Reader(['en'])
        
        # Extract text
        results = reader.readtext(str(test_image))
        
        if results:
            extracted_text = '\n'.join([result[1] for result in results])
            
            if len(extracted_text.strip()) > 10:
                print(f"   ✅ EasyOCR SUCCESS: {len(extracted_text)} characters")
                print(f"   📝 Preview: {extracted_text[:300]}...")
                
                # Save result
                with open("easyocr_result.txt", 'w', encoding='utf-8') as f:
                    f.write(f"=== EASYOCR RESULT ===\n\n")
                    f.write(f"Image: {test_image.name}\n")
                    f.write(f"Text length: {len(extracted_text)} characters\n\n")
                    f.write("=== EXTRACTED TEXT ===\n\n")
                    f.write(extracted_text)
                
                print(f"   💾 Saved result to: easyocr_result.txt")
                return True
            else:
                print(f"   ❌ EasyOCR returned no meaningful text")
        else:
            print(f"   ❌ EasyOCR returned no results")
            
    except ImportError:
        print("   ⚠️ EasyOCR not available (pip install easyocr)")
    except Exception as e:
        print(f"   ❌ EasyOCR error: {e}")
    
    # Test 3: Basic image analysis
    print("\n3️⃣ Basic image analysis:")
    try:
        from PIL import Image
        
        with Image.open(test_image) as img:
            width, height = img.size
            mode = img.mode
            
            print(f"   📐 Image: {width}x{height}, Mode: {mode}")
            
            # Check if image is suitable for OCR
            if width < 100 or height < 100:
                print(f"   ⚠️ Image may be too small for OCR")
            elif width > 4000 or height > 4000:
                print(f"   ⚠️ Image may be too large, consider resizing")
            else:
                print(f"   ✅ Image size suitable for OCR")
            
            # Check contrast
            if mode in ('RGB', 'RGBA'):
                # Sample pixels to check contrast
                sample = img.resize((100, 100))
                pixels = list(sample.getdata())
                
                brightness_values = []
                for pixel in pixels:
                    if mode == 'RGB':
                        r, g, b = pixel
                    else:  # RGBA
                        r, g, b, a = pixel
                    brightness = (r + g + b) / 3
                    brightness_values.append(brightness)
                
                avg_brightness = sum(brightness_values) / len(brightness_values)
                brightness_range = max(brightness_values) - min(brightness_values)
                
                print(f"   💡 Average brightness: {avg_brightness:.1f}")
                print(f"   📊 Brightness range: {brightness_range:.1f}")
                
                if brightness_range > 100:
                    print(f"   ✅ Good contrast for OCR")
                else:
                    print(f"   ⚠️ Low contrast, OCR may be difficult")
            
    except Exception as e:
        print(f"   ❌ Image analysis error: {e}")
    
    return False


async def process_all_onenote_images_with_alternative_ocr():
    """Process all OneNote images with the best available OCR."""
    
    print("\n🖼️ PROCESSING ALL ONENOTE IMAGES WITH ALTERNATIVE OCR")
    print("=" * 60)
    
    image_dir = Path("extracted_onenote_images")
    if not image_dir.exists():
        print("❌ No extracted images directory found")
        return False
    
    image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
    if not image_files:
        print("❌ No image files found")
        return False
    
    print(f"📎 Found {len(image_files)} images to process")
    
    # Try to determine best OCR method
    ocr_method = None
    
    try:
        import pytesseract
        ocr_method = "tesseract"
        print("✅ Using Tesseract OCR")
    except ImportError:
        try:
            import easyocr
            ocr_method = "easyocr"
            print("✅ Using EasyOCR")
        except ImportError:
            print("❌ No alternative OCR libraries available")
            print("Install with: pip install pytesseract or pip install easyocr")
            return False
    
    total_extracted_text = ""
    successful_extractions = 0
    
    for i, image_file in enumerate(sorted(image_files)):
        print(f"\n📷 Processing {image_file.name}:")
        
        try:
            if ocr_method == "tesseract":
                import pytesseract
                from PIL import Image
                
                with Image.open(image_file) as img:
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    extracted_text = pytesseract.image_to_string(img)
                    
            elif ocr_method == "easyocr":
                import easyocr
                reader = easyocr.Reader(['en'])
                results = reader.readtext(str(image_file))
                extracted_text = '\n'.join([result[1] for result in results])
            
            if extracted_text and len(extracted_text.strip()) > 10:
                print(f"   ✅ SUCCESS: {len(extracted_text)} characters")
                print(f"   📝 Preview: {extracted_text[:150]}...")
                
                total_extracted_text += f"\n\n=== {image_file.name.upper()} ===\n"
                total_extracted_text += extracted_text
                successful_extractions += 1
            else:
                print(f"   ❌ No text extracted")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    # Summary and save results
    print(f"\n📊 PROCESSING SUMMARY:")
    print("=" * 60)
    print(f"Images processed: {len(image_files)}")
    print(f"Successful extractions: {successful_extractions}")
    print(f"Total text extracted: {len(total_extracted_text):,} characters")
    
    if total_extracted_text:
        # Save comprehensive results
        output_file = Path("onenote_alternative_ocr_results.txt")
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== ONENOTE ALTERNATIVE OCR RESULTS ===\n\n")
            f.write(f"OCR Method: {ocr_method.upper()}\n")
            f.write(f"Images processed: {len(image_files)}\n")
            f.write(f"Successful extractions: {successful_extractions}\n")
            f.write(f"Total text: {len(total_extracted_text):,} characters\n\n")
            f.write("=== EXTRACTED TEXT FROM ALL IMAGES ===\n")
            f.write(total_extracted_text)
        
        print(f"💾 Saved comprehensive results to: {output_file}")
        
        # Show preview
        print(f"\n📝 EXTRACTED CONTENT PREVIEW:")
        print("=" * 60)
        print(total_extracted_text[:1000])
        if len(total_extracted_text) > 1000:
            print(f"\n... [Content continues for {len(total_extracted_text)-1000:,} more characters]")
        print("=" * 60)
        
        # Compare with previous attempts
        print(f"\n🎉 FINAL COMPARISON:")
        print(f"Original OneNote extraction: ~1,880 characters (metadata only)")
        print(f"Alternative OCR extraction: {len(total_extracted_text):,} characters (ACTUAL CONTENT)")
        
        if len(total_extracted_text) > 1880:
            improvement = len(total_extracted_text) / 1880
            print(f"Improvement factor: {improvement:.1f}x MORE REAL CONTENT!")
            print("🎉 THIS IS THE ACTUAL ONENOTE CONTENT!")
        
        return True
    else:
        print("❌ No text was extracted from any images")
        return False


def main():
    """Run comprehensive OCR debugging and alternative solutions."""
    print("🔍 COMPREHENSIVE OCR DEBUGGING AND SOLUTIONS")
    print("=" * 60)
    
    async def run_all_tests():
        # Debug Mistral OCR
        await debug_mistral_ocr_response()
        
        # Test alternatives
        alternative_success = await test_alternative_ocr_solutions()
        
        # Process all images if alternatives work
        if alternative_success:
            comprehensive_success = await process_all_onenote_images_with_alternative_ocr()
            return comprehensive_success
        else:
            print("\n⚠️ No working OCR found, trying comprehensive processing anyway...")
            comprehensive_success = await process_all_onenote_images_with_alternative_ocr()
            return comprehensive_success
    
    success = asyncio.run(run_all_tests())
    
    print("\n" + "=" * 60)
    print("🎯 COMPREHENSIVE OCR TESTING SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 SUCCESS! Alternative OCR extracted actual OneNote content!")
        print("✅ Real content extracted from OneNote images")
        print("✅ This is the actual research content you were looking for")
        print("✅ Much better than metadata-only extraction")
        print("\nNext steps:")
        print("1. Integrate working OCR into OneNote processor")
        print("2. Use extracted content for entity extraction")
        print("3. Process references from actual content")
        print("4. Update knowledge graph with real data")
    else:
        print("❌ OCR extraction still needs work")
        print("Consider:")
        print("1. Installing OCR libraries: pip install pytesseract easyocr")
        print("2. Checking image quality and content")
        print("3. Manual inspection of extracted images")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
