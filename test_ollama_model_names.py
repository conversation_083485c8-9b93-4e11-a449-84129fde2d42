#!/usr/bin/env python3
"""
Test different Ollama model name variations to find the correct one.
"""

import asyncio
import httpx
import base64
from pathlib import Path


async def test_model_variations():
    """Test different model name variations."""
    
    print("🔍 TESTING OLLAMA MODEL NAME VARIATIONS")
    print("=" * 60)
    
    # Find a test image
    image_dir = Path("extracted_onenote_images")
    test_image = image_dir / "onenote_image_4_jpeg.jpg"
    
    if not test_image.exists():
        image_files = list(image_dir.glob("*.jpg")) + list(image_dir.glob("*.png"))
        if image_files:
            test_image = image_files[0]
        else:
            print("❌ No test images available")
            return
    
    # Read and encode the image (small for testing)
    with open(test_image, 'rb') as f:
        image_data = f.read()
    
    image_base64 = base64.b64encode(image_data).decode('utf-8')
    
    # Test different model name variations
    model_variations = [
        "qwen2.5vl:3b",
        "qwen2.5-vl:3b",
        "qwen2.5vl",
        "qwen2.5-vl",
        "qwen2.5vl:latest",
        "qwen2.5-vl:latest",
        "qwen2-vl:3b",
        "qwen2-vl",
        "qwen:vl",
        "qwen:vision"
    ]
    
    for model in model_variations:
        print(f"\n🧪 Testing model: {model}")
        
        payload = {
            "model": model,
            "prompt": "What do you see in this image?",
            "images": [image_base64],
            "stream": False
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "http://localhost:11434/api/generate",
                    json=payload
                )
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    response_text = result.get('response', '').strip()
                    
                    if response_text:
                        print(f"   ✅ SUCCESS! Model works: {model}")
                        print(f"   Response: {response_text[:100]}...")
                        return model
                    else:
                        print(f"   ⚠️ Model responded but no text")
                elif response.status_code == 404:
                    print(f"   ❌ Model not found")
                else:
                    print(f"   ❌ Error: {response.text}")
                    
        except Exception as e:
            print(f"   ❌ Request error: {e}")
    
    print("\n❌ No working model variation found")
    return None


async def check_available_models():
    """Check what models are actually available via API."""
    
    print("\n🔍 CHECKING AVAILABLE MODELS VIA API")
    print("=" * 60)
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get("http://localhost:11434/api/tags")
            
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                print(f"Found {len(models)} models:")
                for model in models:
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0)
                    modified = model.get('modified_at', 'Unknown')
                    print(f"  - {name} (Size: {size:,} bytes)")
                
                # Look for vision models
                vision_models = []
                for model in models:
                    name = model.get('name', '').lower()
                    if any(keyword in name for keyword in ['vl', 'vision', 'llava', 'qwen']):
                        vision_models.append(model.get('name', ''))
                
                if vision_models:
                    print(f"\nPotential vision models:")
                    for vm in vision_models:
                        print(f"  - {vm}")
                else:
                    print(f"\nNo obvious vision models found")
                    
            else:
                print(f"❌ API request failed: {response.status_code}")
                
    except Exception as e:
        print(f"❌ Error checking models: {e}")


async def main():
    """Run model name testing."""
    
    await check_available_models()
    working_model = await test_model_variations()
    
    if working_model:
        print(f"\n🎉 FOUND WORKING MODEL: {working_model}")
        print("Use this model name in the vision processing script")
    else:
        print(f"\n❌ No working vision model found")
        print("The qwen2.5vl:3b model may not be properly loaded or accessible")


if __name__ == "__main__":
    asyncio.run(main())
