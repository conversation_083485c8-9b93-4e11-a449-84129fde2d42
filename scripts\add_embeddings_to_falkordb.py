"""
<PERSON><PERSON><PERSON> to add vector embeddings to Fact nodes in FalkorDB
"""

import os
import sys
import asyncio
import logging
import json
from datetime import datetime, timezone
from typing import List, Dict, Any, Optional

from dotenv import load_dotenv
import openai
from openai import OpenAI

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.falkordb_adapter import GraphitiFalkorDBAdapter

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def create_vector_index(adapter: GraphitiFalkorDBAdapter) -> bool:
    """
    Create a vector index in FalkorDB for Fact nodes.
    
    Args:
        adapter: FalkorDB adapter
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info("Creating vector index for Fact nodes")
    
    try:
        # Check if the index already exists
        check_index_query = """
        CALL db.indexes() YIELD name, label, properties, type
        WHERE name = 'fact_embedding_index' AND type = 'VECTOR'
        RETURN count(*) > 0 AS exists
        """
        
        result = adapter.execute_cypher(check_index_query)
        
        if result and len(result) > 1 and result[1] and result[1][0][0]:
            logger.info("Vector index 'fact_embedding_index' already exists")
            return True
        
        # Create the vector index
        create_index_query = """
        CREATE VECTOR INDEX fact_embedding_index
        FOR (f:Fact)
        ON (f.embedding)
        OPTIONS {
          dimension: 1536,
          similarity: 'cosine'
        }
        """
        
        adapter.execute_cypher(create_index_query)
        logger.info("Created vector index 'fact_embedding_index'")
        return True
    except Exception as e:
        logger.error(f"Error creating vector index: {e}")
        return False

async def get_facts_without_embeddings(adapter: GraphitiFalkorDBAdapter, batch_size: int = 20) -> List[Dict[str, Any]]:
    """
    Get Fact nodes that don't have embeddings.
    
    Args:
        adapter: FalkorDB adapter
        batch_size: Number of facts to retrieve
        
    Returns:
        List of facts without embeddings
    """
    logger.info(f"Getting Fact nodes without embeddings (batch size: {batch_size})")
    
    try:
        query = f"""
        MATCH (f:Fact)
        WHERE f.embedding IS NULL
        RETURN f.uuid AS uuid, f.body AS body
        LIMIT {batch_size}
        """
        
        result = adapter.execute_cypher(query)
        
        facts = []
        if result and len(result) > 1:
            for row in result[1]:
                facts.append({
                    "uuid": row[0],
                    "body": row[1]
                })
        
        logger.info(f"Found {len(facts)} Fact nodes without embeddings")
        return facts
    except Exception as e:
        logger.error(f"Error getting Facts without embeddings: {e}")
        return []

async def generate_embeddings(api_key: str, texts: List[str], model: str = "text-embedding-3-small") -> List[List[float]]:
    """
    Generate embeddings for a list of texts using OpenAI.
    
    Args:
        api_key: OpenAI API key
        texts: List of texts to embed
        model: Embedding model to use
        
    Returns:
        List of embedding vectors
    """
    logger.info(f"Generating embeddings for {len(texts)} texts using model {model}")
    
    try:
        client = OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=texts,
            model=model
        )
        
        embeddings = [data.embedding for data in response.data]
        logger.info(f"Generated {len(embeddings)} embeddings")
        return embeddings
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        raise

async def update_facts_with_embeddings(adapter: GraphitiFalkorDBAdapter, facts: List[Dict[str, Any]], embeddings: List[List[float]]) -> bool:
    """
    Update Fact nodes with their embeddings.
    
    Args:
        adapter: FalkorDB adapter
        facts: List of facts to update
        embeddings: List of embedding vectors
        
    Returns:
        bool: True if successful, False otherwise
    """
    logger.info(f"Updating {len(facts)} Fact nodes with embeddings")
    
    try:
        for i, fact in enumerate(facts):
            # Convert embedding to JSON string to store in FalkorDB
            embedding_json = json.dumps(embeddings[i])
            timestamp = datetime.now(timezone.utc).isoformat()
            
            query = f"""
            MATCH (f:Fact {{uuid: '{fact["uuid"]}'}})
            SET f.embedding = '{embedding_json}',
                f.embedding_model = 'text-embedding-3-small',
                f.embedding_updated_at = '{timestamp}'
            RETURN f.uuid
            """
            
            result = adapter.execute_cypher(query)
            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"Failed to update fact {fact['uuid']} with embedding")
        
        logger.info(f"Updated {len(facts)} Fact nodes with embeddings")
        return True
    except Exception as e:
        logger.error(f"Error updating Facts with embeddings: {e}")
        return False

async def add_embeddings_to_facts(adapter: GraphitiFalkorDBAdapter, api_key: str, batch_size: int = 20, max_batches: Optional[int] = None) -> int:
    """
    Add embeddings to Fact nodes in batches.
    
    Args:
        adapter: FalkorDB adapter
        api_key: OpenAI API key
        batch_size: Number of facts to process in each batch
        max_batches: Maximum number of batches to process
        
    Returns:
        Number of facts processed
    """
    logger.info("Adding embeddings to Fact nodes")
    
    # Create vector index if it doesn't exist
    if not await create_vector_index(adapter):
        logger.error("Failed to create vector index")
        return 0
    
    batch_count = 0
    total_processed = 0
    
    while True:
        # Check if we've reached the maximum number of batches
        if max_batches is not None and batch_count >= max_batches:
            logger.info(f"Reached maximum number of batches ({max_batches})")
            break
        
        # Get a batch of Facts without embeddings
        facts = await get_facts_without_embeddings(adapter, batch_size)
        
        # If there are no more Facts without embeddings, we're done
        if not facts:
            logger.info("No more Facts without embeddings")
            break
        
        # Generate embeddings for the batch
        texts = [fact["body"] for fact in facts]
        embeddings = await generate_embeddings(api_key, texts)
        
        # Update the Facts with their embeddings
        if not await update_facts_with_embeddings(adapter, facts, embeddings):
            logger.error(f"Failed to update facts with embeddings in batch {batch_count}")
            break
        
        batch_count += 1
        total_processed += len(facts)
        
        logger.info(f"Processed batch {batch_count} ({total_processed} Facts total)")
        
        # Sleep briefly to avoid rate limits
        await asyncio.sleep(1)
    
    logger.info(f"Finished adding embeddings to {total_processed} Fact nodes")
    return total_processed

async def count_facts_with_embeddings(adapter: GraphitiFalkorDBAdapter) -> Dict[str, Any]:
    """
    Count how many Fact nodes have embeddings.
    
    Args:
        adapter: FalkorDB adapter
        
    Returns:
        Dict with count information
    """
    logger.info("Counting Facts with embeddings")
    
    try:
        query = """
        MATCH (f:Fact)
        RETURN count(f) AS total,
               count(f.embedding) AS with_embeddings
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            total = result[1][0][0]
            with_embeddings = result[1][0][1]
            percentage = (with_embeddings / total * 100) if total > 0 else 0
            
            logger.info(f"{with_embeddings} out of {total} Facts have embeddings ({percentage:.2f}%)")
            return {"total": total, "with_embeddings": with_embeddings, "percentage": percentage}
        else:
            logger.error("Failed to count Facts with embeddings")
            return {"total": 0, "with_embeddings": 0, "percentage": 0}
    except Exception as e:
        logger.error(f"Error counting Facts with embeddings: {e}")
        return {"total": 0, "with_embeddings": 0, "percentage": 0}

async def test_semantic_search(adapter: GraphitiFalkorDBAdapter, api_key: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """
    Test semantic search using vector embeddings.
    
    Args:
        adapter: FalkorDB adapter
        api_key: OpenAI API key
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        List of search results
    """
    logger.info(f"Testing semantic search for query: '{query}'")
    
    try:
        # Generate embedding for the query
        client = OpenAI(api_key=api_key)
        response = client.embeddings.create(
            input=query,
            model="text-embedding-3-small"
        )
        query_embedding = response.data[0].embedding
        query_embedding_json = json.dumps(query_embedding)
        
        # Search using vector similarity
        search_query = f"""
        MATCH (f:Fact)
        WHERE f.embedding IS NOT NULL
        WITH f, vector.similarity.cosine(json.decode(f.embedding), json.decode('{query_embedding_json}')) AS score
        ORDER BY score DESC
        LIMIT {limit}
        MATCH (e:Episode)-[:CONTAINS]->(f)
        RETURN f.uuid AS uuid, 
               substring(f.body, 0, 200) + '...' AS preview,
               score,
               e.name AS document
        """
        
        result = adapter.execute_cypher(search_query)
        
        results = []
        if result and len(result) > 1:
            for row in result[1]:
                results.append({
                    "uuid": row[0],
                    "preview": row[1],
                    "score": row[2],
                    "document": row[3]
                })
        
        logger.info(f"Found {len(results)} results for query: '{query}'")
        
        # Display the results
        for i, result in enumerate(results):
            logger.info(f"\nResult {i+1} (Score: {result['score']:.4f}):")
            logger.info(f"Document: {result['document']}")
            logger.info(f"Preview: {result['preview']}")
        
        return results
    except Exception as e:
        logger.error(f"Error performing semantic search: {e}")
        return []

async def main():
    """Main function to add embeddings to the knowledge graph."""
    # Load environment variables
    load_dotenv()
    
    # Get OpenAI API key
    openai_api_key = os.environ.get('OPENAI_API_KEY')
    if not openai_api_key:
        logger.error("OpenAI API key not found in environment variables")
        return
    
    # Check command line arguments
    if len(sys.argv) < 2:
        logger.error("Please specify a command: add-embeddings, count, or search")
        logger.info("Usage:")
        logger.info("  python add_embeddings_to_falkordb.py add-embeddings [batch_size] [max_batches]")
        logger.info("  python add_embeddings_to_falkordb.py count")
        logger.info("  python add_embeddings_to_falkordb.py search <query>")
        return
    
    command = sys.argv[1].lower()
    
    try:
        # Connect to FalkorDB
        adapter = GraphitiFalkorDBAdapter()
        
        if command == "add-embeddings":
            # Get optional parameters
            batch_size = 20
            max_batches = None
            
            if len(sys.argv) >= 3:
                try:
                    batch_size = int(sys.argv[2])
                except ValueError:
                    logger.warning(f"Invalid batch_size value: {sys.argv[2]}. Using default: 20")
            
            if len(sys.argv) >= 4:
                try:
                    max_batches = int(sys.argv[3])
                except ValueError:
                    logger.warning(f"Invalid max_batches value: {sys.argv[3]}. Using default: None (unlimited)")
            
            # Add embeddings to Facts
            total_processed = await add_embeddings_to_facts(adapter, openai_api_key, batch_size, max_batches)
            logger.info(f"Added embeddings to {total_processed} Fact nodes")
        
        elif command == "count":
            # Count Facts with embeddings
            await count_facts_with_embeddings(adapter)
        
        elif command == "search":
            # Test semantic search
            if len(sys.argv) < 3:
                logger.error("Please specify a search query")
                logger.info("Usage: python add_embeddings_to_falkordb.py search <query>")
                return
            
            query = " ".join(sys.argv[2:])
            await test_semantic_search(adapter, openai_api_key, query)
        
        else:
            logger.error(f"Unknown command: {command}")
            logger.info("Available commands: add-embeddings, count, search")
        
    except Exception as e:
        logger.error(f"Error in main function: {e}", exc_info=True)
    finally:
        # Close FalkorDB connection if needed
        if 'adapter' in locals():
            adapter.close()

if __name__ == "__main__":
    asyncio.run(main())
