#!/usr/bin/env python3
"""
Test script for confidence-based entity merging.

This script demonstrates the new confidence-based merging system.
"""

import sys
import os
import logging
from typing import List

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.confidence_based_merging import (
    ConfidenceBasedMerger, MergeStrategy, MergeDecision, get_confidence_merger
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_entities_for_merging() -> List[EntityForDeduplication]:
    """Create test entities with varying confidence levels for merging tests."""
    entities = [
        # High confidence entities that should merge
        EntityForDeduplication(
            uuid="high-conf-1",
            name="Dr. <PERSON>",
            type="Person",
            description="Cardiologist at Harvard Medical School",
            confidence=0.95,
            source_document_id="doc1",
            attributes={
                "title": "Dr.",
                "specialty": "cardiology",
                "institution": "Harvard Medical School"
            }
        ),
        EntityForDeduplication(
            uuid="high-conf-2",
            name="<PERSON>, <PERSON>",
            type="Person",
            description="Heart specialist and researcher at Harvard",
            confidence=0.92,
            source_document_id="doc2",
            attributes={
                "title": "MD",
                "specialty": "cardiovascular medicine",
                "institution": "Harvard Med School"
            }
        ),
        
        # Medium confidence entities (uncertain case)
        EntityForDeduplication(
            uuid="med-conf-1",
            name="John Smith",
            type="Person",
            description="Medical professional",
            confidence=0.7,
            source_document_id="doc3",
            attributes={
                "profession": "doctor"
            }
        ),
        EntityForDeduplication(
            uuid="med-conf-2",
            name="J. Smith",
            type="Person",
            description="Healthcare worker",
            confidence=0.65,
            source_document_id="doc4",
            attributes={
                "profession": "medical"
            }
        ),
        
        # Low confidence entities (should not merge)
        EntityForDeduplication(
            uuid="low-conf-1",
            name="John Smith",
            type="Person",
            description="Software engineer",
            confidence=0.4,
            source_document_id="doc5",
            attributes={
                "profession": "engineering",
                "company": "Google"
            }
        ),
        EntityForDeduplication(
            uuid="low-conf-2",
            name="John Smith",
            type="Person",
            description="Teacher",
            confidence=0.3,
            source_document_id="doc6",
            attributes={
                "profession": "education",
                "school": "Local High School"
            }
        ),
        
        # Same document entities (should be more conservative)
        EntityForDeduplication(
            uuid="same-doc-1",
            name="Harvard University",
            type="Organization",
            description="Private research university",
            confidence=0.9,
            source_document_id="doc7",
            attributes={
                "type": "university",
                "location": "Cambridge, MA"
            }
        ),
        EntityForDeduplication(
            uuid="same-doc-2",
            name="Harvard Medical School",
            type="Organization",
            description="Medical school of Harvard",
            confidence=0.88,
            source_document_id="doc7",  # Same document
            attributes={
                "type": "medical_school",
                "location": "Boston, MA"
            }
        )
    ]
    
    return entities


def test_merge_decisions():
    """Test different merge decision scenarios."""
    logger.info("=== Testing Merge Decisions ===")
    
    entities = create_test_entities_for_merging()
    merger = get_confidence_merger(MergeStrategy.BALANCED)
    
    # Test scenarios
    test_cases = [
        (entities[0], entities[1], "High confidence entities (should merge)"),
        (entities[2], entities[3], "Medium confidence entities (uncertain)"),
        (entities[4], entities[5], "Low confidence entities (should not merge)"),
        (entities[6], entities[7], "Same document entities (conservative)")
    ]
    
    for entity1, entity2, description in test_cases:
        logger.info(f"\n{description}")
        logger.info(f"Entity 1: {entity1.name} (conf: {entity1.confidence})")
        logger.info(f"Entity 2: {entity2.name} (conf: {entity2.confidence})")
        
        # Simulate similarity score based on entity similarity
        if entity1.uuid.startswith("high"):
            similarity_score = 0.9
        elif entity1.uuid.startswith("med"):
            similarity_score = 0.7
        elif entity1.uuid.startswith("low"):
            similarity_score = 0.6
        else:  # same-doc
            similarity_score = 0.75
        
        candidate = merger.evaluate_merge_candidate(entity1, entity2, similarity_score)
        
        logger.info(f"Similarity Score: {candidate.similarity_score:.3f}")
        logger.info(f"Confidence Score: {candidate.confidence_score:.3f}")
        logger.info(f"Uncertainty Score: {candidate.uncertainty_score:.3f}")
        logger.info(f"Decision: {candidate.decision.value}")
        logger.info(f"Reasoning: {candidate.reasoning}")


def test_different_strategies():
    """Test different merging strategies."""
    logger.info("\n=== Testing Different Strategies ===")
    
    entities = create_test_entities_for_merging()
    entity1, entity2 = entities[2], entities[3]  # Medium confidence case
    similarity_score = 0.7
    
    strategies = [MergeStrategy.CONSERVATIVE, MergeStrategy.BALANCED, MergeStrategy.AGGRESSIVE, MergeStrategy.ADAPTIVE]
    
    for strategy in strategies:
        merger = ConfidenceBasedMerger(strategy)
        candidate = merger.evaluate_merge_candidate(entity1, entity2, similarity_score)
        
        logger.info(f"\n{strategy.value.upper()} Strategy:")
        logger.info(f"  Decision: {candidate.decision.value}")
        logger.info(f"  Confidence: {candidate.confidence_score:.3f}")
        logger.info(f"  Uncertainty: {candidate.uncertainty_score:.3f}")


def test_actual_merging():
    """Test the actual merging process."""
    logger.info("\n=== Testing Actual Merging ===")
    
    entities = create_test_entities_for_merging()
    merger = get_confidence_merger(MergeStrategy.BALANCED)
    
    # Get a merge candidate that should merge
    entity1, entity2 = entities[0], entities[1]  # High confidence entities
    candidate = merger.evaluate_merge_candidate(entity1, entity2, 0.9)
    
    if candidate.decision == MergeDecision.MERGE:
        logger.info("Performing merge...")
        logger.info(f"Source: {entity1.name} (UUID: {entity1.uuid})")
        logger.info(f"Target: {entity2.name} (UUID: {entity2.uuid})")
        
        merge_result = merger.perform_merge(candidate)
        
        logger.info(f"\nMerged Entity:")
        logger.info(f"  UUID: {merge_result.merged_entity.uuid}")
        logger.info(f"  Name: {merge_result.merged_entity.name}")
        logger.info(f"  Description: {merge_result.merged_entity.description}")
        logger.info(f"  Confidence: {merge_result.merged_entity.confidence}")
        logger.info(f"  Attributes: {merge_result.merged_entity.attributes}")
        
        logger.info(f"\nMerge Quality: {merge_result.merge_quality:.3f}")
        logger.info(f"Merge Confidence: {merge_result.confidence_score:.3f}")
        logger.info(f"Merge Uncertainty: {merge_result.uncertainty_score:.3f}")
    else:
        logger.info(f"Cannot merge - decision was: {candidate.decision.value}")


def test_uncertainty_handling():
    """Test handling of uncertain cases."""
    logger.info("\n=== Testing Uncertainty Handling ===")
    
    entities = create_test_entities_for_merging()
    merger = get_confidence_merger(MergeStrategy.BALANCED)
    
    # Create several test cases to generate uncertain cases
    test_pairs = [
        (entities[0], entities[1], 0.9),   # Should merge
        (entities[2], entities[3], 0.7),   # Uncertain
        (entities[4], entities[5], 0.6),   # Should not merge
        (entities[6], entities[7], 0.75),  # Uncertain (same doc)
    ]
    
    for entity1, entity2, sim_score in test_pairs:
        merger.evaluate_merge_candidate(entity1, entity2, sim_score)
    
    # Get uncertain cases
    uncertain_cases = merger.get_uncertain_cases()
    logger.info(f"Found {len(uncertain_cases)} uncertain cases:")
    
    for i, case in enumerate(uncertain_cases):
        logger.info(f"\nUncertain Case {i+1}:")
        logger.info(f"  Entities: {case.source_entity.name} vs {case.target_entity.name}")
        logger.info(f"  Similarity: {case.similarity_score:.3f}")
        logger.info(f"  Confidence: {case.confidence_score:.3f}")
        logger.info(f"  Uncertainty: {case.uncertainty_score:.3f}")
        logger.info(f"  Reasoning: {case.reasoning}")


def test_merge_statistics():
    """Test merge statistics and reporting."""
    logger.info("\n=== Testing Merge Statistics ===")
    
    entities = create_test_entities_for_merging()
    merger = get_confidence_merger(MergeStrategy.BALANCED)
    
    # Evaluate multiple pairs
    test_pairs = [
        (entities[0], entities[1], 0.9),   # High conf - should merge
        (entities[2], entities[3], 0.7),   # Med conf - uncertain
        (entities[4], entities[5], 0.6),   # Low conf - no merge
        (entities[6], entities[7], 0.75),  # Same doc - uncertain
        (entities[0], entities[2], 0.8),   # Mixed conf - uncertain
        (entities[1], entities[4], 0.5),   # Very different - no merge
    ]
    
    for entity1, entity2, sim_score in test_pairs:
        merger.evaluate_merge_candidate(entity1, entity2, sim_score)
    
    # Get statistics
    stats = merger.get_merge_statistics()
    
    logger.info("Merge Statistics:")
    logger.info(f"  Total Evaluations: {stats['total_evaluations']}")
    logger.info(f"  Average Confidence: {stats['average_confidence']:.3f}")
    logger.info(f"  Average Uncertainty: {stats['average_uncertainty']:.3f}")
    logger.info(f"  Uncertain Cases: {stats['uncertain_cases_count']}")
    
    logger.info("\nDecision Breakdown:")
    for decision, count in stats['decision_counts'].items():
        percentage = (count / stats['total_evaluations']) * 100
        logger.info(f"  {decision:12}: {count:2} ({percentage:5.1f}%)")


def test_adaptive_strategy():
    """Test the adaptive strategy with different entity types."""
    logger.info("\n=== Testing Adaptive Strategy ===")
    
    # Create entities of different types
    person_entities = [
        EntityForDeduplication(uuid="p1", name="John Doe", type="Person", confidence=0.8),
        EntityForDeduplication(uuid="p2", name="J. Doe", type="Person", confidence=0.75)
    ]
    
    org_entities = [
        EntityForDeduplication(uuid="o1", name="Google Inc", type="Organization", confidence=0.8),
        EntityForDeduplication(uuid="o2", name="Google LLC", type="Organization", confidence=0.75)
    ]
    
    location_entities = [
        EntityForDeduplication(uuid="l1", name="New York", type="Location", confidence=0.8),
        EntityForDeduplication(uuid="l2", name="NYC", type="Location", confidence=0.75)
    ]
    
    merger = ConfidenceBasedMerger(MergeStrategy.ADAPTIVE)
    similarity_score = 0.75  # Borderline case
    
    test_cases = [
        (person_entities, "Person entities"),
        (org_entities, "Organization entities"),
        (location_entities, "Location entities")
    ]
    
    for entities, description in test_cases:
        candidate = merger.evaluate_merge_candidate(entities[0], entities[1], similarity_score)
        
        logger.info(f"\n{description}:")
        logger.info(f"  Decision: {candidate.decision.value}")
        logger.info(f"  Confidence: {candidate.confidence_score:.3f}")
        logger.info(f"  Reasoning: {candidate.reasoning}")


def main():
    """Run all tests."""
    logger.info("Starting Confidence-based Merging Tests")
    logger.info("=" * 60)
    
    try:
        test_merge_decisions()
        test_different_strategies()
        test_actual_merging()
        test_uncertainty_handling()
        test_merge_statistics()
        test_adaptive_strategy()
        
        logger.info("\n" + "=" * 60)
        logger.info("All confidence-based merging tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
