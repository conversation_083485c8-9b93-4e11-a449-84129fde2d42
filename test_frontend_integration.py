#!/usr/bin/env python3
"""
Test frontend integration for reference system
"""

import requests
import time

def test_frontend_integration():
    """Test the frontend integration"""
    
    print("🖥️ Testing Frontend Integration")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:9753"
    
    # Test API endpoints
    print("📡 Testing API Endpoints...")
    
    try:
        # Test references API
        response = requests.get(f"{base_url}/api/references", timeout=5)
        if response.status_code == 200:
            data = response.json()
            refs = data.get('references', [])
            print(f"✅ References API: {len(refs)} references found")
            
            # Show sample reference structure
            if refs:
                sample_ref = refs[0]
                print(f"📋 Sample reference structure:")
                for key, value in sample_ref.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {key}: {value}")
        else:
            print(f"❌ References API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ References API error: {e}")
    
    # Test enhanced upload API
    try:
        response = requests.get(f"{base_url}/api/enhanced-upload/supported-types", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Enhanced Upload API: {len(data.get('supported_extensions', []))} file types supported")
        else:
            print(f"❌ Enhanced Upload API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Enhanced Upload API error: {e}")
    
    # Test main UI pages
    print(f"\n🌐 Testing UI Pages...")
    
    ui_endpoints = [
        ("/", "Main Page"),
        ("/upload", "Upload Page"),
        ("/documents", "Documents Page"),
        ("/entities", "Entities Page"),
    ]
    
    for endpoint, name in ui_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {name} accessible")
            else:
                print(f"❌ {name} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} error: {e}")
    
    print(f"\n🎉 Frontend Integration Test Complete!")

if __name__ == "__main__":
    test_frontend_integration()
