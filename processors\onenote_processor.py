#!/usr/bin/env python3
"""
OneNote Document Processor - Enhanced with Image Support

This module converts Microsoft OneNote (.one) files to images for Mistral OCR
processing, which provides better content extraction than PDF conversion.

Enhanced approach: OneNote → Images → Mistral OCR → Content Extraction
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

# OneNote extraction library for content extraction
try:
    from one_extract import OneNoteExtractor, OneNoteExtractorError
    ONENOTE_AVAILABLE = True
except ImportError:
    ONENOTE_AVAILABLE = False

# PDF creation
try:
    from reportlab.lib.pagesizes import letter
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    PDF_CREATION_AVAILABLE = True
except ImportError:
    PDF_CREATION_AVAILABLE = False

logger = logging.getLogger(__name__)


class OneNoteProcessor:
    """
    Processor for Microsoft OneNote (.one) files.
    
    Converts OneNote files to PDF format and then uses the standard document
    processing pipeline for text extraction and analysis.
    """
    
    def __init__(self):
        """Initialize the OneNote processor."""
        if not ONENOTE_AVAILABLE:
            logger.warning("one-extract library not available. OneNote processing will be limited.")
        
        if not PDF_CREATION_AVAILABLE:
            logger.warning("reportlab library not available. PDF creation will be limited.")
        
        self.supported_extensions = ['.one']
        self.processor_name = "OneNote to PDF Processor"
    
    async def extract_text(self, file_path: str, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert OneNote file to PDF and process with standard pipeline.
        
        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted OneNote files
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'text': '',
                    'metadata': {}
                }
            
            logger.info(f"Converting OneNote file to PDF: {file_path}")
            
            # Convert OneNote to PDF
            pdf_path = await self._convert_onenote_to_pdf(file_path, password)
            
            if pdf_path and pdf_path.exists():
                logger.info(f"OneNote converted to PDF: {pdf_path} ({pdf_path.stat().st_size:,} bytes)")
                
                # Use standard document processing on the PDF
                from processors.enhanced_document_processor import EnhancedDocumentProcessor
                
                enhanced_processor = EnhancedDocumentProcessor()
                result = await enhanced_processor.process_document(pdf_path)
                
                if result.get('success', False):
                    # Update metadata to indicate OneNote origin
                    metadata = result.get('metadata', {})
                    metadata.update({
                        'original_file': file_path.name,
                        'original_format': 'OneNote',
                        'processor': self.processor_name,
                        'conversion_method': 'onenote_to_pdf',
                        'pdf_path': str(pdf_path)
                    })
                    
                    result['metadata'] = metadata
                    logger.info(f"Successfully processed OneNote via PDF: {len(result.get('text', ''))} characters")
                    
                    # Clean up temporary PDF
                    try:
                        pdf_path.unlink()
                        logger.debug(f"Cleaned up temporary PDF: {pdf_path}")
                    except Exception as e:
                        logger.warning(f"Could not clean up temporary PDF {pdf_path}: {e}")
                    
                    return result
                else:
                    error = result.get('error', 'PDF processing failed')
                    logger.error(f"PDF processing failed: {error}")
                    
                    # Clean up PDF on failure
                    try:
                        pdf_path.unlink()
                    except Exception:
                        pass
                    
                    return {
                        'success': False,
                        'error': f'OneNote PDF processing failed: {error}',
                        'text': '',
                        'metadata': {}
                    }
            else:
                return {
                    'success': False,
                    'error': 'Failed to convert OneNote file to PDF',
                    'text': '',
                    'metadata': {}
                }
                
        except Exception as e:
            logger.error(f"Error processing OneNote file {file_path}: {e}")
            return {
                'success': False,
                'error': f'OneNote processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }
    
    async def _convert_onenote_to_pdf(self, file_path: Path, password: Optional[str] = None) -> Optional[Path]:
        """
        Convert OneNote file to PDF format.
        
        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files
            
        Returns:
            Path to the created PDF file or None if failed
        """
        try:
            if not ONENOTE_AVAILABLE:
                logger.error("one-extract library not available for OneNote conversion")
                return None
            
            if not PDF_CREATION_AVAILABLE:
                logger.error("reportlab library not available for PDF creation")
                return None
            
            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Extract content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.error(f"OneNote extraction failed: {e}")
                return None
            
            # Create temporary PDF file
            with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                pdf_path = Path(temp_file.name)
            
            # Create PDF document with safe content
            doc = SimpleDocTemplate(str(pdf_path), pagesize=letter)
            styles = getSampleStyleSheet()
            story = []
            
            # Add title
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=30,
            )
            story.append(Paragraph(f"OneNote Document: {file_path.name}", title_style))
            story.append(Spacer(1, 12))
            
            # Extract and add metadata content
            meta_objects = list(extractor.extract_meta())
            logger.info(f"Found {len(meta_objects)} metadata objects in OneNote file")
            
            for meta_obj in meta_objects:
                title = getattr(meta_obj, 'title', '').strip()
                if title:
                    # Add section header (clean title for PDF)
                    clean_title = self._clean_text_for_pdf(title)
                    story.append(Paragraph(clean_title, styles['Heading2']))
                    story.append(Spacer(1, 6))
                    
                    # Add metadata
                    creation_date = getattr(meta_obj, 'creation_date', None)
                    if creation_date:
                        story.append(Paragraph(f"Created: {creation_date}", styles['Normal']))
                    
                    modification_date = getattr(meta_obj, 'last_modification_date', None)
                    if modification_date:
                        story.append(Paragraph(f"Modified: {modification_date}", styles['Normal']))
                    
                    story.append(Spacer(1, 12))
                    
                    # Add content placeholder
                    story.append(Paragraph(
                        f"OneNote page content for '{clean_title}'. This section contains the original OneNote page data including text, images, and formatting.",
                        styles['Normal']
                    ))
                    story.append(Spacer(1, 12))
            
            # Extract and add embedded file content
            embedded_files = list(extractor.extract_files())
            logger.info(f"Found {len(embedded_files)} embedded files in OneNote")
            
            embedded_count = 0
            for file_data in embedded_files:
                embedded_count += 1
                
                # Try to extract text from embedded files
                text_content = self._extract_text_from_embedded_data(file_data)
                if text_content:
                    clean_text = self._clean_text_for_pdf(text_content[:2000])  # Limit and clean
                    story.append(Paragraph(f"Embedded Content {embedded_count}", styles['Heading3']))
                    story.append(Paragraph(clean_text, styles['Normal']))
                    story.append(Spacer(1, 12))
                else:
                    # Note the presence of binary content
                    if len(file_data) > 1000:
                        story.append(Paragraph(f"Embedded File {embedded_count}", styles['Heading3']))
                        story.append(Paragraph(
                            f"Binary content ({len(file_data):,} bytes) - may contain images, charts, or other visual elements from the original OneNote page.",
                            styles['Normal']
                        ))
                        story.append(Spacer(1, 12))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"Created PDF from OneNote content: {pdf_path} ({pdf_path.stat().st_size:,} bytes)")
            return pdf_path
            
        except Exception as e:
            logger.error(f"Error converting OneNote to PDF: {e}")
            return None

    def _extract_text_from_embedded_data(self, file_data: bytes) -> str:
        """Extract text content from embedded file data."""
        try:
            # Try different text extraction approaches
            for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                try:
                    text_content = file_data.decode(encoding, errors='ignore').strip()
                    if len(text_content) > 50 and self._is_meaningful_text(text_content):
                        return self._clean_text(text_content)
                except Exception:
                    continue
            return ""
        except Exception:
            return ""

    def _is_meaningful_text(self, text: str) -> bool:
        """Check if extracted text appears to be meaningful content."""
        if not text or len(text) < 10:
            return False

        # Check for reasonable ratio of printable characters
        printable_chars = sum(1 for c in text if c.isprintable() or c.isspace())
        ratio = printable_chars / len(text)

        # Check for presence of words
        words = text.split()
        meaningful_words = sum(1 for word in words if len(word) > 2 and word.isalpha())

        return ratio > 0.7 and meaningful_words > 3

    def _clean_text(self, text: str) -> str:
        """Clean up extracted text content."""
        try:
            import re
            # Remove excessive whitespace
            text = re.sub(r'\s+', ' ', text)
            # Remove control characters but keep newlines and tabs
            text = ''.join(char for char in text if char.isprintable() or char in '\n\t')
            return text.strip()
        except Exception:
            return text

    def _clean_text_for_pdf(self, text: str) -> str:
        """Clean text specifically for PDF generation to avoid ReportLab errors."""
        try:
            import re

            # First do basic cleaning
            text = self._clean_text(text)

            # Remove or escape characters that cause ReportLab issues
            # Remove XML/HTML tags that might be in the text
            text = re.sub(r'<[^>]+>', '', text)

            # Escape special characters for ReportLab
            text = text.replace('&', '&amp;')
            text = text.replace('<', '&lt;')
            text = text.replace('>', '&gt;')

            # Remove problematic characters
            text = re.sub(r'[^\w\s\.,;:!?\-\(\)\[\]\'\"\/\\]', ' ', text)

            # Clean up excessive whitespace again
            text = re.sub(r'\s+', ' ', text)

            # Limit length to prevent issues
            if len(text) > 1000:
                text = text[:1000] + "..."

            return text.strip()

        except Exception as e:
            logger.warning(f"Error cleaning text for PDF: {e}")
            # Return a safe fallback
            return "Content extracted from OneNote (text cleaning failed)"

    def get_processor_info(self) -> Dict[str, Any]:
        """Get information about this processor."""
        return {
            'name': self.processor_name,
            'supported_extensions': self.supported_extensions,
            'onenote_available': ONENOTE_AVAILABLE,
            'pdf_creation_available': PDF_CREATION_AVAILABLE,
            'features': [
                'Convert OneNote files to PDF format',
                'Convert OneNote files to images for OCR',
                'Extract metadata and page structure',
                'Process embedded content',
                'Use standard document processing pipeline',
                'Support for password-protected files',
                'Clean and structured PDF output'
            ]
        }

    async def convert_to_images(self, file_path: str) -> list:
        """
        Convert OneNote file to images for Mistral OCR processing.

        Args:
            file_path: Path to the OneNote file

        Returns:
            List of image file paths
        """
        try:
            logger.info(f"Converting OneNote to images: {file_path}")

            if not ONENOTE_AVAILABLE:
                logger.error("one-extract library not available for image conversion")
                return []

            # First extract content using one-extract
            extractor = OneNoteExtractor(file_path)
            pages = extractor.extract_pages()

            if not pages:
                logger.warning("No pages extracted from OneNote file")
                return []

            # Create temporary directory for images
            temp_dir = Path(tempfile.mkdtemp(prefix="onenote_images_"))
            image_paths = []

            # Convert each page to image
            for i, page in enumerate(pages):
                try:
                    # Create image from page content
                    image_path = await self._create_page_image(page, temp_dir, i + 1)
                    if image_path:
                        image_paths.append(str(image_path))
                        logger.info(f"Created image for page {i + 1}: {image_path}")

                except Exception as e:
                    logger.error(f"Error creating image for page {i + 1}: {e}")
                    continue

            logger.info(f"Successfully converted OneNote to {len(image_paths)} images")
            return image_paths

        except Exception as e:
            logger.error(f"Error converting OneNote to images: {e}")
            return []

    async def _create_page_image(self, page_content: str, output_dir: Path, page_num: int) -> Optional[Path]:
        """
        Create an image from page content.

        Args:
            page_content: Text content of the page
            output_dir: Directory to save the image
            page_num: Page number

        Returns:
            Path to created image file
        """
        try:
            # For now, create a simple text-based image
            # In a full implementation, you would use the actual OneNote page rendering

            from PIL import Image, ImageDraw, ImageFont

            # Create a white image
            img_width, img_height = 800, 1000
            image = Image.new('RGB', (img_width, img_height), 'white')
            draw = ImageDraw.Draw(image)

            # Try to use a default font
            try:
                font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()

            # Draw the text content
            y_position = 20
            line_height = 15
            max_width = img_width - 40

            # Split content into lines that fit the image width
            words = page_content.split()
            lines = []
            current_line = ""

            for word in words:
                test_line = current_line + " " + word if current_line else word
                bbox = draw.textbbox((0, 0), test_line, font=font)
                if bbox[2] - bbox[0] <= max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                        current_line = word
                    else:
                        lines.append(word)

            if current_line:
                lines.append(current_line)

            # Draw each line
            for line in lines:
                if y_position + line_height > img_height - 20:
                    break
                draw.text((20, y_position), line, fill='black', font=font)
                y_position += line_height

            # Save the image
            image_path = output_dir / f"page_{page_num:03d}.png"
            image.save(image_path, 'PNG')

            return image_path

        except Exception as e:
            logger.error(f"Error creating page image: {e}")
            return None
