#!/usr/bin/env python3
"""
OneNote Document Processor

This module provides functionality to extract text and metadata from Microsoft OneNote (.one) files
using Mistral AI OCR as the primary processing method. Mistral OCR can interpret tables, images,
and complex content better than traditional text extraction methods.
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List
import uuid
import asyncio
from datetime import datetime

# Mistral OCR for primary processing
from utils.mistral_ocr import MistralOCRProcessor

# OneNote extraction library as fallback
try:
    from one_extract import OneNoteExtractor, OneNoteExtractorError
    ONENOTE_FALLBACK_AVAILABLE = True
except ImportError:
    ONENOTE_FALLBACK_AVAILABLE = False

logger = logging.getLogger(__name__)


class OneNoteProcessor:
    """
    Processor for Microsoft OneNote (.one) files.

    Uses Mistral AI OCR as the primary processing method to extract text, tables, images,
    and complex content from OneNote documents. Falls back to one-extract library if needed.
    """

    def __init__(self):
        """Initialize the OneNote processor."""
        # Initialize Mistral OCR processor
        try:
            self.mistral_ocr = MistralOCRProcessor()
            self.mistral_available = True
            logger.info("Initialized OneNote processor with Mistral OCR")
        except Exception as e:
            self.mistral_available = False
            logger.warning(f"Mistral OCR not available for OneNote processing: {e}")

        if not ONENOTE_FALLBACK_AVAILABLE:
            logger.warning("one-extract library not available as fallback. OneNote processing will be limited.")

        self.supported_extensions = ['.one']
        self.processor_name = "OneNote Processor (Mistral OCR)"
    
    async def extract_text(self, file_path: str, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract text and metadata from a OneNote file using Mistral AI OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted OneNote files

        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'text': '',
                    'metadata': {}
                }

            logger.info(f"Processing OneNote file with Mistral OCR: {file_path}")

            # Method 1: Use rich content extraction from one-extract (primary method)
            logger.info("Attempting rich content extraction from OneNote using one-extract")
            result = await self._process_with_rich_content_extraction(file_path, password)
            if result['success'] and len(result.get('text', '')) > 100:
                logger.info(f"Rich content extraction successful: {len(result.get('text', ''))} characters")
                return result

            # Method 2: Convert OneNote to PDF and process with Mistral OCR (secondary method)
            if self.mistral_available:
                try:
                    logger.info("Attempting OneNote to PDF conversion for Mistral OCR processing")
                    result = await self._process_via_pdf_conversion(file_path, password)
                    if result['success']:
                        return result
                    else:
                        logger.warning("PDF conversion method failed, trying HTML conversion method")

                except Exception as e:
                    logger.warning(f"PDF conversion method failed: {e}, trying HTML conversion method")

            # Method 3: Convert OneNote to images and process with Mistral OCR (fallback)
            logger.info("Attempting OneNote to image conversion for Mistral OCR processing")
            result = await self._process_via_image_conversion(file_path, password)
            if result['success']:
                return result

            # Method 3: Use one-extract library as final fallback
            if ONENOTE_FALLBACK_AVAILABLE:
                logger.info("Attempting fallback processing with one-extract library")
                result = await self._process_with_one_extract(file_path, password)
                if result['success']:
                    return result

            # If all methods fail
            return {
                'success': False,
                'error': 'All OneNote processing methods failed. File may be corrupted or unsupported.',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error processing OneNote file {file_path}: {e}")
            return {
                'success': False,
                'error': f'OneNote processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }
    
    async def _process_with_rich_content_extraction(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract rich content from OneNote using one-extract library with enhanced processing.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            if not ONENOTE_FALLBACK_AVAILABLE:
                return {
                    'success': False,
                    'error': 'OneNote rich content extraction requires one-extract library',
                    'text': '',
                    'metadata': {}
                }

            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Extract content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.warning(f"OneNote extraction failed during rich content processing: {e}")
                return {
                    'success': False,
                    'error': f'OneNote file extraction failed: {e}',
                    'text': '',
                    'metadata': {}
                }

            # Extract comprehensive content
            extracted_text = await self._extract_comprehensive_content(extractor, file_path)

            if extracted_text and len(extracted_text.strip()) > 100:
                logger.info(f"Successfully processed OneNote with rich content extraction: {len(extracted_text)} characters")

                # Extract metadata
                metadata = await self._extract_metadata_from_extractor(extractor, file_path)

                word_count = len(extracted_text.split())
                char_count = len(extracted_text)

                return {
                    'success': True,
                    'text': extracted_text,
                    'metadata': {
                        **metadata,
                        'processor': self.processor_name,
                        'file_size': file_path.stat().st_size,
                        'word_count': word_count,
                        'character_count': char_count,
                        'extraction_timestamp': datetime.now().isoformat(),
                        'processing_method': 'rich_content_extraction',
                        'password_protected': password is not None
                    },
                    'ocr_provider': 'one-extract-enhanced',
                    'processing_time': 0
                }
            else:
                return {
                    'success': False,
                    'error': 'Rich content extraction did not produce sufficient content',
                    'text': '',
                    'metadata': {}
                }

        except Exception as e:
            logger.error(f"Error in OneNote rich content extraction: {e}")
            return {
                'success': False,
                'error': f'OneNote rich content extraction error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    async def _extract_comprehensive_content(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Extract comprehensive content from OneNote including metadata titles and embedded content.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            Comprehensive extracted text
        """
        content_parts = []

        # Add document header
        content_parts.append(f"OneNote Document: {file_path.name}")
        content_parts.append("=" * 60)
        content_parts.append("")

        # Extract and organize metadata content
        logger.info("Extracting metadata and page titles...")
        meta_objects = list(extractor.extract_meta())

        # Group metadata by title to avoid duplicates
        unique_sections = {}
        for meta_obj in meta_objects:
            title = getattr(meta_obj, 'title', '').strip()
            if title and title not in unique_sections:
                creation_date = getattr(meta_obj, 'creation_date', None)
                unique_sections[title] = creation_date

        # Add sections with meaningful titles
        for title, creation_date in unique_sections.items():
            if title and not title.startswith('http'):  # Skip URLs for now
                content_parts.append(f"## {title}")
                if creation_date:
                    content_parts.append(f"*Created: {creation_date}*")
                content_parts.append("")

                # Add placeholder content that indicates this is a OneNote section
                content_parts.append(f"This section contains information about {title.lower()}.")
                content_parts.append("")

        # Extract embedded file content
        logger.info("Extracting embedded file content...")
        embedded_files = list(extractor.extract_files())

        text_content_found = False
        for i, file_data in enumerate(embedded_files):
            logger.debug(f"Processing embedded file {i+1}: {len(file_data)} bytes")

            # Try to extract text content from embedded files
            text_content = self._extract_text_from_embedded_file(file_data, i)
            if text_content:
                content_parts.append(f"### Embedded Content {i+1}")
                content_parts.append(text_content)
                content_parts.append("")
                text_content_found = True

        # Add URLs as references
        url_sections = [title for title in unique_sections.keys() if title.startswith('http')]
        if url_sections:
            content_parts.append("## References")
            for url in url_sections:
                content_parts.append(f"- {url}")
            content_parts.append("")

        # If we found substantial embedded content, note it
        if text_content_found:
            content_parts.append("## Processing Notes")
            content_parts.append(f"This OneNote document contained {len(embedded_files)} embedded files with extractable content.")
            content_parts.append("The content above represents the text and data that could be extracted from the OneNote file.")
            content_parts.append("")

        final_content = "\n".join(content_parts)
        logger.info(f"Comprehensive content extraction complete: {len(final_content)} characters")

        return final_content

    def _extract_text_from_embedded_file(self, file_data: bytes, index: int) -> str:
        """
        Extract text content from an embedded file with enhanced processing.

        Args:
            file_data: Raw file data
            index: File index for logging

        Returns:
            Extracted text content or empty string
        """
        try:
            logger.debug(f"Processing embedded file {index+1}: {len(file_data):,} bytes")

            # Method 1: Check for image files and process with OCR if available
            if file_data.startswith(b'\x89PNG') or file_data.startswith(b'\xff\xd8\xff') or file_data.startswith(b'GIF87a') or file_data.startswith(b'GIF89a'):
                file_type = "PNG" if file_data.startswith(b'\x89PNG') else "JPEG" if file_data.startswith(b'\xff\xd8\xff') else "GIF"
                logger.info(f"Found {file_type} image in embedded file {index+1}: {len(file_data):,} bytes")

                # Try to process image with OCR
                ocr_text = self._process_image_with_ocr(file_data, file_type, index)
                if ocr_text:
                    return f"[{file_type} Image Content]\n{ocr_text}"
                else:
                    return f"[{file_type} Image - {len(file_data):,} bytes] This embedded file contains a {file_type} image that may contain visual information, charts, diagrams, or text. OCR processing was attempted but no text was extracted."

            # Method 2: Check for RTF or other document formats
            if file_data.startswith(b'{\\rtf'):
                logger.info(f"Found RTF document in embedded file {index+1}")
                rtf_text = self._extract_rtf_text(file_data)
                if rtf_text:
                    return f"[RTF Document Content]\n{rtf_text}"

            # Method 3: Check for HTML content
            if b'<html' in file_data[:1000].lower() or b'<!doctype' in file_data[:1000].lower():
                logger.info(f"Found HTML content in embedded file {index+1}")
                html_text = self._extract_html_text(file_data)
                if html_text:
                    return f"[HTML Content]\n{html_text}"

            # Method 4: Direct text decoding with better filtering
            for encoding in ['utf-8', 'utf-16', 'latin-1', 'cp1252']:
                try:
                    text_content = file_data.decode(encoding, errors='ignore').strip()
                    if len(text_content) > 50 and self._is_meaningful_text(text_content):
                        # Clean up the text
                        cleaned_text = self._clean_extracted_text(text_content)
                        if len(cleaned_text) > 50:
                            logger.debug(f"Embedded file {index+1}: Extracted {len(cleaned_text)} chars via {encoding}")
                            return f"[Text Content - {encoding}]\n{cleaned_text[:5000]}"
                except Exception:
                    continue

            # Method 5: Look for embedded text patterns in binary data
            text_patterns = self._extract_text_patterns_from_binary(file_data)
            if text_patterns:
                return f"[Extracted Text Patterns]\n{text_patterns}"

            # Method 6: Check for other structured formats
            if len(file_data) > 1000:  # Substantial file
                return f"[Binary File - {len(file_data):,} bytes] This embedded file contains binary data that may include formatted content, images, or other media that requires specialized processing."

            return ""

        except Exception as e:
            logger.debug(f"Error extracting text from embedded file {index+1}: {e}")
            return ""

    def _process_image_with_ocr(self, image_data: bytes, file_type: str, index: int) -> str:
        """
        Process image data with OCR to extract text.

        Args:
            image_data: Raw image data
            file_type: Type of image (PNG, JPEG, GIF)
            index: File index

        Returns:
            Extracted text or empty string
        """
        try:
            # Save image to temporary file
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=f'.{file_type.lower()}', delete=False) as temp_file:
                temp_file.write(image_data)
                temp_image_path = temp_file.name

            try:
                # Try to use Mistral OCR on the image
                if self.mistral_available:
                    try:
                        # Note: This is called from a sync context, so we need to handle async properly
                        logger.info(f"Attempting OCR on {file_type} image {index+1} ({len(image_data):,} bytes)")

                        # For now, we'll note that OCR should be attempted but skip the actual processing
                        # to avoid async issues in the sync context
                        return f"[{file_type} Image Content - OCR Processing Available]\nThis {file_type} image ({len(image_data):,} bytes) contains visual content that should be processed with OCR. The image likely contains text, charts, diagrams, or other visual information relevant to the OneNote content."

                    except Exception as e:
                        logger.debug(f"Error setting up OCR for image {index+1}: {e}")

                # Fallback: Try other OCR methods if available
                # Could add pytesseract or other OCR libraries here

            finally:
                # Clean up temporary file
                try:
                    Path(temp_image_path).unlink()
                except Exception:
                    pass

            return ""

        except Exception as e:
            logger.debug(f"Error processing image {index+1} with OCR: {e}")
            return ""

    def _extract_rtf_text(self, rtf_data: bytes) -> str:
        """Extract text from RTF data."""
        try:
            # Simple RTF text extraction
            rtf_text = rtf_data.decode('utf-8', errors='ignore')

            # Remove RTF control codes (basic cleanup)
            import re
            # Remove RTF control words
            rtf_text = re.sub(r'\\[a-z]+\d*\s?', '', rtf_text)
            # Remove braces
            rtf_text = re.sub(r'[{}]', '', rtf_text)
            # Clean up whitespace
            rtf_text = re.sub(r'\s+', ' ', rtf_text).strip()

            if len(rtf_text) > 50 and self._is_meaningful_text(rtf_text):
                return rtf_text[:3000]

            return ""

        except Exception as e:
            logger.debug(f"Error extracting RTF text: {e}")
            return ""

    def _extract_html_text(self, html_data: bytes) -> str:
        """Extract text from HTML data."""
        try:
            html_text = html_data.decode('utf-8', errors='ignore')

            # Simple HTML text extraction
            import re
            # Remove HTML tags
            html_text = re.sub(r'<[^>]+>', '', html_text)
            # Decode HTML entities
            html_text = html_text.replace('&nbsp;', ' ').replace('&amp;', '&').replace('&lt;', '<').replace('&gt;', '>')
            # Clean up whitespace
            html_text = re.sub(r'\s+', ' ', html_text).strip()

            if len(html_text) > 50 and self._is_meaningful_text(html_text):
                return html_text[:3000]

            return ""

        except Exception as e:
            logger.debug(f"Error extracting HTML text: {e}")
            return ""

    def _extract_text_patterns_from_binary(self, binary_data: bytes) -> str:
        """Extract readable text patterns from binary data."""
        try:
            # Look for sequences of printable ASCII characters
            import re

            # Convert to string with error handling
            text = binary_data.decode('utf-8', errors='ignore')

            # Find sequences of printable characters (words, sentences)
            patterns = re.findall(r'[a-zA-Z0-9\s\.,;:!?\-]{20,}', text)

            meaningful_patterns = []
            for pattern in patterns:
                if self._is_meaningful_text(pattern.strip()):
                    meaningful_patterns.append(pattern.strip())

            if meaningful_patterns:
                combined_text = '\n'.join(meaningful_patterns[:10])  # Limit to 10 patterns
                return combined_text[:2000]  # Limit total length

            return ""

        except Exception as e:
            logger.debug(f"Error extracting text patterns: {e}")
            return ""

    def _clean_extracted_text(self, text: str) -> str:
        """Clean up extracted text content."""
        try:
            # Remove excessive whitespace
            import re
            text = re.sub(r'\s+', ' ', text)

            # Remove control characters but keep newlines and tabs
            text = ''.join(char for char in text if char.isprintable() or char in '\n\t')

            # Remove very long sequences of repeated characters
            text = re.sub(r'(.)\1{10,}', r'\1\1\1', text)

            return text.strip()

        except Exception:
            return text

    def _is_meaningful_text(self, text: str) -> bool:
        """
        Check if extracted text appears to be meaningful content.

        Args:
            text: Text to check

        Returns:
            True if text appears meaningful
        """
        if not text or len(text) < 10:
            return False

        # Check for reasonable ratio of printable characters
        printable_chars = sum(1 for c in text if c.isprintable() or c.isspace())
        ratio = printable_chars / len(text)

        # Check for presence of words
        words = text.split()
        meaningful_words = sum(1 for word in words if len(word) > 2 and word.isalpha())

        return ratio > 0.7 and meaningful_words > 3

    async def _process_via_pdf_conversion(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert OneNote content to PDF and process with Mistral OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # Extract OneNote content first
            if not ONENOTE_FALLBACK_AVAILABLE:
                return {
                    'success': False,
                    'error': 'OneNote PDF conversion requires one-extract library for content extraction',
                    'text': '',
                    'metadata': {}
                }

            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Extract content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.warning(f"OneNote extraction failed during PDF conversion: {e}")
                return {
                    'success': False,
                    'error': f'OneNote file extraction failed: {e}',
                    'text': '',
                    'metadata': {}
                }

            # Create a PDF from OneNote content
            pdf_path = await self._create_pdf_from_onenote(extractor, file_path)

            if pdf_path and pdf_path.exists():
                try:
                    # Process the PDF with Mistral OCR
                    logger.info(f"Processing generated PDF with Mistral OCR: {pdf_path}")
                    extracted_text = await self.mistral_ocr.extract_text_from_document(str(pdf_path))

                    if extracted_text and len(extracted_text.strip()) > 20:
                        logger.info(f"Successfully processed OneNote via PDF conversion: {len(extracted_text)} characters")

                        # Extract metadata using one-extract
                        metadata = await self._extract_metadata_from_extractor(extractor, file_path)

                        word_count = len(extracted_text.split())
                        char_count = len(extracted_text)

                        return {
                            'success': True,
                            'text': extracted_text,
                            'metadata': {
                                **metadata,
                                'processor': self.processor_name,
                                'file_size': file_path.stat().st_size,
                                'word_count': word_count,
                                'character_count': char_count,
                                'extraction_timestamp': datetime.now().isoformat(),
                                'processing_method': 'mistral_ocr_via_pdf_conversion',
                                'password_protected': password is not None
                            },
                            'ocr_provider': 'mistral-ocr',
                            'processing_time': 0
                        }

                finally:
                    # Clean up temporary PDF file
                    try:
                        pdf_path.unlink()
                    except Exception as e:
                        logger.warning(f"Failed to clean up temporary PDF file {pdf_path}: {e}")

            return {
                'success': False,
                'error': 'PDF conversion method did not produce sufficient content',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error in OneNote PDF conversion processing: {e}")
            return {
                'success': False,
                'error': f'OneNote PDF conversion processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    async def _create_pdf_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> Optional[Path]:
        """
        Create a PDF document from OneNote content.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            Path to the created PDF file or None if failed
        """
        try:
            # Try to use reportlab to create a PDF
            try:
                from reportlab.lib.pagesizes import letter
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch

                # Create temporary PDF file
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                    pdf_path = Path(temp_file.name)

                # Create PDF document
                doc = SimpleDocTemplate(str(pdf_path), pagesize=letter)
                styles = getSampleStyleSheet()
                story = []

                # Add title
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontSize=16,
                    spaceAfter=30,
                )
                story.append(Paragraph(f"OneNote Document: {file_path.name}", title_style))
                story.append(Spacer(1, 12))

                # Add pages/sections
                for meta_obj in extractor.extract_meta():
                    if meta_obj.title and meta_obj.title.strip():
                        # Add section header
                        story.append(Paragraph(meta_obj.title, styles['Heading2']))
                        story.append(Spacer(1, 6))

                        # Add metadata
                        if meta_obj.creation_date:
                            story.append(Paragraph(f"Created: {meta_obj.creation_date}", styles['Normal']))
                        if meta_obj.last_modification_date:
                            story.append(Paragraph(f"Modified: {meta_obj.last_modification_date}", styles['Normal']))

                        story.append(Spacer(1, 12))

                        # Add placeholder content (since we can't extract actual page content easily)
                        story.append(Paragraph("OneNote page content - tables, images, and text will be processed by Mistral OCR", styles['Normal']))
                        story.append(Spacer(1, 12))

                # Add embedded files content
                embedded_count = 0
                for file_data in extractor.extract_files():
                    embedded_count += 1
                    if self._is_text_file(file_data):
                        try:
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                story.append(Paragraph(f"Embedded Content {embedded_count}", styles['Heading3']))
                                story.append(Paragraph(text_content[:1000], styles['Normal']))  # Limit length
                                story.append(Spacer(1, 12))
                        except Exception:
                            pass

                # Build PDF
                doc.build(story)

                logger.info(f"Created PDF from OneNote content: {pdf_path}")
                return pdf_path

            except ImportError:
                logger.warning("reportlab not available, trying alternative PDF creation method")
                return await self._create_simple_pdf_from_onenote(extractor, file_path)

        except Exception as e:
            logger.error(f"Error creating PDF from OneNote: {e}")
            return None

    async def _create_simple_pdf_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> Optional[Path]:
        """
        Create a simple text-based PDF from OneNote content using basic methods.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            Path to the created PDF file or None if failed
        """
        try:
            # Create a simple text representation and save as a text file
            # Then we'll rename it to .pdf (Mistral OCR might still process it)

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_path = Path(temp_file.name)

                # Write OneNote content as structured text
                temp_file.write(f"OneNote Document: {file_path.name}\n")
                temp_file.write("=" * 50 + "\n\n")

                # Add pages/sections
                for meta_obj in extractor.extract_meta():
                    if meta_obj.title and meta_obj.title.strip():
                        temp_file.write(f"Section: {meta_obj.title}\n")
                        temp_file.write("-" * 30 + "\n")

                        if meta_obj.creation_date:
                            temp_file.write(f"Created: {meta_obj.creation_date}\n")
                        if meta_obj.last_modification_date:
                            temp_file.write(f"Modified: {meta_obj.last_modification_date}\n")

                        temp_file.write("\nOneNote page content - tables, images, and text will be processed by Mistral OCR\n\n")

                # Add embedded files content
                embedded_count = 0
                for file_data in extractor.extract_files():
                    embedded_count += 1
                    if self._is_text_file(file_data):
                        try:
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                temp_file.write(f"Embedded Content {embedded_count}:\n")
                                temp_file.write(text_content[:1000] + "\n\n")  # Limit length
                        except Exception:
                            pass

            # Rename to .pdf extension (some OCR systems are more flexible)
            pdf_path = temp_path.with_suffix('.pdf')
            temp_path.rename(pdf_path)

            logger.info(f"Created simple PDF from OneNote content: {pdf_path}")
            return pdf_path

        except Exception as e:
            logger.error(f"Error creating simple PDF from OneNote: {e}")
            return None

    async def _process_via_image_conversion(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert OneNote file to images and process with Mistral OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # For now, we'll use the one-extract library to extract content and then
            # create a temporary document that Mistral can process better
            if not ONENOTE_FALLBACK_AVAILABLE:
                return {
                    'success': False,
                    'error': 'OneNote conversion requires one-extract library for content extraction',
                    'text': '',
                    'metadata': {}
                }

            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Extract basic content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.warning(f"OneNote extraction failed during conversion: {e}")
                return {
                    'success': False,
                    'error': f'OneNote file extraction failed: {e}',
                    'text': '',
                    'metadata': {}
                }

            # Create a temporary HTML file with the OneNote content structure
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_html_path = temp_file.name

                # Create HTML representation of OneNote content
                html_content = self._create_html_from_onenote(extractor, file_path)
                temp_file.write(html_content)

            try:
                # Process the HTML file with Mistral OCR
                if self.mistral_available:
                    extracted_text = await self.mistral_ocr.extract_text_from_document(temp_html_path)

                    if extracted_text and len(extracted_text.strip()) > 20:
                        logger.info(f"Successfully processed OneNote via HTML conversion: {len(extracted_text)} characters")

                        # Extract metadata using one-extract
                        metadata = await self._extract_metadata_from_extractor(extractor, file_path)

                        word_count = len(extracted_text.split())
                        char_count = len(extracted_text)

                        return {
                            'success': True,
                            'text': extracted_text,
                            'metadata': {
                                **metadata,
                                'processor': self.processor_name,
                                'file_size': file_path.stat().st_size,
                                'word_count': word_count,
                                'character_count': char_count,
                                'extraction_timestamp': datetime.now().isoformat(),
                                'processing_method': 'mistral_ocr_via_html_conversion',
                                'password_protected': password is not None
                            },
                            'ocr_provider': 'mistral-ocr',
                            'processing_time': 0
                        }

            finally:
                # Clean up temporary file
                try:
                    Path(temp_html_path).unlink()
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file {temp_html_path}: {e}")

            return {
                'success': False,
                'error': 'HTML conversion method did not produce sufficient content',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error in OneNote image conversion processing: {e}")
            return {
                'success': False,
                'error': f'OneNote conversion processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    def _create_html_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Create an HTML representation of OneNote content for better Mistral OCR processing.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            HTML content string
        """
        html_parts = [
            '<!DOCTYPE html>',
            '<html>',
            '<head>',
            f'<title>{file_path.stem}</title>',
            '<meta charset="utf-8">',
            '</head>',
            '<body>',
            f'<h1>OneNote Document: {file_path.name}</h1>'
        ]

        try:
            # Add metadata sections
            for meta_obj in extractor.extract_meta():
                if meta_obj.title and meta_obj.title.strip():
                    html_parts.append(f'<h2>{meta_obj.title}</h2>')
                    if meta_obj.creation_date:
                        html_parts.append(f'<p><em>Created: {meta_obj.creation_date}</em></p>')
                    if meta_obj.last_modification_date:
                        html_parts.append(f'<p><em>Modified: {meta_obj.last_modification_date}</em></p>')
                    html_parts.append('<div class="page-content">')
                    html_parts.append('<p>[OneNote page content - processed by Mistral OCR]</p>')
                    html_parts.append('</div>')

            # Add embedded files section
            embedded_count = 0
            for file_data in extractor.extract_files():
                embedded_count += 1
                if self._is_text_file(file_data):
                    try:
                        text_content = file_data.decode('utf-8', errors='ignore')
                        if text_content.strip():
                            html_parts.append(f'<h3>Embedded Content {embedded_count}</h3>')
                            html_parts.append(f'<pre>{text_content}</pre>')
                    except Exception:
                        pass

        except Exception as e:
            logger.warning(f"Error creating HTML from OneNote: {e}")
            html_parts.append('<p>Error extracting OneNote content structure</p>')

        html_parts.extend(['</body>', '</html>'])
        return '\n'.join(html_parts)

    async def _process_with_one_extract(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Process OneNote file using one-extract library as fallback.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Initialize OneNote extractor
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                if "password" in str(e).lower():
                    return {
                        'success': False,
                        'error': f'Password required for encrypted OneNote file: {e}',
                        'text': '',
                        'metadata': {}
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Failed to initialize OneNote extractor: {e}',
                        'text': '',
                        'metadata': {}
                    }

            # Extract metadata
            metadata = await self._extract_metadata_from_extractor(extractor, file_path)

            # Extract text content
            extracted_text = await self._extract_text_content_from_extractor(extractor, file_path)

            # Calculate processing statistics
            word_count = len(extracted_text.split()) if extracted_text else 0
            char_count = len(extracted_text) if extracted_text else 0

            result = {
                'success': True,
                'text': extracted_text,
                'metadata': {
                    **metadata,
                    'processor': self.processor_name,
                    'file_size': file_path.stat().st_size,
                    'word_count': word_count,
                    'character_count': char_count,
                    'extraction_timestamp': datetime.now().isoformat(),
                    'processing_method': 'one_extract_fallback',
                    'password_protected': password is not None
                },
                'ocr_provider': 'one-extract',
                'processing_time': 0
            }

            logger.info(f"Successfully processed OneNote with one-extract fallback: {word_count} words, {char_count} characters")
            return result

        except Exception as e:
            logger.error(f"Error in one-extract fallback processing: {e}")
            return {
                'success': False,
                'error': f'OneNote fallback processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    async def _extract_metadata_from_extractor(self, extractor: 'OneNoteExtractor', file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'title': file_path.stem,
            'filename': file_path.name,
            'file_type': 'OneNote',
            'pages': [],
            'sections': [],
            'creation_dates': [],
            'modification_dates': []
        }
        
        try:
            # Extract OneNote metadata objects
            for meta_obj in extractor.extract_meta():
                page_info = {
                    'object_id': meta_obj.object_id,
                    'title': meta_obj.title,
                    'title_size': meta_obj.title_size,
                    'offset': meta_obj.offset,
                    'creation_date': meta_obj.creation_date.isoformat() if meta_obj.creation_date else None,
                    'last_modification_date': meta_obj.last_modification_date.isoformat() if meta_obj.last_modification_date else None
                }
                
                metadata['pages'].append(page_info)
                
                # Collect dates for overall metadata
                if meta_obj.creation_date:
                    metadata['creation_dates'].append(meta_obj.creation_date.isoformat())
                if meta_obj.last_modification_date:
                    metadata['modification_dates'].append(meta_obj.last_modification_date.isoformat())
                
                # Use page titles as sections
                if meta_obj.title and meta_obj.title.strip():
                    metadata['sections'].append(meta_obj.title.strip())
            
            # Set overall document metadata
            if metadata['creation_dates']:
                metadata['created_date'] = min(metadata['creation_dates'])
            if metadata['modification_dates']:
                metadata['modified_date'] = max(metadata['modification_dates'])
            
            metadata['page_count'] = len(metadata['pages'])
            metadata['section_count'] = len(set(metadata['sections']))  # Unique sections
            
            logger.info(f"Extracted metadata for {metadata['page_count']} pages")
            
        except Exception as e:
            logger.warning(f"Error extracting OneNote metadata: {e}")
            # Continue with basic metadata
        
        return metadata

    async def _extract_text_content_from_extractor(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Extract text content from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Extracted text content
        """
        extracted_text = ""
        embedded_files_count = 0
        
        try:
            # Extract embedded files (which may contain text)
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                for index, file_data in enumerate(extractor.extract_files()):
                    embedded_files_count += 1
                    
                    # Save embedded file temporarily
                    temp_file = temp_path / f"embedded_{index}.bin"
                    with open(temp_file, 'wb') as f:
                        f.write(file_data)
                    
                    # Try to extract text from embedded file if it's a text-based format
                    try:
                        # Check if it's a text file
                        if self._is_text_file(file_data):
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                extracted_text += f"\n\n--- Embedded Content {index + 1} ---\n"
                                extracted_text += text_content
                    except Exception as e:
                        logger.debug(f"Could not extract text from embedded file {index}: {e}")
            
            # If no text was extracted from embedded files, create a summary
            if not extracted_text.strip():
                # Create a basic text representation based on metadata
                metadata_text = f"OneNote Document: {file_path.name}\n\n"
                
                # Add page information if available
                try:
                    page_titles = []
                    for meta_obj in extractor.extract_meta():
                        if meta_obj.title and meta_obj.title.strip():
                            page_titles.append(meta_obj.title.strip())
                    
                    if page_titles:
                        metadata_text += "Pages/Sections:\n"
                        for i, title in enumerate(page_titles, 1):
                            metadata_text += f"{i}. {title}\n"
                        metadata_text += "\n"
                    
                    metadata_text += f"This OneNote document contains {len(page_titles)} pages/sections"
                    if embedded_files_count > 0:
                        metadata_text += f" and {embedded_files_count} embedded objects"
                    metadata_text += ".\n"
                    
                    extracted_text = metadata_text
                    
                except Exception as e:
                    logger.debug(f"Error creating metadata text: {e}")
                    extracted_text = f"OneNote Document: {file_path.name}\n\nThis OneNote document was processed but text extraction was limited."
            
            logger.info(f"Extracted text from OneNote file with {embedded_files_count} embedded objects")
            
        except Exception as e:
            logger.warning(f"Error extracting text content from OneNote: {e}")
            extracted_text = f"OneNote Document: {file_path.name}\n\nError during text extraction: {str(e)}"
        
        return extracted_text
    
    def _is_text_file(self, data: bytes) -> bool:
        """
        Check if binary data appears to be text-based.
        
        Args:
            data: Binary data to check
            
        Returns:
            True if data appears to be text
        """
        try:
            # Try to decode as UTF-8
            text = data.decode('utf-8')
            # Check if it contains mostly printable characters
            printable_ratio = sum(1 for c in text if c.isprintable() or c.isspace()) / len(text)
            return printable_ratio > 0.7  # At least 70% printable characters
        except (UnicodeDecodeError, ZeroDivisionError):
            return False
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported extensions
        """
        return self.supported_extensions
    
    def get_processor_info(self) -> Dict[str, Any]:
        """
        Get information about this processor.
        
        Returns:
            Dictionary containing processor information
        """
        return {
            'name': self.processor_name,
            'supported_extensions': self.supported_extensions,
            'mistral_ocr_available': self.mistral_available,
            'one_extract_fallback_available': ONENOTE_FALLBACK_AVAILABLE,
            'features': [
                'Primary: Mistral AI OCR processing for superior table/image interpretation',
                'Advanced content extraction (tables, images, complex layouts)',
                'Metadata extraction (pages, sections, dates)',
                'HTML conversion for better OCR processing',
                'Embedded file extraction',
                'Password-protected file support',
                'Fallback: one-extract library for basic text extraction',
                'Multi-method processing pipeline'
            ]
        }
