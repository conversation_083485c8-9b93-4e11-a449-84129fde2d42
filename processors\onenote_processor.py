#!/usr/bin/env python3
"""
OneNote Document Processor

This module provides functionality to extract text and metadata from Microsoft OneNote (.one) files
using Mistral AI OCR as the primary processing method. Mistral OCR can interpret tables, images,
and complex content better than traditional text extraction methods.
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List
import uuid
import asyncio
from datetime import datetime

# Mistral OCR for primary processing
from utils.mistral_ocr import MistralOCRProcessor

# OneNote extraction library as fallback
try:
    from one_extract import OneNoteExtractor, OneNoteExtractorError
    ONENOTE_FALLBACK_AVAILABLE = True
except ImportError:
    ONENOTE_FALLBACK_AVAILABLE = False

logger = logging.getLogger(__name__)


class OneNoteProcessor:
    """
    Processor for Microsoft OneNote (.one) files.

    Uses Mistral AI OCR as the primary processing method to extract text, tables, images,
    and complex content from OneNote documents. Falls back to one-extract library if needed.
    """

    def __init__(self):
        """Initialize the OneNote processor."""
        # Initialize Mistral OCR processor
        try:
            self.mistral_ocr = MistralOCRProcessor()
            self.mistral_available = True
            logger.info("Initialized OneNote processor with Mistral OCR")
        except Exception as e:
            self.mistral_available = False
            logger.warning(f"Mistral OCR not available for OneNote processing: {e}")

        if not ONENOTE_FALLBACK_AVAILABLE:
            logger.warning("one-extract library not available as fallback. OneNote processing will be limited.")

        self.supported_extensions = ['.one']
        self.processor_name = "OneNote Processor (Mistral OCR)"
    
    async def extract_text(self, file_path: str, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract text and metadata from a OneNote file using Mistral AI OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted OneNote files

        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_path = Path(file_path)

            if not file_path.exists():
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'text': '',
                    'metadata': {}
                }

            logger.info(f"Processing OneNote file with Mistral OCR: {file_path}")

            # Method 1: Convert OneNote to PDF and process with Mistral OCR (primary method)
            if self.mistral_available:
                try:
                    logger.info("Attempting OneNote to PDF conversion for Mistral OCR processing")
                    result = await self._process_via_pdf_conversion(file_path, password)
                    if result['success']:
                        return result
                    else:
                        logger.warning("PDF conversion method failed, trying HTML conversion method")

                except Exception as e:
                    logger.warning(f"PDF conversion method failed: {e}, trying HTML conversion method")

            # Method 2: Convert OneNote to images and process with Mistral OCR (fallback)
            logger.info("Attempting OneNote to image conversion for Mistral OCR processing")
            result = await self._process_via_image_conversion(file_path, password)
            if result['success']:
                return result

            # Method 3: Use one-extract library as final fallback
            if ONENOTE_FALLBACK_AVAILABLE:
                logger.info("Attempting fallback processing with one-extract library")
                result = await self._process_with_one_extract(file_path, password)
                if result['success']:
                    return result

            # If all methods fail
            return {
                'success': False,
                'error': 'All OneNote processing methods failed. File may be corrupted or unsupported.',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error processing OneNote file {file_path}: {e}")
            return {
                'success': False,
                'error': f'OneNote processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }
    
    async def _process_via_pdf_conversion(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert OneNote content to PDF and process with Mistral OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # Extract OneNote content first
            if not ONENOTE_FALLBACK_AVAILABLE:
                return {
                    'success': False,
                    'error': 'OneNote PDF conversion requires one-extract library for content extraction',
                    'text': '',
                    'metadata': {}
                }

            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Extract content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.warning(f"OneNote extraction failed during PDF conversion: {e}")
                return {
                    'success': False,
                    'error': f'OneNote file extraction failed: {e}',
                    'text': '',
                    'metadata': {}
                }

            # Create a PDF from OneNote content
            pdf_path = await self._create_pdf_from_onenote(extractor, file_path)

            if pdf_path and pdf_path.exists():
                try:
                    # Process the PDF with Mistral OCR
                    logger.info(f"Processing generated PDF with Mistral OCR: {pdf_path}")
                    extracted_text = await self.mistral_ocr.extract_text_from_document(str(pdf_path))

                    if extracted_text and len(extracted_text.strip()) > 20:
                        logger.info(f"Successfully processed OneNote via PDF conversion: {len(extracted_text)} characters")

                        # Extract metadata using one-extract
                        metadata = await self._extract_metadata_from_extractor(extractor, file_path)

                        word_count = len(extracted_text.split())
                        char_count = len(extracted_text)

                        return {
                            'success': True,
                            'text': extracted_text,
                            'metadata': {
                                **metadata,
                                'processor': self.processor_name,
                                'file_size': file_path.stat().st_size,
                                'word_count': word_count,
                                'character_count': char_count,
                                'extraction_timestamp': datetime.now().isoformat(),
                                'processing_method': 'mistral_ocr_via_pdf_conversion',
                                'password_protected': password is not None
                            },
                            'ocr_provider': 'mistral-ocr',
                            'processing_time': 0
                        }

                finally:
                    # Clean up temporary PDF file
                    try:
                        pdf_path.unlink()
                    except Exception as e:
                        logger.warning(f"Failed to clean up temporary PDF file {pdf_path}: {e}")

            return {
                'success': False,
                'error': 'PDF conversion method did not produce sufficient content',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error in OneNote PDF conversion processing: {e}")
            return {
                'success': False,
                'error': f'OneNote PDF conversion processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    async def _create_pdf_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> Optional[Path]:
        """
        Create a PDF document from OneNote content.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            Path to the created PDF file or None if failed
        """
        try:
            # Try to use reportlab to create a PDF
            try:
                from reportlab.lib.pagesizes import letter
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch

                # Create temporary PDF file
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                    pdf_path = Path(temp_file.name)

                # Create PDF document
                doc = SimpleDocTemplate(str(pdf_path), pagesize=letter)
                styles = getSampleStyleSheet()
                story = []

                # Add title
                title_style = ParagraphStyle(
                    'CustomTitle',
                    parent=styles['Heading1'],
                    fontSize=16,
                    spaceAfter=30,
                )
                story.append(Paragraph(f"OneNote Document: {file_path.name}", title_style))
                story.append(Spacer(1, 12))

                # Add pages/sections
                for meta_obj in extractor.extract_meta():
                    if meta_obj.title and meta_obj.title.strip():
                        # Add section header
                        story.append(Paragraph(meta_obj.title, styles['Heading2']))
                        story.append(Spacer(1, 6))

                        # Add metadata
                        if meta_obj.creation_date:
                            story.append(Paragraph(f"Created: {meta_obj.creation_date}", styles['Normal']))
                        if meta_obj.last_modification_date:
                            story.append(Paragraph(f"Modified: {meta_obj.last_modification_date}", styles['Normal']))

                        story.append(Spacer(1, 12))

                        # Add placeholder content (since we can't extract actual page content easily)
                        story.append(Paragraph("OneNote page content - tables, images, and text will be processed by Mistral OCR", styles['Normal']))
                        story.append(Spacer(1, 12))

                # Add embedded files content
                embedded_count = 0
                for file_data in extractor.extract_files():
                    embedded_count += 1
                    if self._is_text_file(file_data):
                        try:
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                story.append(Paragraph(f"Embedded Content {embedded_count}", styles['Heading3']))
                                story.append(Paragraph(text_content[:1000], styles['Normal']))  # Limit length
                                story.append(Spacer(1, 12))
                        except Exception:
                            pass

                # Build PDF
                doc.build(story)

                logger.info(f"Created PDF from OneNote content: {pdf_path}")
                return pdf_path

            except ImportError:
                logger.warning("reportlab not available, trying alternative PDF creation method")
                return await self._create_simple_pdf_from_onenote(extractor, file_path)

        except Exception as e:
            logger.error(f"Error creating PDF from OneNote: {e}")
            return None

    async def _create_simple_pdf_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> Optional[Path]:
        """
        Create a simple text-based PDF from OneNote content using basic methods.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            Path to the created PDF file or None if failed
        """
        try:
            # Create a simple text representation and save as a text file
            # Then we'll rename it to .pdf (Mistral OCR might still process it)

            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_path = Path(temp_file.name)

                # Write OneNote content as structured text
                temp_file.write(f"OneNote Document: {file_path.name}\n")
                temp_file.write("=" * 50 + "\n\n")

                # Add pages/sections
                for meta_obj in extractor.extract_meta():
                    if meta_obj.title and meta_obj.title.strip():
                        temp_file.write(f"Section: {meta_obj.title}\n")
                        temp_file.write("-" * 30 + "\n")

                        if meta_obj.creation_date:
                            temp_file.write(f"Created: {meta_obj.creation_date}\n")
                        if meta_obj.last_modification_date:
                            temp_file.write(f"Modified: {meta_obj.last_modification_date}\n")

                        temp_file.write("\nOneNote page content - tables, images, and text will be processed by Mistral OCR\n\n")

                # Add embedded files content
                embedded_count = 0
                for file_data in extractor.extract_files():
                    embedded_count += 1
                    if self._is_text_file(file_data):
                        try:
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                temp_file.write(f"Embedded Content {embedded_count}:\n")
                                temp_file.write(text_content[:1000] + "\n\n")  # Limit length
                        except Exception:
                            pass

            # Rename to .pdf extension (some OCR systems are more flexible)
            pdf_path = temp_path.with_suffix('.pdf')
            temp_path.rename(pdf_path)

            logger.info(f"Created simple PDF from OneNote content: {pdf_path}")
            return pdf_path

        except Exception as e:
            logger.error(f"Error creating simple PDF from OneNote: {e}")
            return None

    async def _process_via_image_conversion(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Convert OneNote file to images and process with Mistral OCR.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # For now, we'll use the one-extract library to extract content and then
            # create a temporary document that Mistral can process better
            if not ONENOTE_FALLBACK_AVAILABLE:
                return {
                    'success': False,
                    'error': 'OneNote conversion requires one-extract library for content extraction',
                    'text': '',
                    'metadata': {}
                }

            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Extract basic content with one-extract
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                logger.warning(f"OneNote extraction failed during conversion: {e}")
                return {
                    'success': False,
                    'error': f'OneNote file extraction failed: {e}',
                    'text': '',
                    'metadata': {}
                }

            # Create a temporary HTML file with the OneNote content structure
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as temp_file:
                temp_html_path = temp_file.name

                # Create HTML representation of OneNote content
                html_content = self._create_html_from_onenote(extractor, file_path)
                temp_file.write(html_content)

            try:
                # Process the HTML file with Mistral OCR
                if self.mistral_available:
                    extracted_text = await self.mistral_ocr.extract_text_from_document(temp_html_path)

                    if extracted_text and len(extracted_text.strip()) > 20:
                        logger.info(f"Successfully processed OneNote via HTML conversion: {len(extracted_text)} characters")

                        # Extract metadata using one-extract
                        metadata = await self._extract_metadata_from_extractor(extractor, file_path)

                        word_count = len(extracted_text.split())
                        char_count = len(extracted_text)

                        return {
                            'success': True,
                            'text': extracted_text,
                            'metadata': {
                                **metadata,
                                'processor': self.processor_name,
                                'file_size': file_path.stat().st_size,
                                'word_count': word_count,
                                'character_count': char_count,
                                'extraction_timestamp': datetime.now().isoformat(),
                                'processing_method': 'mistral_ocr_via_html_conversion',
                                'password_protected': password is not None
                            },
                            'ocr_provider': 'mistral-ocr',
                            'processing_time': 0
                        }

            finally:
                # Clean up temporary file
                try:
                    Path(temp_html_path).unlink()
                except Exception as e:
                    logger.warning(f"Failed to clean up temporary file {temp_html_path}: {e}")

            return {
                'success': False,
                'error': 'HTML conversion method did not produce sufficient content',
                'text': '',
                'metadata': {}
            }

        except Exception as e:
            logger.error(f"Error in OneNote image conversion processing: {e}")
            return {
                'success': False,
                'error': f'OneNote conversion processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    def _create_html_from_onenote(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Create an HTML representation of OneNote content for better Mistral OCR processing.

        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the original file

        Returns:
            HTML content string
        """
        html_parts = [
            '<!DOCTYPE html>',
            '<html>',
            '<head>',
            f'<title>{file_path.stem}</title>',
            '<meta charset="utf-8">',
            '</head>',
            '<body>',
            f'<h1>OneNote Document: {file_path.name}</h1>'
        ]

        try:
            # Add metadata sections
            for meta_obj in extractor.extract_meta():
                if meta_obj.title and meta_obj.title.strip():
                    html_parts.append(f'<h2>{meta_obj.title}</h2>')
                    if meta_obj.creation_date:
                        html_parts.append(f'<p><em>Created: {meta_obj.creation_date}</em></p>')
                    if meta_obj.last_modification_date:
                        html_parts.append(f'<p><em>Modified: {meta_obj.last_modification_date}</em></p>')
                    html_parts.append('<div class="page-content">')
                    html_parts.append('<p>[OneNote page content - processed by Mistral OCR]</p>')
                    html_parts.append('</div>')

            # Add embedded files section
            embedded_count = 0
            for file_data in extractor.extract_files():
                embedded_count += 1
                if self._is_text_file(file_data):
                    try:
                        text_content = file_data.decode('utf-8', errors='ignore')
                        if text_content.strip():
                            html_parts.append(f'<h3>Embedded Content {embedded_count}</h3>')
                            html_parts.append(f'<pre>{text_content}</pre>')
                    except Exception:
                        pass

        except Exception as e:
            logger.warning(f"Error creating HTML from OneNote: {e}")
            html_parts.append('<p>Error extracting OneNote content structure</p>')

        html_parts.extend(['</body>', '</html>'])
        return '\n'.join(html_parts)

    async def _process_with_one_extract(self, file_path: Path, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Process OneNote file using one-extract library as fallback.

        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted files

        Returns:
            Processing result dictionary
        """
        try:
            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()

            # Initialize OneNote extractor
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                if "password" in str(e).lower():
                    return {
                        'success': False,
                        'error': f'Password required for encrypted OneNote file: {e}',
                        'text': '',
                        'metadata': {}
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Failed to initialize OneNote extractor: {e}',
                        'text': '',
                        'metadata': {}
                    }

            # Extract metadata
            metadata = await self._extract_metadata_from_extractor(extractor, file_path)

            # Extract text content
            extracted_text = await self._extract_text_content_from_extractor(extractor, file_path)

            # Calculate processing statistics
            word_count = len(extracted_text.split()) if extracted_text else 0
            char_count = len(extracted_text) if extracted_text else 0

            result = {
                'success': True,
                'text': extracted_text,
                'metadata': {
                    **metadata,
                    'processor': self.processor_name,
                    'file_size': file_path.stat().st_size,
                    'word_count': word_count,
                    'character_count': char_count,
                    'extraction_timestamp': datetime.now().isoformat(),
                    'processing_method': 'one_extract_fallback',
                    'password_protected': password is not None
                },
                'ocr_provider': 'one-extract',
                'processing_time': 0
            }

            logger.info(f"Successfully processed OneNote with one-extract fallback: {word_count} words, {char_count} characters")
            return result

        except Exception as e:
            logger.error(f"Error in one-extract fallback processing: {e}")
            return {
                'success': False,
                'error': f'OneNote fallback processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }

    async def _extract_metadata_from_extractor(self, extractor: 'OneNoteExtractor', file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'title': file_path.stem,
            'filename': file_path.name,
            'file_type': 'OneNote',
            'pages': [],
            'sections': [],
            'creation_dates': [],
            'modification_dates': []
        }
        
        try:
            # Extract OneNote metadata objects
            for meta_obj in extractor.extract_meta():
                page_info = {
                    'object_id': meta_obj.object_id,
                    'title': meta_obj.title,
                    'title_size': meta_obj.title_size,
                    'offset': meta_obj.offset,
                    'creation_date': meta_obj.creation_date.isoformat() if meta_obj.creation_date else None,
                    'last_modification_date': meta_obj.last_modification_date.isoformat() if meta_obj.last_modification_date else None
                }
                
                metadata['pages'].append(page_info)
                
                # Collect dates for overall metadata
                if meta_obj.creation_date:
                    metadata['creation_dates'].append(meta_obj.creation_date.isoformat())
                if meta_obj.last_modification_date:
                    metadata['modification_dates'].append(meta_obj.last_modification_date.isoformat())
                
                # Use page titles as sections
                if meta_obj.title and meta_obj.title.strip():
                    metadata['sections'].append(meta_obj.title.strip())
            
            # Set overall document metadata
            if metadata['creation_dates']:
                metadata['created_date'] = min(metadata['creation_dates'])
            if metadata['modification_dates']:
                metadata['modified_date'] = max(metadata['modification_dates'])
            
            metadata['page_count'] = len(metadata['pages'])
            metadata['section_count'] = len(set(metadata['sections']))  # Unique sections
            
            logger.info(f"Extracted metadata for {metadata['page_count']} pages")
            
        except Exception as e:
            logger.warning(f"Error extracting OneNote metadata: {e}")
            # Continue with basic metadata
        
        return metadata

    async def _extract_text_content_from_extractor(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Extract text content from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Extracted text content
        """
        extracted_text = ""
        embedded_files_count = 0
        
        try:
            # Extract embedded files (which may contain text)
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                for index, file_data in enumerate(extractor.extract_files()):
                    embedded_files_count += 1
                    
                    # Save embedded file temporarily
                    temp_file = temp_path / f"embedded_{index}.bin"
                    with open(temp_file, 'wb') as f:
                        f.write(file_data)
                    
                    # Try to extract text from embedded file if it's a text-based format
                    try:
                        # Check if it's a text file
                        if self._is_text_file(file_data):
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                extracted_text += f"\n\n--- Embedded Content {index + 1} ---\n"
                                extracted_text += text_content
                    except Exception as e:
                        logger.debug(f"Could not extract text from embedded file {index}: {e}")
            
            # If no text was extracted from embedded files, create a summary
            if not extracted_text.strip():
                # Create a basic text representation based on metadata
                metadata_text = f"OneNote Document: {file_path.name}\n\n"
                
                # Add page information if available
                try:
                    page_titles = []
                    for meta_obj in extractor.extract_meta():
                        if meta_obj.title and meta_obj.title.strip():
                            page_titles.append(meta_obj.title.strip())
                    
                    if page_titles:
                        metadata_text += "Pages/Sections:\n"
                        for i, title in enumerate(page_titles, 1):
                            metadata_text += f"{i}. {title}\n"
                        metadata_text += "\n"
                    
                    metadata_text += f"This OneNote document contains {len(page_titles)} pages/sections"
                    if embedded_files_count > 0:
                        metadata_text += f" and {embedded_files_count} embedded objects"
                    metadata_text += ".\n"
                    
                    extracted_text = metadata_text
                    
                except Exception as e:
                    logger.debug(f"Error creating metadata text: {e}")
                    extracted_text = f"OneNote Document: {file_path.name}\n\nThis OneNote document was processed but text extraction was limited."
            
            logger.info(f"Extracted text from OneNote file with {embedded_files_count} embedded objects")
            
        except Exception as e:
            logger.warning(f"Error extracting text content from OneNote: {e}")
            extracted_text = f"OneNote Document: {file_path.name}\n\nError during text extraction: {str(e)}"
        
        return extracted_text
    
    def _is_text_file(self, data: bytes) -> bool:
        """
        Check if binary data appears to be text-based.
        
        Args:
            data: Binary data to check
            
        Returns:
            True if data appears to be text
        """
        try:
            # Try to decode as UTF-8
            text = data.decode('utf-8')
            # Check if it contains mostly printable characters
            printable_ratio = sum(1 for c in text if c.isprintable() or c.isspace()) / len(text)
            return printable_ratio > 0.7  # At least 70% printable characters
        except (UnicodeDecodeError, ZeroDivisionError):
            return False
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported extensions
        """
        return self.supported_extensions
    
    def get_processor_info(self) -> Dict[str, Any]:
        """
        Get information about this processor.
        
        Returns:
            Dictionary containing processor information
        """
        return {
            'name': self.processor_name,
            'supported_extensions': self.supported_extensions,
            'mistral_ocr_available': self.mistral_available,
            'one_extract_fallback_available': ONENOTE_FALLBACK_AVAILABLE,
            'features': [
                'Primary: Mistral AI OCR processing for superior table/image interpretation',
                'Advanced content extraction (tables, images, complex layouts)',
                'Metadata extraction (pages, sections, dates)',
                'HTML conversion for better OCR processing',
                'Embedded file extraction',
                'Password-protected file support',
                'Fallback: one-extract library for basic text extraction',
                'Multi-method processing pipeline'
            ]
        }
