#!/usr/bin/env python3
"""
OneNote Document Processor

This module provides functionality to extract text and metadata from Microsoft OneNote (.one) files
using the one-extract library. It follows the same interface as other document processors.
"""

import logging
import tempfile
from pathlib import Path
from typing import Dict, Any, Optional, List
import uuid
import asyncio
from datetime import datetime

# OneNote extraction library
try:
    from one_extract import OneNoteExtractor, OneNoteExtractorError
    ONENOTE_AVAILABLE = True
except ImportError:
    ONENOTE_AVAILABLE = False

logger = logging.getLogger(__name__)


class OneNoteProcessor:
    """
    Processor for Microsoft OneNote (.one) files.
    
    Extracts text content, metadata, and embedded files from OneNote documents
    using the one-extract library.
    """
    
    def __init__(self):
        """Initialize the OneNote processor."""
        if not ONENOTE_AVAILABLE:
            logger.warning("one-extract library not available. OneNote processing will be limited.")
        
        self.supported_extensions = ['.one']
        self.processor_name = "OneNote Processor"
    
    async def extract_text(self, file_path: str, password: Optional[str] = None) -> Dict[str, Any]:
        """
        Extract text and metadata from a OneNote file.
        
        Args:
            file_path: Path to the OneNote file
            password: Optional password for encrypted OneNote files
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            if not ONENOTE_AVAILABLE:
                return {
                    'success': False,
                    'error': 'one-extract library not installed. Install with: pip install one-extract',
                    'text': '',
                    'metadata': {}
                }
            
            file_path = Path(file_path)
            
            if not file_path.exists():
                return {
                    'success': False,
                    'error': f'File not found: {file_path}',
                    'text': '',
                    'metadata': {}
                }
            
            logger.info(f"Processing OneNote file: {file_path}")
            
            # Read the OneNote file
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            # Initialize OneNote extractor
            try:
                extractor = OneNoteExtractor(data=file_data, password=password)
            except OneNoteExtractorError as e:
                if "password" in str(e).lower():
                    return {
                        'success': False,
                        'error': f'Password required for encrypted OneNote file: {e}',
                        'text': '',
                        'metadata': {}
                    }
                else:
                    return {
                        'success': False,
                        'error': f'Failed to initialize OneNote extractor: {e}',
                        'text': '',
                        'metadata': {}
                    }
            
            # Extract metadata
            metadata = await self._extract_metadata(extractor, file_path)
            
            # Extract embedded files and text content
            extracted_text = await self._extract_text_content(extractor, file_path)
            
            # Calculate processing statistics
            word_count = len(extracted_text.split()) if extracted_text else 0
            char_count = len(extracted_text) if extracted_text else 0
            
            result = {
                'success': True,
                'text': extracted_text,
                'metadata': {
                    **metadata,
                    'processor': self.processor_name,
                    'file_size': file_path.stat().st_size,
                    'word_count': word_count,
                    'character_count': char_count,
                    'extraction_timestamp': datetime.now().isoformat(),
                    'password_protected': password is not None
                },
                'ocr_provider': 'one-extract',
                'processing_time': 0  # Will be calculated by caller
            }
            
            logger.info(f"Successfully processed OneNote file: {file_path}")
            logger.info(f"Extracted {word_count} words, {char_count} characters")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing OneNote file {file_path}: {e}")
            return {
                'success': False,
                'error': f'OneNote processing error: {str(e)}',
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata(self, extractor: 'OneNoteExtractor', file_path: Path) -> Dict[str, Any]:
        """
        Extract metadata from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Dictionary containing metadata
        """
        metadata = {
            'title': file_path.stem,
            'filename': file_path.name,
            'file_type': 'OneNote',
            'pages': [],
            'sections': [],
            'creation_dates': [],
            'modification_dates': []
        }
        
        try:
            # Extract OneNote metadata objects
            for meta_obj in extractor.extract_meta():
                page_info = {
                    'object_id': meta_obj.object_id,
                    'title': meta_obj.title,
                    'title_size': meta_obj.title_size,
                    'offset': meta_obj.offset,
                    'creation_date': meta_obj.creation_date.isoformat() if meta_obj.creation_date else None,
                    'last_modification_date': meta_obj.last_modification_date.isoformat() if meta_obj.last_modification_date else None
                }
                
                metadata['pages'].append(page_info)
                
                # Collect dates for overall metadata
                if meta_obj.creation_date:
                    metadata['creation_dates'].append(meta_obj.creation_date.isoformat())
                if meta_obj.last_modification_date:
                    metadata['modification_dates'].append(meta_obj.last_modification_date.isoformat())
                
                # Use page titles as sections
                if meta_obj.title and meta_obj.title.strip():
                    metadata['sections'].append(meta_obj.title.strip())
            
            # Set overall document metadata
            if metadata['creation_dates']:
                metadata['created_date'] = min(metadata['creation_dates'])
            if metadata['modification_dates']:
                metadata['modified_date'] = max(metadata['modification_dates'])
            
            metadata['page_count'] = len(metadata['pages'])
            metadata['section_count'] = len(set(metadata['sections']))  # Unique sections
            
            logger.info(f"Extracted metadata for {metadata['page_count']} pages")
            
        except Exception as e:
            logger.warning(f"Error extracting OneNote metadata: {e}")
            # Continue with basic metadata
        
        return metadata
    
    async def _extract_text_content(self, extractor: 'OneNoteExtractor', file_path: Path) -> str:
        """
        Extract text content from OneNote file.
        
        Args:
            extractor: OneNoteExtractor instance
            file_path: Path to the file
            
        Returns:
            Extracted text content
        """
        extracted_text = ""
        embedded_files_count = 0
        
        try:
            # Extract embedded files (which may contain text)
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                for index, file_data in enumerate(extractor.extract_files()):
                    embedded_files_count += 1
                    
                    # Save embedded file temporarily
                    temp_file = temp_path / f"embedded_{index}.bin"
                    with open(temp_file, 'wb') as f:
                        f.write(file_data)
                    
                    # Try to extract text from embedded file if it's a text-based format
                    try:
                        # Check if it's a text file
                        if self._is_text_file(file_data):
                            text_content = file_data.decode('utf-8', errors='ignore')
                            if text_content.strip():
                                extracted_text += f"\n\n--- Embedded Content {index + 1} ---\n"
                                extracted_text += text_content
                    except Exception as e:
                        logger.debug(f"Could not extract text from embedded file {index}: {e}")
            
            # If no text was extracted from embedded files, create a summary
            if not extracted_text.strip():
                # Create a basic text representation based on metadata
                metadata_text = f"OneNote Document: {file_path.name}\n\n"
                
                # Add page information if available
                try:
                    page_titles = []
                    for meta_obj in extractor.extract_meta():
                        if meta_obj.title and meta_obj.title.strip():
                            page_titles.append(meta_obj.title.strip())
                    
                    if page_titles:
                        metadata_text += "Pages/Sections:\n"
                        for i, title in enumerate(page_titles, 1):
                            metadata_text += f"{i}. {title}\n"
                        metadata_text += "\n"
                    
                    metadata_text += f"This OneNote document contains {len(page_titles)} pages/sections"
                    if embedded_files_count > 0:
                        metadata_text += f" and {embedded_files_count} embedded objects"
                    metadata_text += ".\n"
                    
                    extracted_text = metadata_text
                    
                except Exception as e:
                    logger.debug(f"Error creating metadata text: {e}")
                    extracted_text = f"OneNote Document: {file_path.name}\n\nThis OneNote document was processed but text extraction was limited."
            
            logger.info(f"Extracted text from OneNote file with {embedded_files_count} embedded objects")
            
        except Exception as e:
            logger.warning(f"Error extracting text content from OneNote: {e}")
            extracted_text = f"OneNote Document: {file_path.name}\n\nError during text extraction: {str(e)}"
        
        return extracted_text
    
    def _is_text_file(self, data: bytes) -> bool:
        """
        Check if binary data appears to be text-based.
        
        Args:
            data: Binary data to check
            
        Returns:
            True if data appears to be text
        """
        try:
            # Try to decode as UTF-8
            text = data.decode('utf-8')
            # Check if it contains mostly printable characters
            printable_ratio = sum(1 for c in text if c.isprintable() or c.isspace()) / len(text)
            return printable_ratio > 0.7  # At least 70% printable characters
        except (UnicodeDecodeError, ZeroDivisionError):
            return False
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of supported file extensions.
        
        Returns:
            List of supported extensions
        """
        return self.supported_extensions
    
    def get_processor_info(self) -> Dict[str, Any]:
        """
        Get information about this processor.
        
        Returns:
            Dictionary containing processor information
        """
        return {
            'name': self.processor_name,
            'supported_extensions': self.supported_extensions,
            'library_available': ONENOTE_AVAILABLE,
            'features': [
                'Text extraction from OneNote pages',
                'Metadata extraction (pages, sections, dates)',
                'Embedded file extraction',
                'Password-protected file support',
                'Page/section structure analysis'
            ]
        }
