/**
 * Entities management for Graphiti Knowledge Graph
 *
 * This script handles the display and filtering of entities extracted from documents.
 */

// Global variables
let allEntities = [];
let entityTypes = [];
let currentEntityType = 'All';
let minMentions = 0;
let minConfidence = 0;
let dateFilter = 'all';
let startDate = null;
let endDate = null;
let currentPage = 1;
let itemsPerPage = 100;
let totalPages = 1;

// DOM elements
const entityList = document.getElementById('entityList');
const entitySearch = document.getElementById('entitySearch');
const entityTypeSearch = document.getElementById('entityTypeSearch');
const entityTypeBadges = document.getElementById('entityTypeBadges');
const minMentionsInput = document.getElementById('minMentions');
const minConfidenceInput = document.getElementById('minConfidence');
const confidenceValueDisplay = document.getElementById('confidenceValue');
const dateFilterSelect = document.getElementById('dateFilter');
const customDateRangeDiv = document.getElementById('customDateRange');
const startDateInput = document.getElementById('startDate');
const endDateInput = document.getElementById('endDate');
const applyFiltersButton = document.getElementById('applyFilters');
const resetFiltersButton = document.getElementById('resetFilters');
const loadingSpinner = document.getElementById('loadingSpinner');
const errorMessage = document.getElementById('errorMessage');
const errorText = document.getElementById('errorText');

// Initialize the page
document.addEventListener('DOMContentLoaded', () => {
    // Load entity types
    loadEntityTypes();

    // Load entities
    loadEntities();

    // Set up event listeners
    setupEventListeners();
});

/**
 * Set up event listeners for the page
 */
function setupEventListeners() {
    // Entity search
    entitySearch.addEventListener('input', filterEntities);

    // Entity type search
    entityTypeSearch.addEventListener('input', filterEntityTypes);

    // Confidence slider
    minConfidenceInput.addEventListener('input', () => {
        minConfidence = parseFloat(minConfidenceInput.value);
        confidenceValueDisplay.textContent = minConfidence.toFixed(2);
    });

    // Date filter
    dateFilterSelect.addEventListener('change', () => {
        dateFilter = dateFilterSelect.value;

        // Show/hide custom date range inputs
        if (dateFilter === 'custom') {
            customDateRangeDiv.style.display = 'block';
        } else {
            customDateRangeDiv.style.display = 'none';
        }
    });

    // Apply filters button
    applyFiltersButton.addEventListener('click', () => {
        // Get filter values
        minMentions = parseInt(minMentionsInput.value) || 0;
        minConfidence = parseFloat(minConfidenceInput.value);
        dateFilter = dateFilterSelect.value;

        // Get custom date range if selected
        if (dateFilter === 'custom') {
            startDate = startDateInput.value ? new Date(startDateInput.value) : null;
            endDate = endDateInput.value ? new Date(endDateInput.value) : null;
        } else {
            startDate = null;
            endDate = null;
        }

        // Reset to first page
        currentPage = 1;

        // Load entities with filters
        loadEntities();
    });

    // Reset filters button
    resetFiltersButton.addEventListener('click', () => {
        // Reset filter values
        minMentionsInput.value = 0;
        minMentions = 0;

        minConfidenceInput.value = 0;
        minConfidence = 0;
        confidenceValueDisplay.textContent = '0';

        dateFilterSelect.value = 'all';
        dateFilter = 'all';
        customDateRangeDiv.style.display = 'none';

        startDateInput.value = '';
        endDateInput.value = '';
        startDate = null;
        endDate = null;

        // Reset to first page
        currentPage = 1;

        // Load entities without filters
        loadEntities();
    });
}

/**
 * Load entity types from the API
 */
async function loadEntityTypes() {
    try {
        const response = await fetch('/api/entity-counts');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        entityTypes = data.counts;

        // Render entity type badges
        renderEntityTypeBadges();
    } catch (error) {
        console.error('Error loading entity types:', error);
        showError('Failed to load entity types');
    }
}

/**
 * Render entity type badges
 */
function renderEntityTypeBadges() {
    // Clear existing badges except "All Types"
    entityTypeBadges.innerHTML = '<div class="entity-type-badge badge badge-primary active" data-type="All">All Types</div>';

    // Add badges for each entity type
    entityTypes.forEach(type => {
        const badge = document.createElement('div');
        badge.className = 'entity-type-badge badge badge-secondary';
        badge.setAttribute('data-type', type.type);
        badge.textContent = `${type.type} (${type.count})`;
        badge.addEventListener('click', () => {
            // Set active badge
            document.querySelectorAll('.entity-type-badge').forEach(b => b.classList.remove('active'));
            badge.classList.add('active');

            // Update current entity type
            currentEntityType = type.type;

            // Load entities for this type
            loadEntities();
        });

        entityTypeBadges.appendChild(badge);
    });

    // Add click event for "All Types" badge
    document.querySelector('.entity-type-badge[data-type="All"]').addEventListener('click', () => {
        // Set active badge
        document.querySelectorAll('.entity-type-badge').forEach(b => b.classList.remove('active'));
        document.querySelector('.entity-type-badge[data-type="All"]').classList.add('active');

        // Update current entity type
        currentEntityType = 'All';

        // Load all entities
        loadEntities();
    });
}

/**
 * Filter entity types based on search input
 */
function filterEntityTypes() {
    const searchTerm = entityTypeSearch.value.toLowerCase();

    // Filter badges
    document.querySelectorAll('.entity-type-badge').forEach(badge => {
        const type = badge.getAttribute('data-type');
        if (type === 'All' || type.toLowerCase().includes(searchTerm)) {
            badge.style.display = 'inline-block';
        } else {
            badge.style.display = 'none';
        }
    });
}

/**
 * Load entities from the API
 */
async function loadEntities() {
    // Show loading spinner
    loadingSpinner.style.display = 'flex';
    entityList.innerHTML = '';
    errorMessage.style.display = 'none';

    try {
        // Build URL
        let url = '/api/entities';
        const params = new URLSearchParams();

        // Add entity type filter
        if (currentEntityType !== 'All') {
            params.append('entity_type', currentEntityType);

            // For specific entity types, request all entities of that type
            params.append('all', 'true');
        } else {
            // For "All" types, use a larger limit to get more entities
            params.append('limit', 1000);
        }

        // Add mention count filter
        if (minMentions > 0) {
            params.append('min_mentions', minMentions);
        }

        // Add confidence filter
        if (minConfidence > 0) {
            params.append('min_confidence', minConfidence);
        }

        // Log the request URL for debugging
        console.log('Loading entities with URL:', url + (params.toString() ? `?${params.toString()}` : ''));

        // Add date filter
        if (dateFilter !== 'all') {
            // Calculate date range based on filter
            const now = new Date();
            let filterStartDate = null;

            switch (dateFilter) {
                case 'today':
                    filterStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
                    break;
                case 'week':
                    filterStartDate = new Date(now);
                    filterStartDate.setDate(now.getDate() - now.getDay());
                    break;
                case 'month':
                    filterStartDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'year':
                    filterStartDate = new Date(now.getFullYear(), 0, 1);
                    break;
                case 'custom':
                    if (startDate) {
                        filterStartDate = startDate;
                    }
                    break;
            }

            // Add date parameters if we have dates
            if (filterStartDate) {
                params.append('start_date', filterStartDate.toISOString().split('T')[0]);
            }

            if (dateFilter === 'custom' && endDate) {
                params.append('end_date', endDate.toISOString().split('T')[0]);
            }
        }

        // Add pagination
        params.append('offset', (currentPage - 1) * itemsPerPage);
        params.append('limit', itemsPerPage);

        if (params.toString()) {
            url += `?${params.toString()}`;
        }

        // Fetch entities
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        allEntities = data.entities;

        // Store the response data for reference
        window.lastEntityResponse = data;

        // Log entity counts
        console.log('Entity counts:', data.type_counts);
        console.log(`Loaded ${allEntities.length} entities out of ${data.count} total`);

        // Update pagination info if available
        if (data.pagination) {
            totalPages = data.pagination.total_pages;
            currentPage = data.pagination.page;
        }

        // Render entities
        renderEntities();

        // Render pagination controls if needed
        if (totalPages > 1) {
            renderPagination();
        }
    } catch (error) {
        console.error('Error loading entities:', error);
        showError('Failed to fetch');
    } finally {
        // Hide loading spinner
        loadingSpinner.style.display = 'none';
    }
}

/**
 * Render pagination controls
 */
function renderPagination() {
    // Create pagination container if it doesn't exist
    let paginationContainer = document.getElementById('paginationContainer');
    if (!paginationContainer) {
        paginationContainer = document.createElement('div');
        paginationContainer.id = 'paginationContainer';
        paginationContainer.className = 'mt-3';
        entityList.parentNode.appendChild(paginationContainer);
    }

    // Clear existing pagination
    paginationContainer.innerHTML = '';

    // Create pagination nav
    const paginationNav = document.createElement('nav');
    paginationNav.setAttribute('aria-label', 'Entity pagination');

    // Create pagination list
    const paginationList = document.createElement('ul');
    paginationList.className = 'pagination justify-content-center';

    // Previous button
    const prevItem = document.createElement('li');
    prevItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;

    const prevLink = document.createElement('a');
    prevLink.className = 'page-link';
    prevLink.href = '#';
    prevLink.textContent = 'Previous';

    if (currentPage > 1) {
        prevLink.addEventListener('click', (e) => {
            e.preventDefault();
            currentPage--;
            loadEntities();
        });
    }

    prevItem.appendChild(prevLink);
    paginationList.appendChild(prevItem);

    // Page numbers
    const maxPages = Math.min(totalPages, 5);
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + maxPages - 1);

    if (endPage - startPage < maxPages - 1) {
        startPage = Math.max(1, endPage - maxPages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
        const pageItem = document.createElement('li');
        pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;

        const pageLink = document.createElement('a');
        pageLink.className = 'page-link';
        pageLink.href = '#';
        pageLink.textContent = i;

        if (i !== currentPage) {
            pageLink.addEventListener('click', (e) => {
                e.preventDefault();
                currentPage = i;
                loadEntities();
            });
        }

        pageItem.appendChild(pageLink);
        paginationList.appendChild(pageItem);
    }

    // Next button
    const nextItem = document.createElement('li');
    nextItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;

    const nextLink = document.createElement('a');
    nextLink.className = 'page-link';
    nextLink.href = '#';
    nextLink.textContent = 'Next';

    if (currentPage < totalPages) {
        nextLink.addEventListener('click', (e) => {
            e.preventDefault();
            currentPage++;
            loadEntities();
        });
    }

    nextItem.appendChild(nextLink);
    paginationList.appendChild(nextItem);

    // Add pagination to container
    paginationNav.appendChild(paginationList);
    paginationContainer.appendChild(paginationNav);
}

/**
 * Render entities in the list
 */
function renderEntities() {
    // Clear entity list
    entityList.innerHTML = '';

    // Group entities by type
    const entitiesByType = {};
    allEntities.forEach(entity => {
        if (!entitiesByType[entity.type]) {
            entitiesByType[entity.type] = [];
        }
        entitiesByType[entity.type].push(entity);
    });

    // If no entities found
    if (Object.keys(entitiesByType).length === 0) {
        entityList.innerHTML = '<div class="alert alert-info">No entities found.</div>';
        return;
    }

    // Get type counts from API response if available
    const typeCounts = window.lastEntityResponse ? window.lastEntityResponse.type_counts || {} : {};

    // Render entities by type in collapsible boxes
    Object.keys(entitiesByType).sort().forEach((type, index) => {
        const entities = entitiesByType[type];
        const typeId = `entity-type-${type.replace(/\s+/g, '-').toLowerCase()}`;

        // Get the total count for this type from the API response
        const totalCount = typeCounts[type] || entities.length;

        // Create collapsible card for this entity type
        const typeCard = document.createElement('div');
        typeCard.className = 'card mb-3';
        typeCard.innerHTML = `
            <div class="card-header d-flex justify-content-between align-items-center" id="heading-${typeId}">
                <h5 class="mb-0">
                    <button class="btn btn-link" type="button" data-bs-toggle="collapse" data-bs-target="#collapse-${typeId}" aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="collapse-${typeId}">
                        ${type} <span class="badge bg-secondary">${totalCount}</span>
                    </button>
                </h5>
                <div>
                    <span class="entity-count">Showing: ${entities.length} of ${totalCount}</span>
                    <button class="btn btn-sm btn-outline-primary ms-2 expand-all-btn" data-type="${type}">Expand All</button>
                </div>
            </div>

            <div id="collapse-${typeId}" class="collapse ${index === 0 ? 'show' : ''}" aria-labelledby="heading-${typeId}">
                <div class="card-body entity-list-container" data-type="${type}">
                    <!-- Entity cards will be added here -->
                </div>
            </div>
        `;

        entityList.appendChild(typeCard);

        // Get the container for entity cards
        const entityListContainer = typeCard.querySelector(`.entity-list-container[data-type="${type}"]`);

        // Add entity cards (with a limit to prevent browser slowdown)
        const maxEntitiesToShow = 100; // Limit to prevent browser slowdown
        const entitiesToShow = entities.slice(0, maxEntitiesToShow);

        entitiesToShow.forEach(entity => {
            const card = document.createElement('div');
            card.className = 'entity-card card mb-2';
            card.setAttribute('data-entity-id', entity.uuid);
            card.innerHTML = `
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">${entity.name}</h5>
                        <span class="badge bg-info">${entity.type}</span>
                    </div>
                    <p class="card-text mt-2">
                        <small class="text-muted">Confidence: ${entity.confidence ? entity.confidence.toFixed(2) : 'N/A'}</small>
                        <small class="text-muted ms-2">Mentions: ${entity.mention_count || 1}</small>
                    </p>
                    <a href="/entity-detail?uuid=${entity.uuid}" class="btn btn-sm btn-outline-primary">View Details</a>
                </div>
            `;

            entityListContainer.appendChild(card);
        });

        // Add a message if there are more entities than we're showing
        if (entities.length > maxEntitiesToShow) {
            const moreMessage = document.createElement('div');
            moreMessage.className = 'alert alert-info mt-3';
            moreMessage.textContent = `Showing ${maxEntitiesToShow} of ${entities.length} entities. Use the search box to find specific entities.`;
            entityListContainer.appendChild(moreMessage);
        }

        // Add event listener for "Expand All" button
        const expandAllBtn = typeCard.querySelector(`.expand-all-btn[data-type="${type}"]`);
        expandAllBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            const container = typeCard.querySelector(`.entity-list-container[data-type="${type}"]`);
            const collapse = bootstrap.Collapse.getInstance(typeCard.querySelector(`#collapse-${typeId}`));

            if (!collapse || !collapse._isShown()) {
                // If collapsed, expand it
                new bootstrap.Collapse(typeCard.querySelector(`#collapse-${typeId}`), {
                    show: true
                });
                expandAllBtn.textContent = 'Collapse All';
            } else {
                // If expanded, collapse it
                collapse.hide();
                expandAllBtn.textContent = 'Expand All';
            }
        });
    });
}

/**
 * Filter entities based on search input
 */
function filterEntities() {
    const searchTerm = entitySearch.value.toLowerCase();

    // Keep track of entity types with visible entities
    const typesWithVisibleEntities = {};

    // Filter entity cards
    document.querySelectorAll('.entity-card').forEach(card => {
        const name = card.querySelector('.card-title').textContent.toLowerCase();
        const type = card.querySelector('.badge').textContent;

        if (name.includes(searchTerm)) {
            card.style.display = 'block';
            // Count visible entities by type
            typesWithVisibleEntities[type] = (typesWithVisibleEntities[type] || 0) + 1;
        } else {
            card.style.display = 'none';
        }
    });

    // Update entity type cards to show count of visible entities
    document.querySelectorAll('.card.mb-3').forEach(typeCard => {
        const headerButton = typeCard.querySelector('.btn-link');
        const type = headerButton.textContent.trim().split(' ')[0];
        const visibleCount = typesWithVisibleEntities[type] || 0;

        // Update badge count
        const badge = headerButton.querySelector('.badge');
        if (badge) {
            badge.textContent = visibleCount;
        }

        // Update total count
        const totalCountSpan = typeCard.querySelector('.entity-count');
        if (totalCountSpan) {
            totalCountSpan.textContent = `Total: ${visibleCount}`;
        }

        // Show/hide the entire type card based on whether it has visible entities
        if (visibleCount > 0) {
            typeCard.style.display = 'block';

            // If searching, expand all type cards with results
            if (searchTerm) {
                const collapseElement = typeCard.querySelector('.collapse');
                if (collapseElement && !collapseElement.classList.contains('show')) {
                    new bootstrap.Collapse(collapseElement, {
                        show: true
                    });
                }
            }
        } else {
            typeCard.style.display = 'none';
        }
    });

    // If no search results, show a message
    const visibleCards = document.querySelectorAll('.entity-card[style*="display: block"]');
    if (searchTerm && visibleCards.length === 0) {
        // Check if we already have a "no results" message
        let noResultsMsg = document.getElementById('no-search-results');
        if (!noResultsMsg) {
            noResultsMsg = document.createElement('div');
            noResultsMsg.id = 'no-search-results';
            noResultsMsg.className = 'alert alert-info';
            noResultsMsg.textContent = 'No entities found matching your search.';
            entityList.prepend(noResultsMsg);
        }
        noResultsMsg.style.display = 'block';
    } else {
        // Hide the "no results" message if it exists
        const noResultsMsg = document.getElementById('no-search-results');
        if (noResultsMsg) {
            noResultsMsg.style.display = 'none';
        }
    }
}

/**
 * Show error message
 */
function showError(message) {
    errorText.textContent = message;
    errorMessage.style.display = 'block';
    loadingSpinner.style.display = 'none';
}
