"""
Entity processing service for the Graphiti application.

This service handles entity extraction and deduplication for documents.
"""

import logging
import uuid
from datetime import datetime, timezone
from typing import Dict, List, Any

from entity_extraction import extract_entities_from_text
from entity_deduplication import EntityDeduplicator
from database.database_service import get_falkordb_adapter
from utils.config import OPENAI_API_KEY, OPENROUTER_API_KEY
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)


class EntityProcessor:
    """Service for processing entities from documents."""

    def __init__(self):
        """Initialize the entity processor."""
        self.entity_deduplicator = None  # Will be initialized lazily

    async def get_entity_deduplicator(self):
        """
        Get the entity deduplicator, initializing it if necessary.

        Returns:
            EntityDeduplicator instance
        """
        if not self.entity_deduplicator:
            self.entity_deduplicator = EntityDeduplicator()
        return self.entity_deduplicator

    async def extract_entities_from_document(self, episode_id: str) -> Dict[str, Any]:
        """
        Extract entities from a document's facts.

        Args:
            episode_id: ID of the episode node

        Returns:
            Result dictionary with entity extraction details
        """
        logger.info(f"Extracting entities from document with episode ID: {episode_id}")

        try:
            # Get the adapter
            adapter = await get_falkordb_adapter()

            # Get facts for this episode
            query = f"""
            MATCH (e:Episode {{uuid: '{episode_id}'}})-[:CONTAINS]->(f:Fact)
            RETURN f.uuid AS uuid, f.body AS body
            """

            result = adapter.execute_cypher(query)

            if not result or len(result) < 2 or not result[1]:
                logger.warning(f"No facts found for episode {episode_id}")
                return {"success": False, "error": "No facts found", "entities_extracted": 0}

            logger.info(f"Found {len(result[1])} facts for episode {episode_id}")

            # Extract entities from each fact
            entities_extracted = 0

            # Get the API key for entity extraction
            api_key = self._get_api_key()
            if not api_key:
                logger.warning("No API key found for entity extraction")
                return {"success": False, "error": "No API key found for entity extraction", "entities_extracted": 0}

            for row in result[1]:
                fact_uuid = row[0]
                fact_body = row[1]

                logger.info(f"Processing fact: {fact_uuid}")
                logger.info(f"Fact body sample: {fact_body[:100]}...")

                # Extract entities from the fact text
                entities = await extract_entities_from_text(api_key, fact_body)

                logger.info(f"Extracted {len(entities)} entities from fact {fact_uuid}")

                # Create entity nodes and relationships
                for entity in entities:
                    logger.info(f"- {entity['name']} ({entity['type']}): {entity.get('description', '')}")

                    # Create entity node
                    entity_created = await self._create_entity_node(entity, episode_id, fact_uuid, adapter)
                    if entity_created:
                        entities_extracted += 1

            logger.info(f"Extracted and created {entities_extracted} entities for episode {episode_id}")

            return {
                "success": True,
                "document_id": episode_id,
                "entities_extracted": entities_extracted
            }

        except Exception as e:
            logger.error(f"Error extracting entities from document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "entities_extracted": 0
            }

    async def deduplicate_entities_for_document(self, episode_id: str) -> Dict[str, Any]:
        """
        Deduplicate entities for a document.

        Args:
            episode_id: ID of the episode node

        Returns:
            Result dictionary with deduplication details
        """
        logger.info(f"Deduplicating entities for document with episode ID: {episode_id}")

        try:
            # Get the entity deduplicator
            deduplicator = await self.get_entity_deduplicator()

            # Run deduplication for entities from this document
            result = await deduplicator.deduplicate_entities(
                document_id=episode_id,
                merge=True
            )

            logger.info(f"Deduplication completed for document {episode_id}")
            logger.info(f"Found {result.duplicate_groups} duplicate groups with {result.total_duplicates} total duplicates")
            logger.info(f"Merged {result.merged_entities} entities")

            return {
                "success": True,
                "document_id": episode_id,
                "duplicates_found": result.total_duplicates,
                "duplicate_groups": result.duplicate_groups,
                "entities_merged": result.merged_entities,
                "processing_time": result.processing_time
            }

        except Exception as e:
            logger.error(f"Error deduplicating entities for document {episode_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "document_id": episode_id,
                "duplicates_found": 0,
                "entities_merged": 0
            }

    def _get_api_key(self) -> str:
        """
        Get the API key for entity extraction.

        Returns:
            API key string or None
        """
        # Use OpenRouter API key if available
        if OPENROUTER_API_KEY:
            logger.info("Using OpenRouter API key for entity extraction")
            return OPENROUTER_API_KEY
        elif OPENAI_API_KEY:
            logger.info("Using OpenAI API key for entity extraction")
            return OPENAI_API_KEY
        else:
            return None

    async def _create_entity_node(self, entity: Dict[str, Any], episode_id: str, fact_uuid: str, adapter) -> bool:
        """
        Create an entity node and link it to a fact.

        Args:
            entity: Entity dictionary with name, type, description
            episode_id: ID of the episode
            fact_uuid: UUID of the fact
            adapter: Database adapter

        Returns:
            True if entity was created successfully, False otherwise
        """
        try:
            # Escape special characters in entity name and description
            entity_name = entity["name"].replace("'", "\\'")
            entity_type = entity["type"]
            entity_description = entity.get("description", "").replace("'", "\\'")

            # Get current timestamp
            timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")

            # Create entity node with UUID and link to fact
            entity_uuid = str(uuid.uuid4())
            create_entity_query = f"""
            MERGE (e:Entity {{name: '{entity_name}', type: '{entity_type}'}})
            ON CREATE SET
                e.uuid = '{entity_uuid}',
                e.description = '{entity_description}',
                e.created_at = '{timestamp}',
                e.source_document_id = '{episode_id}',
                e.source_fact_id = '{fact_uuid}',
                e.confidence = 1.0
            ON MATCH SET
                e.uuid = CASE WHEN e.uuid IS NULL THEN '{entity_uuid}' ELSE e.uuid END
            WITH e
            MATCH (f:Fact {{uuid: '{fact_uuid}'}})
            MERGE (f)-[r:MENTIONS]->(e)
            RETURN e.name as name, e.uuid as uuid
            """

            result = adapter.execute_cypher(create_entity_query)
            if result and len(result) > 1 and len(result[1]) > 0:
                logger.info(f"  Created entity node and relationship for {entity['name']}")
                return True
            else:
                logger.error(f"  Failed to create entity node for {entity['name']}")
                return False

        except Exception as e:
            logger.error(f"Error creating entity node for {entity['name']}: {e}")
            return False
