#!/usr/bin/env python3
"""
Test script for learning-to-rank system.

This script demonstrates the machine learning-based ranking capabilities.
"""

import sys
import os
import logging
import time
import random
from typing import List, Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ranking.learning_to_rank import (
    LearningToRankReranker, UserInteraction, InteractionType, 
    RankingFeatures, RankingExample, get_ltr_reranker
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_mock_search_results() -> List[Dict[str, Any]]:
    """Create mock search results for testing."""
    return [
        {
            'uuid': 'result-1',
            'name': 'Vitamin D Deficiency',
            'summary': 'Common nutritional deficiency affecting bone health',
            'confidence': 0.9,
            'created_at': time.time() - 86400 * 30,  # 30 days ago
            'document_quality': 0.8,
            'source_reliability': 0.9
        },
        {
            'uuid': 'result-2', 
            'name': 'Vitamin D Supplementation',
            'summary': 'Treatment approaches for vitamin D deficiency',
            'confidence': 0.85,
            'created_at': time.time() - 86400 * 7,  # 7 days ago
            'document_quality': 0.9,
            'source_reliability': 0.85
        },
        {
            'uuid': 'result-3',
            'name': 'Calcium Absorption',
            'summary': 'Role of vitamin D in calcium metabolism',
            'confidence': 0.8,
            'created_at': time.time() - 86400 * 60,  # 60 days ago
            'document_quality': 0.7,
            'source_reliability': 0.8
        },
        {
            'uuid': 'result-4',
            'name': 'Bone Health',
            'summary': 'Factors affecting bone density and strength',
            'confidence': 0.75,
            'created_at': time.time() - 86400 * 14,  # 14 days ago
            'document_quality': 0.85,
            'source_reliability': 0.9
        },
        {
            'uuid': 'result-5',
            'name': 'Sunlight Exposure',
            'summary': 'Natural vitamin D synthesis through UV exposure',
            'confidence': 0.7,
            'created_at': time.time() - 86400 * 3,  # 3 days ago
            'document_quality': 0.6,
            'source_reliability': 0.7
        }
    ]


def create_mock_search_scores() -> List[Dict[str, float]]:
    """Create mock search scores for the results."""
    return [
        {'bm25_score': 0.8, 'cosine_similarity': 0.9, 'cross_encoder_score': 0.85},
        {'bm25_score': 0.75, 'cosine_similarity': 0.85, 'cross_encoder_score': 0.8},
        {'bm25_score': 0.6, 'cosine_similarity': 0.7, 'cross_encoder_score': 0.65},
        {'bm25_score': 0.5, 'cosine_similarity': 0.6, 'cross_encoder_score': 0.55},
        {'bm25_score': 0.4, 'cosine_similarity': 0.5, 'cross_encoder_score': 0.45}
    ]


def create_mock_graph_stats() -> List[Dict[str, Any]]:
    """Create mock graph statistics for the results."""
    return [
        {'centrality': 0.8, 'frequency': 0.9, 'degree': 15, 'community_size': 50},
        {'centrality': 0.7, 'frequency': 0.8, 'degree': 12, 'community_size': 45},
        {'centrality': 0.6, 'frequency': 0.6, 'degree': 8, 'community_size': 30},
        {'centrality': 0.5, 'frequency': 0.5, 'degree': 6, 'community_size': 25},
        {'centrality': 0.4, 'frequency': 0.3, 'degree': 4, 'community_size': 20}
    ]


def test_feature_extraction():
    """Test feature extraction for ranking."""
    logger.info("=== Testing Feature Extraction ===")
    
    reranker = get_ltr_reranker()
    results = create_mock_search_results()
    search_scores = create_mock_search_scores()
    graph_stats = create_mock_graph_stats()
    
    query = "vitamin D deficiency treatment"
    
    for i, result in enumerate(results):
        features = reranker.feature_extractor.extract_features(
            query, result, 'node', search_scores[i], graph_stats[i]
        )
        
        logger.info(f"\nResult: {result['name']}")
        logger.info(f"  BM25 Score: {features.bm25_score:.3f}")
        logger.info(f"  Cosine Similarity: {features.cosine_similarity:.3f}")
        logger.info(f"  Entity Confidence: {features.entity_confidence:.3f}")
        logger.info(f"  Entity Centrality: {features.entity_centrality:.3f}")
        logger.info(f"  Document Recency: {features.document_recency:.3f}")
        logger.info(f"  Query Entity Match: {features.query_entity_match:.3f}")


def test_interaction_tracking():
    """Test user interaction tracking."""
    logger.info("\n=== Testing Interaction Tracking ===")
    
    reranker = get_ltr_reranker()
    
    # Simulate user interactions
    interactions = [
        UserInteraction(
            query="vitamin D deficiency",
            result_uuid="result-1",
            result_type="node",
            interaction_type=InteractionType.CLICK,
            timestamp=time.time(),
            position=0
        ),
        UserInteraction(
            query="vitamin D deficiency", 
            result_uuid="result-1",
            result_type="node",
            interaction_type=InteractionType.DWELL,
            timestamp=time.time(),
            position=0,
            dwell_time=45.0
        ),
        UserInteraction(
            query="vitamin D deficiency",
            result_uuid="result-1",
            result_type="node",
            interaction_type=InteractionType.FEEDBACK_POSITIVE,
            timestamp=time.time(),
            position=0
        ),
        UserInteraction(
            query="vitamin D deficiency",
            result_uuid="result-2",
            result_type="node",
            interaction_type=InteractionType.CLICK,
            timestamp=time.time(),
            position=1
        ),
        UserInteraction(
            query="vitamin D deficiency",
            result_uuid="result-3",
            result_type="node",
            interaction_type=InteractionType.FEEDBACK_NEGATIVE,
            timestamp=time.time(),
            position=2
        )
    ]
    
    # Record interactions
    for interaction in interactions:
        reranker.record_interaction(interaction)
    
    # Check interaction features
    for result_uuid in ["result-1", "result-2", "result-3"]:
        features = reranker.interaction_tracker.get_interaction_features(result_uuid, "node")
        relevance = reranker.interaction_tracker.calculate_relevance_score(result_uuid, "node", "vitamin D deficiency")
        
        logger.info(f"\n{result_uuid}:")
        logger.info(f"  Click Rate: {features['historical_click_rate']:.3f}")
        logger.info(f"  Avg Dwell Time: {features['avg_dwell_time']:.1f}s")
        logger.info(f"  Positive Feedback Rate: {features['positive_feedback_rate']:.3f}")
        logger.info(f"  Relevance Score: {relevance:.3f}")


def test_model_training():
    """Test LTR model training."""
    logger.info("\n=== Testing Model Training ===")
    
    reranker = get_ltr_reranker()
    
    # Create training examples
    training_examples = []
    
    for i in range(50):  # Create 50 training examples
        # Random query and result
        queries = ["vitamin D deficiency", "bone health", "calcium absorption", "sunlight exposure"]
        query = random.choice(queries)
        
        # Create random features
        features = RankingFeatures(
            bm25_score=random.uniform(0.3, 1.0),
            cosine_similarity=random.uniform(0.3, 1.0),
            cross_encoder_score=random.uniform(0.3, 1.0),
            entity_centrality=random.uniform(0.0, 1.0),
            entity_frequency=random.uniform(0.0, 1.0),
            entity_confidence=random.uniform(0.5, 1.0),
            entity_recency=random.uniform(0.0, 1.0),
            node_degree=random.randint(1, 20),
            community_size=random.randint(10, 100),
            document_recency=random.uniform(0.0, 1.0),
            document_quality=random.uniform(0.5, 1.0),
            source_reliability=random.uniform(0.5, 1.0),
            historical_click_rate=random.uniform(0.0, 1.0),
            avg_dwell_time=random.uniform(0.0, 60.0),
            positive_feedback_rate=random.uniform(0.0, 1.0),
            query_entity_match=random.uniform(0.0, 1.0),
            query_length=len(query.split()),
            query_complexity=random.uniform(0.0, 1.0)
        )
        
        # Simulate relevance based on some features (higher scores = more relevant)
        relevance = (
            features.bm25_score * 0.3 +
            features.cosine_similarity * 0.3 +
            features.entity_confidence * 0.2 +
            features.historical_click_rate * 0.2
        )
        relevance = min(1.0, max(0.0, relevance + random.uniform(-0.1, 0.1)))  # Add noise
        
        example = RankingExample(
            query=query,
            result_uuid=f"training-result-{i}",
            result_type="node",
            features=features,
            relevance_score=relevance,
            position=random.randint(0, 9),
            timestamp=time.time()
        )
        
        training_examples.append(example)
    
    # Add training examples to model
    for example in training_examples:
        reranker.model.add_training_example(example)
    
    # Force training
    reranker.model.train()
    
    # Test prediction
    test_features = RankingFeatures(
        bm25_score=0.8,
        cosine_similarity=0.9,
        entity_confidence=0.9,
        historical_click_rate=0.7
    )
    
    predicted_relevance = reranker.model.predict_relevance(test_features)
    logger.info(f"Predicted relevance for test features: {predicted_relevance:.3f}")
    
    # Show feature importance
    importance = reranker.model.get_feature_importance()
    if importance:
        logger.info("\nTop 5 Most Important Features:")
        sorted_features = sorted(importance.items(), key=lambda x: abs(x[1]), reverse=True)
        for feature, score in sorted_features[:5]:
            logger.info(f"  {feature}: {score:.3f}")


def test_reranking():
    """Test the complete reranking process."""
    logger.info("\n=== Testing Reranking ===")
    
    reranker = get_ltr_reranker()
    results = create_mock_search_results()
    search_scores = create_mock_search_scores()
    graph_stats = create_mock_graph_stats()
    
    query = "vitamin D deficiency treatment"
    
    logger.info("Original ranking:")
    for i, result in enumerate(results):
        logger.info(f"  {i+1}. {result['name']} (confidence: {result['confidence']:.2f})")
    
    # Rerank using LTR
    reranked_results = reranker.rerank_results(
        query, results, 'node', search_scores, graph_stats
    )
    
    logger.info("\nLTR reranked results:")
    for i, result in enumerate(reranked_results):
        logger.info(f"  {i+1}. {result['name']} (confidence: {result['confidence']:.2f})")


def test_performance_comparison():
    """Compare LTR with simple ranking methods."""
    logger.info("\n=== Performance Comparison ===")
    
    reranker = get_ltr_reranker()
    results = create_mock_search_results()
    search_scores = create_mock_search_scores()
    graph_stats = create_mock_graph_stats()
    
    query = "vitamin D deficiency treatment"
    
    # Original order (by search score)
    logger.info("Original (by BM25 score):")
    for i, result in enumerate(results):
        score = search_scores[i]['bm25_score']
        logger.info(f"  {i+1}. {result['name']} (BM25: {score:.3f})")
    
    # Sort by confidence
    confidence_sorted = sorted(results, key=lambda x: x['confidence'], reverse=True)
    logger.info("\nSorted by confidence:")
    for i, result in enumerate(confidence_sorted):
        logger.info(f"  {i+1}. {result['name']} (confidence: {result['confidence']:.3f})")
    
    # Sort by recency
    recency_sorted = sorted(results, key=lambda x: x['created_at'], reverse=True)
    logger.info("\nSorted by recency:")
    for i, result in enumerate(recency_sorted):
        days_old = (time.time() - result['created_at']) / 86400
        logger.info(f"  {i+1}. {result['name']} ({days_old:.0f} days old)")
    
    # LTR ranking
    ltr_ranked = reranker.rerank_results(query, results, 'node', search_scores, graph_stats)
    logger.info("\nLTR ranking:")
    for i, result in enumerate(ltr_ranked):
        logger.info(f"  {i+1}. {result['name']}")


def test_model_statistics():
    """Test model statistics and diagnostics."""
    logger.info("\n=== Model Statistics ===")
    
    reranker = get_ltr_reranker()
    stats = reranker.get_model_stats()
    
    logger.info(f"Total Interactions: {stats['total_interactions']}")
    logger.info(f"Training Examples: {stats['training_examples']}")
    logger.info(f"Model Loaded: {stats['model_loaded']}")
    
    if stats['feature_importance']:
        logger.info("\nFeature Importance (top 5):")
        sorted_features = sorted(
            stats['feature_importance'].items(), 
            key=lambda x: abs(x[1]), 
            reverse=True
        )
        for feature, importance in sorted_features[:5]:
            logger.info(f"  {feature}: {importance:.4f}")


def main():
    """Run all tests."""
    logger.info("Starting Learning-to-Rank Tests")
    logger.info("=" * 60)
    
    try:
        test_feature_extraction()
        test_interaction_tracking()
        test_model_training()
        test_reranking()
        test_performance_comparison()
        test_model_statistics()
        
        logger.info("\n" + "=" * 60)
        logger.info("All learning-to-rank tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
