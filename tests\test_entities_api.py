"""
Test script to directly check the entities API
"""

import requests
import json

def test_entities_api():
    """Test the entities API directly"""
    url = "http://localhost:9753/api/entities"

    try:
        response = requests.get(url)
        print(f"Status code: {response.status_code}")
        print(f"Response headers: {response.headers}")
        print(f"Response content: {response.content}")

        if response.status_code == 200:
            try:
                data = response.json()
                print("\nParsed JSON response:")
                print(json.dumps(data, indent=2))

                if "entities" in data:
                    print(f"\nFound {len(data['entities'])} entities")
                else:
                    print("\nNo 'entities' key in response")
            except json.JSONDecodeError:
                print("\nResponse is not valid JSON")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_entities_api()
