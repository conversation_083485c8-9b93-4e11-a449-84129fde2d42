#!/usr/bin/env python3
"""
Test script to check graph API endpoints.
"""

import requests

def test_graph_api():
    """Test graph API endpoints"""
    base_url = "http://127.0.0.1:9753"
    
    print("🔍 Testing Graph API Endpoints")
    print("=" * 40)
    
    # Test the main graph endpoint
    print("1. Testing /api/knowledge-graph/graph")
    try:
        response = requests.get(f"{base_url}/api/knowledge-graph/graph?limit=10", timeout=10)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Response keys: {list(data.keys())}")
            
            nodes = data.get('nodes', [])
            relationships = data.get('relationships', [])
            edges = data.get('edges', [])
            
            print(f"Nodes: {len(nodes)}")
            print(f"Relationships: {len(relationships)}")
            print(f"Edges: {len(edges)}")
            
            if nodes:
                print("Sample nodes:")
                for i, node in enumerate(nodes[:3]):
                    name = node.get('name', 'N/A')
                    node_type = node.get('type', 'N/A')
                    uuid = node.get('uuid', 'N/A')
                    print(f"  {i+1}. {name} ({node_type}) - {uuid}")
                    
                    # Check if it's test data
                    if name.startswith('Test'):
                        print(f"     ❌ This is test data!")
                    else:
                        print(f"     ✅ Real data")
            
            if relationships:
                print("Sample relationships:")
                for i, rel in enumerate(relationships[:3]):
                    source = rel.get('source', 'N/A')
                    target = rel.get('target', 'N/A')
                    rel_type = rel.get('type', 'N/A')
                    print(f"  {i+1}. {source} --[{rel_type}]--> {target}")
            elif edges:
                print("Sample edges:")
                for i, edge in enumerate(edges[:3]):
                    source = edge.get('from', edge.get('source', 'N/A'))
                    target = edge.get('to', edge.get('target', 'N/A'))
                    rel_type = edge.get('type', edge.get('label', 'N/A'))
                    print(f"  {i+1}. {source} --[{rel_type}]--> {target}")
            else:
                print("❌ No relationships/edges found")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_graph_api()
