"""
Test FalkorDB connection using Redis client
"""

import sys
import redis

# FalkorDB connection details
falkordb_host = 'localhost'
falkordb_port = 6379
falkordb_password = 'Triathlon16!'

print(f"Trying to connect to FalkorDB at {falkordb_host}:{falkordb_port}")

try:
    # Connect to FalkorDB using Redis client without password
    r = redis.Redis(
        host=falkordb_host,
        port=falkordb_port,
        decode_responses=True
    )

    # Test the connection
    print(f"PING response: {r.ping()}")

    # Test creating a graph
    print("Creating a test graph...")
    result = r.execute_command(
        "GRAPH.QUERY",
        "test_graph",
        "CREATE (n:TestNode {name: 'FalkorDB Test', created: timestamp()}) RETURN n.name"
    )
    print(f"Graph creation result: {result}")

    # Test querying the graph
    print("Querying the test graph...")
    result = r.execute_command(
        "GRAPH.QUERY",
        "test_graph",
        "MATCH (n:TestNode) RETURN n.name, n.created"
    )
    print(f"Query result: {result}")

    print("Connection test successful!")
except Exception as e:
    print(f"Error connecting to FalkorDB: {e}")
    sys.exit(1)
