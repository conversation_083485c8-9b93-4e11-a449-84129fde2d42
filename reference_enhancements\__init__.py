"""
Reference Enhancements Package

This package provides advanced reference management capabilities for Graphiti:
- Reference deduplication using fuzzy matching
- Citation network analysis and visualization
- Bibliographic enrichment from external databases
"""

from .reference_deduplication import ReferenceDeduplcator
from .citation_network import CitationNetworkAnalyzer
from .bibliographic_enrichment import Bibliographic<PERSON>nricher
from .reference_enhancement_suite import ReferenceEnhancementSuite

__all__ = [
    'ReferenceDeduplcator',
    'CitationNetworkAnalyzer', 
    'BibliographicEnricher',
    'ReferenceEnhancementSuite'
]

__version__ = "1.0.0"
