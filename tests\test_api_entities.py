"""
Integration tests for the entity API endpoints.
"""

import os
import sys
import pytest
from pathlib import Path
from fastapi.testclient import Test<PERSON><PERSON>
from unittest.mock import patch, MagicMock

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import app

@patch("services.entity_service.extract_entities_from_document")
def test_extract_entities_endpoint(mock_extract_entities, test_client):
    """
    Test the extract entities endpoint.
    
    Args:
        mock_extract_entities: Mocked extract_entities_from_document function
        test_client: FastAPI test client
    """
    # Mock the extract_entities_from_document function
    mock_extract_entities.return_value = {
        "document_id": "doc1",
        "success": True,
        "entities_extracted": 10
    }
    
    # Make a request to the extract entities endpoint
    response = test_client.post("/api/entities/extract/doc1")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert data["success"] == True
    assert data["document_id"] == "doc1"
    assert data["entities_extracted"] == 10

@patch("services.entity_service.extract_entities_from_document")
def test_extract_entities_endpoint_error(mock_extract_entities, test_client):
    """
    Test the extract entities endpoint with an error.
    
    Args:
        mock_extract_entities: Mocked extract_entities_from_document function
        test_client: FastAPI test client
    """
    # Mock the extract_entities_from_document function to raise an exception
    mock_extract_entities.side_effect = Exception("Test error")
    
    # Make a request to the extract entities endpoint
    response = test_client.post("/api/entities/extract/doc1")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error extracting entities" in response.json()["detail"]

@patch("services.entity_service.get_entity_types")
def test_get_entity_types_endpoint(mock_get_entity_types, test_client):
    """
    Test the get entity types endpoint.
    
    Args:
        mock_get_entity_types: Mocked get_entity_types function
        test_client: FastAPI test client
    """
    # Mock the get_entity_types function
    mock_get_entity_types.return_value = ["Person", "Organization", "Location"]
    
    # Make a request to the entity types endpoint
    response = test_client.get("/api/entity-types")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "entity_types" in data
    assert len(data["entity_types"]) == 3
    assert "Person" in data["entity_types"]
    assert "Organization" in data["entity_types"]
    assert "Location" in data["entity_types"]

@patch("services.entity_service.get_entity_types")
def test_get_entity_types_endpoint_error(mock_get_entity_types, test_client):
    """
    Test the get entity types endpoint with an error.
    
    Args:
        mock_get_entity_types: Mocked get_entity_types function
        test_client: FastAPI test client
    """
    # Mock the get_entity_types function to raise an exception
    mock_get_entity_types.side_effect = Exception("Test error")
    
    # Make a request to the entity types endpoint
    response = test_client.get("/api/entity-types")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting entity types" in response.json()["detail"]

@patch("services.entity_service.get_entity_counts")
def test_get_entity_counts_endpoint(mock_get_entity_counts, test_client):
    """
    Test the get entity counts endpoint.
    
    Args:
        mock_get_entity_counts: Mocked get_entity_counts function
        test_client: FastAPI test client
    """
    # Mock the get_entity_counts function
    mock_get_entity_counts.return_value = {
        "counts": [
            {"type": "Person", "count": 10},
            {"type": "Organization", "count": 5},
            {"type": "Location", "count": 3}
        ],
        "total": 18
    }
    
    # Make a request to the entity counts endpoint
    response = test_client.get("/api/entity-counts")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "counts" in data
    assert "total" in data
    assert data["total"] == 18
    assert len(data["counts"]) == 3
    assert data["counts"][0]["type"] == "Person"
    assert data["counts"][0]["count"] == 10

@patch("services.entity_service.get_entity_counts")
def test_get_entity_counts_endpoint_error(mock_get_entity_counts, test_client):
    """
    Test the get entity counts endpoint with an error.
    
    Args:
        mock_get_entity_counts: Mocked get_entity_counts function
        test_client: FastAPI test client
    """
    # Mock the get_entity_counts function to raise an exception
    mock_get_entity_counts.side_effect = Exception("Test error")
    
    # Make a request to the entity counts endpoint
    response = test_client.get("/api/entity-counts")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting entity counts" in response.json()["detail"]

@patch("services.entity_service.get_entities_by_type")
def test_get_entities_endpoint(mock_get_entities_by_type, test_client):
    """
    Test the get entities endpoint.
    
    Args:
        mock_get_entities_by_type: Mocked get_entities_by_type function
        test_client: FastAPI test client
    """
    # Mock the get_entities_by_type function
    mock_get_entities_by_type.return_value = [
        {
            "uuid": "entity1",
            "name": "Entity 1",
            "type": "Person",
            "confidence": 0.9
        },
        {
            "uuid": "entity2",
            "name": "Entity 2",
            "type": "Person",
            "confidence": 0.8
        }
    ]
    
    # Make a request to the entities endpoint
    response = test_client.get("/api/entities/Person")
    
    # Check the response
    assert response.status_code == 200
    
    # Check the response data
    data = response.json()
    assert "entities" in data
    assert "count" in data
    assert "entity_type" in data
    assert data["entity_type"] == "Person"
    assert data["count"] == 2
    assert len(data["entities"]) == 2
    assert data["entities"][0]["uuid"] == "entity1"
    assert data["entities"][1]["uuid"] == "entity2"

@patch("services.entity_service.get_entities_by_type")
def test_get_entities_endpoint_error(mock_get_entities_by_type, test_client):
    """
    Test the get entities endpoint with an error.
    
    Args:
        mock_get_entities_by_type: Mocked get_entities_by_type function
        test_client: FastAPI test client
    """
    # Mock the get_entities_by_type function to raise an exception
    mock_get_entities_by_type.side_effect = Exception("Test error")
    
    # Make a request to the entities endpoint
    response = test_client.get("/api/entities/Person")
    
    # Check the response
    assert response.status_code == 500
    
    # Check the error message
    assert "Error getting entities" in response.json()["detail"]
