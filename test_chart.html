<!DOCTYPE html>
<html>
<head>
    <title>Chart Test</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <h1>Chart Test</h1>
    <canvas id="test-chart" width="400" height="200"></canvas>
    
    <script>
        // Test data similar to what the entities page should receive
        const testTypeCounts = {
            "Concept": 1807,
            "Food": 1392,
            "Health_Condition": 1177,
            "Therapeutic_Intervention": 980,
            "Person": 979,
            "Research": 893,
            "Bioactive_Compound": 678,
            "Process": 554,
            "Symptom": 545,
            "Organization": 520
        };
        
        function createTestChart(typeCounts) {
            const ctx = document.getElementById('test-chart').getContext('2d');
            
            // Convert type counts object to sorted array
            const sortedTypes = Object.entries(typeCounts)
                .filter(([type, count]) => type && type !== '' && count > 0)
                .sort((a, b) => b[1] - a[1])
                .slice(0, 12);
            
            const labels = sortedTypes.map(([type, count]) => type);
            const data = sortedTypes.map(([type, count]) => count);
            
            console.log('Creating chart with labels:', labels);
            console.log('Creating chart with data:', data);
            
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: [
                            '#667eea', '#764ba2', '#f093fb', '#f5576c',
                            '#4facfe', '#00f2fe', '#43e97b', '#38f9d7',
                            '#ffecd2', '#fcb69f', '#a8edea', '#fed6e3'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        // Create the chart when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Creating test chart...');
            createTestChart(testTypeCounts);
        });
    </script>
</body>
</html>
