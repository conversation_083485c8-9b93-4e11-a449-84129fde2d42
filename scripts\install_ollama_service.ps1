# PowerShell script to install Ollama as a Windows service
# Run as Administrator

param(
    [string]$OllamaPath = "ollama.exe",
    [string]$ServiceName = "OllamaService",
    [string]$DisplayName = "Ollama AI Service",
    [string]$Description = "Ollama AI model serving service"
)

Write-Host "🚀 Installing Ollama as Windows Service..." -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "❌ This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    exit 1
}

# Find Ollama executable
$ollamaExe = Get-Command ollama -ErrorAction SilentlyContinue
if (-not $ollamaExe) {
    Write-Host "❌ Ollama not found in PATH. Please install Ollama first." -ForegroundColor Red
    Write-Host "Download from: https://ollama.ai/download" -ForegroundColor Yellow
    exit 1
}

$OllamaPath = $ollamaExe.Source
Write-Host "✅ Found Ollama at: $OllamaPath" -ForegroundColor Green

# Check if service already exists
$existingService = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
if ($existingService) {
    Write-Host "⚠️ Service '$ServiceName' already exists. Removing..." -ForegroundColor Yellow
    Stop-Service -Name $ServiceName -Force -ErrorAction SilentlyContinue
    sc.exe delete $ServiceName
    Start-Sleep -Seconds 2
}

# Create the service
Write-Host "🔧 Creating Windows service..." -ForegroundColor Cyan

$serviceCommand = "`"$OllamaPath`" serve"

# Use sc.exe to create the service
$result = sc.exe create $ServiceName binPath= $serviceCommand start= auto DisplayName= $DisplayName

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Service created successfully!" -ForegroundColor Green
    
    # Set service description
    sc.exe description $ServiceName $Description
    
    # Configure service recovery options
    sc.exe failure $ServiceName reset= 86400 actions= restart/5000/restart/5000/restart/5000
    
    # Start the service
    Write-Host "🚀 Starting Ollama service..." -ForegroundColor Cyan
    Start-Service -Name $ServiceName
    
    # Check service status
    $service = Get-Service -Name $ServiceName
    if ($service.Status -eq "Running") {
        Write-Host "✅ Ollama service is running!" -ForegroundColor Green
        Write-Host "🎯 Ollama will now start automatically on boot" -ForegroundColor Green
        
        # Test connection
        Start-Sleep -Seconds 3
        try {
            $response = Invoke-RestMethod -Uri "http://localhost:11434/api/version" -TimeoutSec 10
            Write-Host "✅ Ollama is responding: Version $($response.version)" -ForegroundColor Green
        } catch {
            Write-Host "⚠️ Service started but not responding yet. Give it a moment..." -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Failed to start service. Status: $($service.Status)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Failed to create service. Error code: $LASTEXITCODE" -ForegroundColor Red
}

Write-Host "`n📋 Service Management Commands:" -ForegroundColor Cyan
Write-Host "   Start:   Start-Service -Name $ServiceName" -ForegroundColor White
Write-Host "   Stop:    Stop-Service -Name $ServiceName" -ForegroundColor White
Write-Host "   Status:  Get-Service -Name $ServiceName" -ForegroundColor White
Write-Host "   Remove:  sc.exe delete $ServiceName" -ForegroundColor White
