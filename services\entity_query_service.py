"""
Entity query service for the Graphiti application.

This service handles entity querying and retrieval operations.
"""

from typing import List, Dict, Any, Optional

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter
from models.entity import Entity, EntitySummary, EntityTypeCount, EntityTypeCounts

# Set up logger
logger = get_logger(__name__)


async def get_entity_count() -> int:
    """
    Get the total number of entities.

    Returns:
        Total number of entities
    """
    try:
        adapter = await get_falkordb_adapter()

        query = """
        MATCH (e:Entity)
        RETURN COUNT(e) AS count
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            return result[1][0][0]

        return 0

    except Exception as e:
        logger.error(f"Error getting entity count: {str(e)}")
        return 0


async def get_entity_types() -> List[str]:
    """
    Get all entity types.

    Returns:
        List of entity types
    """
    adapter = await get_falkordb_adapter()

    query = """
    MATCH (e:Entity)
    RETURN DISTINCT e.type
    """

    result = adapter.execute_cypher(query)

    entity_types = []
    if result and len(result) > 1:
        for row in result[1]:
            entity_type = row[0]
            # Handle if entity_type is a list or other non-string type
            if isinstance(entity_type, list):
                # Try to extract the type from the list
                for item in entity_type:
                    if isinstance(item, str):
                        entity_types.append(item)
            elif entity_type is not None:
                # Convert to string to ensure it's hashable
                entity_types.append(str(entity_type))

    return entity_types


async def get_entity_counts() -> EntityTypeCounts:
    """
    Get counts of entities by type.

    Returns:
        Entity type counts
    """
    adapter = await get_falkordb_adapter()

    query = """
    MATCH (e:Entity)
    RETURN e.type as type, count(e) as count
    ORDER BY count DESC
    """

    result = adapter.execute_cypher(query)

    counts = []
    total = 0

    if result and len(result) > 1:
        for row in result[1]:
            entity_type = row[0]
            count = row[1]

            # Handle if entity_type is a list or other non-string type
            if isinstance(entity_type, list):
                # Try to extract the type from the list
                for item in entity_type:
                    if isinstance(item, str):
                        entity_type = item
                        break

            # Convert to string to ensure it's hashable
            if entity_type is not None:
                entity_type = str(entity_type)
                counts.append(EntityTypeCount(type=entity_type, count=count))
                total += count

    return EntityTypeCounts(counts=counts, total=total)


async def get_entities_by_type(entity_type: str, limit: int = 100, offset: int = 0) -> List[Entity]:
    """
    Get entities by type.

    Args:
        entity_type: Entity type
        limit: Maximum number of entities to return
        offset: Offset for pagination

    Returns:
        List of entities
    """
    try:
        adapter = await get_falkordb_adapter()

        # Handle if entity_type is a list
        if isinstance(entity_type, list):
            # Try to extract a string from the list
            for item in entity_type:
                if isinstance(item, str):
                    entity_type = item
                    break

        # Convert to string to ensure it's hashable
        entity_type = str(entity_type)

        query = f"""
        MATCH (e:Entity)
        WHERE e.type = '{entity_type}'
        RETURN e
        ORDER BY e.name
        SKIP {offset}
        LIMIT {limit}
        """

        result = adapter.execute_cypher(query)

        entities = []
        if result and len(result) > 1:
            for row in result[1]:
                try:
                    entity_data = row[0]
                    entity = _parse_entity_data(entity_data)
                    if entity:
                        entities.append(entity)
                except Exception as e:
                    logger.warning(f"Error processing entity: {str(e)}")
                    continue

        return entities
    except Exception as e:
        logger.error(f"Error getting entities by type: {str(e)}")
        return []


async def get_entity_by_uuid(entity_uuid: str) -> Optional[Entity]:
    """
    Get an entity by UUID.

    Args:
        entity_uuid: Entity UUID

    Returns:
        Entity or None if not found
    """
    try:
        adapter = await get_falkordb_adapter()

        query = f"""
        MATCH (e:Entity {{uuid: '{entity_uuid}'}})
        RETURN e
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            try:
                entity_data = result[1][0][0]
                return _parse_entity_data(entity_data)
            except Exception as e:
                logger.warning(f"Error processing entity: {str(e)}")
                return None

        return None
    except Exception as e:
        logger.error(f"Error getting entity by UUID: {str(e)}")
        return None


def _parse_entity_data(entity_data) -> Optional[Entity]:
    """
    Parse entity data from FalkorDB into an Entity object.

    Args:
        entity_data: Raw entity data from FalkorDB

    Returns:
        Entity object or None if parsing fails
    """
    try:
        # Create a dictionary to hold entity properties
        entity_dict = {}

        # Check if entity_data is a dictionary
        if isinstance(entity_data, dict):
            entity_dict = entity_data
        # Check if entity_data is a list (FalkorDB format)
        elif isinstance(entity_data, list):
            # Handle nested list format: [['id', 1294], ['labels', ['Entity']], ['properties', [['name', 'value'], ...]]]
            for item in entity_data:
                if isinstance(item, list) and len(item) >= 2:
                    key = item[0]
                    value = item[1]

                    # Handle properties section specially
                    if key == "properties" and isinstance(value, list):
                        # Properties is a list of [key, value] pairs
                        for prop_item in value:
                            if isinstance(prop_item, list) and len(prop_item) >= 2:
                                prop_key = prop_item[0]
                                prop_value = prop_item[1]
                                entity_dict[prop_key] = prop_value
                    else:
                        entity_dict[key] = value
        else:
            logger.warning(f"Unexpected entity data format: {entity_data}")
            return None

        # Extract values and handle list types
        uuid_val = entity_dict.get("uuid", "")
        name_val = entity_dict.get("name", "")
        type_val = entity_dict.get("type", "")
        confidence_val = entity_dict.get("confidence", 1.0)

        # Convert list values to strings
        if isinstance(uuid_val, list) and len(uuid_val) > 0:
            uuid_val = str(uuid_val[0])
        if isinstance(name_val, list) and len(name_val) > 0:
            name_val = str(name_val[0])
        if isinstance(type_val, list) and len(type_val) > 0:
            type_val = str(type_val[0])
        if isinstance(confidence_val, list) and len(confidence_val) > 0:
            confidence_val = float(confidence_val[0])

        # Create the entity object
        entity = Entity(
            uuid=str(uuid_val),
            name=str(name_val),
            type=str(type_val),
            confidence=float(confidence_val)
        )

        return entity

    except Exception as e:
        logger.error(f"Error parsing entity data: {e}")
        logger.error(f"Entity data: {entity_data}")
        return None
