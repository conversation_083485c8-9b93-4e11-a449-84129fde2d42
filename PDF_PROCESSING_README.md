# PDF Processing with Neo4j Knowledge Graph

This project demonstrates how to process PDF documents and build a knowledge graph in Neo4j.

## Overview

We've created a system that:
1. Extracts text from PDF documents
2. Chunks the text into manageable pieces (1200 characters each)
3. Stores the chunks in a Neo4j knowledge graph
4. Provides search capabilities across all documents

## Scripts

### Main Processing Scripts

- **`examples/add_pdf_recursive_chunks.py`**: The primary script for processing PDFs with recursive chunking at 1200 characters per chunk. This is the recommended approach.

- **`examples/add_pdf_pages_to_neo4j.py`**: Alternative script that processes PDFs one page at a time (one page = one fact).

- **`examples/search_neo4j.py`**: Script to search for information across all documents in the knowledge graph.

- **`examples/visualize_knowledge_graph.py`**: Script to generate statistics and visualization queries for the knowledge graph.

### Additional Scripts

- **`examples/create_test_data.py`**: Script to create test data in Neo4j.

- **`examples/check_neo4j_data.py`**: <PERSON><PERSON><PERSON> to check the data in Neo4j.

- **`examples/process_single_pdf.py`**: <PERSON><PERSON>t to process a single PDF file.

- **`examples/process_pdf_first_pages.py`**: Script to process just the first few pages of a PDF.

## Knowledge Graph Structure

The knowledge graph consists of:
- **Episode nodes**: Represent the source documents
- **Fact nodes**: Contain chunks of text from the documents
- **CONTAINS relationships**: Connect Episodes to their Facts
- **RELATED_TO relationships**: Connect related Facts

## Usage

### Processing PDFs

To process a PDF and add it to the knowledge graph:

```bash
python examples/add_pdf_recursive_chunks.py "path/to/your/document.pdf" [max_pages] [chunk_size] [overlap]
```

Parameters:
- `max_pages`: Maximum number of pages to process (optional, default: all pages)
- `chunk_size`: Size of each chunk in characters (optional, default: 1200)
- `overlap`: Overlap between chunks in characters (optional, default: 0)

### Searching the Knowledge Graph

To search for information across all documents:

```bash
python examples/search_neo4j.py "your search term"
```

### Visualizing the Knowledge Graph

To get statistics and visualization queries for the knowledge graph:

```bash
python examples/visualize_knowledge_graph.py
```

## Neo4j Queries

Here are some useful Neo4j Cypher queries to explore the knowledge graph:

```cypher
// View all nodes and relationships
MATCH p=()-[]-() 
RETURN p 
LIMIT 100

// View Episodes and their Facts
MATCH p=(e:Episode)-[:CONTAINS]->(f:Fact) 
RETURN p 
LIMIT 100

// Find Facts containing specific text
MATCH (f:Fact)
WHERE f.body CONTAINS "your_search_term"
RETURN f
LIMIT 10

// Find Episodes with specific names
MATCH (e:Episode)
WHERE e.name CONTAINS "your_search_term"
RETURN e
LIMIT 10

// View the database schema
CALL db.schema.visualization()
```

## Processed Documents

We've successfully processed the following documents:
1. "4 Nutritional therapies Karen Bridgman.pdf" (11 chunks)
2. "3 Antioxidant Controversy Shauna Ashewood.pdf" (9 chunks)
3. "1 Herbal Medicine practice Shauna Ashewood.pdf" (9 chunks)
4. "2 Conventional treatments Karen Bridgman.pdf" (9 chunks)
5. "1-Degree-of-Change.pdf" (8 chunks)

## Next Steps

Potential enhancements for the future:
1. Add vector embeddings for semantic search
2. Implement entity extraction to identify people, organizations, concepts, etc.
3. Create a web interface for searching and visualizing the knowledge graph
4. Connect LLMs to the knowledge graph for more accurate question answering
