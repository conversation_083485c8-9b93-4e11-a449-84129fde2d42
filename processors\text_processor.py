"""
Text Document Processor
Handles .txt, .md, .rtf files with enhanced text extraction.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class TextProcessor:
    """
    Processor for text documents (.txt, .md, .rtf).
    """
    
    def __init__(self):
        """Initialize the text processor."""
        self.supported_extensions = ['.txt', '.md', '.rtf']
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from a text file.
        
        Args:
            file_path: Path to the text file
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Try different encodings
            encodings = ['utf-8', 'utf-8-sig', 'latin1', 'cp1252', 'iso-8859-1']
            text = None
            encoding_used = None
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        text = f.read()
                    encoding_used = encoding
                    break
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    logger.warning(f"Error reading file with encoding {encoding}: {e}")
                    continue
            
            if text is None:
                return {
                    'success': False,
                    'error': f"Could not read file {file_path} with any supported encoding",
                    'text': '',
                    'metadata': {}
                }
            
            # Extract metadata
            metadata = await self._extract_metadata(file_path, encoding_used, len(text))
            
            return {
                'success': True,
                'text': text.strip(),
                'metadata': metadata,
                'ocr_provider': 'text_reader',
                'extraction_method': 'text_reader',
                'encoding': encoding_used
            }
            
        except Exception as e:
            logger.error(f"Error processing text file {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata(self, file_path: Path, encoding: str, text_length: int) -> Dict[str, Any]:
        """Extract metadata from text file."""
        try:
            stat = file_path.stat()
            
            # Count lines and words
            with open(file_path, 'r', encoding=encoding) as f:
                lines = f.readlines()
            
            line_count = len(lines)
            word_count = len(' '.join(lines).split())
            
            # Try to extract title from first line (for markdown files)
            title = file_path.stem
            if file_path.suffix.lower() == '.md' and lines:
                first_line = lines[0].strip()
                if first_line.startswith('#'):
                    title = first_line.lstrip('#').strip()
            
            metadata = {
                'title': title,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'encoding': encoding,
                'line_count': line_count,
                'word_count': word_count,
                'character_count': text_length,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'text_reader'
            }
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting metadata: {e}")
            return {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the text content.
        
        Args:
            file_path: Path to the text file
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
