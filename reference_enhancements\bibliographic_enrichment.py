"""
Bibliographic Enrichment Module

Enriches references with data from external bibliographic databases.
"""

import asyncio
import logging
import json
import re
from typing import List, Dict, Any, Optional
from datetime import datetime
import csv

# HTTP client for API requests
try:
    import aiohttp
    AIOHTTP_AVAILABLE = True
except ImportError:
    AIOHTTP_AVAILABLE = False

# Add the parent directory to the path for imports
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.falkordb_adapter import FalkorDBAdapter

logger = logging.getLogger(__name__)

class BibliographicEnricher:
    """
    Enriches references with data from external bibliographic databases.
    """
    
    def __init__(self):
        """Initialize the bibliographic enricher."""
        self.db = FalkorDBAdapter()
        self.session = None
        
        # API configuration
        self.crossref_email = os.getenv('CROSSREF_EMAIL', '<EMAIL>')
        self.semantic_scholar_api_key = os.getenv('SEMANTIC_SCHOLAR_API_KEY')
        self.pubmed_api_key = os.getenv('PUBMED_API_KEY')
        
        # Rate limiting
        self.crossref_delay = 1.0  # 1 second between requests
        self.semantic_scholar_delay = 0.1  # 100ms between requests
        self.pubmed_delay = 0.34  # ~3 requests per second
        
        if not AIOHTTP_AVAILABLE:
            logger.warning("⚠️ aiohttp not available. External enrichment will be disabled.")
    
    async def enrich_references(self, batch_size: int = 10, max_references: int = 50) -> Dict[str, Any]:
        """
        Enrich references with external bibliographic data.
        
        Args:
            batch_size: Number of references to process in each batch
            max_references: Maximum number of references to enrich
            
        Returns:
            Dictionary with enrichment results
        """
        logger.info(f"🔍 Starting bibliographic enrichment (batch_size={batch_size}, max={max_references})")
        
        if not AIOHTTP_AVAILABLE:
            return {
                "success": False,
                "error": "aiohttp not available for external API requests",
                "enriched_count": 0,
                "total_processed": 0
            }
        
        try:
            # Initialize HTTP session
            await self._init_session()
            
            # Get references that need enrichment
            references = await self._get_references_for_enrichment(max_references)
            logger.info(f"📚 Found {len(references)} references for enrichment")
            
            if not references:
                return {
                    "success": True,
                    "enriched_count": 0,
                    "total_processed": 0,
                    "message": "No references found that need enrichment"
                }
            
            # Process references in batches
            enriched_count = 0
            total_processed = 0
            
            for i in range(0, len(references), batch_size):
                batch = references[i:i + batch_size]
                logger.info(f"🔄 Processing batch {i//batch_size + 1}: {len(batch)} references")
                
                for ref in batch:
                    try:
                        enrichment_result = await self._enrich_single_reference(ref)
                        if enrichment_result.get('enriched', False):
                            enriched_count += 1
                        total_processed += 1
                        
                        # Small delay between requests
                        await asyncio.sleep(0.1)
                        
                    except Exception as e:
                        logger.error(f"❌ Error enriching reference {ref.get('node_id')}: {e}")
                        total_processed += 1
                
                # Delay between batches
                await asyncio.sleep(1.0)
            
            logger.info(f"✅ Enrichment complete: {enriched_count}/{total_processed} references enriched")
            
            return {
                "success": True,
                "enriched_count": enriched_count,
                "total_processed": total_processed,
                "batch_size": batch_size,
                "completion_rate": enriched_count / total_processed if total_processed > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"❌ Error in bibliographic enrichment: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "enriched_count": 0,
                "total_processed": 0
            }
        
        finally:
            await self._close_session()
    
    async def get_enriched_references(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get references that have been enriched with external data.
        
        Args:
            limit: Maximum number of references to return
            
        Returns:
            List of enriched references
        """
        logger.info(f"📊 Getting enriched references (limit={limit})")
        
        try:
            query = """
            MATCH (r:Reference)
            WHERE r.doi IS NOT NULL OR r.pmid IS NOT NULL OR r.semantic_scholar_id IS NOT NULL
            RETURN r.reference_text as reference_text,
                   r.authors as authors,
                   r.title as title,
                   r.year as year,
                   r.journal as journal,
                   r.doi as doi,
                   r.pmid as pmid,
                   r.semantic_scholar_id as semantic_scholar_id,
                   r.citation_count as citation_count,
                   r.enrichment_date as enrichment_date,
                   r.source_document as source_document,
                   id(r) as node_id
            ORDER BY r.enrichment_date DESC
            LIMIT $limit
            """
            
            result = self.db.execute_cypher(query, {'limit': limit})
            
            enriched_refs = []
            if len(result) > 1 and result[1]:
                for row in result[1]:
                    ref_dict = {
                        'reference_text': row[0] if row[0] else '',
                        'authors': row[1] if row[1] else '',
                        'title': row[2] if row[2] else '',
                        'year': row[3] if row[3] else '',
                        'journal': row[4] if row[4] else '',
                        'doi': row[5] if row[5] else '',
                        'pmid': row[6] if row[6] else '',
                        'semantic_scholar_id': row[7] if row[7] else '',
                        'citation_count': row[8] if row[8] else 0,
                        'enrichment_date': row[9] if row[9] else '',
                        'source_document': row[10] if row[10] else '',
                        'node_id': row[11] if len(row) > 11 else None
                    }
                    enriched_refs.append(ref_dict)
            
            logger.info(f"✅ Found {len(enriched_refs)} enriched references")
            return enriched_refs
            
        except Exception as e:
            logger.error(f"❌ Error getting enriched references: {e}")
            return []
    
    async def export_enriched_references(self, output_path: str) -> bool:
        """Export enriched references to CSV file."""
        try:
            enriched_refs = await self.get_enriched_references(limit=1000)
            
            if not enriched_refs:
                logger.warning("⚠️ No enriched references to export")
                return False
            
            with open(output_path, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                
                # Write header
                writer.writerow([
                    'Title', 'Authors', 'Year', 'Journal', 'DOI', 'PMID', 
                    'Semantic_Scholar_ID', 'Citation_Count', 'Enrichment_Date',
                    'Source_Document', 'Reference_Text'
                ])
                
                # Write enriched references
                for ref in enriched_refs:
                    writer.writerow([
                        ref.get('title', ''),
                        ref.get('authors', ''),
                        ref.get('year', ''),
                        ref.get('journal', ''),
                        ref.get('doi', ''),
                        ref.get('pmid', ''),
                        ref.get('semantic_scholar_id', ''),
                        ref.get('citation_count', 0),
                        ref.get('enrichment_date', ''),
                        ref.get('source_document', ''),
                        ref.get('reference_text', '')[:200] + '...' if len(ref.get('reference_text', '')) > 200 else ref.get('reference_text', '')
                    ])
            
            logger.info(f"✅ Exported {len(enriched_refs)} enriched references to: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error exporting enriched references: {e}")
            return False
    
    async def _init_session(self):
        """Initialize HTTP session."""
        if AIOHTTP_AVAILABLE:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=30),
                headers={'User-Agent': 'Graphiti Reference Enricher/1.0'}
            )
    
    async def _close_session(self):
        """Close HTTP session."""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def _get_references_for_enrichment(self, max_references: int) -> List[Dict[str, Any]]:
        """Get references that need enrichment."""
        query = """
        MATCH (r:Reference)
        WHERE (r.doi IS NULL OR r.doi = '') 
          AND (r.pmid IS NULL OR r.pmid = '')
          AND (r.semantic_scholar_id IS NULL OR r.semantic_scholar_id = '')
          AND (r.title IS NOT NULL AND r.title <> '')
        RETURN r.reference_text as reference_text,
               r.authors as authors,
               r.title as title,
               r.year as year,
               r.journal as journal,
               r.source_document as source_document,
               id(r) as node_id
        LIMIT $max_references
        """
        
        result = self.db.execute_cypher(query, {'max_references': max_references})
        
        references = []
        if len(result) > 1 and result[1]:
            for row in result[1]:
                ref_dict = {
                    'reference_text': row[0] if row[0] else '',
                    'authors': row[1] if row[1] else '',
                    'title': row[2] if row[2] else '',
                    'year': row[3] if row[3] else '',
                    'journal': row[4] if row[4] else '',
                    'source_document': row[5] if row[5] else '',
                    'node_id': row[6] if len(row) > 6 else None
                }
                references.append(ref_dict)
        
        return references
    
    async def _enrich_single_reference(self, reference: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich a single reference with external data."""
        enrichment_data = {}
        enriched = False
        
        title = reference.get('title', '').strip()
        authors = reference.get('authors', '').strip()
        
        if not title:
            return {'enriched': False, 'reason': 'No title available'}
        
        try:
            # Try Crossref first
            crossref_data = await self._query_crossref(title, authors)
            if crossref_data:
                enrichment_data.update(crossref_data)
                enriched = True
                await asyncio.sleep(self.crossref_delay)
            
            # Try Semantic Scholar if we have API key
            if self.semantic_scholar_api_key:
                semantic_data = await self._query_semantic_scholar(title, authors)
                if semantic_data:
                    enrichment_data.update(semantic_data)
                    enriched = True
                    await asyncio.sleep(self.semantic_scholar_delay)
            
            # Try PubMed if we have API key and it looks like a medical paper
            if self.pubmed_api_key and self._is_medical_paper(title, reference.get('journal', '')):
                pubmed_data = await self._query_pubmed(title, authors)
                if pubmed_data:
                    enrichment_data.update(pubmed_data)
                    enriched = True
                    await asyncio.sleep(self.pubmed_delay)
            
            # Update reference in database if we found enrichment data
            if enriched:
                await self._update_reference_with_enrichment(reference['node_id'], enrichment_data)
                logger.info(f"✅ Enriched reference: {title[:50]}...")
            
            return {
                'enriched': enriched,
                'enrichment_data': enrichment_data,
                'reference_id': reference['node_id']
            }
            
        except Exception as e:
            logger.error(f"❌ Error enriching reference '{title[:50]}...': {e}")
            return {'enriched': False, 'error': str(e)}
    
    async def _query_crossref(self, title: str, authors: str) -> Optional[Dict[str, Any]]:
        """Query Crossref API for reference data."""
        if not self.session:
            return None
        
        try:
            # Clean title for search
            clean_title = re.sub(r'[^\w\s]', ' ', title).strip()
            
            url = "https://api.crossref.org/works"
            params = {
                'query.title': clean_title,
                'rows': 1,
                'mailto': self.crossref_email
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get('message', {}).get('items', [])
                    
                    if items:
                        item = items[0]
                        return {
                            'doi': item.get('DOI', ''),
                            'crossref_title': item.get('title', [''])[0] if item.get('title') else '',
                            'crossref_journal': item.get('container-title', [''])[0] if item.get('container-title') else '',
                            'crossref_year': str(item.get('published-print', {}).get('date-parts', [['']])[0][0]) if item.get('published-print') else '',
                            'crossref_citation_count': item.get('is-referenced-by-count', 0)
                        }
        
        except Exception as e:
            logger.debug(f"Crossref query failed for '{title[:30]}...': {e}")
        
        return None
    
    async def _query_semantic_scholar(self, title: str, authors: str) -> Optional[Dict[str, Any]]:
        """Query Semantic Scholar API for reference data."""
        if not self.session or not self.semantic_scholar_api_key:
            return None
        
        try:
            url = "https://api.semanticscholar.org/graph/v1/paper/search"
            headers = {'x-api-key': self.semantic_scholar_api_key}
            params = {
                'query': title,
                'limit': 1,
                'fields': 'paperId,title,authors,year,journal,citationCount'
            }
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    papers = data.get('data', [])
                    
                    if papers:
                        paper = papers[0]
                        return {
                            'semantic_scholar_id': paper.get('paperId', ''),
                            'semantic_scholar_citation_count': paper.get('citationCount', 0)
                        }
        
        except Exception as e:
            logger.debug(f"Semantic Scholar query failed for '{title[:30]}...': {e}")
        
        return None
    
    async def _query_pubmed(self, title: str, authors: str) -> Optional[Dict[str, Any]]:
        """Query PubMed API for reference data."""
        if not self.session or not self.pubmed_api_key:
            return None
        
        try:
            # Search PubMed
            search_url = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils/esearch.fcgi"
            search_params = {
                'db': 'pubmed',
                'term': f'"{title}"[Title]',
                'retmode': 'json',
                'retmax': 1,
                'api_key': self.pubmed_api_key
            }
            
            async with self.session.get(search_url, params=search_params) as response:
                if response.status == 200:
                    data = await response.json()
                    id_list = data.get('esearchresult', {}).get('idlist', [])
                    
                    if id_list:
                        pmid = id_list[0]
                        return {
                            'pmid': pmid
                        }
        
        except Exception as e:
            logger.debug(f"PubMed query failed for '{title[:30]}...': {e}")
        
        return None
    
    def _is_medical_paper(self, title: str, journal: str) -> bool:
        """Check if a paper is likely medical/health related."""
        medical_keywords = [
            'health', 'medical', 'clinical', 'patient', 'disease', 'treatment',
            'therapy', 'medicine', 'pharmaceutical', 'drug', 'cancer', 'diabetes',
            'cardiovascular', 'neurology', 'psychiatry', 'surgery', 'pathology'
        ]
        
        text_to_check = (title + ' ' + journal).lower()
        return any(keyword in text_to_check for keyword in medical_keywords)
    
    async def _update_reference_with_enrichment(self, node_id: int, enrichment_data: Dict[str, Any]):
        """Update reference node with enrichment data."""
        # Build SET clause dynamically
        set_clauses = []
        params = {'node_id': node_id, 'enrichment_date': datetime.now().isoformat()}
        
        for key, value in enrichment_data.items():
            if value:  # Only set non-empty values
                set_clauses.append(f"r.{key} = ${key}")
                params[key] = value
        
        if set_clauses:
            set_clauses.append("r.enrichment_date = $enrichment_date")
            
            query = f"""
            MATCH (r:Reference)
            WHERE id(r) = $node_id
            SET {', '.join(set_clauses)}
            """
            
            self.db.execute_cypher(query, params)
