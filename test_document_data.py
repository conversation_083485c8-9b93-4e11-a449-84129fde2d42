#!/usr/bin/env python3
"""
Test script to check document data structure in FalkorDB.
"""

import asyncio
from database.database_service import get_falkordb_adapter

async def test_document_data():
    """Test FalkorDB document data structure"""
    print('Testing FalkorDB document data structure...')
    
    adapter = await get_falkordb_adapter()
    
    # Test 1: Check what a single document looks like
    print('\n1. Testing single document query:')
    query = 'MATCH (n:Episode) RETURN n LIMIT 1'
    result = adapter.execute_cypher(query)
    print(f'Raw result: {result}')
    
    if result and len(result) > 1 and len(result[1]) > 0:
        node_data = result[1][0][0]
        print(f'Node data type: {type(node_data)}')
        print(f'Node data: {node_data}')
        
        if isinstance(node_data, list):
            # Parse the FalkorDB node structure
            for item in node_data:
                if isinstance(item, list) and len(item) == 2:
                    if item[0] == 'properties':
                        print(f'Properties: {item[1]}')
    
    # Test 2: Check document properties directly
    print('\n2. Testing document properties query:')
    query = 'MATCH (n:Episode) RETURN n.uuid, n.name, n.file_path, n.processed_at LIMIT 3'
    result = adapter.execute_cypher(query)
    print(f'Properties result: {result}')
    
    # Test 3: Check property names
    print('\n3. Testing property names:')
    query = 'MATCH (n:Episode) RETURN keys(n) LIMIT 3'
    result = adapter.execute_cypher(query)
    print(f'Keys result: {result}')
    
    # Test 4: Check for documents with actual names
    print('\n4. Testing documents with names:')
    query = 'MATCH (n:Episode) WHERE n.name IS NOT NULL AND n.name <> "" RETURN n.uuid, n.name, n.file_path LIMIT 5'
    result = adapter.execute_cypher(query)
    print(f'Named documents result: {result}')
    
    # Test 5: Count and sample
    print('\n5. Testing document count and sample:')
    query = 'MATCH (n:Episode) RETURN count(n) as total'
    result = adapter.execute_cypher(query)
    print(f'Total documents: {result}')
    
    query = 'MATCH (n:Episode) RETURN n LIMIT 5'
    result = adapter.execute_cypher(query)
    print(f'Sample documents: {result}')

if __name__ == "__main__":
    asyncio.run(test_document_data())
