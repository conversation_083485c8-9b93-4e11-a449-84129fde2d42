"""
Question answering routes for the Graphiti application.
"""

from fastapi import APIRouter, HTTPException, Query, Body
from typing import List, Dict, Any, Optional
import re

from services.qa_service import get_relevant_facts, generate_answer

def get_formatted_citation(fact: dict) -> str:
    """
    Format a fact's citation in a scientific style.

    Args:
        fact: Fact dictionary containing metadata

    Returns:
        Formatted citation string
    """
    document_id = fact.get('document_id')
    document_title = fact.get('document_title')
    document_name = fact.get('document_name', document_title)
    document_author = fact.get('document_author')
    document_year = fact.get('document_year')
    chunk_num = fact.get('chunk_num')
    body = fact.get('body', '')

    # Check if this is a journal citation
    journal_match = re.search(r'([A-Za-z\s]+)\.\s*(\d{4})(?:;|\s+)(\d+)(?:\((\d+)\))?:(\d+-\d+)', body)
    if journal_match:
        journal = journal_match.group(1).strip()
        year = journal_match.group(2).strip()
        volume = journal_match.group(3).strip()
        pages = journal_match.group(5).strip()
        return f"{journal} ({year}). Volume {volume}, pages {pages}"

    # Check if this is a paper title with author
    author_title_match = re.search(r'([A-Z][a-z]+(?:,?\s+(?:and|&)?\s*[A-Z][a-z]+)*,?\s+(?:et\s+al\.?)?,?\s+\(\d{4}\))\s*([^\.]+)', body)
    if author_title_match:
        author_year = author_title_match.group(1).strip()
        title = author_title_match.group(2).strip()
        return f"{author_year}. {title}"

    # Extract markdown content if this is OCR metadata
    if 'OCR' in body and 'markdown=' in body:
        markdown_match = re.search(r'markdown=["\'](.*?)["\']', body)
        if markdown_match:
            # Extract the markdown content
            markdown_content = markdown_match.group(1)

            # Look for a title in the markdown
            title_match = re.search(r'#\s+([^#\n]+)', markdown_content)
            if title_match:
                title = title_match.group(1).strip()
                # Clean up the title
                title = re.sub(r'\\\w+', ' ', title)  # Remove escaped characters
                title = re.sub(r'\s+', ' ', title).strip()  # Clean up whitespace

                if document_title and isinstance(document_title, str):
                    # Clean up the document title
                    document_title = document_title.replace('.pdf', '')
                    # If it starts with a number followed by space, make it more readable
                    document_title = re.sub(r'^(\d+)\s+', '', document_title)
                    return f"{document_title}: {title}"
                else:
                    return title

    # Try to extract section title from the content
    section_title = None

    # Look for markdown headers (# Title)
    header_match = re.search(r'#\s+([^#\n]+)', body)
    if header_match:
        section_title = header_match.group(1).strip()
        # Clean up the section title
        section_title = re.sub(r'\\\w+', ' ', section_title)  # Remove escaped characters
        section_title = re.sub(r'\s+', ' ', section_title).strip()  # Clean up whitespace

    # Look for specific paper citations
    citation_info = extract_citation_from_text(body)

    # Build the citation string
    if citation_info.get('authors') and citation_info.get('year'):
        # We have a proper academic citation
        authors = citation_info['authors']
        year = citation_info['year']
        journal = citation_info.get('journal', '')
        title = citation_info.get('title', '')

        # Format as Author et al. (Year). Title. Journal.
        citation = f"{authors} ({year})"
        if title:
            citation += f". {title}"
        if journal:
            citation += f". {journal}"

    elif section_title:
        # We have a section title
        if document_title and isinstance(document_title, str):
            # Clean up the document title
            document_title = document_title.replace('.pdf', '')
            # If it starts with a number followed by space, make it more readable
            document_title = re.sub(r'^(\d+)\s+', '', document_title)

            # Create a citation with document metadata and section title
            citation = f"{document_title}: {section_title}"

            # Add author and year if available
            if document_author and document_author != "Unknown author":
                citation += f" by {document_author}"
                if document_year:
                    citation += f" ({document_year})"

            # Add document ID as a reference ID
            if document_id:
                citation += f" [DocID: {document_id}]"
        else:
            citation = section_title

    elif document_title and isinstance(document_title, str):
        # Just use the document title
        # Clean up the document title
        document_title = document_title.replace('.pdf', '')
        # If it starts with a number followed by space, make it more readable
        document_title = re.sub(r'^(\d+)\s+', '', document_title)

        # Create a citation with document metadata
        citation = document_title

        # Add author and year if available
        if document_author and document_author != "Unknown author":
            citation += f" by {document_author}"
            if document_year:
                citation += f" ({document_year})"

        if chunk_num:
            citation += f" (Section {chunk_num})"

        # Add document ID as a reference ID
        if document_id:
            citation += f" [DocID: {document_id}]"

    else:
        # Last resort fallback
        if chunk_num:
            citation = f"Document Section {chunk_num}"
        else:
            citation = "Unknown document"

    return citation


def extract_citation_from_text(text: str) -> dict:
    """
    Extract citation information from text.

    Args:
        text: Text to extract citation from

    Returns:
        Dictionary with citation information
    """
    citation_info = {
        'authors': None,
        'year': None,
        'title': None,
        'journal': None
    }

    # Pattern 1: Author et al. (Year). Title. Journal.
    pattern1 = r'([A-Z][a-z]+(?:[\s,]+(?:and|&)?\s*[A-Z][a-z]+)*(?:[\s,]+et\s+al\.?)?)\s*\(?(\d{4})\)?\.?\s*([^\.]+)\.?\s*([^\.]+)?'
    match1 = re.search(pattern1, text)
    if match1:
        citation_info['authors'] = match1.group(1).strip()
        citation_info['year'] = match1.group(2).strip()
        if match1.group(3):
            citation_info['title'] = match1.group(3).strip()
        if match1.group(4):
            citation_info['journal'] = match1.group(4).strip()
        return citation_info

    # Pattern 2: Author, Author, & Author (Year). Title.
    pattern2 = r'([A-Z][a-z]+(?:,\s*[A-Z][a-z]+)+(?:\s*&\s*[A-Z][a-z]+)?)\s*\(?(\d{4})\)?\.?\s*([^\.]+)?'
    match2 = re.search(pattern2, text)
    if match2:
        citation_info['authors'] = match2.group(1).strip()
        citation_info['year'] = match2.group(2).strip()
        if match2.group(3):
            citation_info['title'] = match2.group(3).strip()
        return citation_info

    # Pattern 3: Journal. Year;Volume(Issue):Pages
    pattern3 = r'([A-Za-z\s]+)\.\s*(\d{4})(?:;|\s+)(\d+)(?:\((\d+)\))?:(\d+-\d+)'
    match3 = re.search(pattern3, text)
    if match3:
        citation_info['journal'] = match3.group(1).strip()
        citation_info['year'] = match3.group(2).strip()
        return citation_info

    # Pattern 4: Look for specific author names followed by year
    pattern4 = r'([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)\s+et\s+al\.\s*\(?(\d{4})\)?'
    match4 = re.search(pattern4, text)
    if match4:
        citation_info['authors'] = match4.group(1).strip()
        citation_info['year'] = match4.group(2).strip()
        return citation_info

    return citation_info


def clean_fact_body(body: str) -> str:
    """
    Clean up fact body text by removing OCR metadata and formatting.

    Args:
        body: Raw fact body text

    Returns:
        Cleaned fact body text
    """
    # First, try to extract a scientific citation
    citation_info = extract_citation_from_text(body)
    if citation_info.get('authors') and citation_info.get('year'):
        # We have a proper academic citation
        authors = citation_info['authors']
        year = citation_info['year']
        title = citation_info.get('title', '')
        journal = citation_info.get('journal', '')

        # Format as a proper citation
        citation = f"{authors} ({year})"
        if title:
            citation += f". {title}"
        if journal:
            citation += f". {journal}"

        return citation

    # If no citation found, clean up the text and extract meaningful content

    # Step 1: Remove all OCR metadata and artifacts - more aggressive approach
    # First, check if this is mostly OCR metadata
    if 'OCR' in body and ('PageObject' in body or 'ImageObject' in body or 'dimensions' in body):
        # This is likely OCR metadata - extract only the meaningful content
        # Look for markdown content
        markdown_match = re.search(r'markdown=["\'](.*?)["\']', body)
        if markdown_match:
            # Extract the markdown content
            markdown_content = markdown_match.group(1)
            body = markdown_content

    # Now clean up any remaining OCR artifacts
    body = re.sub(r'OCRPageObject\([^)]*\)', '', body)
    body = re.sub(r'OCRImageObject\([^)]*\)', '', body)
    body = re.sub(r'OCRPageDimensions\([^)]*\)', '', body)
    body = re.sub(r'OCRPage\s*Object\([^)]*\)', '', body)
    body = re.sub(r'pages=\[[^]]*\]', '', body)
    body = re.sub(r'images=\[[^]]*\]', '', body)
    body = re.sub(r'dimensions=[^,)]*', '', body)
    body = re.sub(r'index=\d+', '', body)
    body = re.sub(r'markdown=["\'][^"\']*["\']', '', body)

    # Step 2: Remove markdown formatting
    body = re.sub(r'##+\s*', '', body)  # Remove markdown headers
    body = re.sub(r'\*\*|\*|__|\^|_', '', body)  # Remove bold, italic, etc.
    body = re.sub(r'!\[.*?\]\(.*?\)', '', body)  # Remove image references
    body = re.sub(r'img-\d+\.jpeg', '', body)  # Remove image filenames

    # Step 3: Remove special characters and clean up
    body = re.sub(r'\\\w+', ' ', body)  # Remove escaped characters
    body = re.sub(r'\$(.+?)\$', r'\1', body)  # Remove LaTeX-style math delimiters
    body = re.sub(r'\\', '', body)  # Remove all backslashes
    body = re.sub(r'\(\)', '', body)  # Remove empty parentheses
    body = re.sub(r'\[\]', '', body)  # Remove empty brackets

    # Step 4: Fix common OCR issues
    body = re.sub(r'(?<=[a-z])\.(?=[A-Z])', '. ', body)  # Add space after period if missing
    body = re.sub(r'(?<=[a-z])(?=[A-Z])', ' ', body)  # Add space between words if missing

    # Step 5: Clean up whitespace
    body = re.sub(r'\s+', ' ', body).strip()

    # Step 6: Extract meaningful content

    # Look for section titles (often more meaningful than random sentences)
    section_match = re.search(r'(?:^|\s)((?:Chemotherapy|Radiotherapy|Treatment|Effects|Studies|Clinical|Research|Ginseng|Withania|Astragalus|Cancer)[^\.!?]*[\.!?])', body)
    if section_match:
        return section_match.group(1).strip()

    # First, try to find a complete sentence
    sentences = re.findall(r'[A-Z][^\.!?]*[\.!?]', body)
    if sentences:
        # Use the first complete sentence if it's substantial
        if len(sentences[0]) > 30:
            return sentences[0].strip()
        # Otherwise use the first two sentences if available
        elif len(sentences) > 1:
            return (sentences[0] + ' ' + sentences[1]).strip()
        else:
            return sentences[0].strip()

    # If no complete sentences found, look for meaningful phrases
    phrases = re.findall(r'[A-Z][^,;:]*[,;:]', body)
    if phrases and len(phrases[0]) > 20:
        return phrases[0].strip()

    # If still nothing good found, just take a clean portion of the text
    if len(body) > 20:
        # Find a good starting point (capital letter)
        start_match = re.search(r'[A-Z]', body)
        if start_match:
            start_pos = start_match.start()
            # Take up to 100 characters, but try to end at punctuation
            excerpt = body[start_pos:start_pos+150]
            end_match = re.search(r'[\.!?;,]', excerpt[50:])
            if end_match:
                return excerpt[:50+end_match.start()+1].strip()
            return excerpt.strip()

    # Last resort: just return the cleaned text
    return body[:150].strip()
# Import utilities
from utils.logging_utils import get_logger
from utils.config import LLM_PROVIDER, LLM_MODEL

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api", tags=["qa"])

@router.post("/qa/answer")
async def answer_question(
    question: str = Body(...),
    context: Optional[str] = Body(None),
    llm_provider: Optional[str] = Body(LLM_PROVIDER),
    llm_model: Optional[str] = Body(LLM_MODEL),
    max_facts: int = Body(10, ge=1, le=50),
    response_length: Optional[str] = Body("brief"),
    conversation_context: Optional[List[Dict[str, str]]] = Body([]),
    temperature: Optional[float] = Body(0.3, ge=0.0, le=1.0)
):
    """
    Answer a question.

    Args:
        question: Question
        llm_provider: LLM provider
        llm_model: LLM model
        max_facts: Maximum number of facts to use

    Returns:
        Answer
    """
    try:
        # Log the request
        logger.info(f"Answering question: {question}")
        if context:
            logger.info(f"Context provided: {len(context)} characters")

        # Get relevant facts
        facts = await get_relevant_facts(question, max_facts)

        if not facts:
            return {
                "question": question,
                "answer": "I don't have enough information to answer this question.",
                "references": [],
                "sources": []
            }

        # Generate answer with new parameters
        answer = await generate_answer(
            question=question,
            facts=facts,
            llm_provider=llm_provider,
            llm_model=llm_model,
            response_length=response_length,
            conversation_context=conversation_context,
            temperature=temperature
        )

        # Format the response for the frontend
        sources = []

        # Check if we have enhanced citations
        if "citations" in answer and answer["citations"] and answer["citations"].strip():
            # Use enhanced citation format
            response_data = {
                "question": question,
                "answer": answer.get("answer", ""),
                "citations": answer.get("citations", ""),
                "references": answer.get("references", []),
                "reference_summary": answer.get("reference_summary", {}),
                "citation_style": answer.get("citation_style", "apa"),
                "total_references": answer.get("total_references", 0),
                "sources": answer.get("references", [])  # Enhanced references
            }

            logger.info(f"Returning enhanced citation response with {len(response_data['references'])} references")
        else:
            # Fallback to original format
            # Extract all reference numbers from the answer text
            import re
            reference_numbers = re.findall(r'\[(\d+)\]', answer.get("answer", ""))
            reference_numbers = [int(num) for num in reference_numbers]

            # Create a map of reference numbers to facts
            reference_map = {}
            for i, fact in enumerate(answer.get("references", [])):
                # If we have reference numbers in the answer, use them
                if i < len(reference_numbers):
                    reference_map[reference_numbers[i]] = fact
                else:
                    # Otherwise, use sequential numbering
                    reference_map[i + 1] = fact

            # Sort the reference numbers to ensure they appear in order
            sorted_references = sorted(reference_map.keys())

            # Add each reference to the sources list (legacy format)
            for ref_num in sorted_references:
                fact = reference_map[ref_num]

                # Get document title - if it's not available, try to get it from the document_id
                document_title = fact.get("document_title", "")
                if not document_title and fact.get("document_id"):
                    # Try to get the document title from the database
                    try:
                        from services.document_service import get_document_details
                        doc_details = await get_document_details(fact.get("document_id"))
                        if doc_details and doc_details.name and doc_details.name != "Unknown document":
                            document_title = doc_details.name
                    except Exception as e:
                        logger.warning(f"Error getting document title: {str(e)}")

                sources.append({
                    "document": get_formatted_citation(fact),
                    "preview": clean_fact_body(fact.get("body", "")),
                    "document_id": fact.get("document_id", ""),
                    "document_title": document_title,
                    "chunk_num": fact.get("chunk_num", ""),
                    "reference_number": ref_num,  # Add the reference number
                    "metadata": {
                        "author": fact.get("document_author", ""),
                        "year": fact.get("document_year", ""),
                        "journal": fact.get("journal", ""),
                        "doi": fact.get("doi", "")
                    }
                })

            response_data = {
                "question": question,
                "answer": answer.get("answer", ""),
                "sources": sources
            }

        return response_data

    except Exception as e:
        logger.error(f"Error answering question: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error answering question: {str(e)}"
        )

@router.get("/qa/facts")
async def get_facts(
    question: str = Query(...),
    limit: int = Query(10, ge=1, le=50)
):
    """
    Get facts relevant to a question.

    Args:
        question: Question
        limit: Maximum number of facts to return

    Returns:
        Relevant facts
    """
    try:
        facts = await get_relevant_facts(question, limit)
        return {"facts": facts, "count": len(facts)}

    except Exception as e:
        logger.error(f"Error getting facts: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Error getting facts: {str(e)}"
        )
