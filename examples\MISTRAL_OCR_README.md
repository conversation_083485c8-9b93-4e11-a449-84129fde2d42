# Mistral OCR + Graphiti Integration

This example demonstrates how to process PDF documents using Mistral OCR and add them to a Graphiti knowledge graph.

## Features

- **High-Quality OCR**: Uses Mistral's advanced OCR capabilities to extract text while preserving document structure
- **Markdown Preservation**: Maintains document formatting including headers, paragraphs, lists, and tables
- **Entity Extraction**: Automatically identifies entities and relationships in the documents
- **Knowledge Graph Integration**: Adds documents, entities, and relationships to Graphiti
- **Vector Search**: Enables semantic search across your document collection

## Prerequisites

- Neo4j database (running locally or remotely)
- Mistral API key (for OCR processing)
- OpenAI API key (for LLM and embeddings)
- Python 3.9+
- Graphiti installed (`pip install graphiti-core`)

## Setup Instructions

1. Make sure your Neo4j database is running and accessible.

2. Set up your environment variables:
   - Copy the `.env.example` file to `.env` in the root directory (if not already done)
   - Add your API keys to the `.env` file:
     ```
     MISTRAL_API_KEY=your-mistral-api-key-here
     OPENAI_API_KEY=your-openai-api-key-here
     NEO4J_URI=bolt://localhost:7687
     NEO4J_USER=neo4j
     NEO4J_PASSWORD=Triathlon16!
     ```

3. Install the required packages:
   ```bash
   pip install mistralai graphiti-core
   ```

4. Create a directory for your PDF documents:
   ```bash
   mkdir documents
   ```

5. Copy your PDF files into the `documents` directory.

## Running the Example

Run the script to process your documents and add them to Graphiti:

```bash
python examples/mistral_ocr_graphiti.py
```

## How It Works

1. **Document Processing**:
   - The script scans the `documents` directory for PDF files
   - Each PDF is processed using Mistral OCR to extract text with structure preservation
   - The extracted text is returned in markdown format

2. **Content Processing**:
   - Large documents are split into manageable chunks while preserving structure
   - Each document is added as an Episode in Graphiti
   - Each chunk is added as a Fact connected to the document Episode

3. **Entity Extraction**:
   - The LLM analyzes the document content to identify entities (people, organizations, concepts, etc.)
   - Relationships between entities are identified
   - Entities and relationships are added to the knowledge graph

4. **Vector Embeddings**:
   - Graphiti automatically generates embeddings for all text content
   - These embeddings enable semantic search across your documents

## Querying Your Document Knowledge Graph

After processing your documents, you can query them using Graphiti's search capabilities:

```python
# Search for information in your documents
search_results = await graphiti.search(
    "your search query here",
    limit=5,
)

# Print search results
for i, result in enumerate(search_results.edges):
    print(f"{i+1}. {result.fact} (Score: {result.score})")
```

## Viewing the Knowledge Graph

You can view your document knowledge graph in the Neo4j Browser:

1. Open Neo4j Browser (http://localhost:7474)
2. Connect to your database (username: neo4j, password: Triathlon16!)
3. Run Cypher queries to explore your data:

```cypher
// View all document episodes
MATCH (e:Episode) RETURN e LIMIT 100

// View facts extracted from documents
MATCH (e:Episode)-[:CONTAINS]->(f:Fact) RETURN e, f LIMIT 100

// View entities and relationships
MATCH p=(f1:Fact)-[:RELATED_TO]->(f2:Fact) RETURN p LIMIT 100

// Search for specific content
MATCH (f:Fact)
WHERE f.fact_body CONTAINS "your search term"
RETURN f LIMIT 10
```

## Customization Options

You can customize the script by modifying the following parameters:

- **Chunk Size**: Adjust the `chunk_size` parameter in the `chunk_markdown` function to control how documents are split
- **Entity Extraction**: Set `extract_entities=False` in the `process_document_directory` function call to disable entity extraction
- **OCR Model**: Change the `model` parameter in the `MistralOCRProcessor` initialization to use a different Mistral OCR model
- **LLM Model**: Modify the `model` parameter in the `OpenAIClient` initialization to use a different OpenAI model

## Troubleshooting

- **OCR Processing Errors**: Check that your Mistral API key is correct and has access to the OCR API
- **Neo4j Connection Issues**: Verify that your Neo4j database is running and the connection details are correct
- **Large Document Handling**: If you encounter memory issues with very large documents, try reducing the `chunk_size` parameter

## Additional Resources

- [Mistral OCR Documentation](https://docs.mistral.ai/api/ocr/)
- [Graphiti Documentation](https://help.getzep.com/graphiti)
- [Neo4j Documentation](https://neo4j.com/docs/)
