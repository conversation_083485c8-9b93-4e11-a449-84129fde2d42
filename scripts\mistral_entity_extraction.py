#!/usr/bin/env python3
"""
FalkorDB-compatible Mistral OCR Entity Extraction Script
Uses Mistral OCR for document processing and entity extraction with strong prompts.
"""

import asyncio
import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
import json

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from utils.mistral_ocr import MistralOCRProcessor
from utils.config import get_config
from database.database_service import get_falkordb_adapter, create_episode_node, create_fact_node, create_entity_node
from services.embedding_processor import EmbeddingProcessor

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MistralEntityExtractor:
    """Enhanced entity extraction using Mistral OCR with strong prompts."""

    def __init__(self):
        """Initialize the Mistral entity extractor."""
        self.config = get_config()
        self.mistral_ocr = None
        self.embedding_processor = None

        # Initialize Mistral OCR
        import os
        mistral_api_key = os.getenv('MISTRAL_API_KEY')
        if mistral_api_key:
            try:
                self.mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)
                logger.info("✅ Mistral OCR initialized successfully")
            except Exception as e:
                logger.error(f"❌ Failed to initialize Mistral OCR: {e}")
        else:
            logger.error("❌ No Mistral API key found")

        # Initialize embedding processor
        try:
            self.embedding_processor = EmbeddingProcessor()
            logger.info("✅ Embedding processor initialized")
        except Exception as e:
            logger.warning(f"⚠️ Embedding processor initialization failed: {e}")

    def get_entity_extraction_prompt(self, text: str) -> str:
        """Generate comprehensive prompt for medical entity extraction."""
        return f"""You are an expert medical and scientific knowledge extraction AI. Extract ALL entities and relationships from the following medical/scientific text with maximum comprehensiveness.

CRITICAL INSTRUCTIONS:
1. Extract EVERY medical, scientific, botanical, chemical, and clinical entity mentioned
2. Be EXHAUSTIVE - this is medical literature requiring comprehensive extraction
3. Include ALL diseases, symptoms, conditions, treatments, chemicals, processes, people, organizations
4. Extract specific dosages, measurements, timeframes, and clinical parameters
5. Include ALL plant parts, chemical compounds, biological processes, and mechanisms

ENTITY TYPES (extract ALL that apply):
- Herb: Medicinal plants, botanical remedies (Echinacea, etc.)
- Plant: Plant species, plant parts (roots, aerial parts, flowering tops)
- Chemical: All chemical compounds, acids, alkaloids, constituents (alkylamides, caffeic acid, etc.)
- Disease: Medical conditions, infections, disorders (common cold, rhinovirus, etc.)
- Symptom: Clinical signs, manifestations (fever, cough, sore throat, etc.)
- Treatment: Therapeutic approaches, remedies, interventions
- Process: Biological/chemical processes (phagocytosis, metabolism, absorption)
- Protein: Proteins, receptors, enzymes (cannabinoid receptors, CD69, CD25)
- Cell: Cell types, immune cells (T cells, B cells, granulocytes, natural killer cells)
- Medication: Pharmaceutical drugs, preparations, formulations
- Nutrient: Vitamins, minerals, nutritional compounds
- Condition: Health states, clinical conditions
- Measurement: Dosages, concentrations, timeframes (mg/g, days, weeks)
- Person: Researchers, physicians, authors (Dr. Gerhard Madaus, etc.)
- Organization: Institutions, companies, groups (Native Americans, Eclectics)
- Research: Studies, trials, methodologies (placebo-controlled trial, etc.)
- Location: Geographical locations, body parts (Plains region, liver, spleen)
- Publication: Journals, books, references

RELATIONSHIP TYPES (extract ALL that apply):
- TREATS: X treats Y
- PREVENTS: X prevents Y
- CAUSES: X causes Y
- CONTAINS: X contains Y
- INCREASES: X increases Y
- DECREASES: X decreases Y
- ACTIVATES: X activates Y
- INHIBITS: X inhibits Y
- BINDS_TO: X binds to Y
- METABOLIZES: X metabolizes Y
- ABSORBS: X absorbs Y
- EXPRESSES: X expresses Y
- STIMULATES: X stimulates Y
- MODULATES: X modulates Y
- DERIVED_FROM: X derived from Y
- USED_FOR: X used for Y
- ASSOCIATED_WITH: X associated with Y
- FOUND_IN: X found in Y
- AFFECTS: X affects Y

TEXT TO ANALYZE:
{text}

EXTRACT COMPREHENSIVELY - aim for 50+ entities from this rich medical text. Include:
- All plant species and parts mentioned
- All chemical compounds and constituents
- All diseases, symptoms, and conditions
- All biological processes and mechanisms
- All people, organizations, and research
- All measurements, dosages, and timeframes
- All treatments and therapeutic uses

Return ONLY a valid JSON object:
{{
    "entities": [
        {{
            "name": "exact entity name from text",
            "type": "entity type from list above",
            "description": "detailed description based on context",
            "confidence": 0.95
        }}
    ],
    "relationships": [
        {{
            "source": "source entity name",
            "target": "target entity name",
            "type": "relationship type from list above",
            "description": "detailed description of relationship",
            "confidence": 0.90
        }}
    ]
}}

IMPORTANT: Return ONLY the JSON object, no markdown formatting."""

    async def extract_entities_from_text(self, text: str) -> Dict[str, Any]:
        """Extract entities from text using Mistral with strong prompts."""
        if not self.mistral_ocr:
            logger.error("Mistral OCR not available")
            return {"entities": [], "relationships": []}

        try:
            # Process full text in chunks if needed, but don't truncate
            original_length = len(text)

            # Skip very short text
            if len(text.strip()) < 50:
                logger.info("Text too short for entity extraction")
                return {"entities": [], "relationships": []}

            # For very long text, process in overlapping chunks and merge results
            if len(text) > 12000:
                logger.info(f"Processing long text ({original_length} chars) in chunks for comprehensive extraction")
                return await self._extract_from_long_text(text)

            logger.info(f"Processing text of {original_length} characters for entity extraction")

            prompt = self.get_entity_extraction_prompt(text)

            # Use OpenRouter client for entity extraction (more reliable)
            from utils.open_router_client import OpenRouterClient
            import os

            openrouter_api_key = os.getenv('OPENROUTER_API_KEY') or os.getenv('OPEN_ROUTER_API_KEY')
            if not openrouter_api_key:
                logger.error("No OpenRouter API key found")
                return {"entities": [], "relationships": []}

            client = OpenRouterClient(
                api_key=openrouter_api_key,
                model="meta-llama/llama-4-maverick"
            )

            response_text = client.generate_completion(
                system_prompt="You are an expert medical and scientific knowledge extraction AI.",
                user_prompt=prompt,
                temperature=0.1,
                max_tokens=2000
            )

            # Parse JSON response
            try:
                # Clean up response if it has markdown formatting
                if "```json" in response_text:
                    response_text = response_text.split("```json")[1].split("```")[0].strip()
                elif "```" in response_text:
                    response_text = response_text.split("```")[1].split("```")[0].strip()

                extracted_data = json.loads(response_text)

                # Validate structure
                if not isinstance(extracted_data, dict):
                    raise ValueError("Response is not a dictionary")

                entities = extracted_data.get("entities", [])
                relationships = extracted_data.get("relationships", [])

                logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
                return extracted_data

            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse JSON response: {e}")
                logger.debug(f"Raw response: {response_text[:500]}...")
                return {"entities": [], "relationships": []}

        except Exception as e:
            logger.error(f"Error in entity extraction: {e}")
            return {"entities": [], "relationships": []}

    async def _extract_from_long_text(self, text: str) -> Dict[str, Any]:
        """Extract entities from long text by processing in overlapping chunks."""
        chunk_size = 10000
        overlap = 2000
        chunks = []

        # Split text into overlapping chunks
        start = 0
        while start < len(text):
            end = min(start + chunk_size, len(text))
            chunk = text[start:end]
            chunks.append(chunk)
            start = end - overlap
            if start >= len(text):
                break

        logger.info(f"Processing {len(chunks)} chunks for comprehensive entity extraction")

        all_entities = []
        all_relationships = []
        entity_names = set()  # To avoid duplicates

        for i, chunk in enumerate(chunks):
            logger.info(f"Processing chunk {i+1}/{len(chunks)}")
            try:
                chunk_result = await self._extract_from_single_chunk(chunk)

                # Add unique entities
                for entity in chunk_result.get("entities", []):
                    if entity["name"] not in entity_names:
                        all_entities.append(entity)
                        entity_names.add(entity["name"])

                # Add all relationships (they might be different even with same entities)
                all_relationships.extend(chunk_result.get("relationships", []))

            except Exception as e:
                logger.warning(f"Error processing chunk {i+1}: {e}")
                continue

        logger.info(f"Extracted {len(all_entities)} unique entities and {len(all_relationships)} relationships from long text")

        return {
            "entities": all_entities,
            "relationships": all_relationships
        }

    async def _extract_from_single_chunk(self, text: str) -> Dict[str, Any]:
        """Extract entities from a single chunk of text."""
        prompt = self.get_entity_extraction_prompt(text)

        # Use OpenRouter client for entity extraction (more reliable)
        from utils.open_router_client import OpenRouterClient
        import os

        openrouter_api_key = os.getenv('OPENROUTER_API_KEY') or os.getenv('OPEN_ROUTER_API_KEY')
        if not openrouter_api_key:
            logger.error("No OpenRouter API key found")
            return {"entities": [], "relationships": []}

        client = OpenRouterClient(
            api_key=openrouter_api_key,
            model="meta-llama/llama-4-maverick"
        )

        response_text = client.generate_completion(
            system_prompt="You are an expert medical and scientific knowledge extraction AI.",
            user_prompt=prompt,
            temperature=0.1,
            max_tokens=3000  # Increased for comprehensive extraction
        )

        # Parse JSON response
        try:
            # Clean up response if it has markdown formatting
            if "```json" in response_text:
                response_text = response_text.split("```json")[1].split("```")[0].strip()
            elif "```" in response_text:
                response_text = response_text.split("```")[1].split("```")[0].strip()

            extracted_data = json.loads(response_text)

            # Validate structure
            if not isinstance(extracted_data, dict):
                raise ValueError("Response is not a dictionary")

            entities = extracted_data.get("entities", [])
            relationships = extracted_data.get("relationships", [])

            return extracted_data

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            logger.debug(f"Raw response: {response_text[:500]}...")
            return {"entities": [], "relationships": []}

    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """Process a document with Mistral OCR and extract entities."""
        logger.info(f"Processing document: {file_path}")

        if not self.mistral_ocr:
            return {"success": False, "error": "Mistral OCR not available"}

        try:
            # Extract text using Mistral OCR
            logger.info("Extracting text with Mistral OCR...")
            ocr_result = await self.mistral_ocr.process_pdf(file_path)

            if not ocr_result.get("success"):
                return {"success": False, "error": f"OCR failed: {ocr_result.get('error')}"}

            text = ocr_result.get("text", "")
            if not text:
                return {"success": False, "error": "No text extracted"}

            logger.info(f"Extracted {len(text)} characters of text")

            # Extract entities
            logger.info("Extracting entities...")
            entities_data = await self.extract_entities_from_text(text)

            return {
                "success": True,
                "text": text,
                "entities": entities_data.get("entities", []),
                "relationships": entities_data.get("relationships", []),
                "text_length": len(text)
            }

        except Exception as e:
            logger.error(f"Error processing document: {e}")
            return {"success": False, "error": str(e)}

    async def process_and_store_document(self, file_path: str, episode_id: str = None) -> Dict[str, Any]:
        """Process document and store entities in FalkorDB."""
        result = await self.process_document(file_path)

        if not result["success"]:
            return result

        try:
            db = get_falkordb_adapter()

            # Create episode if not provided
            if not episode_id:
                document_name = Path(file_path).stem
                episode_id = await create_episode_node(
                    f"Document: {document_name}",
                    {
                        'uuid': f"doc_{document_name}_{int(asyncio.get_event_loop().time())}",
                        'source_file': file_path,
                        'processed_with': 'mistral_ocr'
                    }
                )
                logger.info(f"Created episode: {episode_id}")

            # Store entities
            entity_count = 0
            for entity in result["entities"]:
                try:
                    await create_entity_node(
                        entity["name"],
                        entity["type"],
                        {
                            'uuid': f"entity_{entity['name'].replace(' ', '_')}_{int(asyncio.get_event_loop().time())}",
                            'description': entity.get("description", ""),
                            'confidence': entity.get("confidence", 0.0),
                            'source_document': file_path,
                            'episode_id': episode_id
                        }
                    )
                    entity_count += 1
                except Exception as e:
                    logger.warning(f"Failed to store entity {entity['name']}: {e}")

            logger.info(f"✅ Stored {entity_count} entities in FalkorDB")

            result["stored_entities"] = entity_count
            result["episode_id"] = episode_id
            return result

        except Exception as e:
            logger.error(f"Error storing entities: {e}")
            result["storage_error"] = str(e)
            return result

async def main():
    """Main function to test entity extraction."""
    import argparse

    parser = argparse.ArgumentParser(description="Extract entities from PDF using Mistral OCR")
    parser.add_argument("file_path", help="Path to PDF file")
    parser.add_argument("--output", help="Output JSON file path")
    parser.add_argument("--store", action="store_true", help="Store entities in FalkorDB")

    args = parser.parse_args()

    if not os.path.exists(args.file_path):
        logger.error(f"File not found: {args.file_path}")
        return

    # Initialize extractor
    extractor = MistralEntityExtractor()

    # Process document
    if args.store:
        result = await extractor.process_and_store_document(args.file_path)
    else:
        result = await extractor.process_document(args.file_path)

    if result["success"]:
        logger.info(f"✅ Successfully processed {args.file_path}")
        logger.info(f"📊 Extracted {len(result['entities'])} entities")
        logger.info(f"🔗 Extracted {len(result['relationships'])} relationships")

        if args.store and "stored_entities" in result:
            logger.info(f"💾 Stored {result['stored_entities']} entities in FalkorDB")

        # Save results if output path provided
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            logger.info(f"💾 Results saved to {args.output}")

        # Print sample entities
        for i, entity in enumerate(result['entities'][:5]):
            logger.info(f"Entity {i+1}: {entity['name']} ({entity['type']})")

    else:
        logger.error(f"❌ Failed to process {args.file_path}: {result['error']}")

if __name__ == "__main__":
    asyncio.run(main())
