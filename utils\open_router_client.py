"""
Client for Open Router API to access various LLM models
"""

import os
import json
import logging
import httpx
import asyncio
from typing import List, Dict, Any, Optional, Union

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class OpenRouterClient:
    """Client for Open Router API to access various LLM models"""

    def __init__(self, api_key: str = None, model: str = "qwen/qwen3-4b"):
        """
        Initialize the OpenRouterClient

        Args:
            api_key: The API key for Open Router
            model: The model to use (default: qwen/qwen3-4b)
        """
        self.api_key = api_key or os.environ.get('OPEN_ROUTER_API_KEY')
        if not self.api_key:
            raise ValueError("Open Router API key is required")

        self.model = model
        self.base_url = "https://openrouter.ai/api/v1"
        logger.info(f"Initialized OpenRouterClient with model: {model}")

    def generate_completion(self, system_prompt: str, user_prompt: str, temperature: float = 0.3, max_tokens: int = 2000) -> str:
        """
        Synchronous method to generate a completion using the Open Router API

        Args:
            system_prompt: The system prompt
            user_prompt: The user prompt
            temperature: Temperature for generation
            max_tokens: Maximum number of tokens to generate

        Returns:
            The generated text
        """
        logger.info(f"Generating completion with model: {self.model}")

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://graphiti.knowledge-graph.ai"  # Replace with your actual domain
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        # Log payload safely without special characters
        logger.info(f"Making POST request to {self.base_url}/chat/completions")
        logger.info(f"Model: {self.model}, Temperature: {temperature}, Max tokens: {max_tokens}")

        try:
            with httpx.Client() as client:
                response = client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=60.0
                )

                logger.info(f"Received response with status code: {response.status_code}")

                if response.status_code == 200:
                    response_json = response.json()
                    logger.info(f"Successful response from Open Router API: {response_json}")

                    # Extract the generated text from the response
                    if 'choices' in response_json and len(response_json['choices']) > 0:
                        if 'message' in response_json['choices'][0] and 'content' in response_json['choices'][0]['message']:
                            return response_json['choices'][0]['message']['content']

                    # If we couldn't extract the text, return an error message
                    return "I'm sorry, but I couldn't generate a response at this time."
                else:
                    logger.error(f"Error from Open Router API: {response.status_code} - {response.text}")
                    return f"Error: {response.status_code} - {response.text}"
        except Exception as e:
            logger.error(f"Error generating completion: {str(e)}")
            return f"Error generating completion: {str(e)}"

    async def generate_completion_async(self, messages: List[Dict[str, str]], temperature: float = 0.7, max_tokens: int = 2000) -> Dict[str, Any]:
        """
        Asynchronous method to generate a completion using the Open Router API

        Args:
            messages: List of message dictionaries with 'role' and 'content' keys
            temperature: Temperature for generation
            max_tokens: Maximum number of tokens to generate

        Returns:
            Dictionary containing the response
        """
        logger.info(f"Generating completion with model: {self.model}")
        logger.info(f"Messages: {messages}")

        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://graphiti.knowledge-graph.ai"  # Replace with your actual domain
        }

        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        # Log request safely without special characters
        logger.info(f"Making async POST request to {self.base_url}/chat/completions")
        logger.info(f"Model: {self.model}, Temperature: {temperature}, Max tokens: {max_tokens}")

        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload,
                    timeout=60.0
                )

                logger.info(f"Received response with status code: {response.status_code}")

                if response.status_code == 200:
                    response_json = response.json()
                    logger.info(f"Successful response from Open Router API: {response_json}")
                    return response_json
                else:
                    logger.error(f"Error from Open Router API: {response.status_code} - {response.text}")
                    raise Exception(f"Open Router API error: {response.status_code} - {response.text}")
        except Exception as e:
            logger.error(f"Error generating completion: {str(e)}")
            raise
