#!/usr/bin/env python3
"""
Test UI fixes for document and entity display
"""

import requests
import time

def test_ui_fixes():
    """Test the UI fixes for document and entity display"""
    
    print("🖥️ Testing UI Fixes")
    print("=" * 50)
    
    base_url = "http://127.0.0.1:9753"
    
    # Test 1: Documents API
    print("📄 Testing Documents API...")
    try:
        response = requests.get(f"{base_url}/api/documents", timeout=10)
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            print(f"✅ Documents API working - {len(documents)} documents found")
            
            if documents:
                sample_doc = documents[0]
                print(f"📋 Sample document structure:")
                for key, value in sample_doc.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"  {key}: {value}")
                
                # Check for proper field names
                has_filename = 'filename' in sample_doc
                has_upload_date = 'upload_date' in sample_doc
                print(f"  ✅ Has filename field: {has_filename}")
                print(f"  ✅ Has upload_date field: {has_upload_date}")
        else:
            print(f"❌ Documents API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Documents API error: {e}")
    
    # Test 2: Entities API
    print(f"\n🏷️ Testing Entities API...")
    try:
        response = requests.get(f"{base_url}/api/entities", timeout=10)
        if response.status_code == 200:
            data = response.json()
            entities = data.get('entities', [])
            print(f"✅ Entities API working - {len(entities)} entities found")
            
            # Look for entities with special characters
            special_entities = [e for e in entities if '$' in e.get('name', '')]
            if special_entities:
                print(f"📋 Found {len(special_entities)} entities with special characters:")
                for entity in special_entities[:5]:  # Show first 5
                    name = entity.get('name', 'Unknown')
                    entity_type = entity.get('type', 'Unknown')
                    print(f"  - {name} ({entity_type})")
                    
                    # Test the formatting function logic
                    formatted_name = name.replace('$eta$', 'β').replace('$alpha$', 'α').replace('$beta$', 'β')
                    if formatted_name != name:
                        print(f"    → Would be formatted as: {formatted_name}")
            else:
                print("  No entities with special characters found")
        else:
            print(f"❌ Entities API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Entities API error: {e}")
    
    # Test 3: UI Pages
    print(f"\n🌐 Testing UI Pages...")
    
    ui_pages = [
        ("/documents", "Documents Page"),
        ("/entities", "Entities Page"),
        ("/references", "References Page"),
    ]
    
    for endpoint, name in ui_pages:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=10)
            if response.status_code == 200:
                content = response.text
                # Check for JavaScript functions
                has_format_function = 'formatEntityName' in content
                has_bootstrap = 'bootstrap' in content.lower()
                print(f"✅ {name} accessible")
                if endpoint == "/entities":
                    print(f"  - Has formatEntityName function: {has_format_function}")
                print(f"  - Has Bootstrap styling: {has_bootstrap}")
            else:
                print(f"❌ {name} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {name} error: {e}")
    
    # Test 4: JavaScript Files
    print(f"\n📜 Testing JavaScript Files...")
    
    js_files = [
        "/static/entities.js",
        "/static/js/entity_detail.js",
    ]
    
    for js_file in js_files:
        try:
            response = requests.get(f"{base_url}{js_file}", timeout=5)
            if response.status_code == 200:
                content = response.text
                has_format_function = 'formatEntityName' in content
                has_replacements = '$eta$' in content and 'β' in content
                print(f"✅ {js_file} accessible")
                print(f"  - Has formatEntityName function: {has_format_function}")
                print(f"  - Has character replacements: {has_replacements}")
            else:
                print(f"❌ {js_file} failed: {response.status_code}")
        except Exception as e:
            print(f"❌ {js_file} error: {e}")
    
    print(f"\n🎉 UI Fixes Test Complete!")

if __name__ == "__main__":
    test_ui_fixes()
