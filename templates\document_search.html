<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Search - Graphiti Knowledge Graph</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    
    <style>
        .search-result {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        
        .search-result-title {
            font-size: 1.2rem;
            margin-bottom: 5px;
        }
        
        .search-result-metadata {
            font-size: 0.9rem;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .search-result-content {
            margin-bottom: 10px;
        }
        
        .search-result-highlight {
            background-color: #fff3cd;
            padding: 2px;
            border-radius: 3px;
        }
        
        .search-filters {
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Document Search</h1>
        
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item active" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item">Documents</li>
                <li class="breadcrumb-item active" aria-current="page">Search</li>
            </ol>
        </nav>
        
        <!-- Search Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Search Documents</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="input-group mb-3">
                            <input type="text" id="search-input" class="form-control" placeholder="Enter search query...">
                            <button class="btn btn-primary" type="button" id="search-button">
                                <i class="bi bi-search"></i> Search
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="searchType" id="semanticSearch" value="semantic" checked>
                            <label class="form-check-label" for="semanticSearch">Semantic Search</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="searchType" id="keywordSearch" value="keyword">
                            <label class="form-check-label" for="keywordSearch">Keyword Search</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="radio" name="searchType" id="hybridSearch" value="hybrid">
                            <label class="form-check-label" for="hybridSearch">Hybrid</label>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse" data-bs-target="#advancedFilters" aria-expanded="false" aria-controls="advancedFilters">
                            Advanced Filters <i class="bi bi-chevron-down"></i>
                        </button>
                    </div>
                </div>
                
                <div class="collapse mt-3" id="advancedFilters">
                    <div class="search-filters">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="document-type-filter" class="form-label">Document Type</label>
                                    <select class="form-select" id="document-type-filter">
                                        <option value="">All Types</option>
                                        <option value="pdf">PDF</option>
                                        <option value="text">Text</option>
                                        <option value="word">Word</option>
                                        <option value="html">HTML</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="date-range-filter" class="form-label">Date Range</label>
                                    <select class="form-select" id="date-range-filter">
                                        <option value="">Any Time</option>
                                        <option value="1d">Past 24 Hours</option>
                                        <option value="1w">Past Week</option>
                                        <option value="1m">Past Month</option>
                                        <option value="1y">Past Year</option>
                                        <option value="custom">Custom Range</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="entity-filter" class="form-label">Contains Entity</label>
                                    <select class="form-select" id="entity-filter">
                                        <option value="">Any Entity</option>
                                        <!-- Entity types will be populated dynamically -->
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row" id="custom-date-range" style="display: none;">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="start-date" class="form-label">Start Date</label>
                                    <input type="date" class="form-control" id="start-date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="end-date" class="form-label">End Date</label>
                                    <input type="date" class="form-control" id="end-date">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Search Results -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">Search Results</h5>
                <div class="btn-group">
                    <button class="btn btn-outline-secondary btn-sm" id="export-results-button" disabled>
                        <i class="bi bi-download"></i> Export
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" id="save-search-button" disabled>
                        <i class="bi bi-bookmark"></i> Save Search
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div id="search-loading" class="loading" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Searching documents...</p>
                </div>
                
                <div id="search-results">
                    <p class="text-muted">Enter a search query to find documents</p>
                </div>
                
                <div id="search-pagination" class="mt-4 d-flex justify-content-center" style="display: none;">
                    <!-- Pagination will be added dynamically -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="static/js/graphiti_ui.js"></script>
    <script src="static/js/graphiti_ui_part2.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the document search page
            initializeSearchTab();
            loadEntityTypes();
            
            // Set up event listeners
            document.getElementById('search-button').addEventListener('click', function() {
                performSearch();
            });
            
            document.getElementById('search-input').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
            
            document.getElementById('date-range-filter').addEventListener('change', function() {
                if (this.value === 'custom') {
                    document.getElementById('custom-date-range').style.display = 'flex';
                } else {
                    document.getElementById('custom-date-range').style.display = 'none';
                }
            });
            
            document.getElementById('export-results-button').addEventListener('click', function() {
                exportSearchResults();
            });
            
            document.getElementById('save-search-button').addEventListener('click', function() {
                saveSearch();
            });
        });
        
        function loadEntityTypes() {
            // Fetch entity types from the API
            fetch('/api/entity-types')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Populate entity type filter
                    const entityFilter = document.getElementById('entity-filter');
                    
                    // Clear existing options except the first one
                    while (entityFilter.options.length > 1) {
                        entityFilter.remove(1);
                    }
                    
                    // Add entity types
                    data.entity_types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        entityFilter.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading entity types:', error);
                });
        }
        
        function performSearch() {
            // Get search query
            const searchQuery = document.getElementById('search-input').value;
            
            if (!searchQuery) {
                return;
            }
            
            // Get search type
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Get filters
            const documentType = document.getElementById('document-type-filter').value;
            const dateRange = document.getElementById('date-range-filter').value;
            const entityType = document.getElementById('entity-filter').value;
            
            // Get custom date range if selected
            let startDate = null;
            let endDate = null;
            
            if (dateRange === 'custom') {
                startDate = document.getElementById('start-date').value;
                endDate = document.getElementById('end-date').value;
            }
            
            // Show loading spinner
            const loadingSpinner = document.getElementById('search-loading');
            loadingSpinner.style.display = 'flex';
            
            // Clear previous results
            const searchResults = document.getElementById('search-results');
            searchResults.innerHTML = '';
            
            // Hide pagination
            const searchPagination = document.getElementById('search-pagination');
            searchPagination.style.display = 'none';
            
            // Build request data
            const requestData = {
                query: searchQuery,
                search_type: searchType,
                filters: {
                    document_type: documentType,
                    date_range: dateRange,
                    entity_type: entityType,
                    start_date: startDate,
                    end_date: endDate
                },
                page: 1,
                page_size: 10
            };
            
            // Perform search
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Enable export and save buttons
                    document.getElementById('export-results-button').disabled = false;
                    document.getElementById('save-search-button').disabled = false;
                    
                    // Display results
                    displaySearchResults(data);
                })
                .catch(error => {
                    console.error('Error performing search:', error);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Show error message
                    searchResults.innerHTML = `<div class="alert alert-danger">Error performing search: ${error.message}</div>`;
                });
        }
        
        function displaySearchResults(data) {
            const searchResults = document.getElementById('search-results');
            
            // If no results, show message
            if (data.results.length === 0) {
                searchResults.innerHTML = '<div class="alert alert-info">No results found for your search query</div>';
                return;
            }
            
            // Show result count
            const resultCount = document.createElement('p');
            resultCount.className = 'mb-4';
            resultCount.textContent = `Found ${data.total} results for "${data.query}"`;
            searchResults.appendChild(resultCount);
            
            // Create results list
            data.results.forEach(result => {
                const resultItem = document.createElement('div');
                resultItem.className = 'search-result';
                
                // Create result title
                const resultTitle = document.createElement('h5');
                resultTitle.className = 'search-result-title';
                
                const titleLink = document.createElement('a');
                titleLink.href = `/document/${result.document_id}`;
                titleLink.textContent = result.title || 'Untitled Document';
                
                resultTitle.appendChild(titleLink);
                resultItem.appendChild(resultTitle);
                
                // Create result metadata
                const resultMetadata = document.createElement('div');
                resultMetadata.className = 'search-result-metadata';
                
                // Format date
                let dateStr = 'Unknown date';
                if (result.date) {
                    const date = new Date(result.date);
                    dateStr = date.toLocaleDateString();
                }
                
                resultMetadata.innerHTML = `
                    <span class="me-3"><i class="bi bi-file-earmark"></i> ${result.document_type || 'Unknown'}</span>
                    <span class="me-3"><i class="bi bi-calendar"></i> ${dateStr}</span>
                    <span><i class="bi bi-tag"></i> ${result.tags?.join(', ') || 'No tags'}</span>
                `;
                
                resultItem.appendChild(resultMetadata);
                
                // Create result content
                const resultContent = document.createElement('div');
                resultContent.className = 'search-result-content';
                resultContent.innerHTML = result.snippet || 'No preview available';
                
                resultItem.appendChild(resultContent);
                
                // Create result footer
                const resultFooter = document.createElement('div');
                resultFooter.className = 'd-flex justify-content-between align-items-center';
                
                // Add score if available
                if (result.score) {
                    const scoreElement = document.createElement('span');
                    scoreElement.className = 'badge bg-info';
                    scoreElement.textContent = `Score: ${result.score.toFixed(2)}`;
                    resultFooter.appendChild(scoreElement);
                }
                
                // Add view button
                const viewButton = document.createElement('a');
                viewButton.href = `/document/${result.document_id}`;
                viewButton.className = 'btn btn-sm btn-primary';
                viewButton.textContent = 'View Document';
                
                resultFooter.appendChild(viewButton);
                resultItem.appendChild(resultFooter);
                
                searchResults.appendChild(resultItem);
            });
            
            // Create pagination if needed
            if (data.total > data.page_size) {
                createPagination(data.page, Math.ceil(data.total / data.page_size));
            }
        }
        
        function createPagination(currentPage, totalPages) {
            const paginationContainer = document.getElementById('search-pagination');
            paginationContainer.innerHTML = '';
            paginationContainer.style.display = 'flex';
            
            const pagination = document.createElement('nav');
            pagination.setAttribute('aria-label', 'Search results pagination');
            
            const paginationList = document.createElement('ul');
            paginationList.className = 'pagination';
            
            // Previous page button
            const prevItem = document.createElement('li');
            prevItem.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
            
            const prevLink = document.createElement('a');
            prevLink.className = 'page-link';
            prevLink.href = '#';
            prevLink.setAttribute('aria-label', 'Previous');
            prevLink.innerHTML = '<span aria-hidden="true">&laquo;</span>';
            
            prevLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage > 1) {
                    loadPage(currentPage - 1);
                }
            });
            
            prevItem.appendChild(prevLink);
            paginationList.appendChild(prevItem);
            
            // Page numbers
            const maxPages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxPages / 2));
            let endPage = Math.min(totalPages, startPage + maxPages - 1);
            
            if (endPage - startPage + 1 < maxPages) {
                startPage = Math.max(1, endPage - maxPages + 1);
            }
            
            for (let i = startPage; i <= endPage; i++) {
                const pageItem = document.createElement('li');
                pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;
                
                const pageLink = document.createElement('a');
                pageLink.className = 'page-link';
                pageLink.href = '#';
                pageLink.textContent = i;
                
                pageLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    loadPage(i);
                });
                
                pageItem.appendChild(pageLink);
                paginationList.appendChild(pageItem);
            }
            
            // Next page button
            const nextItem = document.createElement('li');
            nextItem.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
            
            const nextLink = document.createElement('a');
            nextLink.className = 'page-link';
            nextLink.href = '#';
            nextLink.setAttribute('aria-label', 'Next');
            nextLink.innerHTML = '<span aria-hidden="true">&raquo;</span>';
            
            nextLink.addEventListener('click', function(e) {
                e.preventDefault();
                if (currentPage < totalPages) {
                    loadPage(currentPage + 1);
                }
            });
            
            nextItem.appendChild(nextLink);
            paginationList.appendChild(nextItem);
            
            pagination.appendChild(paginationList);
            paginationContainer.appendChild(pagination);
        }
        
        function loadPage(page) {
            // Get search query
            const searchQuery = document.getElementById('search-input').value;
            
            if (!searchQuery) {
                return;
            }
            
            // Get search type
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Get filters
            const documentType = document.getElementById('document-type-filter').value;
            const dateRange = document.getElementById('date-range-filter').value;
            const entityType = document.getElementById('entity-filter').value;
            
            // Get custom date range if selected
            let startDate = null;
            let endDate = null;
            
            if (dateRange === 'custom') {
                startDate = document.getElementById('start-date').value;
                endDate = document.getElementById('end-date').value;
            }
            
            // Show loading spinner
            const loadingSpinner = document.getElementById('search-loading');
            loadingSpinner.style.display = 'flex';
            
            // Clear previous results
            const searchResults = document.getElementById('search-results');
            searchResults.innerHTML = '';
            
            // Build request data
            const requestData = {
                query: searchQuery,
                search_type: searchType,
                filters: {
                    document_type: documentType,
                    date_range: dateRange,
                    entity_type: entityType,
                    start_date: startDate,
                    end_date: endDate
                },
                page: page,
                page_size: 10
            };
            
            // Perform search
            fetch('/api/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Display results
                    displaySearchResults(data);
                    
                    // Scroll to top of results
                    searchResults.scrollIntoView({ behavior: 'smooth' });
                })
                .catch(error => {
                    console.error('Error loading page:', error);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Show error message
                    searchResults.innerHTML = `<div class="alert alert-danger">Error loading page: ${error.message}</div>`;
                });
        }
        
        function exportSearchResults() {
            // Get search query
            const searchQuery = document.getElementById('search-input').value;
            
            if (!searchQuery) {
                return;
            }
            
            // Get search type
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Get filters
            const documentType = document.getElementById('document-type-filter').value;
            const dateRange = document.getElementById('date-range-filter').value;
            const entityType = document.getElementById('entity-filter').value;
            
            // Build query string
            let queryString = `?query=${encodeURIComponent(searchQuery)}&search_type=${searchType}`;
            
            if (documentType) {
                queryString += `&document_type=${encodeURIComponent(documentType)}`;
            }
            
            if (dateRange) {
                queryString += `&date_range=${encodeURIComponent(dateRange)}`;
            }
            
            if (entityType) {
                queryString += `&entity_type=${encodeURIComponent(entityType)}`;
            }
            
            // Open export URL in new tab
            window.open(`/api/search/export${queryString}`, '_blank');
        }
        
        function saveSearch() {
            // Get search query
            const searchQuery = document.getElementById('search-input').value;
            
            if (!searchQuery) {
                return;
            }
            
            // Get search type
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Get filters
            const documentType = document.getElementById('document-type-filter').value;
            const dateRange = document.getElementById('date-range-filter').value;
            const entityType = document.getElementById('entity-filter').value;
            
            // Build request data
            const requestData = {
                query: searchQuery,
                search_type: searchType,
                filters: {
                    document_type: documentType,
                    date_range: dateRange,
                    entity_type: entityType
                }
            };
            
            // Save search
            fetch('/api/search/save', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    alert('Search saved successfully!');
                })
                .catch(error => {
                    console.error('Error saving search:', error);
                    alert(`Error saving search: ${error.message}`);
                });
        }
    </script>
</body>
</html>
