# Graphiti API Documentation

This document provides detailed information about the Graphiti API endpoints, including request and response formats.

## Document Endpoints

### Get Documents List

Retrieves a paginated list of documents in the knowledge graph.

**Endpoint:** `GET /api/documents`

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `page_size` (integer, default: 10, max: 100): Number of documents per page

**Response Format:**
```json
{
  "documents": [
    {
      "id": "string",
      "filename": "string",
      "file_type": "pdf|text|word|html|spreadsheet|presentation|ebook|image|other",
      "upload_date": "datetime (ISO format)",
      "chunks": 0,
      "entities": 0,
      "references": 0,
      "metadata": {
        "title": "string",
        "authors": ["string"],
        "publication_date": "datetime (ISO format)",
        "publisher": "string",
        "doi": "string",
        "isbn": "string",
        "keywords": ["string"],
        "abstract": "string",
        "language": "string",
        "pages": 0,
        "source_url": "string",
        "custom_metadata": {}
      }
    }
  ],
  "total": 0,
  "page": 1,
  "page_size": 10
}
```

**Example Request:**
```
GET /api/documents?page=1&page_size=5
```

**Example Response:**
```json
{
  "documents": [
    {
      "id": "ccf88410-91e2-4957-8bd9-3df84cad2784",
      "filename": "1-Degree-of-Change.pdf",
      "file_type": "pdf",
      "upload_date": "2023-05-15T14:30:22.123456",
      "chunks": 217,
      "entities": 1594,
      "references": 12,
      "metadata": null
    },
    {
      "id": "52f012e8-f098-4da6-b1eb-925fb726a405",
      "filename": "2 Conventional treatments Karen Bridgman.pdf",
      "file_type": "pdf",
      "upload_date": "2023-05-14T10:15:30.654321",
      "chunks": 32,
      "entities": 443,
      "references": 8,
      "metadata": null
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 5
}
```

### Get Document Details

Retrieves detailed information about a specific document.

**Endpoint:** `GET /api/documents/{document_id}`

**Path Parameters:**
- `document_id` (string, required): Unique identifier of the document

**Response Format:**
```json
{
  "uuid": "string",
  "name": "string",
  "file_path": "string",
  "processed_at": "datetime (ISO format)",
  "chunks": 0,
  "entities": 0,
  "references": 0,
  "file_type": "pdf|text|word|html|spreadsheet|presentation|ebook|image|other"
}
```

**Example Request:**
```
GET /api/documents/ccf88410-91e2-4957-8bd9-3df84cad2784
```

**Example Response:**
```json
{
  "uuid": "ccf88410-91e2-4957-8bd9-3df84cad2784",
  "name": "1-Degree-of-Change.pdf",
  "file_path": "/uploads/1-Degree-of-Change.pdf",
  "processed_at": "2023-05-15T14:30:22.123456",
  "chunks": 217,
  "entities": 1594,
  "references": 12,
  "file_type": "pdf"
}
```

### Upload Document

Uploads and processes a single document.

**Endpoint:** `POST /api/upload`

**Form Parameters:**
- `file` (file, required): Document file to upload
- `chunk_size` (integer, default: 1200): Size of text chunks
- `overlap` (integer, default: 0): Overlap between chunks
- `extract_entities` (boolean, default: true): Whether to extract entities
- `extract_references` (boolean, default: true): Whether to extract references
- `extract_metadata` (boolean, default: true): Whether to extract metadata

**Response Format:**
```json
{
  "filename": "string",
  "id": "string",
  "file_type": "string",
  "chunks": 0,
  "entities": 0,
  "references": 0,
  "success": true,
  "entity_extraction_running": false,
  "reference_extraction_running": false,
  "ocr_provider": "string",
  "error": "string"
}
```

**Example Response:**
```json
{
  "filename": "research-paper.pdf",
  "id": "ccf88410-91e2-4957-8bd9-3df84cad2784",
  "file_type": "document",
  "chunks": 217,
  "entities": 1594,
  "references": 12,
  "success": true,
  "entity_extraction_running": false,
  "reference_extraction_running": false,
  "ocr_provider": "mistral",
  "error": null
}
```

### Batch Upload Documents

Uploads and processes multiple documents in parallel.

**Endpoint:** `POST /api/batch-upload`

**Form Parameters:**
- `files` (array of files, required): Document files to upload
- `chunk_size` (integer, default: 1200): Size of text chunks
- `overlap` (integer, default: 0): Overlap between chunks
- `extract_entities` (boolean, default: true): Whether to extract entities
- `extract_references` (boolean, default: true): Whether to extract references
- `extract_metadata` (boolean, default: true): Whether to extract metadata
- `max_parallel_processes` (integer, default: 4): Maximum number of documents to process in parallel

**Response Format:**
```json
{
  "total_files": 0,
  "successful_uploads": 0,
  "failed_uploads": 0,
  "documents": [
    {
      "filename": "string",
      "id": "string",
      "file_type": "string",
      "chunks": 0,
      "entities": 0,
      "references": 0,
      "success": true,
      "entity_extraction_running": false,
      "reference_extraction_running": false,
      "ocr_provider": "string",
      "error": "string"
    }
  ],
  "success": true,
  "error": "string"
}
```

## Document Processing Status

### Get Processing Status

Get the status of the document processing queue.

**Endpoint:** `GET /api/processing-status`

**Response Format:**
```json
{
  "is_processing": true,
  "queue_length": 0,
  "current_document": "string",
  "current_progress": {
    "document_id": "string",
    "status": "string",
    "progress_percentage": 0,
    "step_name": "string",
    "details": {}
  },
  "documents_progress": {}
}
```

### Get Document Progress

Get the progress of a specific document being processed.

**Endpoint:** `GET /api/document-progress/{document_id}`

**Path Parameters:**
- `document_id` (string, required): Unique identifier of the document

**Response Format:**
```json
{
  "document_id": "string",
  "status": "string",
  "progress_percentage": 0,
  "step_name": "string",
  "details": {}
}
```
