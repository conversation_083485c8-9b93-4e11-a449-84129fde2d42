"""
Model-related routes for the Graphiti application settings.
"""

from fastapi import APIRouter, HTTPException, status
from typing import Dict, Any, List

from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Create router
router = APIRouter(tags=["models"])


async def get_dynamic_available_models() -> Dict[str, Any]:
    """
    Get available models dynamically from various providers.

    Returns:
        Dictionary of available models by provider
    """
    models = {
        "openrouter": [],
        "ollama": [],
        "openai": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo", "gpt-4o-mini"],
        "gemini": ["gemini-1.5-pro", "gemini-1.5-flash", "gemini-1.0-pro"],
        "mistral": ["mistral-large-latest", "mistral-medium-latest", "mistral-small-latest"]
    }

    # Get OpenRouter models
    try:
        openrouter_models = await get_openrouter_models()
        if openrouter_models:
            models["openrouter"] = openrouter_models
        else:
            # Fallback to static list with free models first
            models["openrouter"] = [
                # Free models first
                "meta-llama/llama-3.3-8b-instruct:free",
                "nvidia/llama-3.3-nemotron-super-49b-v1:free",
                "moonshotai/kimi-dev-72b:free",
                "deepseek/deepseek-r1-0528-qwen3-8b:free",
                "deepseek/deepseek-r1-0528:free",
                "sarvamai/sarvam-m:free",
                "mistralai/devstral-small:free",
                "google/gemma-3n-e4b-it:free",
                "nousresearch/deephermes-3-mistral-24b-preview:free",
                "microsoft/phi-4-reasoning-plus:free",
                "microsoft/phi-4-reasoning:free",
                "huggingfaceh4/zephyr-7b-beta:free",
                # Paid models
                "meta-llama/llama-4-maverick",
                "anthropic/claude-3-opus-20240229",
                "anthropic/claude-3-sonnet-20240229",
                "anthropic/claude-3-haiku-20240307",
                "google/gemini-1.5-pro-latest",
                "mistralai/mistral-large-latest",
                "mistralai/mistral-nemo",
                "google/gemma-3-27b-it"
            ]
    except Exception as e:
        logger.warning(f"Failed to fetch OpenRouter models: {e}")
        models["openrouter"] = [
            # Free models first
            "meta-llama/llama-3.3-8b-instruct:free",
            "nvidia/llama-3.3-nemotron-super-49b-v1:free",
            "moonshotai/kimi-dev-72b:free",
            "deepseek/deepseek-r1-0528:free",
            "microsoft/phi-4-reasoning:free",
            # Paid models
            "meta-llama/llama-4-maverick",
            "anthropic/claude-3-opus-20240229",
            "anthropic/claude-3-sonnet-20240229",
            "google/gemini-1.5-pro-latest",
            "mistralai/mistral-large-latest"
        ]

    # Get Ollama models
    try:
        ollama_models = await get_ollama_models()
        if ollama_models:
            models["ollama"] = ollama_models
        else:
            # Fallback to static list
            models["ollama"] = ["medllama3-v20", "llama3-8b", "mistral-7b", "gemma-7b", "snowflake-arctic-embed2"]
    except Exception as e:
        logger.warning(f"Failed to fetch Ollama models: {e}")
        models["ollama"] = ["medllama3-v20", "llama3-8b", "mistral-7b", "gemma-7b", "snowflake-arctic-embed2"]

    return models


async def get_openrouter_models() -> List[str]:
    """
    Fetch available models from OpenRouter API, prioritizing free models.

    Returns:
        List of available OpenRouter model names with free models first
    """
    try:
        import httpx
        import os

        api_key = os.environ.get('OPEN_ROUTER_API_KEY')
        if not api_key:
            logger.warning("OpenRouter API key not found")
            return []

        async with httpx.AsyncClient() as client:
            response = await client.get(
                "https://openrouter.ai/api/v1/models",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "HTTP-Referer": "https://localhost:9753",
                    "X-Title": "Graphiti Knowledge Graph"
                },
                timeout=10.0
            )

            if response.status_code == 200:
                data = response.json()
                free_models = []
                paid_models = []

                for model in data.get("data", []):
                    model_id = model.get("id", "")
                    if model_id:
                        # Check if model is free (has ":free" suffix or pricing indicates free)
                        pricing = model.get("pricing", {})
                        prompt_cost = pricing.get("prompt", "0")
                        completion_cost = pricing.get("completion", "0")

                        is_free = (
                            ":free" in model_id.lower() or
                            (prompt_cost == "0" and completion_cost == "0")
                        )

                        if is_free:
                            free_models.append(model_id)
                        else:
                            paid_models.append(model_id)

                # Prioritize free models, then add paid models
                all_models = free_models + paid_models

                logger.info(f"Fetched {len(all_models)} models from OpenRouter ({len(free_models)} free, {len(paid_models)} paid)")
                return all_models[:100]  # Increased limit to show more models
            else:
                logger.warning(f"OpenRouter API returned status {response.status_code}")
                return []

    except Exception as e:
        logger.error(f"Error fetching OpenRouter models: {e}")
        return []


async def get_ollama_models() -> List[str]:
    """
    Fetch available models from Ollama API.

    Returns:
        List of available Ollama model names
    """
    try:
        import httpx
        import os

        ollama_base_url = os.environ.get('OLLAMA_BASE_URL', 'http://localhost:11434')

        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{ollama_base_url}/api/tags",
                timeout=5.0
            )

            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get("models", []):
                    model_name = model.get("name", "")
                    if model_name:
                        models.append(model_name)

                logger.info(f"Fetched {len(models)} models from Ollama")
                return models
            else:
                logger.warning(f"Ollama API returned status {response.status_code}")
                return []

    except Exception as e:
        logger.warning(f"Error fetching Ollama models (Ollama may not be running): {e}")
        return []


@router.get("/settings/models")
async def get_available_models_endpoint():
    """
    Get available models from all providers.

    Returns:
        Dictionary of available models by provider
    """
    try:
        models = await get_dynamic_available_models()
        return {"available_models": models}
    except Exception as e:
        logger.error(f"Error getting available models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting available models: {str(e)}"
        )


@router.get("/ollama/models")
async def get_ollama_models_endpoint():
    """
    Get available models from Ollama.

    Returns:
        List of available Ollama models
    """
    try:
        models = await get_ollama_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Error getting Ollama models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting Ollama models: {str(e)}"
        )


@router.get("/openrouter/models")
async def get_openrouter_models_endpoint():
    """
    Get available models from OpenRouter.

    Returns:
        List of available OpenRouter models
    """
    try:
        models = await get_openrouter_models()
        return {"models": models}
    except Exception as e:
        logger.error(f"Error getting OpenRouter models: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error getting OpenRouter models: {str(e)}"
        )
