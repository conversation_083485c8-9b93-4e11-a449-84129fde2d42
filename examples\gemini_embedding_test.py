"""
Test script for Google Generative AI embeddings
"""

import os
from dotenv import load_dotenv
import google.generativeai as genai

def main():
    try:
        # Load environment variables
        load_dotenv()

        # Get Google API key
        api_key = os.environ.get('GOOGLE_API_KEY')
        if not api_key:
            print("No Google API key found in environment variables")
            return

        print(f"API key found: {api_key[:5]}...{api_key[-5:]}")

        # Configure the API
        genai.configure(api_key=api_key)

        # List all available models
        print("Available models:")
        for model in genai.list_models():
            print(f"- {model.name} (supported methods: {model.supported_generation_methods})")
        print()

        # Test all available embedding models
        embedding_models = [
            "embedding-001",
            "text-embedding-004",
            "gemini-embedding-exp-03-07"
        ]

        test_text = "This is a test sentence for embedding."

        for model_name in embedding_models:
            try:
                print(f"\nCreating embedding with model: {model_name}")
                # For each model, try with different task types
                task_types = [None, "SEMANTIC_SIMILARITY", "RETRIEVAL_QUERY"]

                for task_type in task_types:
                    try:
                        print(f"  Testing with task_type: {task_type if task_type else 'None (default)'}")

                        if task_type:
                            result = genai.embed_content(
                                model=model_name,
                                content=test_text,
                                task_type=task_type
                            )
                        else:
                            result = genai.embed_content(
                                model=model_name,
                                content=test_text
                            )

                        # Print embedding information
                        embedding = result['embedding']
                        print(f"  Embedding dimension: {len(embedding)}")
                        print(f"  First 5 values: {embedding[:5]}")
                        print()
                    except Exception as e:
                        print(f"  Error with task_type {task_type}: {e}")
                        print()

                # End of task type testing
            except Exception as e:
                print(f"Error with model {model_name}: {e}")

        print("Test completed successfully!")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
