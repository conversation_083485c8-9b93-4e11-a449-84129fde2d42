"""
Final test for Google Generative AI embeddings
"""

import os
import sys
from dotenv import load_dotenv
import google.generativeai as genai

def main():
    try:
        # Load environment variables
        load_dotenv()

        # Get Google API key
        api_key = os.environ.get('GOOGLE_API_KEY')
        if not api_key:
            print("No Google API key found in environment variables")
            return

        print(f"API key found: {api_key[:5]}...{api_key[-5:]}")

        # Configure the API
        genai.configure(api_key=api_key)

        # List available models
        print("\nAvailable models:")
        for model in genai.list_models():
            print(f"- {model.name}")
            print(f"  Supported methods: {model.supported_generation_methods}")

        # Test with text-embedding-004
        model_name = "models/text-embedding-004"
        content_text = "What is the meaning of life?"

        print(f"\nTesting embedding with {model_name}:")
        try:
            # Try with task_type parameter
            result = genai.embed_content(
                model=model_name,
                content=content_text,
                task_type="RETRIEVAL_QUERY"
            )

            embedding = result['embedding']
            print(f"Embedding values (first 5): {embedding[:5]}...")
            print(f"Embedding dimension: {len(embedding)}")
        except Exception as e:
            print(f"Error with {model_name}: {e}")
            print("Detailed error:", sys.exc_info())

        print("\nTest completed!")
    except Exception as e:
        print(f"Error: {e}")
        print("Detailed error:", sys.exc_info())

if __name__ == "__main__":
    main()
