/**
 * Sidebar functionality for Graphiti Knowledge Graph
 * 
 * This script handles the sidebar navigation, including collapsing/expanding
 * and highlighting the active page.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Get current page path
    const currentPath = window.location.pathname;
    
    // Set active link
    setActiveSidebarLink(currentPath);
    
    // Toggle sidebar on mobile
    const sidebarCollapseBtn = document.getElementById('sidebarCollapseBtn');
    if (sidebarCollapseBtn) {
        sidebarCollapseBtn.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const content = document.getElementById('content');
            
            sidebar.classList.toggle('active');
            if (content) {
                content.classList.toggle('active');
            }
        });
    }
    
    // Toggle sidebar collapse on desktop
    const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
    if (sidebarToggleBtn) {
        sidebarToggleBtn.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const content = document.getElementById('content');
            
            sidebar.classList.toggle('collapsed');
            if (content) {
                content.classList.toggle('expanded');
            }
            
            // Update toggle button icon
            const icon = sidebarToggleBtn.querySelector('i');
            if (sidebar.classList.contains('collapsed')) {
                icon.classList.remove('fa-angle-left');
                icon.classList.add('fa-angle-right');
            } else {
                icon.classList.remove('fa-angle-right');
                icon.classList.add('fa-angle-left');
            }
        });
    }
    
    // Auto-expand submenu if child is active
    const activeLink = document.querySelector('.sidebar-link.active');
    if (activeLink) {
        const submenu = activeLink.closest('.collapse');
        if (submenu) {
            submenu.classList.add('show');
            
            // Set parent dropdown as active
            const dropdownToggle = document.querySelector(`[data-bs-toggle="collapse"][href="#${submenu.id}"]`);
            if (dropdownToggle) {
                dropdownToggle.classList.add('active');
                dropdownToggle.setAttribute('aria-expanded', 'true');
            }
        }
    }
});

/**
 * Set the active sidebar link based on the current page path
 */
function setActiveSidebarLink(currentPath) {
    // Remove active class from all links
    document.querySelectorAll('.sidebar-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Find matching link
    let activeLink = null;
    
    // First try exact match
    document.querySelectorAll('.sidebar-link').forEach(link => {
        if (link.getAttribute('href') === currentPath) {
            activeLink = link;
        }
    });
    
    // If no exact match, try data-page attribute
    if (!activeLink) {
        const pageName = currentPath.split('/').pop().split('?')[0] || 'home';
        document.querySelectorAll('.sidebar-link[data-page]').forEach(link => {
            if (link.getAttribute('data-page') === pageName) {
                activeLink = link;
            }
        });
    }
    
    // If still no match, try partial match
    if (!activeLink && currentPath !== '/') {
        document.querySelectorAll('.sidebar-link').forEach(link => {
            const href = link.getAttribute('href');
            if (href !== '/' && currentPath.includes(href)) {
                activeLink = link;
            }
        });
    }
    
    // Set active class
    if (activeLink) {
        activeLink.classList.add('active');
    } else if (currentPath === '/') {
        // Default to home if no match
        const homeLink = document.querySelector('.sidebar-link[href="/"]');
        if (homeLink) {
            homeLink.classList.add('active');
        }
    }
}
