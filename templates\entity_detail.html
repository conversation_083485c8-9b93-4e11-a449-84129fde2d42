{% extends "layouts/base.html" %}

{% block title %}Entity Detail - Graphiti Knowledge Graph{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/">Home</a></li>
<li class="breadcrumb-item"><a href="/entities">Entities</a></li>
<li class="breadcrumb-item active" aria-current="page" id="entity-breadcrumb">Entity Detail</li>
{% endblock %}

{% block additional_css %}
<!-- Add Vis.js for network visualization -->
<script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
<style>
    .entity-card {
        margin-bottom: 20px;
    }
    .entity-property {
        margin-bottom: 10px;
    }
    .property-label {
        font-weight: bold;
    }
    .relationship-card {
        margin-bottom: 10px;
        cursor: pointer;
    }
    .relationship-card:hover {
        background-color: #f8f9fa;
    }
    #relationshipNetwork {
        width: 100%;
        height: 400px;
        border: 1px solid #ddd;
        background-color: #f8f9fa;
    }
    .loading-spinner {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 200px;
    }
    .edit-button {
        margin-left: 10px;
    }
    .edit-form {
        display: none;
        margin-top: 10px;
    }
    .confidence-badge {
        margin-left: 10px;
    }
    .document-link {
        margin-top: 5px;
        display: block;
    }
    .tab-content {
        padding: 20px 0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Loading spinner -->
    <div id="loadingSpinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>

    <!-- Error message -->
    <div id="errorMessage" class="alert alert-danger" style="display: none;">
        Error loading entity: <span id="errorText"></span>
    </div>

    <!-- Entity detail content -->
    <div id="entityDetail" style="display: none;">
        <div class="row">
            <div class="col-md-12">
                <h2 id="entityName"></h2>
                <div class="d-flex align-items-center">
                    <span class="badge bg-info" id="entityType"></span>
                    <span class="badge bg-secondary confidence-badge">Confidence: <span id="entityConfidence"></span></span>
                    <button id="editEntityBtn" class="btn btn-sm btn-outline-primary edit-button">Edit</button>
                </div>
            </div>
        </div>

        <!-- Edit form -->
        <div id="editForm" class="edit-form">
            <div class="card">
                <div class="card-body">
                    <form id="entityEditForm">
                        <div class="mb-3">
                            <label for="editName" class="form-label">Name</label>
                            <input type="text" class="form-control" id="editName">
                        </div>
                        <div class="mb-3">
                            <label for="editType" class="form-label">Type</label>
                            <input type="text" class="form-control" id="editType">
                        </div>
                        <div class="mb-3">
                            <label for="editConfidence" class="form-label">Confidence</label>
                            <input type="number" class="form-control" id="editConfidence" min="0" max="1" step="0.01">
                        </div>
                        <button type="submit" class="btn btn-primary">Save</button>
                        <button type="button" class="btn btn-secondary" id="cancelEdit">Cancel</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Tabs for different views -->
        <ul class="nav nav-tabs mt-4" id="entityTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="relationships-tab" data-bs-toggle="tab" data-bs-target="#relationships" type="button" role="tab" aria-controls="relationships" aria-selected="true">Relationships</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="facts-tab" data-bs-toggle="tab" data-bs-target="#facts" type="button" role="tab" aria-controls="facts" aria-selected="false">Facts</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="documents-tab" data-bs-toggle="tab" data-bs-target="#documents" type="button" role="tab" aria-controls="documents" aria-selected="false">Documents</button>
            </li>
        </ul>

        <div class="tab-content" id="entityTabContent">
            <!-- Relationships Tab -->
            <div class="tab-pane fade show active" id="relationships" role="tabpanel" aria-labelledby="relationships-tab">
                <div class="row">
                    <div class="col-md-12">
                        <h4>Relationships</h4>
                        <div id="relationshipsList"></div>
                    </div>
                </div>
            </div>

            <!-- Facts Tab -->
            <div class="tab-pane fade" id="facts" role="tabpanel" aria-labelledby="facts-tab">
                <div class="row">
                    <div class="col-md-12">
                        <h4>Associated Facts</h4>
                        <div id="factsList"></div>
                    </div>
                </div>
            </div>

            <!-- Documents Tab -->
            <div class="tab-pane fade" id="documents" role="tabpanel" aria-labelledby="documents-tab">
                <div class="row">
                    <div class="col-md-12">
                        <h4>Source Documents</h4>
                        <div id="documentsList"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block additional_js %}
<script>
document.addEventListener('DOMContentLoaded', async () => {
    // DOM elements
    const loadingSpinner = document.getElementById('loadingSpinner');
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    const entityDetail = document.getElementById('entityDetail');
    const entityName = document.getElementById('entityName');
    const entityType = document.getElementById('entityType');
    const entityConfidence = document.getElementById('entityConfidence');
    const relationshipsList = document.getElementById('relationshipsList');
    const factsList = document.getElementById('factsList');
    const documentsList = document.getElementById('documentsList');
    const editEntityBtn = document.getElementById('editEntityBtn');
    const editForm = document.getElementById('editForm');
    const editName = document.getElementById('editName');
    const editType = document.getElementById('editType');
    const editConfidence = document.getElementById('editConfidence');
    const cancelEdit = document.getElementById('cancelEdit');
    const entityEditForm = document.getElementById('entityEditForm');
    const breadcrumbItem = document.getElementById('entity-breadcrumb');

    // Get entity UUID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const entityUuid = urlParams.get('uuid');

    if (!entityUuid) {
        showError('No entity UUID provided');
        return;
    }

    // Set up event listeners
    if (editEntityBtn) {
        editEntityBtn.addEventListener('click', () => {
            if (editForm && entityData && entityData.entity) {
                editForm.style.display = 'block';

                if (editName) editName.value = entityData.entity.name;
                if (editType) editType.value = entityData.entity.type;
                if (editConfidence) editConfidence.value = entityData.entity.confidence;
            }
        });
    }

    if (cancelEdit) {
        cancelEdit.addEventListener('click', () => {
            if (editForm) {
                editForm.style.display = 'none';
            }
        });
    }

    if (entityEditForm) {
        entityEditForm.addEventListener('submit', async (event) => {
            event.preventDefault();

            const name = editName ? editName.value : '';
            const type = editType ? editType.value : '';
            const confidence = editConfidence ? parseFloat(editConfidence.value) : 1.0;

            try {
                const response = await fetch(`/api/entity/${entityUuid}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name,
                        type,
                        confidence
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                // Reload entity data
                loadEntityData();

                // Hide edit form
                if (editForm) {
                    editForm.style.display = 'none';
                }
            } catch (error) {
                console.error('Error updating entity:', error);
                alert('Failed to update entity: ' + error.message);
            }
        });
    }

    // Load entity data
    let entityData = null;

    async function loadEntityData() {
        // Show loading spinner
        if (loadingSpinner) loadingSpinner.style.display = 'flex';
        if (entityDetail) entityDetail.style.display = 'none';
        if (errorMessage) errorMessage.style.display = 'none';

        try {
            // Fetch entity data
            console.log(`Fetching entity data for UUID: ${entityUuid}`);
            const response = await fetch(`/api/entity/${entityUuid}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            entityData = await response.json();
            console.log("Entity data received:", entityData);

            if (!entityData || !entityData.entity) {
                throw new Error("Invalid entity data received");
            }

            // Render entity data
            renderEntityData();

            // Hide loading spinner
            if (loadingSpinner) loadingSpinner.style.display = 'none';
            if (entityDetail) entityDetail.style.display = 'block';
        } catch (error) {
            console.error('Error loading entity data:', error);
            showError(`Failed to load entity data: ${error.message}`);
        }
    }

    function renderEntityData() {
        const entity = entityData.entity;

        // Set entity details
        if (entityName) entityName.textContent = entity.name || "Unknown";
        if (entityType) entityType.textContent = entity.type || "Unknown";
        if (entityConfidence) {
            // Make sure confidence is a number before calling toFixed
            let confidenceValue = entity.confidence;
            if (typeof confidenceValue !== 'number') {
                // Try to convert to number if it's a string
                confidenceValue = parseFloat(confidenceValue);
                // If conversion fails, use default value
                if (isNaN(confidenceValue)) {
                    confidenceValue = 0;
                }
            }
            entityConfidence.textContent = confidenceValue.toFixed(2);
        }

        // Update page title and breadcrumb
        document.title = `${entity.name} - Entity Detail - Graphiti Knowledge Graph`;
        if (breadcrumbItem) {
            breadcrumbItem.textContent = entity.name;
        }

        // Render relationships
        renderRelationships();

        // Render facts
        if (entityData.facts && entityData.facts.length > 0 && factsList) {
            factsList.innerHTML = '<ul class="list-group">' +
                entityData.facts.map(fact => `
                    <li class="list-group-item">
                        <p>${fact.body || 'No content available'}</p>
                    </li>
                `).join('') +
                '</ul>';
        } else if (factsList) {
            factsList.innerHTML = '<div class="alert alert-info">No facts available for this entity.</div>';
        }

        // Render documents
        if (entityData.documents && entityData.documents.length > 0 && documentsList) {
            documentsList.innerHTML = '<ul class="list-group">' +
                entityData.documents.map(doc => `
                    <li class="list-group-item">
                        <h5>${doc.title || 'Untitled Document'}</h5>
                        <p>${doc.description || 'No description available'}</p>
                        <a href="/document-detail?uuid=${doc.uuid}" class="btn btn-sm btn-outline-primary">View Document</a>
                    </li>
                `).join('') +
                '</ul>';
        } else if (documentsList) {
            documentsList.innerHTML = '<div class="alert alert-info">No source documents available for this entity.</div>';
        }
    }

    function renderRelationships() {
        if (!relationshipsList) {
            console.error("Relationships list element not found");
            return;
        }

        if (!entityData.relationships || entityData.relationships.length === 0) {
            relationshipsList.innerHTML = '<div class="alert alert-info">No relationships found for this entity.</div>';
            return;
        }

        const relationships = entityData.relationships;

        // Group relationships by type
        const relationshipsByType = {};
        relationships.forEach(rel => {
            const type = rel.type || "Unknown";
            if (!relationshipsByType[type]) {
                relationshipsByType[type] = [];
            }
            relationshipsByType[type].push(rel);
        });

        // Clear relationships list
        relationshipsList.innerHTML = '';

        // Render relationships by type
        Object.keys(relationshipsByType).sort().forEach(type => {
            const rels = relationshipsByType[type];

            // Create type header
            const typeHeader = document.createElement('h5');
            typeHeader.className = 'mt-3';
            typeHeader.textContent = `${type} (${rels.length})`;
            relationshipsList.appendChild(typeHeader);

            // Create relationship cards
            rels.forEach(rel => {
                const card = document.createElement('div');
                card.className = 'card relationship-card';

                // Determine if this is an outgoing or incoming relationship
                const isOutgoing = rel.target_uuid !== undefined;
                const targetName = isOutgoing ? (rel.target_name || "Unknown") : (rel.source_name || "Unknown");
                const targetType = isOutgoing ? (rel.target_type || "Unknown") : (rel.source_type || "Unknown");
                const targetUuid = isOutgoing ? rel.target_uuid : rel.source_uuid;
                const direction = isOutgoing ? 'to' : 'from';

                if (targetUuid) {
                    card.innerHTML = `
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-secondary">${direction}</span>
                                    <strong>${targetName}</strong>
                                    <span class="badge bg-info">${targetType}</span>
                                </div>
                                <a href="/entity-detail?uuid=${targetUuid}" class="btn btn-sm btn-outline-primary">View</a>
                            </div>
                        </div>
                    `;
                } else {
                    card.innerHTML = `
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <span class="badge bg-secondary">${direction}</span>
                                    <strong>${targetName}</strong>
                                    <span class="badge bg-info">${targetType}</span>
                                </div>
                                <span class="text-muted">No link available</span>
                            </div>
                        </div>
                    `;
                }

                relationshipsList.appendChild(card);
            });
        });
    }

    function showError(message) {
        console.error("Error:", message);

        if (errorText) errorText.textContent = message;
        if (errorMessage) errorMessage.style.display = 'block';
        if (loadingSpinner) loadingSpinner.style.display = 'none';
        if (entityDetail) entityDetail.style.display = 'none';

        // Update the page title and breadcrumb to show error
        document.title = "Entity Not Found - Graphiti Knowledge Graph";

        // Update breadcrumb
        if (breadcrumbItem) {
            breadcrumbItem.textContent = "Entity Not Found";
        }
    }

    // Start loading entity data
    loadEntityData();
});
</script>
{% endblock %}
