"""
Reference processor for the worker system.

This module provides the reference processor implementation for the worker system.
"""

from typing import Dict, List, Any, Optional, Union
import asyncio

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

async def process_reference_task(task: Dict[str, Any], add_task_callback=None) -> Dict[str, Any]:
    """
    Process a reference extraction task.

    Args:
        task: Reference extraction task to process
        add_task_callback: Callback function to add new tasks to the queue

    Returns:
        Result of the reference extraction
    """
    from reference_extraction import ReferenceExtractor
    from utils.config import get_config

    document_id = task.get("document_id")
    file_path = task.get("file_path")

    logger.info(f"Extracting references from document {document_id}")

    # Get the reference extractor
    config = get_config()
    reference_extractor = ReferenceExtractor(llm_provider=config['llm']['provider'])

    # Extract references from the document
    references = await reference_extractor.extract_references(file_path)

    # If we have a callback, queue database write task for the references
    if add_task_callback and references:
        from workers.base import WorkerType

        await add_task_callback(
            WorkerType.DATABASE_WRITER,
            {
                "id": f"references_{task.get('id')}",
                "type": "references",
                "document_id": document_id,
                "references": references
            }
        )

    return {
        "document_id": document_id,
        "references": references
    }
