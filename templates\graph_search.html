<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Search - Graphiti Knowledge Graph</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
    
    <!-- Vis.js for Network Visualization -->
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <link href="https://unpkg.com/vis-network/styles/vis-network.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="static/styles.css">
    
    <style>
        #graph-container {
            width: 100%;
            height: 500px;
            border: 1px solid #ccc;
            background-color: #f8f9fa;
        }
        
        .search-results {
            max-height: 500px;
            overflow-y: auto;
        }
        
        .path-item {
            margin-bottom: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        
        .path-node {
            display: inline-block;
            padding: 5px 10px;
            margin: 0 5px;
            border-radius: 15px;
            background-color: #e9ecef;
        }
        
        .path-arrow {
            margin: 0 5px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Graph Search</h1>
        
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light bg-light mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="/">Graphiti</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/batch-upload">Batch Upload</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/entities">Entities</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle active" href="#" id="knowledgeGraphDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Knowledge Graph
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="knowledgeGraphDropdown">
                                <li><a class="dropdown-item" href="/knowledge-graph">Explorer</a></li>
                                <li><a class="dropdown-item active" href="/graph-search">Graph Search</a></li>
                            </ul>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="documentsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                Documents
                            </a>
                            <ul class="dropdown-menu" aria-labelledby="documentsDropdown">
                                <li><a class="dropdown-item" href="/documents">Document List</a></li>
                                <li><a class="dropdown-item" href="/document-search">Search Documents</a></li>
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/qa">Q&A</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/references">References</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/settings">Settings</a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>
        
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="mb-4">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Home</a></li>
                <li class="breadcrumb-item">Knowledge Graph</li>
                <li class="breadcrumb-item active" aria-current="page">Graph Search</li>
            </ol>
        </nav>
        
        <!-- Search Form -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Search Knowledge Graph</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="start-node" class="form-label">Start Node</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="start-node" placeholder="Enter node name or ID">
                                <button class="btn btn-outline-secondary" type="button" id="start-node-search-button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="end-node" class="form-label">End Node</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="end-node" placeholder="Enter node name or ID">
                                <button class="btn btn-outline-secondary" type="button" id="end-node-search-button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="max-path-length" class="form-label">Maximum Path Length</label>
                            <input type="number" class="form-control" id="max-path-length" min="1" max="10" value="3">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="relationship-types" class="form-label">Relationship Types</label>
                            <select class="form-select" id="relationship-types" multiple>
                                <option value="all" selected>All Types</option>
                                <!-- Relationship types will be populated dynamically -->
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button class="btn btn-primary" id="search-button">
                        <i class="bi bi-search"></i> Find Paths
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Search Results -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Paths Found</h5>
                    </div>
                    <div class="card-body">
                        <div id="search-loading" class="loading" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Searching for paths...</p>
                        </div>
                        <div id="search-results" class="search-results">
                            <p class="text-muted">Search results will appear here</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Path Visualization</h5>
                    </div>
                    <div class="card-body">
                        <div id="graph-container"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Node Search Modal -->
        <div class="modal fade" id="node-search-modal" tabindex="-1" aria-labelledby="node-search-modal-label" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="node-search-modal-label">Search Nodes</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <div class="input-group mb-3">
                            <input type="text" class="form-control" id="modal-search-input" placeholder="Enter node name">
                            <button class="btn btn-primary" type="button" id="modal-search-button">Search</button>
                        </div>
                        
                        <div id="modal-search-loading" class="loading" style="display: none;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Searching...</p>
                        </div>
                        
                        <div id="modal-search-results">
                            <!-- Search results will be displayed here -->
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="static/js/graphiti_ui.js"></script>
    <script src="static/js/graphiti_ui_part2.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the graph search page
            loadRelationshipTypes();
            initializeNetwork();
            
            // Set up event listeners
            document.getElementById('start-node-search-button').addEventListener('click', function() {
                openNodeSearchModal('start');
            });
            
            document.getElementById('end-node-search-button').addEventListener('click', function() {
                openNodeSearchModal('end');
            });
            
            document.getElementById('search-button').addEventListener('click', function() {
                searchPaths();
            });
            
            document.getElementById('modal-search-button').addEventListener('click', function() {
                searchNodes();
            });
        });
        
        // Current search target (start or end node)
        let currentSearchTarget = null;
        
        // Network visualization
        let network = null;
        
        function initializeNetwork() {
            const container = document.getElementById('graph-container');
            
            // Create empty network
            const data = {
                nodes: new vis.DataSet([]),
                edges: new vis.DataSet([])
            };
            
            const options = {
                nodes: {
                    shape: 'dot',
                    size: 16,
                    font: {
                        size: 12,
                        face: 'Arial'
                    },
                    borderWidth: 2
                },
                edges: {
                    width: 1,
                    smooth: {
                        type: 'continuous'
                    },
                    arrows: {
                        to: {
                            enabled: true,
                            scaleFactor: 0.5
                        }
                    }
                },
                physics: {
                    stabilization: true,
                    barnesHut: {
                        gravitationalConstant: -80000,
                        springConstant: 0.001,
                        springLength: 200
                    }
                },
                interaction: {
                    navigationButtons: true,
                    keyboard: true
                }
            };
            
            network = new vis.Network(container, data, options);
        }
        
        function loadRelationshipTypes() {
            // Fetch relationship types from the API
            fetch('/api/relationship-types')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Populate relationship types dropdown
                    const relationshipTypes = document.getElementById('relationship-types');
                    
                    // Clear existing options except the first one
                    while (relationshipTypes.options.length > 1) {
                        relationshipTypes.remove(1);
                    }
                    
                    // Add relationship types
                    data.relationship_types.forEach(type => {
                        const option = document.createElement('option');
                        option.value = type;
                        option.textContent = type;
                        relationshipTypes.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error loading relationship types:', error);
                });
        }
        
        function openNodeSearchModal(target) {
            currentSearchTarget = target;
            
            // Clear previous search results
            document.getElementById('modal-search-input').value = '';
            document.getElementById('modal-search-results').innerHTML = '';
            
            // Update modal title
            const modalTitle = document.getElementById('node-search-modal-label');
            modalTitle.textContent = `Search for ${target === 'start' ? 'Start' : 'End'} Node`;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('node-search-modal'));
            modal.show();
        }
        
        function searchNodes() {
            const searchQuery = document.getElementById('modal-search-input').value;
            
            if (!searchQuery) {
                return;
            }
            
            // Show loading spinner
            const loadingSpinner = document.getElementById('modal-search-loading');
            loadingSpinner.style.display = 'block';
            
            // Clear previous results
            const searchResults = document.getElementById('modal-search-results');
            searchResults.innerHTML = '';
            
            // Fetch nodes from the API
            fetch(`/api/entities?limit=10&search=${encodeURIComponent(searchQuery)}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Display results
                    if (data.entities.length === 0) {
                        searchResults.innerHTML = '<div class="alert alert-info">No nodes found</div>';
                        return;
                    }
                    
                    // Create results list
                    const resultsList = document.createElement('div');
                    resultsList.className = 'list-group';
                    
                    data.entities.forEach(entity => {
                        const resultItem = document.createElement('button');
                        resultItem.type = 'button';
                        resultItem.className = 'list-group-item list-group-item-action';
                        resultItem.innerHTML = `
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">${entity.name}</h5>
                                <small>${entity.type}</small>
                            </div>
                            <p class="mb-1">ID: ${entity.uuid}</p>
                        `;
                        
                        resultItem.addEventListener('click', function() {
                            selectNode(entity);
                        });
                        
                        resultsList.appendChild(resultItem);
                    });
                    
                    searchResults.appendChild(resultsList);
                })
                .catch(error => {
                    console.error('Error searching nodes:', error);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Show error message
                    searchResults.innerHTML = `<div class="alert alert-danger">Error searching nodes: ${error.message}</div>`;
                });
        }
        
        function selectNode(entity) {
            // Set the selected node in the input field
            const inputField = document.getElementById(`${currentSearchTarget}-node`);
            inputField.value = entity.name;
            inputField.dataset.uuid = entity.uuid;
            
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('node-search-modal'));
            modal.hide();
        }
        
        function searchPaths() {
            // Get search parameters
            const startNodeUuid = document.getElementById('start-node').dataset.uuid;
            const endNodeUuid = document.getElementById('end-node').dataset.uuid;
            const maxPathLength = document.getElementById('max-path-length').value;
            
            // Get selected relationship types
            const relationshipTypesSelect = document.getElementById('relationship-types');
            const selectedOptions = Array.from(relationshipTypesSelect.selectedOptions);
            const relationshipTypes = selectedOptions.map(option => option.value);
            
            // Validate inputs
            if (!startNodeUuid) {
                alert('Please select a start node');
                return;
            }
            
            if (!endNodeUuid) {
                alert('Please select an end node');
                return;
            }
            
            // Show loading spinner
            const loadingSpinner = document.getElementById('search-loading');
            loadingSpinner.style.display = 'block';
            
            // Clear previous results
            const searchResults = document.getElementById('search-results');
            searchResults.innerHTML = '';
            
            // Build request data
            const requestData = {
                start_node: startNodeUuid,
                end_node: endNodeUuid,
                max_path_length: parseInt(maxPathLength),
                relationship_types: relationshipTypes.includes('all') ? [] : relationshipTypes
            };
            
            // Fetch paths from the API
            fetch('/api/knowledge-graph/paths', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Display results
                    if (data.paths.length === 0) {
                        searchResults.innerHTML = '<div class="alert alert-info">No paths found between the selected nodes</div>';
                        return;
                    }
                    
                    // Create results list
                    const resultsList = document.createElement('div');
                    
                    data.paths.forEach((path, index) => {
                        const pathItem = document.createElement('div');
                        pathItem.className = 'path-item';
                        
                        // Create path header
                        const pathHeader = document.createElement('div');
                        pathHeader.className = 'd-flex justify-content-between align-items-center mb-2';
                        
                        const pathTitle = document.createElement('h6');
                        pathTitle.className = 'mb-0';
                        pathTitle.textContent = `Path ${index + 1} (${path.nodes.length - 1} steps)`;
                        
                        const viewButton = document.createElement('button');
                        viewButton.className = 'btn btn-sm btn-primary';
                        viewButton.textContent = 'Visualize';
                        viewButton.addEventListener('click', function() {
                            visualizePath(path);
                        });
                        
                        pathHeader.appendChild(pathTitle);
                        pathHeader.appendChild(viewButton);
                        pathItem.appendChild(pathHeader);
                        
                        // Create path visualization
                        const pathVisualization = document.createElement('div');
                        pathVisualization.className = 'path-visualization';
                        
                        // Add nodes and relationships
                        path.nodes.forEach((node, nodeIndex) => {
                            // Add node
                            const nodeElement = document.createElement('span');
                            nodeElement.className = 'path-node';
                            nodeElement.textContent = node.name;
                            pathVisualization.appendChild(nodeElement);
                            
                            // Add arrow if not the last node
                            if (nodeIndex < path.nodes.length - 1) {
                                const arrow = document.createElement('span');
                                arrow.className = 'path-arrow';
                                arrow.innerHTML = '<i class="bi bi-arrow-right"></i>';
                                pathVisualization.appendChild(arrow);
                            }
                        });
                        
                        pathItem.appendChild(pathVisualization);
                        resultsList.appendChild(pathItem);
                    });
                    
                    searchResults.appendChild(resultsList);
                    
                    // Visualize the first path
                    visualizePath(data.paths[0]);
                })
                .catch(error => {
                    console.error('Error searching paths:', error);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Show error message
                    searchResults.innerHTML = `<div class="alert alert-danger">Error searching paths: ${error.message}</div>`;
                });
        }
        
        function visualizePath(path) {
            // Create nodes and edges for the path
            const nodes = path.nodes.map(node => ({
                id: node.id,
                label: node.name,
                group: node.type,
                color: getNodeColor(node.type)
            }));
            
            const edges = [];
            for (let i = 0; i < path.relationships.length; i++) {
                const rel = path.relationships[i];
                edges.push({
                    from: rel.source_id,
                    to: rel.target_id,
                    label: rel.type,
                    arrows: 'to'
                });
            }
            
            // Update network
            network.setData({
                nodes: new vis.DataSet(nodes),
                edges: new vis.DataSet(edges)
            });
            
            // Fit the network to view all nodes
            network.fit();
        }
        
        function getNodeColor(type) {
            // Color mapping for different node types
            const colorMap = {
                'Person': '#4e79a7',
                'Organization': '#f28e2c',
                'Location': '#e15759',
                'Disease': '#76b7b2',
                'Symptom': '#59a14f',
                'Treatment': '#edc949',
                'Medication': '#af7aa1',
                'Food': '#ff9da7',
                'Herb': '#9c755f',
                'Nutrient': '#bab0ab'
            };
            
            return colorMap[type] || '#b3b3b3';
        }
    </script>
</body>
</html>
