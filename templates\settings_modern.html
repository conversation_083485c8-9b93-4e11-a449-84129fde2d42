{% extends "base_modern.html" %}

{% block title %}Settings - Graphiti{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Settings</h1>
        <p class="text-muted">Configure your Graphiti platform</p>
    </div>
    <div>
        <button class="btn btn-success" onclick="saveAllSettings()">
            <i class="bi bi-check-circle"></i> Save All
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="resetSettings()">
            <i class="bi bi-arrow-clockwise"></i> Reset
        </button>
    </div>
</div>

<!-- Settings Tabs -->
<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="settings-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="llm-tab" data-bs-toggle="tab" data-bs-target="#llm-settings" type="button" role="tab">
                    <i class="bi bi-robot"></i> LLM Settings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="embedding-tab" data-bs-toggle="tab" data-bs-target="#embedding-settings" type="button" role="tab">
                    <i class="bi bi-vector-pen"></i> Embeddings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="database-tab" data-bs-toggle="tab" data-bs-target="#database-settings" type="button" role="tab">
                    <i class="bi bi-database"></i> Database
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="tab" data-bs-target="#system-settings" type="button" role="tab">
                    <i class="bi bi-gear"></i> System
                </button>
            </li>
        </ul>
    </div>
    
    <div class="card-body">
        <div class="tab-content" id="settings-tab-content">
            <!-- LLM Settings -->
            <div class="tab-pane fade show active" id="llm-settings" role="tabpanel">
                <h5 class="mb-4">Language Model Configuration</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="llm-provider" class="form-label">Provider</label>
                            <select class="form-select" id="llm-provider">
                                <option value="openrouter">OpenRouter</option>
                                <option value="ollama">Ollama (Local)</option>
                                <option value="openai">OpenAI</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="llm-model" class="form-label">Model</label>
                            <select class="form-select" id="llm-model">
                                <option value="">Loading models...</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="llm-api-key" class="form-label">API Key</label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="llm-api-key" placeholder="Enter API key">
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility('llm-api-key')">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <small class="text-muted">Required for OpenRouter and OpenAI</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="llm-temperature" class="form-label">Temperature</label>
                            <input type="range" class="form-range" id="llm-temperature" min="0" max="1" step="0.1" value="0.7">
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Conservative</small>
                                <small class="text-muted">Creative</small>
                            </div>
                            <small class="text-muted">Current: <span id="llm-temperature-value">0.7</span></small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="llm-max-tokens" class="form-label">Max Tokens</label>
                            <input type="number" class="form-control" id="llm-max-tokens" value="2048" min="100" max="8192">
                            <small class="text-muted">Maximum response length</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="llm-top-k" class="form-label">Top K</label>
                            <input type="number" class="form-control" id="llm-top-k" value="40" min="1" max="100">
                            <small class="text-muted">Vocabulary diversity</small>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Model Recommendations:</strong>
                    <ul class="mb-0 mt-2">
                        <li><strong>OpenRouter:</strong> meta-llama/llama-4-maverick for balanced performance</li>
                        <li><strong>Ollama:</strong> medllama3-v20 for medical/health content</li>
                        <li><strong>OpenAI:</strong> gpt-4o for general-purpose tasks</li>
                    </ul>
                </div>
            </div>
            
            <!-- Embedding Settings -->
            <div class="tab-pane fade" id="embedding-settings" role="tabpanel">
                <h5 class="mb-4">Embedding Configuration</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="embedding-provider" class="form-label">Provider</label>
                            <select class="form-select" id="embedding-provider">
                                <option value="ollama">Ollama (Local)</option>
                                <option value="openai">OpenAI</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="embedding-model" class="form-label">Model</label>
                            <select class="form-select" id="embedding-model">
                                <option value="snowflake-arctic-embed2">snowflake-arctic-embed2</option>
                                <option value="text-embedding-3-small">text-embedding-3-small</option>
                                <option value="text-embedding-3-large">text-embedding-3-large</option>
                            </select>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="use-local-embeddings">
                            <label class="form-check-label" for="use-local-embeddings">
                                Use local embeddings (Ollama)
                            </label>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="chunk-size" class="form-label">Chunk Size</label>
                            <input type="number" class="form-control" id="chunk-size" value="1200" min="100" max="5000">
                            <small class="text-muted">Characters per chunk</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="chunk-overlap" class="form-label">Chunk Overlap</label>
                            <input type="number" class="form-control" id="chunk-overlap" value="0" min="0" max="500">
                            <small class="text-muted">Overlap between chunks</small>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="recursive-chunking" checked>
                            <label class="form-check-label" for="recursive-chunking">
                                Use recursive chunking
                            </label>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-success">
                    <i class="bi bi-lightbulb"></i>
                    <strong>Recommended:</strong> Use snowflake-arctic-embed2 with Ollama for best performance with 1024-dimensional embeddings.
                </div>
            </div>
            
            <!-- Database Settings -->
            <div class="tab-pane fade" id="database-settings" role="tabpanel">
                <h5 class="mb-4">Database Configuration</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>FalkorDB (Knowledge Graph)</h6>
                        <div class="mb-3">
                            <label for="falkordb-host" class="form-label">Host</label>
                            <input type="text" class="form-control" id="falkordb-host" value="localhost">
                        </div>
                        
                        <div class="mb-3">
                            <label for="falkordb-port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="falkordb-port" value="6379" min="1" max="65535">
                        </div>
                        
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-success rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                    <i class="bi bi-check text-white"></i>
                                </div>
                            </div>
                            <div>
                                <small class="text-success" id="falkordb-connection-status">Connected</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>Redis Vector Search</h6>
                        <div class="mb-3">
                            <label for="redis-host" class="form-label">Host</label>
                            <input type="text" class="form-control" id="redis-host" value="localhost">
                        </div>
                        
                        <div class="mb-3">
                            <label for="redis-port" class="form-label">Port</label>
                            <input type="number" class="form-control" id="redis-port" value="6380" min="1" max="65535">
                        </div>
                        
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <div class="bg-info rounded-circle d-flex align-items-center justify-content-center" style="width: 30px; height: 30px;">
                                    <i class="bi bi-check text-white"></i>
                                </div>
                            </div>
                            <div>
                                <small class="text-info" id="redis-connection-status">Connected</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning mt-3">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>Note:</strong> Database connection changes require application restart to take effect.
                </div>
            </div>
            
            <!-- System Settings -->
            <div class="tab-pane fade" id="system-settings" role="tabpanel">
                <h5 class="mb-4">System Configuration</h5>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="max-file-size" class="form-label">Max File Size (MB)</label>
                            <input type="number" class="form-control" id="max-file-size" value="50" min="1" max="1000">
                            <small class="text-muted">Maximum upload file size</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="max-parallel-processes" class="form-label">Max Parallel Processes</label>
                            <input type="number" class="form-control" id="max-parallel-processes" value="4" min="1" max="16">
                            <small class="text-muted">Concurrent document processing</small>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="log-level" class="form-label">Log Level</label>
                            <select class="form-select" id="log-level">
                                <option value="DEBUG">Debug</option>
                                <option value="INFO" selected>Info</option>
                                <option value="WARNING">Warning</option>
                                <option value="ERROR">Error</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Theme</label>
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="theme" id="theme-light" value="light" checked>
                                <label class="btn btn-outline-primary" for="theme-light">
                                    <i class="bi bi-sun"></i> Light
                                </label>
                                
                                <input type="radio" class="btn-check" name="theme" id="theme-dark" value="dark">
                                <label class="btn btn-outline-primary" for="theme-dark">
                                    <i class="bi bi-moon"></i> Dark
                                </label>
                                
                                <input type="radio" class="btn-check" name="theme" id="theme-auto" value="auto">
                                <label class="btn btn-outline-primary" for="theme-auto">
                                    <i class="bi bi-circle-half"></i> Auto
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    <strong>Performance Tips:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Increase parallel processes for faster document processing</li>
                        <li>Adjust chunk size based on your document types</li>
                        <li>Use local models for better privacy and reduced API costs</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Connection Modal -->
<div class="modal fade" id="test-connection-modal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Test Connection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="connection-test-results"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentSettings = {};

document.addEventListener('DOMContentLoaded', function() {
    loadCurrentSettings();
    setupEventListeners();
});

function setupEventListeners() {
    // Temperature slider
    document.getElementById('llm-temperature').addEventListener('input', function() {
        document.getElementById('llm-temperature-value').textContent = this.value;
    });
    
    // Provider change handlers
    document.getElementById('llm-provider').addEventListener('change', loadLLMModels);
    document.getElementById('embedding-provider').addEventListener('change', loadEmbeddingModels);
    
    // Theme change handler
    document.querySelectorAll('input[name="theme"]').forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.checked) {
                applyTheme(this.value);
            }
        });
    });
}

async function loadCurrentSettings() {
    try {
        const response = await fetch('/api/settings');
        if (response.ok) {
            currentSettings = await response.json();
            populateSettings(currentSettings);
            await loadLLMModels();
            await loadEmbeddingModels();
        }
    } catch (error) {
        console.error('Error loading settings:', error);
        showAlert('Error loading settings', 'danger');
    }
}

function populateSettings(settings) {
    // LLM Settings
    if (settings.llm_settings) {
        document.getElementById('llm-provider').value = settings.llm_settings.provider || 'openrouter';
        document.getElementById('llm-temperature').value = settings.llm_settings.temperature || 0.7;
        document.getElementById('llm-temperature-value').textContent = settings.llm_settings.temperature || 0.7;
        document.getElementById('llm-max-tokens').value = settings.llm_settings.max_tokens || 2048;
        document.getElementById('llm-top-k').value = settings.llm_settings.top_k || 40;
    }
    
    // Embedding Settings
    if (settings.embedding_settings) {
        document.getElementById('embedding-provider').value = settings.embedding_settings.provider || 'ollama';
        document.getElementById('embedding-model').value = settings.embedding_settings.model || 'snowflake-arctic-embed2';
        document.getElementById('use-local-embeddings').checked = settings.embedding_settings.use_local || false;
        document.getElementById('chunk-size').value = settings.embedding_settings.chunk_size || 1200;
        document.getElementById('chunk-overlap').value = settings.embedding_settings.chunk_overlap || 0;
        document.getElementById('recursive-chunking').checked = settings.embedding_settings.recursive_chunking !== false;
    }
    
    // Database Settings
    if (settings.database_settings) {
        document.getElementById('falkordb-host').value = settings.database_settings.falkordb_host || 'localhost';
        document.getElementById('falkordb-port').value = settings.database_settings.falkordb_port || 6379;
        document.getElementById('redis-host').value = settings.database_settings.redis_host || 'localhost';
        document.getElementById('redis-port').value = settings.database_settings.redis_port || 6380;
    }
    
    // System Settings
    if (settings.system_settings) {
        document.getElementById('max-file-size').value = settings.system_settings.max_file_size || 50;
        document.getElementById('max-parallel-processes').value = settings.system_settings.max_parallel_processes || 4;
        document.getElementById('log-level').value = settings.system_settings.log_level || 'INFO';
    }
}

async function loadLLMModels() {
    const provider = document.getElementById('llm-provider').value;
    const modelSelect = document.getElementById('llm-model');
    
    try {
        const response = await fetch('/api/settings/models');
        if (response.ok) {
            const data = await response.json();
            const models = data.available_models[provider] || [];
            
            modelSelect.innerHTML = '';
            models.forEach(model => {
                const option = document.createElement('option');
                option.value = model;
                option.textContent = model;
                modelSelect.appendChild(option);
            });
            
            // Set current model if available
            if (currentSettings.llm_settings && currentSettings.llm_settings.model) {
                modelSelect.value = currentSettings.llm_settings.model;
            }
        }
    } catch (error) {
        console.error('Error loading LLM models:', error);
        modelSelect.innerHTML = '<option value="">Error loading models</option>';
    }
}

async function loadEmbeddingModels() {
    // Embedding models are mostly static, but we can enhance this later
    const provider = document.getElementById('embedding-provider').value;
    const modelSelect = document.getElementById('embedding-model');
    
    if (provider === 'ollama') {
        modelSelect.innerHTML = `
            <option value="snowflake-arctic-embed2">snowflake-arctic-embed2</option>
            <option value="nomic-embed-text">nomic-embed-text</option>
            <option value="all-minilm">all-minilm</option>
        `;
    } else if (provider === 'openai') {
        modelSelect.innerHTML = `
            <option value="text-embedding-3-small">text-embedding-3-small</option>
            <option value="text-embedding-3-large">text-embedding-3-large</option>
            <option value="text-embedding-ada-002">text-embedding-ada-002</option>
        `;
    }
}

async function saveAllSettings() {
    const button = event.target;
    const hideLoading = showLoading(button);
    
    try {
        const settings = {
            llm_settings: {
                provider: document.getElementById('llm-provider').value,
                model: document.getElementById('llm-model').value,
                api_key: document.getElementById('llm-api-key').value,
                temperature: parseFloat(document.getElementById('llm-temperature').value),
                max_tokens: parseInt(document.getElementById('llm-max-tokens').value),
                top_k: parseInt(document.getElementById('llm-top-k').value)
            },
            embedding_settings: {
                provider: document.getElementById('embedding-provider').value,
                model: document.getElementById('embedding-model').value,
                use_local: document.getElementById('use-local-embeddings').checked,
                chunk_size: parseInt(document.getElementById('chunk-size').value),
                chunk_overlap: parseInt(document.getElementById('chunk-overlap').value),
                recursive_chunking: document.getElementById('recursive-chunking').checked
            },
            database_settings: {
                falkordb_host: document.getElementById('falkordb-host').value,
                falkordb_port: parseInt(document.getElementById('falkordb-port').value),
                redis_host: document.getElementById('redis-host').value,
                redis_port: parseInt(document.getElementById('redis-port').value)
            },
            system_settings: {
                max_file_size: parseInt(document.getElementById('max-file-size').value),
                max_parallel_processes: parseInt(document.getElementById('max-parallel-processes').value),
                log_level: document.getElementById('log-level').value
            }
        };
        
        const response = await fetch('/api/settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(settings)
        });
        
        if (response.ok) {
            const result = await response.json();
            showAlert(`Settings saved successfully! Updated ${result.updated_sections.length} sections.`, 'success');
        } else {
            throw new Error('Failed to save settings');
        }
        
    } catch (error) {
        console.error('Error saving settings:', error);
        showAlert('Error saving settings: ' + error.message, 'danger');
    } finally {
        hideLoading();
    }
}

async function resetSettings() {
    if (confirm('Are you sure you want to reset all settings to default values?')) {
        try {
            const response = await fetch('/api/settings/reset', {
                method: 'POST'
            });
            
            if (response.ok) {
                showAlert('Settings reset to default values', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error('Failed to reset settings');
            }
        } catch (error) {
            console.error('Error resetting settings:', error);
            showAlert('Error resetting settings: ' + error.message, 'danger');
        }
    }
}

function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = input.nextElementSibling;
    const icon = button.querySelector('i');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'bi bi-eye-slash';
    } else {
        input.type = 'password';
        icon.className = 'bi bi-eye';
    }
}

function applyTheme(theme) {
    if (theme === 'auto') {
        // Use system preference
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        theme = prefersDark ? 'dark' : 'light';
    }
    
    document.documentElement.setAttribute('data-bs-theme', theme);
    localStorage.setItem('theme', theme);
}

// Load saved theme on page load
document.addEventListener('DOMContentLoaded', function() {
    const savedTheme = localStorage.getItem('theme') || 'light';
    const themeRadio = document.querySelector(`input[name="theme"][value="${savedTheme}"]`);
    if (themeRadio) {
        themeRadio.checked = true;
        applyTheme(savedTheme);
    }
});
</script>
{% endblock %}
