#!/usr/bin/env python3
"""
Test the precise OneNote processor that extracts each page as a separate document.
"""

import sys
import os
import logging
import asyncio
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_precise_onenote_processor():
    """Test the precise OneNote processor."""
    
    print("🔍 TESTING PRECISE ONENOTE PROCESSOR")
    print("=" * 60)
    
    # Find the Brain.one file
    uploads_dir = Path("uploads")
    onenote_files = list(uploads_dir.glob("*Brain.one*"))
    
    if not onenote_files:
        print("❌ No Brain.one files found in uploads directory")
        return False
    
    # Use the most recent one
    onenote_file = onenote_files[-1]
    print(f"🔍 Testing with: {onenote_file.name}")
    print(f"📁 File size: {onenote_file.stat().st_size:,} bytes")
    
    try:
        from processors.precise_onenote_processor import PreciseOneNoteProcessor
        
        # Initialize processor
        processor = PreciseOneNoteProcessor()
        print(f"✅ Initialized: {processor.processor_name}")
        
        # Check processor info
        info = processor.get_processor_info()
        print(f"📋 Features: {len(info['features'])} available")
        print(f"   OneNote library: {info['onenote_available']}")
        
        # Test the processing
        print(f"\n🚀 Starting Precise OneNote Page Extraction...")
        result = await processor.extract_text(str(onenote_file))
        
        print(f"\n📊 PROCESSING RESULTS:")
        print(f"Success: {result.get('success', False)}")
        
        if result.get('success'):
            text = result.get('text', '')
            metadata = result.get('metadata', {})
            page_results = result.get('page_results', [])
            
            print(f"✅ Total text extracted: {len(text):,} characters")
            print(f"✅ Total pages processed: {metadata.get('total_pages', 0)}")
            print(f"✅ Successful pages: {metadata.get('successful_pages', 0)}")
            print(f"✅ Total word count: {metadata.get('word_count', 0):,}")
            print(f"✅ Total references: {metadata.get('total_references_found', 0)}")
            print(f"✅ Total entities: {metadata.get('total_entities_extracted', 0)}")
            
            # Show details for each page
            print(f"\n📄 PAGE DETAILS:")
            print("-" * 60)
            for page_info in metadata.get('pages', []):
                print(f"Page {page_info['page_number']}: {page_info['title']}")
                print(f"  📝 Words: {page_info['word_count']:,}")
                print(f"  📚 References: {page_info['references_found']}")
                print(f"  🏷️ Entities: {page_info['entities_extracted']}")
                print()
            
            # Show first part of extracted text from each page
            print(f"\n📝 EXTRACTED CONTENT BY PAGE:")
            print("=" * 60)
            
            # Split the combined text by page markers
            pages_text = text.split("=== PAGE ")
            for i, page_text in enumerate(pages_text[1:], 1):  # Skip first empty split
                lines = page_text.split('\n')
                page_header = lines[0] if lines else f"Page {i}"
                page_content = '\n'.join(lines[1:]) if len(lines) > 1 else ""
                
                print(f"\n📄 PAGE {i}: {page_header}")
                print("-" * 40)
                
                # Show first 500 characters of each page
                preview = page_content[:500].strip()
                if preview:
                    print(preview)
                    if len(page_content) > 500:
                        print(f"\n... [Page continues for {len(page_content)-500:,} more characters]")
                else:
                    print("[No content extracted for this page]")
                print("-" * 40)
            
            # Save full content for inspection
            output_file = Path("precise_onenote_processing_result.txt")
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("=== PRECISE ONENOTE PROCESSING RESULT ===\n\n")
                f.write(f"File: {onenote_file.name}\n")
                f.write(f"Total pages: {metadata.get('total_pages', 0)}\n")
                f.write(f"Successful pages: {metadata.get('successful_pages', 0)}\n")
                f.write(f"Total text length: {len(text):,} characters\n")
                f.write(f"Total word count: {metadata.get('word_count', 0):,}\n")
                f.write(f"Total references: {metadata.get('total_references_found', 0)}\n")
                f.write(f"Total entities: {metadata.get('total_entities_extracted', 0)}\n\n")
                
                f.write("=== PAGE SUMMARY ===\n\n")
                for page_info in metadata.get('pages', []):
                    f.write(f"Page {page_info['page_number']}: {page_info['title']}\n")
                    f.write(f"  Words: {page_info['word_count']:,}\n")
                    f.write(f"  References: {page_info['references_found']}\n")
                    f.write(f"  Entities: {page_info['entities_extracted']}\n\n")
                
                f.write("=== FULL EXTRACTED TEXT ===\n\n")
                f.write(text)
                
                f.write("\n\n=== INDIVIDUAL PAGE RESULTS ===\n\n")
                for i, page_result in enumerate(page_results, 1):
                    f.write(f"--- PAGE {i} RESULT ---\n")
                    f.write(f"Success: {page_result.get('success', False)}\n")
                    f.write(f"Text length: {len(page_result.get('text', ''))}\n")
                    page_metadata = page_result.get('metadata', {})
                    f.write(f"Page title: {page_metadata.get('page_title', 'Unknown')}\n")
                    f.write(f"Word count: {page_metadata.get('word_count', 0)}\n")
                    f.write(f"References: {page_metadata.get('references_found', 0)}\n")
                    f.write(f"Entities: {page_metadata.get('entities_extracted', 0)}\n\n")
            
            print(f"💾 Saved full processing result to: {output_file}")
            
            # Compare with previous attempts
            print(f"\n🎉 COMPARISON WITH PREVIOUS ATTEMPTS:")
            print(f"Previous OneNote extraction: ~1,880 characters (metadata only)")
            print(f"Simple processor: 0 characters (PDF conversion issues)")
            print(f"Precise processor: {len(text):,} characters (ACTUAL PAGE CONTENT)")
            
            if len(text) > 1880:
                improvement = len(text) / 1880
                print(f"Improvement factor: {improvement:.1f}x more content!")
                
                if len(text) > 50000:
                    print("🎉 OUTSTANDING! This is comprehensive content extraction!")
                elif len(text) > 20000:
                    print("🎉 EXCELLENT! This is substantial content extraction!")
                elif len(text) > 10000:
                    print("✅ VERY GOOD! Significant improvement in content extraction!")
                else:
                    print("✅ GOOD! Better content extraction than previous attempts!")
            
            # Check if we found the ginger references
            if "References" in text and "Vasala PA" in text:
                print("\n🎯 REFERENCE DETECTION:")
                print("✅ Found the detailed ginger references you mentioned!")
                print("✅ This confirms the processor is extracting actual page content!")
            
            return True
            
        else:
            error = result.get('error', 'Unknown error')
            print(f"❌ Processing failed: {error}")
            return False
            
    except Exception as e:
        print(f"❌ Error during precise processing test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run precise OneNote processor tests."""
    print("🔍 PRECISE ONENOTE PROCESSOR TESTS")
    print("=" * 60)
    
    success = asyncio.run(test_precise_onenote_processor())
    
    print("\n" + "=" * 60)
    print("🎯 PRECISE PROCESSOR TEST SUMMARY")
    print("=" * 60)
    
    if success:
        print("🎉 Precise OneNote processor is working!")
        print("✅ Each OneNote page extracted as separate document")
        print("✅ Complete content preservation achieved")
        print("✅ Individual page processing through full pipeline")
        print("✅ References and entities extracted per page")
        print("\nBenefits of precise approach:")
        print("- Treats each OneNote page as separate document")
        print("- Preserves all content including references")
        print("- Processes each page through complete pipeline")
        print("- Maintains page structure and organization")
        print("- Extracts actual content, not just metadata")
    else:
        print("❌ Precise processor needs work")
        print("Check the logs above for specific issues")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
