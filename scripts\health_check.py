#!/usr/bin/env python3
"""
Comprehensive health check script for Graphiti system.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent))

import requests
import redis
from utils.config import get_config

def check_web_server():
    """Check if the main web server is running."""
    try:
        config = get_config()
        host = config.get('HOST', 'localhost')
        port = config.get('PORT', 9753)
        
        response = requests.get(f"http://{host}:{port}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Web server: Running")
            return True
        else:
            print(f"❌ Web server: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Web server: {e}")
        return False

def check_falkordb():
    """Check FalkorDB connection."""
    try:
        config = get_config()
        host = config.get('FALKORDB_HOST', 'localhost')
        port = config.get('FALKORDB_PORT', 6379)
        password = config.get('FALKORDB_PASSWORD', '')
        
        r = redis.Redis(host=host, port=port, password=password, decode_responses=True)
        r.ping()
        print("✅ FalkorDB: Connected")
        return True
    except Exception as e:
        print(f"❌ FalkorDB: {e}")
        return False

def check_redis_vector():
    """Check Redis Vector Search connection."""
    try:
        config = get_config()
        host = config.get('REDIS_VECTOR_SEARCH_HOST', 'localhost')
        port = config.get('REDIS_VECTOR_SEARCH_PORT', 6380)
        password = config.get('REDIS_VECTOR_SEARCH_PASSWORD', '')
        
        r = redis.Redis(host=host, port=port, password=password, decode_responses=True)
        r.ping()
        print("✅ Redis Vector Search: Connected")
        return True
    except Exception as e:
        print(f"❌ Redis Vector Search: {e}")
        return False

def check_ollama():
    """Check Ollama service."""
    try:
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama: Running ({len(models)} models)")
            return True
        else:
            print(f"❌ Ollama: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama: {e}")
        return False

def check_api_keys():
    """Check if required API keys are configured."""
    config = get_config()
    keys_status = []
    
    # Check OpenRouter API key
    if config.get('OPENROUTER_API_KEY'):
        print("✅ OpenRouter API Key: Configured")
        keys_status.append(True)
    else:
        print("⚠️ OpenRouter API Key: Not configured")
        keys_status.append(False)
    
    # Check OpenAI API key
    if config.get('OPENAI_API_KEY'):
        print("✅ OpenAI API Key: Configured")
        keys_status.append(True)
    else:
        print("⚠️ OpenAI API Key: Not configured")
        keys_status.append(False)
    
    # Check Mistral API key
    if config.get('MISTRAL_API_KEY'):
        print("✅ Mistral API Key: Configured")
        keys_status.append(True)
    else:
        print("⚠️ Mistral API Key: Not configured")
        keys_status.append(False)
    
    return any(keys_status)

def check_directories():
    """Check if required directories exist."""
    directories = ['uploads', 'documents', 'references', 'processed']
    all_good = True
    
    for directory in directories:
        if os.path.exists(directory):
            print(f"✅ Directory {directory}: Exists")
        else:
            print(f"⚠️ Directory {directory}: Missing")
            all_good = False
    
    return all_good

def main():
    """Run comprehensive health check."""
    print("🔍 Graphiti System Health Check")
    print("=" * 40)
    
    checks = [
        ("Web Server", check_web_server),
        ("FalkorDB", check_falkordb),
        ("Redis Vector Search", check_redis_vector),
        ("Ollama", check_ollama),
        ("API Keys", check_api_keys),
        ("Directories", check_directories),
    ]
    
    results = []
    for name, check_func in checks:
        print(f"\n🔍 Checking {name}...")
        result = check_func()
        results.append(result)
    
    print("\n" + "=" * 40)
    print("📊 Health Check Summary")
    print("=" * 40)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All checks passed! ({passed}/{total})")
        sys.exit(0)
    else:
        print(f"⚠️ {total - passed} checks failed. ({passed}/{total})")
        sys.exit(1)

if __name__ == "__main__":
    main()
