"""
Text processing utilities for the Graphiti application.
"""

import re
from typing import List, Dict, Any, Set

from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

# Common stop words for filtering
STOP_WORDS = {
    "a", "an", "the", "and", "or", "but", "if", "because", "as", "what",
    "when", "where", "which", "who", "whom", "whose", "why", "how",
    "about", "tell", "explain", "describe", "information", "know", "understand",
    "me", "my", "i", "we", "our", "us", "you", "your", "he", "she", "it", "they", "them",
    "this", "that", "these", "those", "is", "are", "was", "were", "be", "been", "being",
    "have", "has", "had", "do", "does", "did", "can", "could", "will", "would", "shall", "should",
    "may", "might", "must", "for", "of", "with", "without", "in", "on", "at", "by", "to", "from"
}

def extract_keywords(text: str, min_length: int = 3) -> List[str]:
    """
    Extract meaningful keywords from text.
    
    Args:
        text: Input text
        min_length: Minimum length of keywords to extract
        
    Returns:
        List of extracted keywords
    """
    # Convert to lowercase
    text = text.lower()
    
    # Split into words and filter out stop words and short words
    words = text.split()
    keywords = [word for word in words if word not in STOP_WORDS and len(word) >= min_length]
    
    # If no keywords found, use the original words
    if not keywords:
        keywords = [word for word in words if len(word) >= min_length]
    
    # If still no keywords, use the whole text
    if not keywords:
        keywords = [text]
    
    return keywords

def clean_text(text: str) -> str:
    """
    Clean text by removing extra whitespace, special characters, etc.
    
    Args:
        text: Input text
        
    Returns:
        Cleaned text
    """
    # Replace multiple whitespace with a single space
    text = re.sub(r'\s+', ' ', text)
    
    # Remove leading/trailing whitespace
    text = text.strip()
    
    return text

def split_text_into_chunks(text: str, chunk_size: int = 1200, overlap: int = 0) -> List[str]:
    """
    Split text into chunks of specified size with optional overlap.
    
    Args:
        text: Input text
        chunk_size: Maximum size of each chunk
        overlap: Number of characters to overlap between chunks
        
    Returns:
        List of text chunks
    """
    # Clean the text
    text = clean_text(text)
    
    # If text is shorter than chunk_size, return it as a single chunk
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        # Calculate end position
        end = start + chunk_size
        
        # If we're at the end of the text, just use the rest
        if end >= len(text):
            chunks.append(text[start:])
            break
        
        # Try to find a good breaking point (end of sentence or paragraph)
        # Look for period, question mark, exclamation mark, or newline followed by space or end of text
        break_point = -1
        
        # Look for paragraph break first
        paragraph_break = text.rfind('\n\n', start, end)
        if paragraph_break != -1:
            break_point = paragraph_break + 2
        
        # If no paragraph break, look for sentence end
        if break_point == -1:
            for i in range(end, start, -1):
                if i < len(text) and text[i-1] in '.!?' and (i == len(text) or text[i].isspace()):
                    break_point = i
                    break
        
        # If no good breaking point found, just break at chunk_size
        if break_point == -1 or break_point <= start:
            break_point = end
        
        # Add the chunk
        chunks.append(text[start:break_point])
        
        # Calculate next start position with overlap
        start = break_point - overlap
    
    return chunks

def split_text_recursively(text: str, chunk_size: int = 1200, overlap: int = 0) -> List[str]:
    """
    Split text recursively into chunks, trying to maintain semantic coherence.
    
    Args:
        text: Input text
        chunk_size: Maximum size of each chunk
        overlap: Number of characters to overlap between chunks
        
    Returns:
        List of text chunks
    """
    # Clean the text
    text = clean_text(text)
    
    # If text is shorter than chunk_size, return it as a single chunk
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    
    # Try to split by double newlines (paragraphs)
    paragraphs = text.split('\n\n')
    
    # If splitting by paragraphs results in chunks smaller than chunk_size
    if all(len(p) <= chunk_size for p in paragraphs):
        current_chunk = ""
        for paragraph in paragraphs:
            # If adding this paragraph would exceed chunk_size, start a new chunk
            if len(current_chunk) + len(paragraph) + 2 > chunk_size and current_chunk:
                chunks.append(current_chunk)
                current_chunk = paragraph
            else:
                if current_chunk:
                    current_chunk += '\n\n' + paragraph
                else:
                    current_chunk = paragraph
        
        # Add the last chunk
        if current_chunk:
            chunks.append(current_chunk)
    else:
        # Some paragraphs are too large, split them further
        for paragraph in paragraphs:
            if len(paragraph) <= chunk_size:
                chunks.append(paragraph)
            else:
                # Split by sentences
                sentences = re.split(r'(?<=[.!?])\s+', paragraph)
                current_chunk = ""
                
                for sentence in sentences:
                    # If adding this sentence would exceed chunk_size, start a new chunk
                    if len(current_chunk) + len(sentence) + 1 > chunk_size and current_chunk:
                        chunks.append(current_chunk)
                        current_chunk = sentence
                    else:
                        if current_chunk:
                            current_chunk += ' ' + sentence
                        else:
                            current_chunk = sentence
                
                # Add the last chunk
                if current_chunk:
                    chunks.append(current_chunk)
    
    # If any chunks are still too large, split them by chunk_size
    final_chunks = []
    for chunk in chunks:
        if len(chunk) <= chunk_size:
            final_chunks.append(chunk)
        else:
            final_chunks.extend(split_text_into_chunks(chunk, chunk_size, overlap))
    
    return final_chunks
