"""
<PERSON><PERSON>t to check entities in the FalkorDB database.
"""

import os
import sys
import asyncio
from pathlib import Path

# Add the parent directory to the path so we can import the application modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.falkordb_adapter import GraphitiFalkorDBAdapter
from utils.logging_utils import get_logger

# Set up logger
logger = get_logger(__name__)

async def main():
    """Main function."""
    # Connect to FalkorDB
    adapter = GraphitiFalkorDBAdapter()
    
    # Check if connected
    if not adapter.is_connected():
        logger.error("Failed to connect to FalkorDB")
        return
    
    logger.info("Connected to FalkorDB")
    
    # Get all episodes
    episodes_query = """
    MATCH (e:Episode)
    RETURN e.uuid as uuid, e.name as name, e.total_chunks as chunks
    """
    
    episodes_result = adapter.execute_cypher(episodes_query)
    
    if not episodes_result or len(episodes_result) < 2 or not episodes_result[1]:
        logger.info("No episodes found in the database")
        return
    
    logger.info(f"Found {len(episodes_result[1])} episodes in the database")
    
    for episode in episodes_result[1]:
        uuid = episode[0]
        name = episode[1]
        chunks = episode[2]
        
        logger.info(f"Episode: {name} (UUID: {uuid}, Chunks: {chunks})")
        
        # Get facts for this episode
        facts_query = f"""
        MATCH (e:Episode {{uuid: '{uuid}'}})-[:CONTAINS]->(f:Fact)
        RETURN count(f) as fact_count
        """
        
        facts_result = adapter.execute_cypher(facts_query)
        
        if facts_result and len(facts_result) > 1 and facts_result[1]:
            fact_count = facts_result[1][0][0]
            logger.info(f"  Facts: {fact_count}")
        else:
            logger.info("  No facts found for this episode")
        
        # Get entities for this episode
        entities_query = f"""
        MATCH (e:Episode {{uuid: '{uuid}'}})-[:CONTAINS]->(f:Fact)-[:MENTIONS]->(entity:Entity)
        RETURN entity.name as name, entity.type as type, count(entity) as count
        """
        
        entities_result = adapter.execute_cypher(entities_query)
        
        if entities_result and len(entities_result) > 1 and entities_result[1]:
            logger.info(f"  Entities:")
            for entity in entities_result[1]:
                entity_name = entity[0]
                entity_type = entity[1]
                entity_count = entity[2]
                logger.info(f"    {entity_name} ({entity_type}): {entity_count} mentions")
        else:
            logger.info("  No entities found for this episode")
    
    # Get all entities
    entities_query = """
    MATCH (e:Entity)
    RETURN e.type as type, count(e) as count
    """
    
    entities_result = adapter.execute_cypher(entities_query)
    
    if entities_result and len(entities_result) > 1 and entities_result[1]:
        logger.info(f"Entity types in the database:")
        for entity in entities_result[1]:
            entity_type = entity[0]
            entity_count = entity[1]
            logger.info(f"  {entity_type}: {entity_count}")
    else:
        logger.info("No entities found in the database")
    
    # Close the connection
    adapter.close()

if __name__ == "__main__":
    asyncio.run(main())
