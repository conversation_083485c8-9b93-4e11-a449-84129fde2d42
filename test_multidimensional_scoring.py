#!/usr/bin/env python3
"""
Test script for multi-dimensional entity scoring.

This script demonstrates the new multi-dimensional scoring system.
"""

import sys
import os
import logging
from typing import List

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication.models import EntityForDeduplication
from entity_deduplication.multi_dimensional_scoring import get_multi_dimensional_scorer
from entity_deduplication.utils import calculate_entity_similarity_multidimensional

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_test_entities_with_attributes() -> List[EntityForDeduplication]:
    """Create test entities with rich attributes for multi-dimensional testing."""
    entities = [
        # Person entities with different levels of detail
        EntityForDeduplication(
            uuid="person-1",
            name="Dr. <PERSON>",
            type="Person",
            description="Medical researcher specializing in cardiovascular disease",
            confidence=0.9,
            source_document_id="doc1",
            attributes={
                "title": "Dr.",
                "specialty": "cardiology",
                "institution": "Harvard Medical School",
                "years_experience": 15
            },
            embedding=[0.1, 0.2, 0.3, 0.4, 0.5] * 200  # Mock 1000-dim embedding
        ),
        EntityForDeduplication(
            uuid="person-2",
            name="<PERSON> A. <PERSON>",
            type="Person", 
            description="Cardiologist and researcher at Harvard",
            confidence=0.85,
            source_document_id="doc2",
            attributes={
                "title": "Doctor",
                "specialty": "cardiovascular medicine",
                "institution": "Harvard Med School",
                "years_experience": 16
            },
            embedding=[0.11, 0.21, 0.31, 0.41, 0.51] * 200  # Similar but slightly different
        ),
        EntityForDeduplication(
            uuid="person-3",
            name="John Smith",
            type="Person",
            description="Software engineer at Google",
            confidence=0.8,
            source_document_id="doc3",
            attributes={
                "title": "Engineer",
                "specialty": "machine learning",
                "institution": "Google",
                "years_experience": 8
            },
            embedding=[0.8, 0.7, 0.6, 0.5, 0.4] * 200  # Very different embedding
        ),
        
        # Organization entities
        EntityForDeduplication(
            uuid="org-1",
            name="Harvard Medical School",
            type="Organization",
            description="Prestigious medical school in Boston, Massachusetts",
            confidence=0.95,
            source_document_id="doc1",
            attributes={
                "type": "medical_school",
                "location": "Boston, MA",
                "founded": 1782,
                "ranking": 1
            },
            embedding=[0.2, 0.3, 0.4, 0.5, 0.6] * 200
        ),
        EntityForDeduplication(
            uuid="org-2",
            name="Harvard Med School",
            type="Organization",
            description="Medical education institution in Boston",
            confidence=0.8,
            source_document_id="doc4",
            attributes={
                "type": "medical_institution",
                "location": "Boston",
                "founded": 1782,
                "ranking": 1
            },
            embedding=[0.21, 0.31, 0.41, 0.51, 0.61] * 200
        ),
        
        # Location entities
        EntityForDeduplication(
            uuid="loc-1",
            name="Boston",
            type="Location",
            description="Capital city of Massachusetts",
            confidence=0.9,
            source_document_id="doc5",
            attributes={
                "state": "Massachusetts",
                "country": "USA",
                "population": 685094,
                "type": "city"
            },
            embedding=[0.3, 0.4, 0.5, 0.6, 0.7] * 200
        ),
        EntityForDeduplication(
            uuid="loc-2",
            name="Boston, Massachusetts",
            type="Location",
            description="Major city in New England",
            confidence=0.88,
            source_document_id="doc6",
            attributes={
                "state": "MA",
                "country": "United States",
                "population": 685000,
                "type": "metropolitan_area"
            },
            embedding=[0.31, 0.41, 0.51, 0.61, 0.71] * 200
        )
    ]
    
    return entities


def test_multidimensional_scoring():
    """Test the multi-dimensional scoring system."""
    logger.info("=== Testing Multi-dimensional Scoring ===")
    
    entities = create_test_entities_with_attributes()
    scorer = get_multi_dimensional_scorer()
    
    # Test pairs that should show different scoring patterns
    test_pairs = [
        (entities[0], entities[1], "Dr. John Smith vs John A. Smith (Same person, different docs)"),
        (entities[0], entities[2], "Dr. John Smith vs John Smith (Different people, same name)"),
        (entities[3], entities[4], "Harvard Medical School vs Harvard Med School (Same org)"),
        (entities[5], entities[6], "Boston vs Boston, Massachusetts (Same location)")
    ]
    
    for entity1, entity2, description in test_pairs:
        logger.info(f"\n{description}")
        logger.info(f"Entity 1: {entity1.name} (Type: {entity1.type})")
        logger.info(f"Entity 2: {entity2.name} (Type: {entity2.type})")
        
        # Calculate multi-dimensional similarity
        components = scorer.calculate_similarity(entity1, entity2)
        
        logger.info("Similarity Components:")
        for component, score in components.to_dict().items():
            logger.info(f"  {component:12}: {score:.3f}")
        
        # Also test the integrated function
        overall_score, match_type = calculate_entity_similarity_multidimensional(entity1, entity2)
        logger.info(f"Overall Score: {overall_score:.3f} (Match Type: {match_type})")


def test_component_analysis():
    """Test individual components of the scoring system."""
    logger.info("\n=== Testing Individual Components ===")
    
    entities = create_test_entities_with_attributes()
    scorer = get_multi_dimensional_scorer()
    
    # Test semantic similarity
    logger.info("\nSemantic Similarity Tests:")
    entity1, entity2 = entities[0], entities[1]  # Similar entities
    semantic_score = scorer._calculate_semantic_similarity(entity1, entity2)
    logger.info(f"Similar entities semantic score: {semantic_score:.3f}")
    
    entity1, entity2 = entities[0], entities[2]  # Different entities
    semantic_score = scorer._calculate_semantic_similarity(entity1, entity2)
    logger.info(f"Different entities semantic score: {semantic_score:.3f}")
    
    # Test syntactic similarity
    logger.info("\nSyntactic Similarity Tests:")
    entity1, entity2 = entities[0], entities[1]  # Dr. John Smith vs John A. Smith
    syntactic_score = scorer._calculate_syntactic_similarity(entity1, entity2)
    logger.info(f"'Dr. John Smith' vs 'John A. Smith': {syntactic_score:.3f}")
    
    entity1, entity2 = entities[3], entities[4]  # Harvard Medical School vs Harvard Med School
    syntactic_score = scorer._calculate_syntactic_similarity(entity1, entity2)
    logger.info(f"'Harvard Medical School' vs 'Harvard Med School': {syntactic_score:.3f}")
    
    # Test contextual similarity
    logger.info("\nContextual Similarity Tests:")
    entity1, entity2 = entities[0], entities[1]  # Different documents
    contextual_score = scorer._calculate_contextual_similarity(entity1, entity2)
    logger.info(f"Different documents, similar descriptions: {contextual_score:.3f}")
    
    # Test attribute similarity
    logger.info("\nAttribute Similarity Tests:")
    entity1, entity2 = entities[0], entities[1]  # Similar attributes
    attribute_score = scorer._calculate_attribute_similarity(entity1, entity2)
    logger.info(f"Similar professional attributes: {attribute_score:.3f}")
    
    entity1, entity2 = entities[0], entities[2]  # Different attributes
    attribute_score = scorer._calculate_attribute_similarity(entity1, entity2)
    logger.info(f"Different professional attributes: {attribute_score:.3f}")


def test_weight_sensitivity():
    """Test how different weight configurations affect scoring."""
    logger.info("\n=== Testing Weight Sensitivity ===")
    
    entities = create_test_entities_with_attributes()
    entity1, entity2 = entities[0], entities[1]  # Dr. John Smith vs John A. Smith
    
    # Test different weight configurations
    weight_configs = [
        {"semantic": 0.5, "syntactic": 0.2, "contextual": 0.15, "attribute": 0.1, "confidence": 0.05},
        {"semantic": 0.2, "syntactic": 0.5, "contextual": 0.15, "attribute": 0.1, "confidence": 0.05},
        {"semantic": 0.2, "syntactic": 0.2, "contextual": 0.4, "attribute": 0.15, "confidence": 0.05},
        {"semantic": 0.2, "syntactic": 0.2, "contextual": 0.15, "attribute": 0.4, "confidence": 0.05}
    ]
    
    weight_names = ["Semantic-heavy", "Syntactic-heavy", "Context-heavy", "Attribute-heavy"]
    
    for i, weights in enumerate(weight_configs):
        scorer = get_multi_dimensional_scorer(weights)
        components = scorer.calculate_similarity(entity1, entity2)
        
        logger.info(f"\n{weight_names[i]} Configuration:")
        logger.info(f"  Overall Score: {components.overall_score:.3f}")
        logger.info(f"  Primary Components: Semantic={components.semantic_score:.3f}, "
                   f"Syntactic={components.syntactic_score:.3f}")


def test_performance_comparison():
    """Compare performance of original vs multi-dimensional scoring."""
    logger.info("\n=== Performance Comparison ===")
    
    entities = create_test_entities_with_attributes()
    
    # Test pairs
    test_pairs = [
        (entities[0], entities[1]),  # Should match
        (entities[0], entities[2]),  # Should not match
        (entities[3], entities[4]),  # Should match
        (entities[5], entities[6])   # Should match
    ]
    
    logger.info("Comparison of scoring methods:")
    logger.info("Pair | Original | Multi-dim | Match Type")
    logger.info("-" * 45)
    
    for i, (entity1, entity2) in enumerate(test_pairs):
        # Original scoring (fallback in the function)
        from entity_deduplication.utils import calculate_entity_similarity
        orig_score, orig_type = calculate_entity_similarity(entity1, entity2)
        
        # Multi-dimensional scoring
        multi_score, multi_type = calculate_entity_similarity_multidimensional(entity1, entity2)
        
        logger.info(f"{i+1:4} | {orig_score:8.3f} | {multi_score:9.3f} | {multi_type}")


def main():
    """Run all tests."""
    logger.info("Starting Multi-dimensional Scoring Tests")
    logger.info("=" * 60)
    
    try:
        test_multidimensional_scoring()
        test_component_analysis()
        test_weight_sensitivity()
        test_performance_comparison()
        
        logger.info("\n" + "=" * 60)
        logger.info("All multi-dimensional scoring tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
