"""
Document processor for the worker system.

This module provides the document processor implementation for the worker system.
"""

from typing import Dict, List, Any, Optional, Union
import asyncio

# Configure logging
from utils.logging_utils import get_logger
logger = get_logger(__name__)

async def process_document_task(task: Dict[str, Any], add_task_callback=None) -> Dict[str, Any]:
    """
    Process a document task.
    
    Args:
        task: Document task to process
        add_task_callback: Callback function to add new tasks to the queue
        
    Returns:
        Result of the document processing
    """
    from services.document_processing_service import get_document_processing_service
    
    file_path = task.get("file_path")
    chunk_size = task.get("chunk_size", 1200)
    overlap = task.get("overlap", 0)
    
    logger.info(f"Processing document: {file_path}")
    
    # Get the document processing service
    service = await get_document_processing_service()
    
    # Process the document (text extraction only)
    result = await service.process_document(
        file_path,
        chunk_size=chunk_size,
        overlap=overlap,
        extract_entities=False,  # We'll do this in a separate worker
        extract_references=False,  # We'll do this in a separate worker
        extract_metadata=True,
        generate_embeddings=False  # We'll do this in a separate worker
    )
    
    # If successful and we have a callback, queue tasks for entity extraction, reference extraction, and embedding generation
    if result.get("success", False) and add_task_callback:
        from workers.base import WorkerType
        
        # Queue entity extraction tasks for each chunk
        for chunk_id, chunk in enumerate(result.get("chunks", [])):
            await add_task_callback(
                WorkerType.ENTITY_EXTRACTOR,
                {
                    "id": f"{result.get('episode_id')}_{chunk_id}",
                    "document_id": result.get("episode_id"),
                    "chunk_id": chunk_id,
                    "text": chunk.get("body"),
                    "fact_id": chunk.get("uuid")
                }
            )
        
        # Queue reference extraction task
        await add_task_callback(
            WorkerType.REFERENCE_EXTRACTOR,
            {
                "id": result.get("episode_id"),
                "document_id": result.get("episode_id"),
                "file_path": file_path
            }
        )
        
        # Queue embedding generation tasks for each chunk
        for chunk_id, chunk in enumerate(result.get("chunks", [])):
            await add_task_callback(
                WorkerType.EMBEDDING_GENERATOR,
                {
                    "id": f"{result.get('episode_id')}_{chunk_id}",
                    "document_id": result.get("episode_id"),
                    "chunk_id": chunk_id,
                    "text": chunk.get("body"),
                    "fact_id": chunk.get("uuid")
                }
            )
    
    return result
