"""
Search functionality service for the Graphiti application.
"""

from typing import List, Dict, Any, Optional, Union
from datetime import datetime

from utils.logging_utils import get_logger
from database.database_service import get_falkordb_adapter
from models.knowledge_graph import SearchQuery, SearchResult

# Set up logger
logger = get_logger(__name__)

async def search_knowledge_graph(query: SearchQuery) -> SearchResult:
    """
    Search the knowledge graph.
    
    Args:
        query: Search query
        
    Returns:
        Search results
    """
    adapter = await get_falkordb_adapter()
    
    # Build the query
    cypher_query = "MATCH "
    
    # Add entity type filter
    if query.entity_types:
        entity_types = [f"'{entity_type}'" for entity_type in query.entity_types]
        entity_types_str = ", ".join(entity_types)
        cypher_query += f"(e:Entity) WHERE e.type IN [{entity_types_str}] "
    else:
        cypher_query += "(e:Entity) "
    
    # Add text search
    if query.query:
        # Escape single quotes in the query
        escaped_query = query.query.replace("'", "\\'")
        
        # Add text search condition
        if "WHERE" in cypher_query:
            cypher_query += f"AND (e.name CONTAINS '{escaped_query}' OR e.description CONTAINS '{escaped_query}') "
        else:
            cypher_query += f"WHERE (e.name CONTAINS '{escaped_query}' OR e.description CONTAINS '{escaped_query}') "
    
    # Add relationship filter
    if query.relationship_types:
        relationship_types = [f"'{rel_type}'" for rel_type in query.relationship_types]
        relationship_types_str = ", ".join(relationship_types)
        
        # Add relationship pattern
        cypher_query += f"OPTIONAL MATCH (e)-[r]->(target:Entity) WHERE type(r) IN [{relationship_types_str}] "
    else:
        # Add optional relationship pattern
        cypher_query += "OPTIONAL MATCH (e)-[r]->(target:Entity) "
    
    # Add confidence filter
    if query.min_confidence:
        # Add confidence condition
        if "WHERE" in cypher_query:
            cypher_query += f"AND (r IS NULL OR r.confidence >= {query.min_confidence}) "
        else:
            cypher_query += f"WHERE (r IS NULL OR r.confidence >= {query.min_confidence}) "
    
    # Add return clause
    cypher_query += "RETURN e, r, target "
    
    # Add limit
    cypher_query += f"LIMIT {query.limit}"
    
    # Execute the query
    result = adapter.execute_cypher(cypher_query)
    
    # Process the results
    entities = []
    relationships = []
    facts = []
    
    if result and len(result) > 1:
        # Create a set to track processed entities
        processed_entities = set()
        
        for row in result[1]:
            entity = row[0]
            relationship = row[1]
            target = row[2]
            
            # Add entity if not already processed
            if entity and entity.get("uuid") not in processed_entities:
                entities.append(entity)
                processed_entities.add(entity.get("uuid"))
            
            # Add relationship and target if they exist
            if relationship and target:
                relationships.append({
                    "source": entity.get("uuid"),
                    "target": target.get("uuid"),
                    "type": relationship.get("type"),
                    "properties": relationship
                })
                
                # Add target entity if not already processed
                if target.get("uuid") not in processed_entities:
                    entities.append(target)
                    processed_entities.add(target.get("uuid"))
    
    # Get facts mentioning the entities
    if entities:
        entity_uuids = [f"'{entity.get('uuid')}'" for entity in entities]
        entity_uuids_str = ", ".join(entity_uuids)
        
        facts_query = f"""
        MATCH (f:Fact)-[:MENTIONS]->(e:Entity)
        WHERE e.uuid IN [{entity_uuids_str}]
        RETURN f.uuid as uuid, f.body as body, e.uuid as entity_uuid
        LIMIT 100
        """
        
        facts_result = adapter.execute_cypher(facts_query)
        
        if facts_result and len(facts_result) > 1:
            for row in facts_result[1]:
                fact_uuid = row[0]
                fact_body = row[1]
                entity_uuid = row[2]
                
                facts.append({
                    "uuid": fact_uuid,
                    "body": fact_body,
                    "entity_uuid": entity_uuid
                })
    
    return SearchResult(
        entities=entities,
        relationships=relationships,
        facts=facts
    )

async def search_documents(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Search documents.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    adapter = await get_falkordb_adapter()
    
    # Escape single quotes in the query
    escaped_query = query.replace("'", "\\'")
    
    # Build the query
    cypher_query = f"""
    MATCH (e:Episode)
    WHERE e.name CONTAINS '{escaped_query}'
    RETURN e.uuid as uuid, e.name as name, e.file_path as file_path
    LIMIT {limit}
    """
    
    # Execute the query
    result = adapter.execute_cypher(cypher_query)
    
    # Process the results
    documents = []
    
    if result and len(result) > 1:
        for row in result[1]:
            uuid = row[0]
            name = row[1]
            file_path = row[2]
            
            documents.append({
                "uuid": uuid,
                "name": name,
                "file_path": file_path
            })
    
    return documents

async def search_facts(query: str, limit: int = 10) -> List[Dict[str, Any]]:
    """
    Search facts.
    
    Args:
        query: Search query
        limit: Maximum number of results to return
        
    Returns:
        Search results
    """
    adapter = await get_falkordb_adapter()
    
    # Escape single quotes in the query
    escaped_query = query.replace("'", "\\'")
    
    # Build the query
    cypher_query = f"""
    MATCH (f:Fact)
    WHERE f.body CONTAINS '{escaped_query}'
    RETURN f.uuid as uuid, f.body as body
    LIMIT {limit}
    """
    
    # Execute the query
    result = adapter.execute_cypher(cypher_query)
    
    # Process the results
    facts = []
    
    if result and len(result) > 1:
        for row in result[1]:
            uuid = row[0]
            body = row[1]
            
            facts.append({
                "uuid": uuid,
                "body": body
            })
    
    return facts
