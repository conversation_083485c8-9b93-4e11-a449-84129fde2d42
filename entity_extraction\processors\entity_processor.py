"""
Entity processor for creating and managing entity nodes in the knowledge graph.

This module provides functionality for processing entities and managing their representation
in the knowledge graph database. It handles database operations such as creating constraints,
retrieving facts without entities, creating entity nodes, and gathering statistics about
entities in the knowledge graph.

The EntityProcessor class encapsulates all the database operations related to entities,
providing a clean interface for other components to interact with the entity data in the
knowledge graph.

Example:
    ```python
    import asyncio
    from entity_extraction.processors.entity_processor import EntityProcessor

    async def process_entities(driver):
        # Create an entity processor
        processor = EntityProcessor(driver)

        # Create constraints for Entity nodes
        await processor.create_entity_constraints()

        # Get facts without entities
        facts = await processor.get_facts_without_entities(batch_size=10)

        # Create entity nodes for a fact
        entities = [
            {"name": "Vitamin C", "type": "Nutrient", "description": "An essential vitamin"},
            {"name": "Immune System", "type": "Process", "description": "Body's defense system"}
        ]
        await processor.create_entity_nodes(fact_uuid="123", entities=entities)

        # Get entity statistics
        stats = await processor.get_entity_statistics()
        print(f"Total entities: {sum(ec['count'] for ec in stats['entity_counts'])}")

    # Run the async function
    asyncio.run(process_entities(driver))
    ```
"""

import uuid
import logging
from datetime import datetime, timezone
from typing import List, Dict, Any

# Set up logging
logger = logging.getLogger(__name__)


class EntityProcessor:
    """
    Processor for creating and managing entity nodes in the knowledge graph.

    This class provides methods for interacting with entity nodes in the knowledge graph database.
    It handles operations such as creating constraints, retrieving facts without entities,
    creating entity nodes, and gathering statistics about entities.

    The processor is designed to work with graph databases that support the Cypher query language,
    such as Neo4j and FalkorDB. It uses asynchronous database operations for improved performance.

    Attributes:
        driver: Database driver for connecting to the graph database
        logger (Logger): Logger instance for this class
    """

    def __init__(self, driver):
        """
        Initialize the entity processor.

        Args:
            driver: Database driver
        """
        self.driver = driver
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")

    async def create_entity_constraints(self):
        """
        Create constraints for Entity nodes in the database.

        This method creates a uniqueness constraint on the combination of name and type
        for Entity nodes, ensuring that each entity with a specific name and type is unique
        in the database. It also creates an index on the entity type for faster querying.

        The method first checks if the constraint already exists to avoid errors when
        running the method multiple times.

        Raises:
            Exception: If there is an error creating the constraints
        """
        logger.info("Creating constraints for Entity nodes")

        try:
            async with self.driver.session() as session:
                # Check if the constraint already exists
                result = await session.run(
                    """
                    SHOW CONSTRAINTS
                    YIELD name
                    WHERE name = 'entity_name_type_constraint'
                    RETURN count(*) > 0 AS exists
                    """
                )
                record = await result.single()

                if record and record.get("exists", False):
                    logger.info("Constraint 'entity_name_type_constraint' already exists")
                    return

                # Create the constraint
                result = await session.run(
                    """
                    CREATE CONSTRAINT entity_name_type_constraint IF NOT EXISTS
                    FOR (e:Entity)
                    REQUIRE (e.name, e.type) IS UNIQUE
                    """
                )
                await result.consume()
                logger.info("Created constraint 'entity_name_type_constraint'")

                # Create index for entity type
                result = await session.run(
                    """
                    CREATE INDEX entity_type_index IF NOT EXISTS
                    FOR (e:Entity)
                    ON (e.type)
                    """
                )
                await result.consume()
                logger.info("Created index 'entity_type_index'")
        except Exception as e:
            logger.error(f"Error creating constraints: {e}")
            raise

    async def get_facts_without_entities(self, batch_size=5):
        """
        Get facts that don't have entity relationships.

        This method retrieves facts from the database that don't have any MENTIONS
        relationships to Entity nodes. These facts are candidates for entity extraction.

        The method uses a Cypher query to find facts without entity relationships and
        returns them in batches for processing. This batched approach helps manage
        memory usage and processing load when dealing with large numbers of facts.

        Args:
            batch_size (int, optional): Number of facts to retrieve in one batch.
                Defaults to 5.

        Returns:
            List[Dict[str, Any]]: A list of facts without entity relationships, where each
                fact is a dictionary with the following keys:
                - uuid (str): The UUID of the fact
                - body (str): The text content of the fact

        Raises:
            Exception: If there is an error retrieving facts from the database
        """
        logger.info(f"Getting facts without entity relationships (batch size: {batch_size})")

        try:
            async with self.driver.session() as session:
                result = await session.run(
                    """
                    MATCH (f:Fact)
                    WHERE NOT EXISTS((f)-[:MENTIONS]->(:Entity))
                    RETURN f.uuid AS uuid, f.body AS body
                    LIMIT $batch_size
                    """,
                    {"batch_size": batch_size}
                )

                facts = []
                async for record in result:
                    facts.append({
                        "uuid": record["uuid"],
                        "body": record["body"]
                    })

                logger.info(f"Found {len(facts)} facts without entity relationships")
                return facts
        except Exception as e:
            logger.error(f"Error getting facts without entities: {e}")
            return []

    async def create_entity_nodes(self, fact_uuid: str, entities: List[Dict[str, Any]]):
        """
        Create Entity nodes and MENTIONS relationships.

        This method creates or merges Entity nodes in the database based on the provided
        list of entities, and creates MENTIONS relationships from the specified fact to
        these entities.

        For each entity, the method:
        1. Generates a UUID if one doesn't exist
        2. Creates or merges the Entity node with the given name and type
        3. Sets properties like description, created_at, and updated_at
        4. Adds source document metadata (document ID, title)
        5. Creates a MENTIONS relationship from the fact to the entity

        The method uses MERGE operations to avoid creating duplicate entities, and
        implements conditional property setting to preserve existing data when appropriate.

        Args:
            fact_uuid (str): UUID of the fact that mentions the entities
            entities (List[Dict[str, Any]]): List of entities to create, where each entity
                is a dictionary with at least the following keys:
                - name (str): The name of the entity
                - type (str): The type of the entity
                - description (Optional[str]): A description of the entity
                - confidence (Optional[float]): Confidence score for the entity extraction

        Raises:
            Exception: If there is an error creating entity nodes or relationships
        """
        logger.info(f"Creating {len(entities)} entity nodes for fact {fact_uuid}")

        if not entities:
            logger.info(f"No entities to create for fact {fact_uuid}")
            return

        try:
            async with self.driver.session() as session:
                # Get the fact to get document information
                result = await session.run(
                    """
                    MATCH (f:Fact {uuid: $fact_uuid})
                    OPTIONAL MATCH (e:Episode)-[:CONTAINS]->(f)
                    RETURN f.body AS body, f.chunk_num AS chunk_num,
                           e.uuid AS episode_uuid, e.title AS episode_title
                    """,
                    {"fact_uuid": fact_uuid}
                )
                fact_record = await result.single()

                # Extract document metadata
                episode_uuid = fact_record.get("episode_uuid")
                episode_title = fact_record.get("episode_title")
                chunk_num = fact_record.get("chunk_num")

                for entity in entities:
                    # Generate a UUID for the entity
                    entity_uuid = str(uuid.uuid4())

                    # Get confidence score if available
                    confidence = entity.get("confidence", 1.0)

                    # Create or merge the entity node with document metadata
                    result = await session.run(
                        """
                        MERGE (e:Entity {name: $name, type: $type})
                        ON CREATE SET
                            e.uuid = $uuid,
                            e.description = $description,
                            e.source_document_id = $source_document_id,
                            e.source_document_title = $source_document_title,
                            e.source_fact_id = $source_fact_id,
                            e.confidence = $confidence,
                            e.created_at = datetime($timestamp)
                        ON MATCH SET
                            e.uuid = CASE
                                WHEN e.uuid IS NULL
                                THEN $uuid
                                ELSE e.uuid
                            END,
                            e.description = CASE
                                WHEN e.description IS NULL OR size(e.description) < size($description)
                                THEN $description
                                ELSE e.description
                            END,
                            e.updated_at = datetime($timestamp)
                        RETURN e.name AS name, e.type AS type, e.uuid AS uuid
                        """,
                        {
                            "name": entity["name"],
                            "type": entity["type"],
                            "uuid": entity_uuid,
                            "description": entity.get("description", ""),
                            "source_document_id": episode_uuid,
                            "source_document_title": episode_title,
                            "source_fact_id": fact_uuid,
                            "confidence": confidence,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    )
                    record = await result.single()

                    # Create the MENTIONS relationship with metadata
                    result = await session.run(
                        """
                        MATCH (f:Fact {uuid: $fact_uuid})
                        MATCH (e:Entity {name: $entity_name, type: $entity_type})
                        MERGE (f)-[r:MENTIONS]->(e)
                        ON CREATE SET
                            r.created_at = datetime($timestamp),
                            r.confidence = $confidence,
                            r.chunk_num = $chunk_num
                        RETURN type(r) AS type
                        """,
                        {
                            "fact_uuid": fact_uuid,
                            "entity_name": record["name"],
                            "entity_type": record["type"],
                            "confidence": confidence,
                            "chunk_num": chunk_num,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    )
                    await result.consume()

                logger.info(f"Created {len(entities)} entity nodes and relationships for fact {fact_uuid}")
        except Exception as e:
            logger.error(f"Error creating entity nodes: {e}")
            raise

    async def get_entity_statistics(self):
        """
        Get statistics about entities in the knowledge graph.

        This method retrieves various statistics about entities in the knowledge graph,
        including:
        - Counts of entities by type
        - Count of MENTIONS relationships
        - Count of RELATED_TO relationships
        - Top entities by number of mentions

        These statistics provide insights into the composition and structure of the
        knowledge graph, which can be useful for monitoring, reporting, and analysis.

        Returns:
            Dict[str, Any]: A dictionary with the following keys:
                - entity_counts (List[Dict[str, Any]]): Counts of entities by type
                - mentions_count (int): Count of MENTIONS relationships
                - related_to_count (int): Count of RELATED_TO relationships
                - top_entities (List[Dict[str, Any]]): Top entities by mentions

        Raises:
            Exception: If there is an error retrieving statistics from the database
        """
        logger.info("Getting entity statistics")

        try:
            async with self.driver.session() as session:
                # Count entities by type
                result = await session.run(
                    """
                    MATCH (e:Entity)
                    RETURN e.type AS type, count(e) AS count
                    ORDER BY count DESC
                    """
                )

                entity_counts = []
                async for record in result:
                    entity_counts.append({
                        "type": record["type"],
                        "count": record["count"]
                    })

                logger.info("Entity counts by type:")
                for entity_count in entity_counts:
                    logger.info(f"  {entity_count['type']}: {entity_count['count']}")

                # Count MENTIONS relationships
                result = await session.run(
                    """
                    MATCH ()-[r:MENTIONS]->()
                    RETURN count(r) AS count
                    """
                )
                record = await result.single()
                mentions_count = record["count"]
                logger.info(f"MENTIONS relationships: {mentions_count}")

                # Count RELATED_TO relationships
                result = await session.run(
                    """
                    MATCH ()-[r:RELATED_TO]->()
                    RETURN count(r) AS count
                    """
                )
                record = await result.single()
                related_to_count = record["count"]
                logger.info(f"RELATED_TO relationships: {related_to_count}")

                # Get top entities by mentions with source document information
                result = await session.run(
                    """
                    MATCH (e:Entity)<-[r:MENTIONS]-()
                    WITH e, count(r) AS mentions
                    ORDER BY mentions DESC
                    LIMIT 10
                    RETURN
                        e.name AS name,
                        e.type AS type,
                        e.source_document_id AS source_document_id,
                        e.source_document_title AS source_document_title,
                        e.confidence AS confidence,
                        mentions
                    """
                )

                top_entities = []
                async for record in result:
                    top_entities.append({
                        "name": record["name"],
                        "type": record["type"],
                        "source_document_id": record["source_document_id"],
                        "source_document_title": record["source_document_title"],
                        "confidence": record["confidence"],
                        "mentions": record["mentions"]
                    })

                logger.info("Top entities by mentions:")
                for entity in top_entities:
                    logger.info(f"  {entity['name']} ({entity['type']}): {entity['mentions']} mentions")

                return {
                    "entity_counts": entity_counts,
                    "mentions_count": mentions_count,
                    "related_to_count": related_to_count,
                    "top_entities": top_entities
                }
        except Exception as e:
            logger.error(f"Error getting entity statistics: {e}")
            return {}
