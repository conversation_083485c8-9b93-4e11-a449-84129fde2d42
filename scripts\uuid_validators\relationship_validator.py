"""
Relationship UUID validator.
"""

import logging
from typing import List, Dict, Any
from .base_validator import BaseValidator

logger = logging.getLogger(__name__)


class RelationshipValidator(BaseValidator):
    """Validator for relationship UUIDs."""

    def __init__(self, fix: bool = False, verbose: bool = False):
        super().__init__(fix, verbose)
        self.stats = {
            "relationships_total": 0,
            "relationships_missing_uuid": 0,
            "relationships_fixed": 0
        }

    async def validate(self):
        """Check relationships for missing UUIDs."""
        adapter = await self.get_adapter()

        # Count all relationships
        count_query = """
        MATCH ()-[r]->()
        RETURN count(r) as total
        """

        count_result = adapter.execute_cypher(count_query)

        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            self.stats["relationships_total"] = count_result[1][0][0]

        # Find relationships without UUIDs
        missing_query = """
        MATCH ()-[r]->()
        WHERE r.uuid IS NULL
        RETURN id(r) as id, type(r) as type
        LIMIT 10000
        """

        missing_result = adapter.execute_cypher(missing_query)
        relationships = []

        if missing_result and len(missing_result) > 1 and len(missing_result[1]) > 0:
            for row in missing_result[1]:
                relationships.append({
                    "id": row[0],
                    "type": row[1]
                })

            self.stats["relationships_missing_uuid"] = len(relationships)
            self.log_info(f"Found {len(relationships)} relationships without UUIDs")

            if self.fix:
                fixed_count = await self._fix_relationships(relationships)
                self.stats["relationships_fixed"] = fixed_count

    async def _fix_relationships(self, relationships: List[Dict[str, Any]]) -> int:
        """
        Fix relationships missing UUIDs.

        Args:
            relationships: List of relationships to fix

        Returns:
            Number of relationships fixed
        """
        adapter = await self.get_adapter()
        fixed_count = 0

        for rel in relationships:
            rel_uuid = self.generate_new_uuid()
            timestamp = self.get_timestamp()

            update_query = f"""
            MATCH ()-[r]->()
            WHERE id(r) = {rel['id']}
            SET r.uuid = '{rel_uuid}',
                r.updated_at = '{timestamp}'
            RETURN type(r) as type, r.uuid as uuid
            """

            update_result = adapter.execute_cypher(update_query)

            if update_result and len(update_result) > 1 and len(update_result[1]) > 0:
                fixed_count += 1
                self.log_info(f"Fixed relationship of type {update_result[1][0][0]} with UUID {update_result[1][0][1]}")

        return fixed_count
