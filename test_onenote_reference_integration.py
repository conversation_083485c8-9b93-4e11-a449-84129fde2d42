#!/usr/bin/env python3
"""
Test script for OneNote reference extraction integration.

This script tests that OneNote files are properly routed through the OneNote processor
instead of being sent directly to Mistral OCR, which was causing the MIME type errors.
"""

import sys
import os
import logging
import tempfile
from pathlib import Path
from typing import Dict, Any

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_reference_processor_onenote_routing():
    """Test that reference processor routes OneNote files correctly."""
    logger.info("=== Testing Reference Processor OneNote Routing ===")
    
    try:
        from services.reference_processor import ReferenceProcessor
        import asyncio
        
        # Create a mock OneNote file
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            # Write OneNote file header
            temp_file.write(b'\xe4\x52\x5c\x7b\x8c\xd8\xa7\x4d\xae\xb1\x53\x78\xd0\x29\x96\xd3')
            temp_file.write(b'Mock OneNote content for reference extraction testing')
            temp_file_path = temp_file.name
        
        try:
            # Initialize reference processor
            processor = ReferenceProcessor()
            
            # Test the Mistral OCR reference extraction method
            logger.info(f"Testing reference extraction with OneNote file: {temp_file_path}")
            result = asyncio.run(processor._extract_references_with_mistral_ocr(temp_file_path))
            
            logger.info(f"Reference extraction result: {result}")
            
            # Check if the result indicates OneNote processing was attempted
            if result.get('success', False):
                logger.info("✅ Reference extraction succeeded (OneNote processor worked)")
                return True
            else:
                error_msg = result.get('error', '')
                if 'OneNote' in error_msg or 'one-extract' in error_msg:
                    logger.info("✅ OneNote processor was used (expected failure with mock file)")
                    return True
                elif 'Invalid document type' in error_msg or 'application/octet-stream' in error_msg:
                    logger.error("❌ OneNote file was sent directly to Mistral OCR (routing failed)")
                    return False
                else:
                    logger.info(f"ℹ️ Other processing method was used: {error_msg}")
                    return True
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"❌ Error testing reference processor OneNote routing: {e}")
        return False


def test_improved_reference_extractor_onenote_routing():
    """Test that improved reference extractor routes OneNote files correctly."""
    logger.info("\n=== Testing Improved Reference Extractor OneNote Routing ===")
    
    try:
        from services.improved_reference_extractor import ImprovedReferenceExtractor
        import asyncio
        
        # Create a mock OneNote file
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            temp_file.write(b'\xe4\x52\x5c\x7b\x8c\xd8\xa7\x4d\xae\xb1\x53\x78\xd0\x29\x96\xd3')
            temp_file.write(b'Mock OneNote content for improved reference extraction testing')
            temp_file_path = temp_file.name
        
        try:
            # Initialize improved reference extractor
            extractor = ImprovedReferenceExtractor()
            
            # Test the extraction method
            logger.info(f"Testing improved reference extraction with OneNote file: {temp_file_path}")
            result = asyncio.run(extractor.extract_references(temp_file_path))
            
            logger.info(f"Improved reference extraction result: {result}")
            
            # Check if the result indicates OneNote processing was attempted
            if result.get('success', False):
                logger.info("✅ Improved reference extraction succeeded (OneNote processor worked)")
                return True
            else:
                error_msg = result.get('error', '')
                if 'OneNote' in error_msg or 'one-extract' in error_msg:
                    logger.info("✅ OneNote processor was used (expected failure with mock file)")
                    return True
                elif 'Invalid document type' in error_msg or 'application/octet-stream' in error_msg:
                    logger.error("❌ OneNote file was sent directly to Mistral OCR (routing failed)")
                    return False
                else:
                    logger.info(f"ℹ️ Other processing method was used: {error_msg}")
                    return True
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"❌ Error testing improved reference extractor OneNote routing: {e}")
        return False


def test_enhanced_document_processor_onenote_routing():
    """Test that enhanced document processor routes OneNote files correctly."""
    logger.info("\n=== Testing Enhanced Document Processor OneNote Routing ===")
    
    try:
        from processors.enhanced_document_processor import EnhancedDocumentProcessor
        import asyncio
        
        # Create a mock OneNote file
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            temp_file.write(b'\xe4\x52\x5c\x7b\x8c\xd8\xa7\x4d\xae\xb1\x53\x78\xd0\x29\x96\xd3')
            temp_file.write(b'Mock OneNote content for enhanced processor testing')
            temp_file_path = temp_file.name
        
        try:
            # Initialize enhanced document processor
            processor = EnhancedDocumentProcessor()
            
            # Test the advanced reference extraction method
            logger.info(f"Testing enhanced processor reference extraction with OneNote file: {temp_file_path}")
            result = asyncio.run(processor._extract_references_advanced(Path(temp_file_path)))
            
            logger.info(f"Enhanced processor reference extraction result: {result}")
            
            # Check if the result indicates OneNote processing was attempted
            if result.get('success', False):
                logger.info("✅ Enhanced processor reference extraction succeeded")
                return True
            else:
                error_msg = result.get('error', '')
                if 'OneNote' in error_msg or 'one-extract' in error_msg:
                    logger.info("✅ OneNote processor was used (expected failure with mock file)")
                    return True
                elif 'Invalid document type' in error_msg or 'application/octet-stream' in error_msg:
                    logger.error("❌ OneNote file was sent directly to Mistral OCR (routing failed)")
                    return False
                else:
                    logger.info(f"ℹ️ Other processing method was used: {error_msg}")
                    return True
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"❌ Error testing enhanced document processor OneNote routing: {e}")
        return False


def test_mistral_ocr_onenote_rejection():
    """Test that Mistral OCR properly rejects OneNote files."""
    logger.info("\n=== Testing Mistral OCR OneNote Rejection ===")
    
    try:
        from utils.mistral_ocr import MistralOCRProcessor
        import asyncio
        
        # Create a mock OneNote file
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            temp_file.write(b'\xe4\x52\x5c\x7b\x8c\xd8\xa7\x4d\xae\xb1\x53\x78\xd0\x29\x96\xd3')
            temp_file.write(b'Mock OneNote content for Mistral OCR rejection testing')
            temp_file_path = temp_file.name
        
        try:
            # Initialize Mistral OCR processor
            mistral_ocr = MistralOCRProcessor()
            
            # Test direct processing (should be rejected)
            logger.info(f"Testing direct Mistral OCR processing of OneNote file: {temp_file_path}")
            result = asyncio.run(mistral_ocr.extract_text_from_document(temp_file_path))
            
            logger.info(f"Mistral OCR result: '{result}'")
            
            # Should return empty string due to our OneNote rejection logic
            if result == "":
                logger.info("✅ Mistral OCR correctly rejected OneNote file")
                return True
            else:
                logger.warning(f"⚠️ Mistral OCR processed OneNote file: {result}")
                return False
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"❌ Error testing Mistral OCR OneNote rejection: {e}")
        return False


def test_onenote_processor_direct():
    """Test OneNote processor directly."""
    logger.info("\n=== Testing OneNote Processor Direct ===")
    
    try:
        from processors.onenote_processor import OneNoteProcessor
        import asyncio
        
        # Create a mock OneNote file
        with tempfile.NamedTemporaryFile(suffix='.one', delete=False) as temp_file:
            temp_file.write(b'\xe4\x52\x5c\x7b\x8c\xd8\xa7\x4d\xae\xb1\x53\x78\xd0\x29\x96\xd3')
            temp_file.write(b'Mock OneNote content for direct processor testing')
            temp_file_path = temp_file.name
        
        try:
            # Initialize OneNote processor
            processor = OneNoteProcessor()
            
            # Test direct processing
            logger.info(f"Testing direct OneNote processor: {temp_file_path}")
            result = asyncio.run(processor.extract_text(temp_file_path))
            
            logger.info(f"OneNote processor result: {result}")
            
            # Check processing method used
            if result.get('success', False):
                processing_method = result.get('metadata', {}).get('processing_method', 'unknown')
                logger.info(f"✅ OneNote processor succeeded with method: {processing_method}")
                return True
            else:
                error_msg = result.get('error', '')
                logger.info(f"ℹ️ OneNote processor failed as expected: {error_msg}")
                # This is expected with a mock file
                return True
            
        finally:
            # Clean up
            os.unlink(temp_file_path)
            
    except Exception as e:
        logger.error(f"❌ Error testing OneNote processor direct: {e}")
        return False


def main():
    """Run all OneNote reference integration tests."""
    logger.info("Starting OneNote Reference Integration Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Reference Processor OneNote Routing", test_reference_processor_onenote_routing),
        ("Improved Reference Extractor OneNote Routing", test_improved_reference_extractor_onenote_routing),
        ("Enhanced Document Processor OneNote Routing", test_enhanced_document_processor_onenote_routing),
        ("Mistral OCR OneNote Rejection", test_mistral_ocr_onenote_rejection),
        ("OneNote Processor Direct", test_onenote_processor_direct),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            if result is None:
                result = True  # Assume success if no explicit return
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.error(f"❌ {test_name} test failed")
        except Exception as e:
            logger.error(f"❌ {test_name} test error: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("ONENOTE REFERENCE INTEGRATION TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{status} - {test_name}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! OneNote reference integration is working.")
        logger.info("\nKey Fixes Applied:")
        logger.info("✅ Reference processor routes OneNote files to OneNote processor")
        logger.info("✅ Improved reference extractor handles OneNote files properly")
        logger.info("✅ Enhanced document processor uses OneNote processor for .one files")
        logger.info("✅ Mistral OCR rejects direct OneNote file processing")
        logger.info("✅ OneNote processor handles PDF conversion for Mistral OCR")
    else:
        logger.warning("⚠️ Some tests failed. Check the logs above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
