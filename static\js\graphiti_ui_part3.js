/**
 * Graphiti UI Part 3 - Additional functionality for the Graphiti Knowledge Graph UI
 *
 * This file contains functions for the Metadata, References, and Settings tabs.
 */

// Global data storage
const documentData = {
    list: []
};

/**
 * Initialize the Documents tab
 */
function initializeDocumentsTab() {
    console.log("Initializing Documents tab");

    // Load document list
    loadDocumentList();
}

/**
 * Initialize the Metadata tab (alias for backward compatibility)
 */
function initializeMetadataTab() {
    console.log("Initializing Metadata tab (redirecting to Documents tab)");
    initializeDocumentsTab();
}

/**
 * Load document list
 */
function loadDocumentList() {
    // Show loading spinner
    const loadingSpinner = document.getElementById('documents-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch documents
    fetch('/api/documents?page=1&page_size=10')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store document data
            if (typeof documentData !== 'undefined') {
                documentData.list = data.documents;
            } else {
                console.warn('documentData is not defined');
            }

            // Render document list
            renderDocumentList(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading documents:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const documentsList = document.getElementById('documents-list');
            if (documentsList) {
                documentsList.innerHTML = `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
            }
        });
}

/**
 * Load document metadata (alias for backward compatibility)
 */
function loadDocumentMetadata() {
    console.log("loadDocumentMetadata called (redirecting to loadDocumentList)");
    loadDocumentList();
}

/**
 * Render document list
 *
 * @param {Object} data - Document data from the API
 */
function renderDocumentList(data) {
    const documentsList = document.getElementById('documents-list');
    if (!documentsList) {
        console.error("Documents list element not found");
        return;
    }

    // Clear existing content
    documentsList.innerHTML = '';

    // If no documents, show message
    if (!data.documents || data.documents.length === 0) {
        documentsList.innerHTML = '<div class="alert alert-info">No documents found.</div>';
        return;
    }

    // Create documents table
    const table = document.createElement('table');
    table.className = 'table table-striped table-hover';

    // Create table header
    const thead = document.createElement('thead');
    thead.innerHTML = `
        <tr>
            <th>Filename</th>
            <th>Type</th>
            <th>Upload Date</th>
            <th>Chunks</th>
            <th>Entities</th>
            <th>References</th>
            <th>Actions</th>
        </tr>
    `;
    table.appendChild(thead);

    // Create table body
    const tbody = document.createElement('tbody');

    // Add document rows
    data.documents.forEach(doc => {
        const row = document.createElement('tr');

        // Format date
        const uploadDate = new Date(doc.upload_date);
        const formattedDate = uploadDate.toLocaleDateString() + ' ' + uploadDate.toLocaleTimeString();

        row.innerHTML = `
            <td>${doc.filename}</td>
            <td>${doc.file_type}</td>
            <td>${formattedDate}</td>
            <td>${doc.chunks}</td>
            <td>${doc.entities}</td>
            <td>${doc.references}</td>
            <td>
                <a href="/documents/${doc.uuid}" class="btn btn-sm btn-primary">View</a>
            </td>
        `;

        tbody.appendChild(row);
    });

    table.appendChild(tbody);
    documentsList.appendChild(table);

    // Add pagination if available
    if (data.total > data.page_size) {
        const paginationContainer = document.createElement('div');
        paginationContainer.className = 'mt-4 d-flex justify-content-center';

        const pagination = document.createElement('nav');
        pagination.setAttribute('aria-label', 'Document pagination');

        const paginationList = document.createElement('ul');
        paginationList.className = 'pagination';

        // Calculate pagination values
        const totalPages = Math.ceil(data.total / data.page_size);
        const currentPage = data.page;
        const hasPrev = currentPage > 1;
        const hasNext = currentPage < totalPages;

        // Previous page button
        const prevItem = document.createElement('li');
        prevItem.className = `page-item ${hasPrev ? '' : 'disabled'}`;

        const prevLink = document.createElement('a');
        prevLink.className = 'page-link';
        prevLink.href = '#';
        prevLink.textContent = 'Previous';
        prevLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (hasPrev) {
                loadDocumentPage(currentPage - 1);
            }
        });

        prevItem.appendChild(prevLink);
        paginationList.appendChild(prevItem);

        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const pageItem = document.createElement('li');
            pageItem.className = `page-item ${i === currentPage ? 'active' : ''}`;

            const pageLink = document.createElement('a');
            pageLink.className = 'page-link';
            pageLink.href = '#';
            pageLink.textContent = i;
            pageLink.addEventListener('click', function(e) {
                e.preventDefault();
                loadDocumentPage(i);
            });

            pageItem.appendChild(pageLink);
            paginationList.appendChild(pageItem);
        }

        // Next page button
        const nextItem = document.createElement('li');
        nextItem.className = `page-item ${hasNext ? '' : 'disabled'}`;

        const nextLink = document.createElement('a');
        nextLink.className = 'page-link';
        nextLink.href = '#';
        nextLink.textContent = 'Next';
        nextLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (hasNext) {
                loadDocumentPage(currentPage + 1);
            }
        });

        nextItem.appendChild(nextLink);
        paginationList.appendChild(nextItem);

        pagination.appendChild(paginationList);
        paginationContainer.appendChild(pagination);
        documentsList.appendChild(paginationContainer);
    }
}

/**
 * Render document metadata (alias for backward compatibility)
 *
 * @param {Object} data - Document data from the API
 */
function renderDocumentMetadata(data) {
    console.log("renderDocumentMetadata called (redirecting to renderDocumentList)");
    renderDocumentList(data);
}

/**
 * Load a specific page of documents
 *
 * @param {number} page - Page number to load
 */
function loadDocumentPage(page) {
    // Show loading spinner
    const loadingSpinner = document.getElementById('documents-loading');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'flex';
    }

    // Fetch documents
    fetch(`/api/documents?page=${page}&page_size=10`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Store document data
            if (typeof documentData !== 'undefined') {
                documentData.list = data.documents;
            } else {
                console.warn('documentData is not defined');
            }

            // Render document list
            renderDocumentList(data);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }
        })
        .catch(error => {
            console.error('Error loading document page:', error);

            // Hide loading spinner
            if (loadingSpinner) {
                loadingSpinner.style.display = 'none';
            }

            // Show error message
            const documentsList = document.getElementById('documents-list');
            if (documentsList) {
                documentsList.innerHTML = `<div class="alert alert-danger">Error loading documents: ${error.message}</div>`;
            }
        });
}

/**
 * Load a specific page of document metadata (alias for backward compatibility)
 *
 * @param {number} page - Page number to load
 */
function loadDocumentMetadataPage(page) {
    console.log("loadDocumentMetadataPage called (redirecting to loadDocumentPage)");
    loadDocumentPage(page);
}

/**
 * Initialize the References tab
 */
function initializeReferencesTab() {
    console.log("Initializing References tab");

    // Load references
    loadReferences();

    // Set up event listeners
    setupReferenceEventListeners();
}

/**
 * Set up event listeners for the References tab
 */
function setupReferenceEventListeners() {
    // Add event listener for document selector
    const documentSelector = document.getElementById('document-selector');
    if (documentSelector) {
        documentSelector.addEventListener('change', function() {
            filterReferencesByDocument();
        });

        // Load documents for the selector
        loadDocumentsForSelector();
    }

    // Add event listener for reference search input
    const referenceSearchInput = document.getElementById('reference-search-input');
    if (referenceSearchInput) {
        referenceSearchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchReferences();
            }
        });
    }

    // Add event listener for export references button
    const exportReferencesButton = document.getElementById('export-references-button');
    if (exportReferencesButton) {
        exportReferencesButton.addEventListener('click', function() {
            exportReferencesCSV();
        });
    }

    // Add event listener for visualize references button
    const visualizeReferencesButton = document.getElementById('visualize-references-button');
    if (visualizeReferencesButton) {
        visualizeReferencesButton.addEventListener('click', function() {
            visualizeReferences();
        });
    }
}

/**
 * Load documents for the document selector
 */
function loadDocumentsForSelector() {
    // Fetch documents
    fetch('/api/documents?page=1&page_size=100')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Populate document selector
            const documentSelector = document.getElementById('document-selector');
            if (documentSelector) {
                // Clear existing options except the first one
                while (documentSelector.options.length > 1) {
                    documentSelector.remove(1);
                }

                // Add document options
                data.documents.forEach(doc => {
                    const option = document.createElement('option');
                    // Use filename for value since references are stored with filename as source_document
                    option.value = doc.filename;
                    option.textContent = doc.filename;
                    documentSelector.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading documents for selector:', error);
        });
}
